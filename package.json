{"name": "nuxt-tools", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.2.1", "@fortawesome/free-regular-svg-icons": "^6.2.1", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@fortawesome/vue-fontawesome": "^3.0.5", "@mmzp/sql-parser": "^0.2.1", "@nuxt/ui": "^2.11.1", "@nuxtjs/i18n": "^9.5.6", "@pinia/nuxt": "^0.5.1", "ascii85": "^1.0.2", "cron-parser": "^4.7.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "highlight.js": "^11.11.1", "ini": "^3.0.1", "javascript-obfuscator": "^4.0.0", "js-base64": "^3.7.3", "js-calendar-converter": "^0.0.4", "jsoneditor": "^9.9.2", "jwt-decode": "^3.1.2", "marked": "^15.0.12", "mathjs": "^11.5.0", "morse-decoder": "^3.0.1", "netmask": "^2.0.2", "nuxt": "^3.17.5", "nzh": "^1.0.8", "php-serialize": "^4.0.2", "pinia": "^2.1.7", "punycode": "^2.1.1", "qrcode": "^1.5.1", "sql-formatter": "^12.0.4", "tailwindcss": "^4.1.10", "tesseract.js": "^4.0.0", "vue": "^3.4.0", "vue-router": "^4.2.5", "yaml": "^2.1.3"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxt/eslint-config": "^0.2.0", "@types/node": "^20.10.0", "eslint": "^8.49.0", "sass": "^1.69.0", "typescript": "^5.3.0"}}