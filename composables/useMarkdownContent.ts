/**
 * Composable for loading markdown content based on current locale
 */
export const useMarkdownContent = () => {
  const { locale } = useI18n()

  /**
   * Load markdown content for a specific tool
   * @param toolName - The name of the tool (e.g., 'base64', 'sha1')
   * @returns Promise<string> - The markdown content
   */
  const loadToolMarkdown = async (toolName: string): Promise<string> => {
    try {
      // Try to load the markdown file for the current locale
      const response = await fetch(`/mds/${locale.value}/${toolName}.md`)
      
      if (response.ok) {
        return await response.text()
      } else {
        // Fallback to Chinese if the current locale file doesn't exist
        const fallbackResponse = await fetch(`/mds/zh/${toolName}.md`)
        if (fallbackResponse.ok) {
          return await fallbackResponse.text()
        } else {
          throw new Error(`Markdown file not found for tool: ${toolName}`)
        }
      }
    } catch (error) {
      console.error(`Failed to load markdown for ${toolName}:`, error)
      return `# ${toolName}\n\nContent not available.`
    }
  }

  /**
   * Reactive markdown content that updates when locale changes
   * @param toolName - The name of the tool
   * @returns Ref<string> - Reactive markdown content
   */
  const useToolMarkdown = (toolName: string) => {
    const content = ref('')

    // Load content initially and when locale changes
    const loadContent = async () => {
      content.value = await loadToolMarkdown(toolName)
    }

    // Load content on mount
    onMounted(loadContent)

    // Watch for locale changes and reload content
    watch(locale, loadContent)

    return content
  }

  return {
    loadToolMarkdown,
    useToolMarkdown
  }
}
