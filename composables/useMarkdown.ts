/**
 * Markdown 内容管理工具
 * 用于管理工具页面的 Markdown 文档内容
 */

export interface ToolMarkdownContent {
  title: string
  description: string
  features: string[]
  examples: Array<{
    title: string
    input: string
    output: string
    language?: string
  }>
  useCases: Array<{
    title: string
    description: string
    code?: string
    language?: string
  }>
  technicalDetails?: Array<{
    title: string
    content: string
  }>
  tips?: string[]
  warnings?: string[]
}

export const useMarkdown = () => {
  /**
   * 生成工具页面的 Markdown 内容
   */
  const generateToolMarkdown = (content: ToolMarkdownContent): string => {
    let markdown = `# ${content.title}\n\n${content.description}\n\n`

    // 主要特性
    if (content.features.length > 0) {
      markdown += `## ✨ 主要特性\n\n`
      content.features.forEach(feature => {
        markdown += `- ${feature}\n`
      })
      markdown += '\n'
    }

    // 使用示例
    if (content.examples.length > 0) {
      markdown += `## 📖 使用示例\n\n`
      content.examples.forEach((example, index) => {
        if (content.examples.length > 1) {
          markdown += `### ${example.title}\n\n`
        }
        
        markdown += `**输入：**\n\`\`\`${example.language || 'text'}\n${example.input}\n\`\`\`\n\n`
        markdown += `**输出：**\n\`\`\`${example.language || 'text'}\n${example.output}\n\`\`\`\n\n`
      })
    }

    // 应用场景
    if (content.useCases.length > 0) {
      markdown += `## 🎯 应用场景\n\n`
      content.useCases.forEach((useCase, index) => {
        markdown += `### ${index + 1}. ${useCase.title}\n\n${useCase.description}\n\n`
        if (useCase.code) {
          markdown += `\`\`\`${useCase.language || 'text'}\n${useCase.code}\n\`\`\`\n\n`
        }
      })
    }

    // 技术细节
    if (content.technicalDetails && content.technicalDetails.length > 0) {
      markdown += `## 🔧 技术细节\n\n`
      content.technicalDetails.forEach(detail => {
        markdown += `### ${detail.title}\n\n${detail.content}\n\n`
      })
    }

    // 使用技巧
    if (content.tips && content.tips.length > 0) {
      markdown += `## 💡 使用技巧\n\n`
      content.tips.forEach(tip => {
        markdown += `- ${tip}\n`
      })
      markdown += '\n'
    }

    // 注意事项
    if (content.warnings && content.warnings.length > 0) {
      markdown += `## ⚠️ 注意事项\n\n`
      content.warnings.forEach((warning, index) => {
        markdown += `${index + 1}. ${warning}\n`
      })
      markdown += '\n'
    }

    // 结尾
    markdown += `## 🚀 开始使用\n\n`
    markdown += `1. 在上方输入框中输入要处理的内容\n`
    markdown += `2. 点击相应的操作按钮\n`
    markdown += `3. 查看处理结果\n`
    markdown += `4. 复制或下载结果\n\n`
    markdown += `> **提示**：工具支持多种数据格式，请确保输入内容格式正确。`

    return markdown
  }

  /**
   * 常用的 Markdown 模板
   */
  const templates = {
    // 编码解码工具模板
    encodingTool: (toolName: string, algorithm: string): ToolMarkdownContent => ({
      title: `${toolName} 工具`,
      description: `${toolName} 是一种常用的${algorithm}算法，广泛应用于数据处理和信息安全领域。`,
      features: [
        `🚀 **快速${algorithm}**：高效的${algorithm}处理`,
        '📱 **多格式支持**：支持文本、文件等多种输入格式',
        '🔒 **安全可靠**：使用标准算法，确保结果准确',
        '💾 **结果下载**：支持将结果保存为文件',
        '🌐 **中文支持**：完美支持中文字符处理'
      ],
      examples: [],
      useCases: []
    }),

    // 格式化工具模板
    formatterTool: (toolName: string, format: string): ToolMarkdownContent => ({
      title: `${toolName} 工具`,
      description: `${toolName} 可以对${format}格式的数据进行格式化、验证和美化处理。`,
      features: [
        `🎯 **${format}格式化**：自动格式化和美化代码`,
        '✅ **语法验证**：检测并提示格式错误',
        '🔧 **自定义选项**：支持多种格式化选项',
        '📋 **一键复制**：快速复制格式化结果',
        '📱 **响应式设计**：支持各种设备访问'
      ],
      examples: [],
      useCases: []
    }),

    // 生成器工具模板
    generatorTool: (toolName: string, generateType: string): ToolMarkdownContent => ({
      title: `${toolName} 工具`,
      description: `${toolName} 可以快速生成${generateType}，满足各种开发和测试需求。`,
      features: [
        `⚡ **快速生成**：一键生成${generateType}`,
        '🎛️ **自定义选项**：支持多种生成参数',
        '📊 **批量生成**：支持批量生成多个结果',
        '💾 **导出功能**：支持多种格式导出',
        '🔄 **实时预览**：实时查看生成结果'
      ],
      examples: [],
      useCases: []
    })
  }

  return {
    generateToolMarkdown,
    templates
  }
}
