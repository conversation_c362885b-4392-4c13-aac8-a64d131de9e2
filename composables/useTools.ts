export interface ToolInfo {
  name: string
  route: string
  categoryList: string[]
}

export interface CategoryInfo {
  name: string
  num: number
  toolList: ToolInfo[]
}

export const useTools = () => {
  const toolInfoList: ToolInfo[] = [
    // 编码工具
    {
      name: 'base64',
      route: '/encrypt/base64',
      categoryList: ['code', 'it'],
    },
    {
      name: 'md5',
      route: '/encrypt/md5',
      categoryList: ['code', 'it'],
    },
    {
      name: 'sha1',
      route: '/encrypt/sha1',
      categoryList: ['code', 'it'],
    },
    {
      name: 'urlencode',
      route: '/encrypt/url',
      categoryList: ['code', 'it'],
    },
    {
      name: 'jwtParse',
      route: '/encrypt/jwt',
      categoryList: ['code', 'it'],
    },
    // 格式化工具
    {
      name: 'JSON2YAML',
      route: '/format/json2yaml',
      categoryList: ['format', 'it'],
    },
    {
      name: 'SQLFormatter',
      route: '/format/sql',
      categoryList: ['format', 'it'],
    },
    {
      name: 'numberToZh',
      route: '/format/numberToZh',
      categoryList: ['format', 'tool'],
    },
    {
      name: 'NetscapeCookies',
      route: '/format/netscapeCookies',
      categoryList: ['format', 'it'],
    },
    {
      name: 'YAMLFormatter',
      route: '/format/yaml',
      categoryList: ['format', 'it'],
    },
    // 文本工具
    {
      name: 'reverseString',
      route: '/text/reverseString',
      categoryList: ['text', 'tool'],
    },
    // 生成器工具
    {
      name: 'qrGenerator',
      route: '/generator/qr',
      categoryList: ['generator', 'tool'],
    },
    {
      name: 'UUIDGenerator',
      route: '/generator/uuid',
      categoryList: ['generator', 'it'],
    },
    {
      name: 'JSONToPhpArrayCode',
      route: '/it/json-to-php-array-code',
      categoryList: ['it'],
    }
  ]

  const getAll = () => toolInfoList

  const getAllCategoryList = (): Record<string, CategoryInfo> => {
    const categoryMap: Record<string, CategoryInfo> = {}

    toolInfoList.forEach(item => {
      item.categoryList.forEach(categoryName => {
        if (categoryMap[categoryName] === undefined) {
          categoryMap[categoryName] = {
            name: categoryName,
            num: 1,
            toolList: [item]
          }
        } else {
          categoryMap[categoryName].num++
          categoryMap[categoryName].toolList.push(item)
        }
      })
    })

    return categoryMap
  }

  const getToolsByCategory = (categoryName: string): ToolInfo[] => {
    const categoryMap = getAllCategoryList()
    return categoryMap[categoryName]?.toolList || []
  }

  const searchTools = (keyword: string): ToolInfo[] => {
    return toolInfoList.filter(tool => 
      tool.name.toLowerCase().includes(keyword.toLowerCase())
    )
  }

  const toolLocalesKey = (toolTittle: string) => `tools.${toolTittle}`

  return {
    getAll,
    getAllCategoryList,
    getToolsByCategory,
    searchTools,
    toolLocalesKey
  }
}
