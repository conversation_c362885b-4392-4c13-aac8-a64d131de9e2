export interface ToolInfo {
  title: string
  route: string
  categoryList: string[]
  description?: string
}

export interface CategoryInfo {
  name: string
  num: number
  toolList: ToolInfo[]
}

export const useTools = () => {
  const toolInfoList: ToolInfo[] = [
    // 编码工具
    {
      title: 'base64',
      route: '/encrypt/base64',
      categoryList: ['编码', 'IT'],
      description: 'Base64 编码解码工具，支持文本和文件的Base64编码和解码'
    },
    {
      title: 'md5',
      route: '/encrypt/md5',
      categoryList: ['编码', 'IT'],
      description: 'MD5 哈希加密工具'
    },
    {
      title: 'sha1',
      route: '/encrypt/sha1',
      categoryList: ['编码', 'IT'],
      description: 'SHA1 哈希加密工具'
    },
    {
      title: 'URL 编码解码',
      route: '/encrypt/url',
      categoryList: ['编码', 'IT'],
      description: 'URL 编码解码工具'
    },
    {
      title: 'JWT 解析',
      route: '/encrypt/jwt',
      categoryList: ['编码', 'IT'],
      description: 'JWT Token 解析工具'
    },
    // 格式化工具
    {
      title: 'JSON转YAML',
      route: '/format/json2yaml',
      categoryList: ['格式化', 'IT'],
      description: 'JSON 和 YAML 格式互转工具'
    },
    {
      title: 'SQL 格式化',
      route: '/format/sql',
      categoryList: ['格式化', 'IT'],
      description: 'SQL 语句格式化和美化工具'
    },
    {
      title: '数字转中文',
      route: '/format/numberToZh',
      categoryList: ['格式化', '工具'],
      description: '阿拉伯数字转换为中文数字'
    },
    {
      title: 'Netscape Cookies',
      route: '/format/netscapeCookies',
      categoryList: ['格式化', 'IT'],
      description: 'Netscape Cookies格式转换工具'
    },
    {
      title: 'YAML 格式化',
      route: '/format/yaml',
      categoryList: ['格式化', 'IT'],
      description: 'YAML 格式化和验证工具'
    },
    // 文本工具
    {
      title: '字符串反转',
      route: '/text/reverseString',
      categoryList: ['文本', '工具'],
      description: '字符串反转和文本处理工具'
    },
    // 生成器工具
    {
      title: '二维码生成器',
      route: '/generator/qr',
      categoryList: ['生成器', '工具'],
      description: '在线二维码生成工具'
    },
    {
      title: 'UUID 生成器',
      route: '/generator/uuid',
      categoryList: ['生成器', 'IT'],
      description: 'UUID 唯一标识符生成工具'
    },
    {
      title: 'JSON 转 PHP 数组代码',
      route: '/it/json-to-php-array-code',
      categoryList: ['IT'],
      description: '将 JSON 转换为 PHP 的数组代码，直接复制可以粘贴到 PHP 代码中'
    }
  ]

  const getAll = () => toolInfoList

  const getAllCategoryList = (): Record<string, CategoryInfo> => {
    const categoryMap: Record<string, CategoryInfo> = {}

    toolInfoList.forEach(item => {
      item.categoryList.forEach(categoryName => {
        if (categoryMap[categoryName] === undefined) {
          categoryMap[categoryName] = {
            name: categoryName,
            num: 1,
            toolList: [item]
          }
        } else {
          categoryMap[categoryName].num++
          categoryMap[categoryName].toolList.push(item)
        }
      })
    })

    return categoryMap
  }

  const getToolsByCategory = (categoryName: string): ToolInfo[] => {
    const categoryMap = getAllCategoryList()
    return categoryMap[categoryName]?.toolList || []
  }

  const searchTools = (keyword: string): ToolInfo[] => {
    return toolInfoList.filter(tool => 
      tool.title.toLowerCase().includes(keyword.toLowerCase()) ||
      tool.description?.toLowerCase().includes(keyword.toLowerCase())
    )
  }

  return {
    getAll,
    getAllCategoryList,
    getToolsByCategory,
    searchTools
  }
}
