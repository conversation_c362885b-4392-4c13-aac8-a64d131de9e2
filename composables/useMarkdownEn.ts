/**
 * English Markdown content management tool
 * For managing English tool page Markdown documentation content
 */

export interface ToolMarkdownContentEn {
  title: string
  description: string
  features: string[]
  examples: Array<{
    title: string
    input: string
    output: string
    language?: string
  }>
  useCases: Array<{
    title: string
    description: string
    code?: string
    language?: string
  }>
  technicalDetails?: Array<{
    title: string
    content: string
  }>
  tips?: string[]
  warnings?: string[]
}

export const useMarkdownEn = () => {
  /**
   * Generate English tool page Markdown content
   */
  const generateToolMarkdownEn = (content: ToolMarkdownContentEn): string => {
    let markdown = `# ${content.title}\n\n${content.description}\n\n`

    // Key Features
    if (content.features.length > 0) {
      markdown += `## ✨ Key Features\n\n`
      content.features.forEach(feature => {
        markdown += `- ${feature}\n`
      })
      markdown += '\n'
    }

    // Usage Examples
    if (content.examples.length > 0) {
      markdown += `## 📖 Usage Examples\n\n`
      content.examples.forEach((example, index) => {
        if (content.examples.length > 1) {
          markdown += `### ${example.title}\n\n`
        }
        
        markdown += `**Input:**\n\`\`\`${example.language || 'text'}\n${example.input}\n\`\`\`\n\n`
        markdown += `**Output:**\n\`\`\`${example.language || 'text'}\n${example.output}\n\`\`\`\n\n`
      })
    }

    // Use Cases
    if (content.useCases.length > 0) {
      markdown += `## 🎯 Use Cases\n\n`
      content.useCases.forEach((useCase, index) => {
        markdown += `### ${index + 1}. ${useCase.title}\n\n${useCase.description}\n\n`
        if (useCase.code) {
          markdown += `\`\`\`${useCase.language || 'text'}\n${useCase.code}\n\`\`\`\n\n`
        }
      })
    }

    // Technical Details
    if (content.technicalDetails && content.technicalDetails.length > 0) {
      markdown += `## 🔧 Technical Details\n\n`
      content.technicalDetails.forEach(detail => {
        markdown += `### ${detail.title}\n\n${detail.content}\n\n`
      })
    }

    // Usage Tips
    if (content.tips && content.tips.length > 0) {
      markdown += `## 💡 Usage Tips\n\n`
      content.tips.forEach(tip => {
        markdown += `- ${tip}\n`
      })
      markdown += '\n'
    }

    // Important Notes
    if (content.warnings && content.warnings.length > 0) {
      markdown += `## ⚠️ Important Notes\n\n`
      content.warnings.forEach((warning, index) => {
        markdown += `${index + 1}. ${warning}\n`
      })
      markdown += '\n'
    }

    // Getting Started
    markdown += `## 🚀 Getting Started\n\n`
    markdown += `1. Enter your content in the input field above\n`
    markdown += `2. Click the appropriate action button\n`
    markdown += `3. View the processed result\n`
    markdown += `4. Copy or download the result\n\n`
    markdown += `> **Tip**: The tool supports multiple data formats. Please ensure your input content is in the correct format.`

    return markdown
  }

  /**
   * Common English Markdown templates
   */
  const templatesEn = {
    // Encoding/Decoding tool template
    encodingTool: (toolName: string, algorithm: string): ToolMarkdownContentEn => ({
      title: `${toolName} Tool`,
      description: `${toolName} is a commonly used ${algorithm} algorithm widely applied in data processing and information security fields.`,
      features: [
        `🚀 **Fast ${algorithm}**: Efficient ${algorithm} processing`,
        '📱 **Multiple Format Support**: Support text, files and various input formats',
        '🔒 **Secure & Reliable**: Uses standard algorithm ensuring accurate results',
        '💾 **Result Download**: Support saving results as files',
        '🌐 **Unicode Support**: Perfect support for international characters'
      ],
      examples: [],
      useCases: []
    }),

    // Formatting tool template
    formatterTool: (toolName: string, format: string): ToolMarkdownContentEn => ({
      title: `${toolName} Tool`,
      description: `${toolName} can format, validate and beautify ${format} format data.`,
      features: [
        `🎯 **${format} Formatting**: Automatic formatting and code beautification`,
        '✅ **Syntax Validation**: Detect and highlight format errors',
        '🔧 **Custom Options**: Support multiple formatting options',
        '📋 **One-Click Copy**: Quick copy of formatted results',
        '📱 **Responsive Design**: Support access from various devices'
      ],
      examples: [],
      useCases: []
    }),

    // Generator tool template
    generatorTool: (toolName: string, generateType: string): ToolMarkdownContentEn => ({
      title: `${toolName} Tool`,
      description: `${toolName} can quickly generate ${generateType} to meet various development and testing needs.`,
      features: [
        `⚡ **Quick Generation**: One-click generation of ${generateType}`,
        '🎛️ **Custom Options**: Support multiple generation parameters',
        '📊 **Batch Generation**: Support generating multiple results at once',
        '💾 **Export Function**: Support exporting in multiple formats',
        '🔄 **Real-time Preview**: Real-time view of generation results'
      ],
      examples: [],
      useCases: []
    })
  }

  return {
    generateToolMarkdownEn,
    templatesEn
  }
}
