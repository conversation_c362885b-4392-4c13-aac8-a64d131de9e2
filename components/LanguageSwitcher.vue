<template>
  <div class="relative">
    <!-- Language Switcher Button -->
    <button
      @click="toggleDropdown"
      class="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
      </svg>
      <span>{{ currentLocale.name }}</span>
      <svg 
        class="w-4 h-4 transition-transform duration-200"
        :class="{ 'rotate-180': isOpen }"
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>

    <!-- Dropdown Menu -->
    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-show="isOpen"
        class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
      >
        <div class="py-1">
          <button
            v-for="locale in availableLocales"
            :key="locale.code"
            @click="switchLanguage(locale.code)"
            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150"
            :class="{ 'bg-gray-100 text-gray-900': locale.code === $i18n.locale.value }"
          >
            <span class="mr-3 text-lg">{{ getFlag(locale.code) }}</span>
            <div class="flex flex-col items-start">
              <span class="font-medium">{{ locale.name }}</span>
              <span class="text-xs text-gray-500">{{ locale.code.toUpperCase() }}</span>
            </div>
            <svg 
              v-if="locale.code === $i18n.locale.value"
              class="ml-auto w-4 h-4 text-green-600" 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </transition>

    <!-- Overlay to close dropdown when clicking outside -->
    <div
      v-if="isOpen"
      @click="closeDropdown"
      class="fixed inset-0 z-40"
    ></div>
  </div>
</template>

<script setup>
const { $i18n } = useNuxtApp()
const switchLocalePath = useSwitchLocalePath()

// Reactive state
const isOpen = ref(false)

// Available locales from i18n config
const availableLocales = computed(() => $i18n.locales.value)

// Current locale info
const currentLocale = computed(() => {
  return availableLocales.value.find(locale => locale.code === $i18n.locale.value) || availableLocales.value[0]
})

// Toggle dropdown
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

// Close dropdown
const closeDropdown = () => {
  isOpen.value = false
}

// Switch language
const switchLanguage = async (localeCode) => {
  if (localeCode !== $i18n.locale.value) {
    await navigateTo(switchLocalePath(localeCode))
  }
  closeDropdown()
}

// Get flag emoji for locale
const getFlag = (localeCode) => {
  const flags = {
    'zh': '🇨🇳',
    'en': '🇺🇸',
    'ja': '🇯🇵',
    'ko': '🇰🇷',
    'fr': '🇫🇷',
    'de': '🇩🇪',
    'es': '🇪🇸',
    'it': '🇮🇹',
    'pt': '🇵🇹',
    'ru': '🇷🇺'
  }
  return flags[localeCode] || '🌐'
}

// Close dropdown when clicking escape key
onMounted(() => {
  const handleEscape = (event) => {
    if (event.key === 'Escape') {
      closeDropdown()
    }
  }
  
  document.addEventListener('keydown', handleEscape)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleEscape)
  })
})
</script>
