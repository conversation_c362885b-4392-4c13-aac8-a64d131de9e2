<template>
  <div class="markdown-content" v-html="renderedMarkdown"></div>
</template>

<script setup lang="ts">
import { marked } from 'marked'
import hljs from 'highlight.js'

interface Props {
  content: string
}

const props = defineProps<Props>()

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {
        console.error('Highlight.js error:', err)
      }
    }
    return hljs.highlightAuto(code).value
  },
  langPrefix: 'hljs language-',
  breaks: true,
  gfm: true
})

// 渲染 Markdown
const renderedMarkdown = computed(() => {
  if (!props.content) return ''
  return marked(props.content)
})
</script>

<style scoped>
.markdown-content {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
  line-height: 1.6;
}

/* Markdown 样式 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin-top: 0;
  margin-bottom: 16px;
  color: #374151;
  font-weight: 600;
}

.markdown-content :deep(h1) {
  font-size: 2em;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.markdown-content :deep(h2) {
  font-size: 1.5em;
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 6px;
}

.markdown-content :deep(h3) {
  font-size: 1.25em;
}

.markdown-content :deep(p) {
  margin-bottom: 16px;
  color: #6b7280;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin-bottom: 16px;
  padding-left: 24px;
  color: #6b7280;
}

.markdown-content :deep(li) {
  margin-bottom: 4px;
}

.markdown-content :deep(blockquote) {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #00b96b;
  background: #f0f9f4;
  color: #374151;
}

.markdown-content :deep(code) {
  padding: 2px 6px;
  background: #f3f4f6;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  color: #e11d48;
}

.markdown-content :deep(pre) {
  margin: 16px 0;
  padding: 16px;
  background: #1f2937;
  border-radius: 6px;
  overflow-x: auto;
  border: 1px solid #374151;
}

.markdown-content :deep(pre code) {
  padding: 0;
  background: transparent;
  color: #f9fafb;
  font-size: 14px;
  line-height: 1.5;
}

.markdown-content :deep(table) {
  width: 100%;
  margin: 16px 0;
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  text-align: left;
}

.markdown-content :deep(th) {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.markdown-content :deep(td) {
  color: #6b7280;
}

.markdown-content :deep(a) {
  color: #00b96b;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.markdown-content :deep(a:hover) {
  border-bottom-color: #00b96b;
}

.markdown-content :deep(strong) {
  font-weight: 600;
  color: #374151;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #6b7280;
}

.markdown-content :deep(hr) {
  margin: 24px 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

/* 代码高亮样式 */
.markdown-content :deep(.hljs) {
  background: #1f2937 !important;
  color: #f9fafb;
}

.markdown-content :deep(.hljs-keyword) {
  color: #8b5cf6;
}

.markdown-content :deep(.hljs-string) {
  color: #10b981;
}

.markdown-content :deep(.hljs-number) {
  color: #f59e0b;
}

.markdown-content :deep(.hljs-comment) {
  color: #9ca3af;
  font-style: italic;
}

.markdown-content :deep(.hljs-function) {
  color: #3b82f6;
}

.markdown-content :deep(.hljs-variable) {
  color: #ef4444;
}

.markdown-content :deep(.hljs-title) {
  color: #06b6d4;
}

.markdown-content :deep(.hljs-attr) {
  color: #8b5cf6;
}

.markdown-content :deep(.hljs-built_in) {
  color: #f59e0b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-content {
    padding: 16px;
    margin-top: 16px;
  }
  
  .markdown-content :deep(pre) {
    padding: 12px;
    font-size: 12px;
  }
  
  .markdown-content :deep(table) {
    font-size: 14px;
  }
  
  .markdown-content :deep(th),
  .markdown-content :deep(td) {
    padding: 6px 8px;
  }
}
</style>
