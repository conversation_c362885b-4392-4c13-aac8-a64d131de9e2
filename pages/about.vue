<template>
  <div class="about-page">
    <h1>关于本站</h1>
    
    <div class="card">
      <h3>关于工具迷</h3>
      <p>
        工具迷是一个专注于提供高质量在线工具的网站，我们致力于为开发者、设计师和普通用户提供便捷、高效的在线工具服务。
      </p>
      <p>
        我们的目标是成为"下一代在线效率工具"，通过现代化的技术栈和用户友好的界面设计，让每个人都能轻松使用各种实用工具。
      </p>
    </div>

    <div class="card">
      <h3>工具分类</h3>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
        <div
          v-for="(category, key) in categories"
          :key="key"
          class="category-card"
          @click="goToCategory(category.name)"
        >
          <h4>{{ category.name }}</h4>
          <p>{{ category.num }} 个工具</p>
          <button class="category-btn">
            查看工具 →
          </button>
        </div>
      </div>
    </div>

    <div class="card">
      <h3>联系我们</h3>
      <p>
        如果您有任何建议、问题或合作意向，欢迎通过以下方式联系我们：
      </p>
      <ul>
        <li>
          <a 
            href="https://support.qq.com/product/290995" 
            target="_blank"
            style="color: #00b96b; text-decoration: none;"
          >
            反馈建议
          </a>
        </li>
        <li>网站：https://www.gongjumi.com</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
const { getAllCategoryList } = useTools()

// SEO 配置
useSeoMeta({
  title: '关于本站 - 工具迷',
  description: '了解工具迷网站的更多信息，技术栈和功能特性',
  keywords: '关于,工具迷,在线工具,Nuxt3,技术栈'
})

const categories = computed(() => getAllCategoryList())

const goToCategory = (categoryName: string) => {
  navigateTo(`/zh/category?name=${categoryName}`)
}
</script>

<style scoped>
.about-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.about-page h1 {
  text-align: center;
  font-size: 32px;
  margin-bottom: 32px;
  color: #374151;
}

.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card h3 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #374151;
}

.card h4 {
  font-size: 18px;
  margin-bottom: 12px;
  color: #374151;
  font-weight: 600;
}

.card p {
  line-height: 1.6;
  color: #6b7280;
  margin-bottom: 16px;
}

.card ul {
  list-style: none;
  padding: 0;
}

.card ul li {
  padding: 4px 0;
  color: #6b7280;
}

.category-card {
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.category-card:hover {
  border-color: #00b96b;
  box-shadow: 0 4px 12px rgba(0, 185, 107, 0.15);
  transform: translateY(-2px);
}

.category-card h4 {
  margin-bottom: 8px;
  font-size: 16px;
}

.category-card p {
  margin-bottom: 12px;
  font-size: 14px;
  color: #6b7280;
}

.category-btn {
  background: #00b96b;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s;
}

.category-btn:hover {
  background: #009456;
}
</style>
