<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-center mb-8">颜色工具测试页面</h1>
    
    <div class="text-center mb-8">
      <NuxtLink 
        to="/color" 
        class="inline-block bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition-colors"
      >
        打开颜色选择器工具
      </NuxtLink>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 颜色预览卡片 -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold mb-4">主要颜色</h3>
        <div class="space-y-3">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded bg-blue-500"></div>
            <span class="font-mono text-sm">#3b82f6</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded bg-green-500"></div>
            <span class="font-mono text-sm">#10b981</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded bg-red-500"></div>
            <span class="font-mono text-sm">#ef4444</span>
          </div>
        </div>
      </div>

      <!-- 渐变预览 -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold mb-4">渐变效果</h3>
        <div class="space-y-3">
          <div class="h-8 rounded bg-gradient-to-r from-blue-400 to-purple-500"></div>
          <div class="h-8 rounded bg-gradient-to-r from-green-400 to-blue-500"></div>
          <div class="h-8 rounded bg-gradient-to-r from-pink-400 to-red-500"></div>
        </div>
      </div>

      <!-- 对比度示例 -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold mb-4">对比度示例</h3>
        <div class="space-y-3">
          <div class="p-3 bg-black text-white rounded">
            <span class="text-sm">高对比度文本</span>
          </div>
          <div class="p-3 bg-gray-300 text-gray-700 rounded">
            <span class="text-sm">中等对比度文本</span>
          </div>
          <div class="p-3 bg-gray-100 text-gray-400 rounded">
            <span class="text-sm">低对比度文本</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能说明 -->
    <div class="mt-12 bg-gray-50 rounded-lg p-8">
      <h2 class="text-2xl font-bold mb-6 text-center">颜色工具功能</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h3 class="text-lg font-semibold mb-3">🎨 颜色选择</h3>
          <ul class="space-y-2 text-gray-600">
            <li>• 可视化颜色选择器</li>
            <li>• 支持HEX、RGB、HSL、HSV格式</li>
            <li>• 实时颜色预览</li>
            <li>• 颜色历史记录</li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-semibold mb-3">🌈 调色板生成</h3>
          <ul class="space-y-2 text-gray-600">
            <li>• 互补色方案</li>
            <li>• 类似色方案</li>
            <li>• 三角色方案</li>
            <li>• 单色方案</li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-semibold mb-3">📊 对比度分析</h3>
          <ul class="space-y-2 text-gray-600">
            <li>• WCAG无障碍标准检测</li>
            <li>• AA/AAA级别验证</li>
            <li>• 实时对比度计算</li>
            <li>• 可读性预览</li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-semibold mb-3">🔧 实用功能</h3>
          <ul class="space-y-2 text-gray-600">
            <li>• 一键复制颜色代码</li>
            <li>• 预设颜色库</li>
            <li>• 格式自动转换</li>
            <li>• 本地存储历史</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
useHead({
  title: '颜色工具测试 - ToolMi',
  meta: [
    { name: 'description', content: '测试颜色选择器工具的功能和界面' }
  ]
})
</script>

<style scoped>
/* 自定义样式 */
.container {
  max-width: 1200px;
}
</style>
