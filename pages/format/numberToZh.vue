<template>
  <div class="simple-textarea">
    <h2>数字转中文</h2>
    <p>阿拉伯数字转换为中文数字，支持简体和繁体</p>
    
    <div class="form-item">
      <label>输入数字</label>
      <input
        v-model="inputValue"
        type="text"
        placeholder="请输入数字，如：12345"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="form-item">
      <label>转换类型</label>
      <div class="radio-group">
        <label class="radio-item">
          <input v-model="convertType" type="radio" value="simple" />
          简体中文
        </label>
        <label class="radio-item">
          <input v-model="convertType" type="radio" value="traditional" />
          繁体中文
        </label>
        <label class="radio-item">
          <input v-model="convertType" type="radio" value="financial" />
          大写金额
        </label>
      </div>
    </div>

    <div class="btn-group">
      <button @click="convertNumber" class="btn btn-primary">
        转换
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        复制
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        载入示例
      </button>
    </div>

    <div class="form-item">
      <label>转换结果</label>
      <textarea
        v-model="outputValue"
        rows="3"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 示例展示 -->
    <div class="examples">
      <h3>转换示例</h3>
      <div class="example-grid">
        <div class="example-item">
          <strong>123</strong> → 一百二十三
        </div>
        <div class="example-item">
          <strong>1234</strong> → 一千二百三十四
        </div>
        <div class="example-item">
          <strong>12345</strong> → 一万二千三百四十五
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard } = useUtils()

// SEO 配置
useSeoMeta({
  title: '数字转中文工具 - 阿拉伯数字转换中文大写金额 | 工具迷',
  description: '数字转中文在线工具，支持阿拉伯数字转换为简体中文、繁体中文和大写金额格式。适用于财务报表、合同文件、发票开具等场景。支持千亿以内数字转换，提供实时预览和一键复制功能。',
  keywords: '数字转中文,阿拉伯数字转换,中文数字,大写金额,繁体中文,简体中文,财务工具,发票金额,合同金额,在线转换'
})

const inputValue = ref('')
const outputValue = ref('')
const convertType = ref('simple')
const errorMessage = ref('')
const exampleNumber = '12345'

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: '数字转中文工具',
  description: '数字转中文工具可以将阿拉伯数字快速转换为中文数字格式，支持简体中文、繁体中文和大写金额三种格式。广泛应用于财务报表、合同文件、发票开具等需要中文数字表示的场景。',
  features: [
    '🔢 **多格式支持**：简体中文、繁体中文、大写金额三种格式',
    '⚡ **实时转换**：输入数字即时显示转换结果',
    '📊 **大数支持**：支持千亿以内的数字转换',
    '💰 **金额专用**：大写金额格式专为财务场景设计',
    '📋 **一键复制**：快速复制转换结果到剪贴板'
  ],
  examples: [
    {
      title: '简体中文示例',
      input: '12345',
      output: '一万二千三百四十五',
      language: 'text'
    },
    {
      title: '繁体中文示例',
      input: '12345',
      output: '壹萬貳仟叁佰肆拾伍',
      language: 'text'
    },
    {
      title: '大写金额示例',
      input: '12345',
      output: '壹萬貳仟叁佰肆拾伍元整',
      language: 'text'
    }
  ],
  useCases: [
    {
      title: '财务报表',
      description: '在财务报表中使用中文大写金额，确保数据的正式性和防篡改性：',
      code: `营业收入：壹仟贰佰叁拾肆万伍仟陆佰柒拾捌元整
净利润：玖拾捌万柒仟陆佰伍拾肆元整
总资产：伍仟陆佰柒拾捌万玖仟零壹拾贰元整`,
      language: 'text'
    },
    {
      title: '合同文件',
      description: '在合同中使用中文数字表示金额，增强法律效力：',
      code: `合同金额：人民币壹佰贰拾叁万肆仟伍佰陆拾柒元整（¥1,234,567.00）
首付款：人民币叁拾陆万捌仟玖佰零壹元整（¥368,901.00）
尾款：人民币捌拾陆万伍仟陆佰陆拾陆元整（¥865,666.00）`,
      language: 'text'
    },
    {
      title: '发票开具',
      description: '开具发票时使用标准的中文大写金额格式：',
      code: `发票金额：伍万捌仟柒佰陆拾伍元肆角贰分
税额：柒仟陆佰贰拾玖元柒角零伍分
价税合计：陆万叁仟叁佰玖拾伍元壹角柒分`,
      language: 'text'
    },
    {
      title: '银行业务',
      description: '银行转账、支票填写等业务中的金额表示：',
      code: `转账金额：贰拾万零叁仟肆佰伍拾陆元整
支票金额：壹拾伍万捌仟玖佰柒拾陆元整
存款金额：叁拾贰万壹仟贰佰叁拾肆元整`,
      language: 'text'
    }
  ],
  technicalDetails: [
    {
      title: '转换规则',
      content: `数字转中文遵循以下规则：

**基础数字映射：**
- 简体：零一二三四五六七八九
- 繁体：零壹贰叁肆伍陆柒捌玖

**单位处理：**
- 个、十、百、千、万、十万、百万、千万、亿
- 繁体：個、拾、佰、仟、萬、拾萬、佰萬、仟萬、億

**特殊规则：**
- "一十" 简化为 "十"
- 连续的零合并为一个"零"
- 末尾的零省略
- 大写金额自动添加"元整"`
    },
    {
      title: '支持范围',
      content: `工具支持的数字范围和格式：

**数字范围：** 0 - 999,999,999,999（千亿以内）
**输入格式：** 整数和小数（小数部分会被忽略）
**输出格式：**
- 简体中文：日常使用的中文数字
- 繁体中文：正式文档使用的繁体数字
- 大写金额：财务专用的大写格式

**限制说明：**
- 不支持负数
- 小数部分会被自动忽略
- 超出范围的数字会提示错误`
    }
  ],
  tips: [
    '**格式选择**：日常使用选简体，正式文档选繁体，财务场景选大写金额',
    '**实时转换**：输入数字后会自动转换，无需手动点击按钮',
    '**复制技巧**：转换完成后点击复制按钮，可直接粘贴到文档中',
    '**格式切换**：可以随时切换转换格式，结果会实时更新'
  ],
  warnings: [
    '**数字范围**：仅支持千亿以内的数字，超出范围会显示错误',
    '**小数处理**：工具会自动忽略小数部分，只转换整数部分',
    '**格式规范**：大写金额格式严格按照财务规范，适用于正式文件',
    '**字符编码**：复制结果时注意文档的字符编码设置'
  ]
})

// 中文数字映射
const simpleDigits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
const traditionalDigits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
const financialDigits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']

const simpleUnits = ['', '十', '百', '千', '万', '十', '百', '千', '亿']
const traditionalUnits = ['', '拾', '佰', '仟', '萬', '拾', '佰', '仟', '億']
const financialUnits = ['', '拾', '佰', '仟', '萬', '拾', '佰', '仟', '億']

const numberToChineseSimple = (num: number): string => {
  if (num === 0) return '零'
  
  const digits = simpleDigits
  const units = simpleUnits
  
  let result = ''
  let numStr = num.toString()
  let len = numStr.length
  
  for (let i = 0; i < len; i++) {
    const digit = parseInt(numStr[i])
    const unitIndex = len - i - 1
    
    if (digit !== 0) {
      result += digits[digit] + (unitIndex > 0 ? units[unitIndex] : '')
    } else if (result && !result.endsWith('零')) {
      result += '零'
    }
  }
  
  // 处理特殊情况
  result = result.replace(/零+/g, '零')
  result = result.replace(/零$/, '')
  
  // 处理"一十"的特殊情况
  if (result.startsWith('一十')) {
    result = result.substring(1)
  }
  
  return result
}

const convertNumber = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = '请输入数字'
    outputValue.value = ''
    return
  }

  const num = parseFloat(inputValue.value.trim())
  
  if (isNaN(num)) {
    errorMessage.value = '请输入有效的数字'
    outputValue.value = ''
    return
  }

  if (num < 0 || num > 999999999999) {
    errorMessage.value = '数字超出支持范围（0-999,999,999,999）'
    outputValue.value = ''
    return
  }

  try {
    let result = ''
    
    if (convertType.value === 'simple') {
      result = numberToChineseSimple(Math.floor(num))
    } else if (convertType.value === 'traditional') {
      result = numberToChineseSimple(Math.floor(num))
        .replace(/一/g, '壹')
        .replace(/二/g, '贰')
        .replace(/三/g, '叁')
        .replace(/四/g, '肆')
        .replace(/五/g, '伍')
        .replace(/六/g, '陆')
        .replace(/七/g, '柒')
        .replace(/八/g, '捌')
        .replace(/九/g, '玖')
        .replace(/十/g, '拾')
        .replace(/百/g, '佰')
        .replace(/千/g, '仟')
        .replace(/万/g, '萬')
        .replace(/亿/g, '億')
    } else if (convertType.value === 'financial') {
      result = numberToChineseSimple(Math.floor(num))
        .replace(/一/g, '壹')
        .replace(/二/g, '贰')
        .replace(/三/g, '叁')
        .replace(/四/g, '肆')
        .replace(/五/g, '伍')
        .replace(/六/g, '陆')
        .replace(/七/g, '柒')
        .replace(/八/g, '捌')
        .replace(/九/g, '玖')
        .replace(/十/g, '拾')
        .replace(/百/g, '佰')
        .replace(/千/g, '仟')
        .replace(/万/g, '萬')
        .replace(/亿/g, '億') + '元整'
    }
    
    outputValue.value = result
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = '转换失败，请检查输入'
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const loadExample = () => {
  inputValue.value = exampleNumber
}

// 监听输入变化，自动转换
watch(inputValue, () => {
  if (inputValue.value.trim()) {
    convertNumber()
  }
})

watch(convertType, () => {
  if (inputValue.value.trim()) {
    convertNumber()
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.radio-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: normal;
  cursor: pointer;
}

.radio-item input[type="radio"] {
  margin: 0;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.examples {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.examples h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #374151;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.example-item {
  padding: 8px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
