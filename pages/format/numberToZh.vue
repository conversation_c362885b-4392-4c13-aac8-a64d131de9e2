<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.numberToZh.title') }}</h2>
    <p>{{ $t('tools.numberToZh.description') }}</p>

    <div class="form-item">
      <label>{{ $t('tools.numberToZh.inputLabel') }}</label>
      <input
        v-model="inputValue"
        type="text"
        :placeholder="$t('tools.numberToZh.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="form-item">
      <label>{{ $t('tools.numberToZh.typeLabel') }}</label>
      <div class="radio-group">
        <label class="radio-item">
          <input v-model="convertType" type="radio" value="simple" />
          {{ $t('tools.numberToZh.simple') }}
        </label>
        <label class="radio-item">
          <input v-model="convertType" type="radio" value="traditional" />
          {{ $t('tools.numberToZh.traditional') }}
        </label>
        <label class="radio-item">
          <input v-model="convertType" type="radio" value="financial" />
          {{ $t('tools.numberToZh.financial') }}
        </label>
      </div>
    </div>

    <div class="btn-group">
      <button @click="convertNumber" class="btn btn-primary">
        {{ $t('tools.numberToZh.convert') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('common.output') }}</label>
      <textarea
        v-model="outputValue"
        rows="3"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 示例展示 -->
    <div class="examples">
      <h3>转换示例</h3>
      <div class="example-grid">
        <div class="example-item">
          <strong>123</strong> → 一百二十三
        </div>
        <div class="example-item">
          <strong>1234</strong> → 一千二百三十四
        </div>
        <div class="example-item">
          <strong>12345</strong> → 一万二千三百四十五
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.numberToZh.seo_title'),
  description: t('tools.numberToZh.seo_description'),
  keywords: t('tools.numberToZh.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const convertType = ref('simple')
const errorMessage = ref('')
const exampleNumber = '12345'

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('numberToZh')







const convertNumber = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    outputValue.value = ''
    return
  }

  const num = parseFloat(inputValue.value.trim())
  
  if (isNaN(num)) {
    errorMessage.value = t('tools.numberToZh.convertError')
    outputValue.value = ''
    return
  }

  if (num < 0 || num > 999999999999) {
    errorMessage.value = t('tools.numberToZh.convertError')
    outputValue.value = ''
    return
  }

  try {
    let result = ''
    
    if (convertType.value === 'simple') {
      result = numberToChineseSimple(Math.floor(num))
    } else if (convertType.value === 'traditional') {
      result = numberToChineseSimple(Math.floor(num))
        .replace(/一/g, '壹')
        .replace(/二/g, '贰')
        .replace(/三/g, '叁')
        .replace(/四/g, '肆')
        .replace(/五/g, '伍')
        .replace(/六/g, '陆')
        .replace(/七/g, '柒')
        .replace(/八/g, '捌')
        .replace(/九/g, '玖')
        .replace(/十/g, '拾')
        .replace(/百/g, '佰')
        .replace(/千/g, '仟')
        .replace(/万/g, '萬')
        .replace(/亿/g, '億')
    } else if (convertType.value === 'financial') {
      result = numberToChineseSimple(Math.floor(num))
        .replace(/一/g, '壹')
        .replace(/二/g, '贰')
        .replace(/三/g, '叁')
        .replace(/四/g, '肆')
        .replace(/五/g, '伍')
        .replace(/六/g, '陆')
        .replace(/七/g, '柒')
        .replace(/八/g, '捌')
        .replace(/九/g, '玖')
        .replace(/十/g, '拾')
        .replace(/百/g, '佰')
        .replace(/千/g, '仟')
        .replace(/万/g, '萬')
        .replace(/亿/g, '億') + '元整'
    }
    
    outputValue.value = result
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = t('tools.numberToZh.convertError')
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleNumber
}

// 监听输入变化，自动转换
watch(inputValue, () => {
  if (inputValue.value.trim()) {
    convertNumber()
  }
})

watch(convertType, () => {
  if (inputValue.value.trim()) {
    convertNumber()
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.radio-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: normal;
  cursor: pointer;
}

.radio-item input[type="radio"] {
  margin: 0;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.examples {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.examples h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #374151;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.example-item {
  padding: 8px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
