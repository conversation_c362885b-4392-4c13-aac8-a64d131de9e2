<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.yaml.title') }}</h2>
    <p>{{ $t('tools.yaml.description') }}</p>

    <div class="form-item">
      <label>{{ $t('tools.yaml.inputLabel') }}</label>
      <textarea
        v-model="inputValue"
        rows="10"
        :placeholder="$t('tools.yaml.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace; font-size: 12px;"
      />
    </div>

    <div class="btn-group">
      <button @click="formatYAML" class="btn btn-primary">
        {{ $t('tools.yaml.format') }}
      </button>
      <button @click="validateYAML" class="btn btn-info">
        {{ $t('tools.yaml.validate') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
      <button @click="clearAll" class="btn btn-danger">
        {{ $t('tools.yaml.clear') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('tools.yaml.outputLabel') }}</label>
      <textarea
        v-model="outputValue"
        rows="10"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace; font-size: 12px;"
      />
    </div>

    <!-- 验证结果 -->
    <div v-if="validationResult" class="validation-result">
      <div :class="['validation-status', validationResult.isValid ? 'valid' : 'invalid']">
        <strong>{{ validationResult.isValid ? $t('tools.yaml.validStatus') : $t('tools.yaml.invalidStatus') }}</strong>
        <p v-if="!validationResult.isValid">{{ validationResult.error }}</p>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- YAML信息 -->
    <div class="yaml-info">
      <h3>YAML 语法要点</h3>
      <div class="info-grid">
        <div class="info-item">
          <h4>缩进</h4>
          <p>使用空格缩进，不要使用Tab</p>
          <code>key:
  subkey: value</code>
        </div>
        <div class="info-item">
          <h4>列表</h4>
          <p>使用短横线表示列表项</p>
          <code>items:
  - item1
  - item2</code>
        </div>
        <div class="info-item">
          <h4>字符串</h4>
          <p>可以使用引号或不使用引号</p>
          <code>name: "John Doe"
age: 30</code>
        </div>
        <div class="info-item">
          <h4>多行文本</h4>
          <p>使用 | 或 > 表示多行文本</p>
          <code>description: |
  This is a
  multi-line text</code>
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import YAML from 'yaml'

const { copyToClipboard } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.yaml.seo_title'),
  description: t('tools.yaml.seo_description'),
  keywords: t('tools.yaml.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const errorMessage = ref('')
const validationResult = ref(null)

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('yaml')


const exampleYAML = `# 配置文件示例
app:
  name: "工具迷"
  version: "1.0.0"
  debug: true
  
database:
  host: "localhost"
  port: 5432
  credentials:
    username: "admin"
    password: "secret"

features:
  - "多语言支持"
  - "SEO友好"
  - "响应式设计"

description: |
  这是一个多行文本示例
  可以包含多行内容
  保持原有的换行格式`

const formatYAML = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    outputValue.value = ''
    return
  }

  try {
    // 解析YAML
    const parsed = YAML.parse(inputValue.value)
    
    // 重新格式化
    outputValue.value = YAML.stringify(parsed, {
      indent: 2,
      lineWidth: 80,
      minContentWidth: 20
    })
    
    errorMessage.value = ''
    validationResult.value = { isValid: true }
  } catch (error) {
    errorMessage.value = `${t('tools.yaml.formatError')}: ${error.message}`
    outputValue.value = ''
    validationResult.value = { isValid: false, error: error.message }
  }
}

const validateYAML = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    validationResult.value = null
    return
  }

  try {
    YAML.parse(inputValue.value)
    validationResult.value = { isValid: true }
    errorMessage.value = ''
  } catch (error) {
    validationResult.value = { isValid: false, error: error.message }
    errorMessage.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleYAML
}

const clearAll = () => {
  inputValue.value = ''
  outputValue.value = ''
  errorMessage.value = ''
  validationResult.value = null
}

// 监听输入变化，自动验证
watch(inputValue, () => {
  if (inputValue.value.trim()) {
    validateYAML()
  } else {
    validationResult.value = null
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.validation-result {
  margin: 16px 0;
}

.validation-status {
  padding: 12px;
  border-radius: 4px;
  border: 1px solid;
}

.validation-status.valid {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.validation-status.invalid {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.validation-status strong {
  display: block;
  margin-bottom: 4px;
}

.validation-status p {
  margin: 0;
  font-size: 14px;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.yaml-info {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.yaml-info h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #374151;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.info-item h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.info-item p {
  margin-bottom: 8px;
  font-size: 12px;
  color: #6b7280;
}

.info-item code {
  display: block;
  padding: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  font-size: 11px;
  font-family: monospace;
  white-space: pre;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
