<template>
  <div class="simple-textarea">
    <h2>YAML 格式化</h2>
    <p>YAML 格式化、验证和美化工具</p>
    
    <div class="form-item">
      <label>YAML 输入</label>
      <textarea
        v-model="inputValue"
        rows="10"
        placeholder="请输入YAML内容"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace; font-size: 12px;"
      />
    </div>

    <div class="btn-group">
      <button @click="formatYAML" class="btn btn-primary">
        格式化
      </button>
      <button @click="validateYAML" class="btn btn-info">
        验证
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        复制
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        载入示例
      </button>
      <button @click="clearAll" class="btn btn-danger">
        清空
      </button>
    </div>

    <div class="form-item">
      <label>格式化输出</label>
      <textarea
        v-model="outputValue"
        rows="10"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace; font-size: 12px;"
      />
    </div>

    <!-- 验证结果 -->
    <div v-if="validationResult" class="validation-result">
      <div :class="['validation-status', validationResult.isValid ? 'valid' : 'invalid']">
        <strong>{{ validationResult.isValid ? '✓ YAML 格式正确' : '✗ YAML 格式错误' }}</strong>
        <p v-if="!validationResult.isValid">{{ validationResult.error }}</p>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- YAML信息 -->
    <div class="yaml-info">
      <h3>YAML 语法要点</h3>
      <div class="info-grid">
        <div class="info-item">
          <h4>缩进</h4>
          <p>使用空格缩进，不要使用Tab</p>
          <code>key:
  subkey: value</code>
        </div>
        <div class="info-item">
          <h4>列表</h4>
          <p>使用短横线表示列表项</p>
          <code>items:
  - item1
  - item2</code>
        </div>
        <div class="info-item">
          <h4>字符串</h4>
          <p>可以使用引号或不使用引号</p>
          <code>name: "John Doe"
age: 30</code>
        </div>
        <div class="info-item">
          <h4>多行文本</h4>
          <p>使用 | 或 > 表示多行文本</p>
          <code>description: |
  This is a
  multi-line text</code>
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import YAML from 'yaml'

const { copyToClipboard } = useUtils()

// SEO 配置
useSeoMeta({
  title: 'YAML 格式化工具 - 在线 YAML 验证美化 | 工具迷',
  description: 'YAML 格式化在线工具，支持 YAML 文件格式化、验证、美化。自动检测语法错误，规范缩进格式。适用于配置文件、CI/CD、Docker Compose、Kubernetes 等场景。',
  keywords: 'YAML格式化,YAML验证,YAML美化,配置文件,Docker Compose,Kubernetes,CI/CD,在线工具,语法检查'
})

const inputValue = ref('')
const outputValue = ref('')
const errorMessage = ref('')
const validationResult = ref(null)

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: 'YAML 格式化工具',
  description: 'YAML（YAML Ain\'t Markup Language）是一种人类可读的数据序列化标准，广泛用于配置文件和数据交换。本工具提供 YAML 格式化、验证和美化功能，帮助开发者编写正确、规范的 YAML 文件。',
  features: [
    '🎯 **智能格式化**：自动规范缩进和换行格式',
    '✅ **语法验证**：实时检测 YAML 语法错误',
    '🎨 **美化输出**：生成易读的格式化结果',
    '🔧 **错误提示**：详细的错误信息和位置提示',
    '📋 **一键复制**：格式化结果可直接复制使用'
  ],
  examples: [
    {
      title: '基础配置示例',
      input: `app:
name: "工具迷"
version: "1.0.0"
debug: true
database:
host: "localhost"
port: 5432`,
      output: `app:
  name: "工具迷"
  version: "1.0.0"
  debug: true
database:
  host: "localhost"
  port: 5432`,
      language: 'yaml'
    },
    {
      title: '复杂结构示例',
      input: `services:
web:
image: nginx:latest
ports:
- "80:80"
- "443:443"
environment:
- NODE_ENV=production
volumes:
- ./nginx.conf:/etc/nginx/nginx.conf
depends_on:
- api
api:
build: .
environment:
DATABASE_URL: ******************************/myapp`,
      output: `services:
  web:
    image: nginx:latest
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - api
  api:
    build: .
    environment:
      DATABASE_URL: ******************************/myapp`,
      language: 'yaml'
    }
  ],
  useCases: [
    {
      title: 'Docker Compose 配置',
      description: '格式化 Docker Compose 文件，确保服务配置清晰：',
      code: `version: '3.8'
services:
  web:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./html:/usr/share/nginx/html
    depends_on:
      - api

  api:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/app
    depends_on:
      - db

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: app
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:`,
      language: 'yaml'
    },
    {
      title: 'Kubernetes 配置',
      description: '格式化 Kubernetes 资源配置文件：',
      code: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-app
  labels:
    app: web-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: web-app
  template:
    metadata:
      labels:
        app: web-app
    spec:
      containers:
        - name: web
          image: nginx:1.20
          ports:
            - containerPort: 80
          env:
            - name: NODE_ENV
              value: "production"
          resources:
            requests:
              memory: "64Mi"
              cpu: "250m"
            limits:
              memory: "128Mi"
              cpu: "500m"`,
      language: 'yaml'
    },
    {
      title: 'CI/CD 配置',
      description: '格式化 GitHub Actions 或 GitLab CI 配置：',
      code: `name: Build and Deploy

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Build application
        run: npm run build

      - name: Deploy to production
        if: github.ref == 'refs/heads/main'
        run: npm run deploy
        env:
          DEPLOY_TOKEN: \${{ secrets.DEPLOY_TOKEN }}`,
      language: 'yaml'
    },
    {
      title: '应用配置文件',
      description: '格式化应用程序的配置文件：',
      code: `app:
  name: "工具迷"
  version: "1.0.0"
  debug: false

server:
  host: "0.0.0.0"
  port: 3000
  ssl:
    enabled: true
    cert: "/path/to/cert.pem"
    key: "/path/to/key.pem"

database:
  primary:
    host: "localhost"
    port: 5432
    name: "toolmi"
    user: "admin"
    password: "secret"
    pool:
      min: 2
      max: 10

  redis:
    host: "localhost"
    port: 6379
    db: 0

logging:
  level: "info"
  format: "json"
  outputs:
    - type: "file"
      path: "/var/log/app.log"
    - type: "console"`,
      language: 'yaml'
    }
  ],
  technicalDetails: [
    {
      title: 'YAML 语法规则',
      content: `YAML 遵循严格的语法规则：

**缩进规则：**
- 使用空格进行缩进，不能使用 Tab
- 同级元素必须使用相同的缩进
- 子元素比父元素多缩进 2 个空格

**数据类型：**
- 字符串：可以使用引号或不使用引号
- 数字：整数和浮点数
- 布尔值：true/false、yes/no、on/off
- 空值：null 或 ~
- 日期：ISO 8601 格式

**集合类型：**
- 列表：使用短横线 (-) 表示
- 字典：使用冒号 (:) 分隔键值对
- 多行文本：使用 | 或 > 表示`
    },
    {
      title: '常见错误和解决方案',
      content: `YAML 编写中的常见错误：

**缩进错误：**
- 混用空格和 Tab
- 缩进不一致
- 解决：统一使用空格，保持缩进一致

**语法错误：**
- 冒号后缺少空格
- 引号不匹配
- 特殊字符未转义

**结构错误：**
- 列表和字典混用
- 键名重复
- 数据类型不匹配

**最佳实践：**
- 使用引号包围包含特殊字符的字符串
- 为复杂配置添加注释
- 保持一致的命名风格`
    }
  ],
  tips: [
    '**实时验证**：输入 YAML 内容时会自动进行语法验证',
    '**错误定位**：语法错误会显示具体的行号和错误信息',
    '**格式标准**：格式化后的 YAML 遵循标准的缩进和换行规则',
    '**注释保留**：格式化过程会保留原有的注释内容'
  ],
  warnings: [
    '**缩进敏感**：YAML 对缩进非常敏感，确保使用空格而非 Tab',
    '**引号使用**：包含特殊字符的字符串建议使用引号包围',
    '**数据类型**：注意布尔值和字符串的区别（true vs "true"）',
    '**文件编码**：确保 YAML 文件使用 UTF-8 编码保存'
  ]
})

const exampleYAML = `# 配置文件示例
app:
  name: "工具迷"
  version: "1.0.0"
  debug: true
  
database:
  host: "localhost"
  port: 5432
  credentials:
    username: "admin"
    password: "secret"

features:
  - "多语言支持"
  - "SEO友好"
  - "响应式设计"

description: |
  这是一个多行文本示例
  可以包含多行内容
  保持原有的换行格式`

const formatYAML = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = '请输入YAML内容'
    outputValue.value = ''
    return
  }

  try {
    // 解析YAML
    const parsed = YAML.parse(inputValue.value)
    
    // 重新格式化
    outputValue.value = YAML.stringify(parsed, {
      indent: 2,
      lineWidth: 80,
      minContentWidth: 20
    })
    
    errorMessage.value = ''
    validationResult.value = { isValid: true }
  } catch (error) {
    errorMessage.value = `格式化失败: ${error.message}`
    outputValue.value = ''
    validationResult.value = { isValid: false, error: error.message }
  }
}

const validateYAML = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = '请输入YAML内容'
    validationResult.value = null
    return
  }

  try {
    YAML.parse(inputValue.value)
    validationResult.value = { isValid: true }
    errorMessage.value = ''
  } catch (error) {
    validationResult.value = { isValid: false, error: error.message }
    errorMessage.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const loadExample = () => {
  inputValue.value = exampleYAML
}

const clearAll = () => {
  inputValue.value = ''
  outputValue.value = ''
  errorMessage.value = ''
  validationResult.value = null
}

// 监听输入变化，自动验证
watch(inputValue, () => {
  if (inputValue.value.trim()) {
    validateYAML()
  } else {
    validationResult.value = null
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.validation-result {
  margin: 16px 0;
}

.validation-status {
  padding: 12px;
  border-radius: 4px;
  border: 1px solid;
}

.validation-status.valid {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.validation-status.invalid {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.validation-status strong {
  display: block;
  margin-bottom: 4px;
}

.validation-status p {
  margin: 0;
  font-size: 14px;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.yaml-info {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.yaml-info h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #374151;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.info-item h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.info-item p {
  margin-bottom: 8px;
  font-size: 12px;
  color: #6b7280;
}

.info-item code {
  display: block;
  padding: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  font-size: 11px;
  font-family: monospace;
  white-space: pre;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
