<template>
  <div class="simple-textarea">
    <h2>Netscape Cookies 格式转换</h2>
    <p>Netscape Cookies格式与JSON格式互转工具</p>
    
    <div class="form-item">
      <label>输入格式</label>
      <div class="radio-group">
        <label class="radio-item">
          <input v-model="inputFormat" type="radio" value="netscape" />
          Netscape格式
        </label>
        <label class="radio-item">
          <input v-model="inputFormat" type="radio" value="json" />
          JSON格式
        </label>
      </div>
    </div>

    <div class="form-item">
      <label>输入内容</label>
      <textarea
        v-model="inputValue"
        rows="8"
        :placeholder="inputFormat === 'netscape' ? netscapePlaceholder : jsonPlaceholder"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace; font-size: 12px;"
      />
    </div>

    <div class="btn-group">
      <button @click="convertCookies" class="btn btn-primary">
        转换
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        复制
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        载入示例
      </button>
      <button @click="clearAll" class="btn btn-danger">
        清空
      </button>
    </div>

    <div class="form-item">
      <label>转换结果</label>
      <textarea
        v-model="outputValue"
        rows="8"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace; font-size: 12px;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 格式说明 -->
    <div class="format-info">
      <h3>格式说明</h3>
      <div class="format-grid">
        <div class="format-item">
          <h4>Netscape格式</h4>
          <p>每行一个Cookie，字段用制表符分隔：</p>
          <code>domain	flag	path	secure	expiration	name	value</code>
        </div>
        <div class="format-item">
          <h4>JSON格式</h4>
          <p>标准的JSON数组格式，包含Cookie对象：</p>
          <code>[{"domain": "...", "name": "...", "value": "..."}]</code>
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard } = useUtils()

// SEO 配置
useSeoMeta({
  title: 'Netscape Cookies 格式化工具 - Cookie 数据转换 | 工具迷',
  description: 'Netscape Cookies 格式化在线工具，支持 Netscape 格式与 JSON 格式双向转换。适用于浏览器数据导入导出、Cookie 管理、Web 开发调试等场景。',
  keywords: 'Netscape Cookies,Cookie格式化,Cookie转换,浏览器数据,Web开发,Cookie管理,数据导入导出'
})

const inputValue = ref('')
const outputValue = ref('')
const inputFormat = ref('netscape')
const errorMessage = ref('')

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = `# Netscape Cookies 格式化工具

Netscape Cookies 格式是一种经典的 Cookie 存储格式，由 Netscape 浏览器首创，现在仍广泛用于浏览器数据导入导出。本工具支持 Netscape 格式与 JSON 格式的双向转换，方便开发者处理和管理 Cookie 数据。

## ✨ 主要特性

- 🔄 **双向转换**：支持 Netscape 格式转 JSON 和 JSON 转 Netscape 格式
- ✅ **格式验证**：自动检测和验证 Cookie 数据格式
- 📊 **结构化显示**：清晰展示 Cookie 的各个字段信息
- 🔧 **错误提示**：详细的错误信息和修复建议
- 📋 **一键复制**：转换结果可直接复制使用

## 🎯 应用场景

### 浏览器数据迁移
在不同浏览器间迁移 Cookie 数据，方便用户切换浏览器时保持登录状态。

### Web 开发调试
在开发过程中管理和调试 Cookie，测试不同的 Cookie 配置效果。

### 自动化测试
在自动化测试中批量设置 Cookie，模拟用户的登录状态和偏好设置。

### 数据分析
分析用户的 Cookie 数据，了解用户行为和偏好模式。

## 💡 使用技巧

- **格式检查**：转换前检查数据格式，确保字段完整和正确
- **字符编码**：注意 Cookie 值的字符编码，避免特殊字符问题
- **时间处理**：Unix 时间戳转换时注意时区和精度问题
- **批量处理**：大量 Cookie 数据建议分批处理，避免浏览器卡顿

## ⚠️ 注意事项

- **隐私安全**：Cookie 可能包含敏感信息，处理时注意数据安全
- **格式兼容**：不同浏览器的 Cookie 格式可能略有差异
- **过期时间**：注意 Cookie 的过期时间，避免使用过期数据
- **域名限制**：Cookie 的域名和路径限制可能影响使用效果`

const netscapePlaceholder = `# Netscape HTTP Cookie File
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123
.example.com	TRUE	/	FALSE	1640995200	user_token	xyz789`

const jsonPlaceholder = `[
  {
    "domain": ".example.com",
    "flag": true,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "session_id",
    "value": "abc123"
  }
]`

const exampleNetscape = `# Netscape HTTP Cookie File
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123
.example.com	TRUE	/	FALSE	1640995200	user_token	xyz789
.google.com	TRUE	/	TRUE	1672531200	auth_token	token123`

const exampleJSON = `[
  {
    "domain": ".example.com",
    "flag": true,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "session_id",
    "value": "abc123"
  },
  {
    "domain": ".example.com",
    "flag": true,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "user_token",
    "value": "xyz789"
  }
]`

const parseNetscapeCookies = (content: string) => {
  const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'))
  const cookies = []
  
  for (const line of lines) {
    const parts = line.split('\t')
    if (parts.length >= 7) {
      cookies.push({
        domain: parts[0],
        flag: parts[1] === 'TRUE',
        path: parts[2],
        secure: parts[3] === 'TRUE',
        expiration: parseInt(parts[4]),
        name: parts[5],
        value: parts[6]
      })
    }
  }
  
  return cookies
}

const formatToNetscape = (cookies: any[]) => {
  let result = '# Netscape HTTP Cookie File\n'
  
  for (const cookie of cookies) {
    result += `${cookie.domain}\t${cookie.flag ? 'TRUE' : 'FALSE'}\t${cookie.path}\t${cookie.secure ? 'TRUE' : 'FALSE'}\t${cookie.expiration}\t${cookie.name}\t${cookie.value}\n`
  }
  
  return result
}

const convertCookies = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = '请输入要转换的内容'
    outputValue.value = ''
    return
  }

  try {
    if (inputFormat.value === 'netscape') {
      // Netscape 转 JSON
      const cookies = parseNetscapeCookies(inputValue.value)
      outputValue.value = JSON.stringify(cookies, null, 2)
    } else {
      // JSON 转 Netscape
      const cookies = JSON.parse(inputValue.value)
      if (!Array.isArray(cookies)) {
        throw new Error('JSON格式应该是一个数组')
      }
      outputValue.value = formatToNetscape(cookies)
    }
    
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `转换失败: ${error.message}`
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const loadExample = () => {
  if (inputFormat.value === 'netscape') {
    inputValue.value = exampleNetscape
  } else {
    inputValue.value = exampleJSON
  }
}

const clearAll = () => {
  inputValue.value = ''
  outputValue.value = ''
  errorMessage.value = ''
}

// 监听输入格式变化，自动转换
watch(inputFormat, () => {
  if (inputValue.value.trim()) {
    convertCookies()
  }
})

// 监听输入变化，自动转换
watch(inputValue, () => {
  if (inputValue.value.trim()) {
    convertCookies()
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.radio-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: normal;
  cursor: pointer;
}

.radio-item input[type="radio"] {
  margin: 0;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.format-info {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.format-info h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #374151;
}

.format-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.format-item {
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.format-item h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.format-item p {
  margin-bottom: 8px;
  font-size: 12px;
  color: #6b7280;
}

.format-item code {
  display: block;
  padding: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  font-size: 11px;
  font-family: monospace;
  word-break: break-all;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
