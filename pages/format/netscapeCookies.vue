<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.netscapeCookies.title') }}</h2>
    <p>{{ $t('tools.netscapeCookies.description') }}</p>

    <div class="form-item">
      <label>{{ $t('tools.netscapeCookies.inputFormatLabel') }}</label>
      <div class="radio-group">
        <label class="radio-item">
          <input v-model="inputFormat" type="radio" value="netscape" />
          {{ $t('tools.netscapeCookies.netscapeFormat') }}
        </label>
        <label class="radio-item">
          <input v-model="inputFormat" type="radio" value="json" />
          {{ $t('tools.netscapeCookies.jsonFormat') }}
        </label>
      </div>
    </div>

    <div class="form-item">
      <label>{{ $t('tools.netscapeCookies.inputLabel') }}</label>
      <textarea
        v-model="inputValue"
        rows="8"
        :placeholder="inputFormat === 'netscape' ? netscapePlaceholder : jsonPlaceholder"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace; font-size: 12px;"
      />
    </div>

    <div class="btn-group">
      <button @click="convertCookies" class="btn btn-primary">
        {{ $t('tools.netscapeCookies.convert') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
      <button @click="clearAll" class="btn btn-danger">
        {{ $t('tools.netscapeCookies.clear') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('tools.netscapeCookies.resultLabel') }}</label>
      <textarea
        v-model="outputValue"
        rows="8"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace; font-size: 12px;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 格式说明 -->
    <div class="format-info">
      <h3>格式说明</h3>
      <div class="format-grid">
        <div class="format-item">
          <h4>Netscape格式</h4>
          <p>每行一个Cookie，字段用制表符分隔：</p>
          <code>domain	flag	path	secure	expiration	name	value</code>
        </div>
        <div class="format-item">
          <h4>JSON格式</h4>
          <p>标准的JSON数组格式，包含Cookie对象：</p>
          <code>[{"domain": "...", "name": "...", "value": "..."}]</code>
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.netscapeCookies.seo_title'),
  description: t('tools.netscapeCookies.seo_description'),
  keywords: t('tools.netscapeCookies.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const inputFormat = ref('netscape')
const errorMessage = ref('')

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('netscapeCookies')







const convertCookies = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    outputValue.value = ''
    return
  }

  try {
    if (inputFormat.value === 'netscape') {
      // Netscape 转 JSON
      const cookies = parseNetscapeCookies(inputValue.value)
      outputValue.value = JSON.stringify(cookies, null, 2)
    } else {
      // JSON 转 Netscape
      const cookies = JSON.parse(inputValue.value)
      if (!Array.isArray(cookies)) {
        throw new Error('JSON格式应该是一个数组')
      }
      outputValue.value = formatToNetscape(cookies)
    }
    
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.netscapeCookies.convertError')}: ${error.message}`
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  if (inputFormat.value === 'netscape') {
    inputValue.value = exampleNetscape
  } else {
    inputValue.value = exampleJSON
  }
}

const clearAll = () => {
  inputValue.value = ''
  outputValue.value = ''
  errorMessage.value = ''
}

// 监听输入格式变化，自动转换
watch(inputFormat, () => {
  if (inputValue.value.trim()) {
    convertCookies()
  }
})

// 监听输入变化，自动转换
watch(inputValue, () => {
  if (inputValue.value.trim()) {
    convertCookies()
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.radio-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: normal;
  cursor: pointer;
}

.radio-item input[type="radio"] {
  margin: 0;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.format-info {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.format-info h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #374151;
}

.format-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.format-item {
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.format-item h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.format-item p {
  margin-bottom: 8px;
  font-size: 12px;
  color: #6b7280;
}

.format-item code {
  display: block;
  padding: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  font-size: 11px;
  font-family: monospace;
  word-break: break-all;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
