<template>
  <div class="simple-textarea">
    <h2>SQL 格式化</h2>
    <p>SQL 语句格式化和美化工具</p>
    
    <div class="form-item">
      <label>SQL 输入</label>
      <textarea
        v-model="inputValue"
        rows="8"
        placeholder="请输入SQL语句"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace;"
      />
    </div>

    <div class="btn-group">
      <button @click="formatSQL" class="btn btn-primary">
        格式化
      </button>
      <button @click="minifySQL" class="btn btn-info">
        压缩
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        复制
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        载入示例
      </button>
    </div>

    <div class="form-item">
      <label>格式化输出</label>
      <textarea
        v-model="outputValue"
        rows="8"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import { format } from 'sql-formatter'

const { copyToClipboard } = useUtils()

// SEO 配置
useSeoMeta({
  title: 'SQL 格式化工具 - 在线 SQL 美化压缩 | 工具迷',
  description: 'SQL 格式化在线工具，支持 SQL 语句美化、压缩、格式化。自动添加缩进、换行，关键字大写，提升 SQL 代码可读性。支持 MySQL、PostgreSQL、Oracle、SQL Server 等数据库语法。',
  keywords: 'SQL格式化,SQL美化,SQL压缩,SQL工具,数据库查询,MySQL,PostgreSQL,Oracle,SQL Server,在线格式化'
})

const inputValue = ref('')
const outputValue = ref('')
const errorMessage = ref('')
const exampleSQL = `SELECT u.id, u.name, u.email, p.title, p.content FROM users u LEFT JOIN posts p ON u.id = p.user_id WHERE u.status = 'active' AND p.published_at IS NOT NULL ORDER BY p.created_at DESC LIMIT 10;`

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: 'SQL 格式化工具',
  description: 'SQL 格式化工具可以将压缩或混乱的 SQL 语句转换为格式规范、易于阅读的形式。支持自动缩进、换行、关键字大写等功能，同时提供 SQL 压缩功能，适用于各种数据库系统。',
  features: [
    '🎯 **智能格式化**：自动添加适当的缩进和换行',
    '🔤 **关键字大写**：SQL 关键字自动转换为大写',
    '📦 **压缩功能**：移除多余空格，压缩 SQL 语句',
    '🗄️ **多数据库支持**：兼容 MySQL、PostgreSQL、Oracle、SQL Server',
    '📋 **一键复制**：格式化结果可直接复制使用'
  ],
  examples: [
    {
      title: '格式化示例',
      input: 'SELECT u.id,u.name,u.email FROM users u WHERE u.status=\'active\' ORDER BY u.created_at DESC;',
      output: `SELECT
  u.id,
  u.name,
  u.email
FROM
  users u
WHERE
  u.status = 'active'
ORDER BY
  u.created_at DESC;`,
      language: 'sql'
    },
    {
      title: '复杂查询格式化',
      input: 'SELECT u.id,u.name,p.title,COUNT(c.id) as comment_count FROM users u LEFT JOIN posts p ON u.id=p.user_id LEFT JOIN comments c ON p.id=c.post_id WHERE u.status=\'active\' AND p.published_at IS NOT NULL GROUP BY u.id,p.id HAVING COUNT(c.id)>5 ORDER BY comment_count DESC LIMIT 10;',
      output: `SELECT
  u.id,
  u.name,
  p.title,
  COUNT(c.id) AS comment_count
FROM
  users u
  LEFT JOIN posts p ON u.id = p.user_id
  LEFT JOIN comments c ON p.id = c.post_id
WHERE
  u.status = 'active'
  AND p.published_at IS NOT NULL
GROUP BY
  u.id,
  p.id
HAVING
  COUNT(c.id) > 5
ORDER BY
  comment_count DESC
LIMIT
  10;`,
      language: 'sql'
    }
  ],
  useCases: [
    {
      title: '代码审查',
      description: '在代码审查过程中，格式化 SQL 语句提高可读性：',
      code: `-- 格式化前（难以阅读）
SELECT u.id,u.name,u.email,p.title FROM users u LEFT JOIN posts p ON u.id=p.user_id WHERE u.status='active';

-- 格式化后（清晰易读）
SELECT
  u.id,
  u.name,
  u.email,
  p.title
FROM
  users u
  LEFT JOIN posts p ON u.id = p.user_id
WHERE
  u.status = 'active';`,
      language: 'sql'
    },
    {
      title: '文档编写',
      description: '在技术文档中展示格式规范的 SQL 示例：',
      code: `-- 用户数据查询示例
SELECT
  u.id AS user_id,
  u.username,
  u.email,
  u.created_at,
  COUNT(p.id) AS post_count
FROM
  users u
  LEFT JOIN posts p ON u.id = p.author_id
WHERE
  u.status = 'active'
  AND u.email_verified = 1
GROUP BY
  u.id
ORDER BY
  u.created_at DESC;`,
      language: 'sql'
    },
    {
      title: '性能优化',
      description: '格式化复杂查询，便于分析和优化：',
      code: `-- 优化前的复杂查询
SELECT
  o.id,
  o.order_number,
  u.username,
  SUM(oi.quantity * oi.price) AS total_amount
FROM
  orders o
  INNER JOIN users u ON o.user_id = u.id
  INNER JOIN order_items oi ON o.id = oi.order_id
  INNER JOIN products p ON oi.product_id = p.id
WHERE
  o.created_at >= '2024-01-01'
  AND o.status = 'completed'
  AND p.category_id IN (1, 2, 3)
GROUP BY
  o.id,
  o.order_number,
  u.username
HAVING
  SUM(oi.quantity * oi.price) > 100
ORDER BY
  total_amount DESC;`,
      language: 'sql'
    },
    {
      title: '数据库迁移',
      description: '整理和格式化迁移脚本中的 SQL 语句：',
      code: `-- 创建用户表
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_created_at ON users (created_at);`,
      language: 'sql'
    }
  ],
  technicalDetails: [
    {
      title: '格式化规则',
      content: `SQL 格式化遵循以下规则：

**关键字处理：**
- SELECT、FROM、WHERE、ORDER BY 等关键字大写
- 每个主要子句独占一行
- 子查询适当缩进

**字段和表名：**
- 多个字段时每个字段一行
- 表别名保持原样
- 长字段名自动换行

**条件语句：**
- AND、OR 操作符前换行
- 复杂条件适当分组
- 括号内容保持对齐

**JOIN 语句：**
- 每个 JOIN 独占一行
- ON 条件适当缩进
- 多个 JOIN 条件垂直对齐`
    },
    {
      title: '支持的语法',
      content: `工具支持标准 SQL 语法和主流数据库扩展：

**基础语句：**
- SELECT、INSERT、UPDATE、DELETE
- CREATE、ALTER、DROP
- GRANT、REVOKE

**高级特性：**
- 子查询和 CTE（公用表表达式）
- 窗口函数和分析函数
- 存储过程和函数调用
- 触发器和视图定义

**数据库兼容性：**
- MySQL 5.7+ / 8.0+
- PostgreSQL 9.6+
- Oracle 11g+
- SQL Server 2012+
- SQLite 3.x`
    }
  ],
  tips: [
    '**批量处理**：可以同时格式化多个 SQL 语句，用分号分隔',
    '**保留注释**：格式化过程会保留 SQL 注释内容',
    '**自定义缩进**：工具使用 2 个空格作为缩进单位',
    '**压缩功能**：需要压缩 SQL 时使用压缩按钮，适合生产环境'
  ],
  warnings: [
    '**语法检查**：工具主要用于格式化，不提供完整的语法验证',
    '**数据安全**：格式化过程在浏览器本地进行，不会上传数据',
    '**复杂语句**：极其复杂的 SQL 可能需要手动调整格式',
    '**方言差异**：某些数据库特有语法可能格式化效果不佳'
  ]
})

const formatSQL = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = '请输入SQL语句'
    return
  }

  try {
    outputValue.value = format(inputValue.value, {
      language: 'sql',
      tabWidth: 2,
      useTabs: false,
      keywordCase: 'upper',
      linesBetweenQueries: 2
    })
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `格式化失败: ${error.message}`
    outputValue.value = ''
  }
}

const minifySQL = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = '请输入SQL语句'
    return
  }

  try {
    // 简单的压缩：移除多余空格和换行
    const minified = inputValue.value
      .replace(/\s+/g, ' ')
      .replace(/\s*([(),;])\s*/g, '$1')
      .trim()
    
    outputValue.value = minified
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `压缩失败: ${error.message}`
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const loadExample = () => {
  inputValue.value = exampleSQL
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
