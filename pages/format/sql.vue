<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.sql.title') }}</h2>
    <p>{{ $t('tools.sql.description') }}</p>

    <div class="form-item">
      <label>{{ $t('tools.sql.inputLabel') }}</label>
      <textarea
        v-model="inputValue"
        rows="8"
        :placeholder="$t('tools.sql.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace;"
      />
    </div>

    <div class="btn-group">
      <button @click="formatSQL" class="btn btn-primary">
        {{ $t('tools.sql.format') }}
      </button>
      <button @click="minifySQL" class="btn btn-info">
        {{ $t('tools.sql.minify') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('tools.sql.outputLabel') }}</label>
      <textarea
        v-model="outputValue"
        rows="8"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import { format } from 'sql-formatter'

const { copyToClipboard } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.sql.seo_title'),
  description: t('tools.sql.seo_description'),
  keywords: t('tools.sql.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const errorMessage = ref('')
const exampleSQL = `SELECT u.id, u.name, u.email, p.title, p.content FROM users u LEFT JOIN posts p ON u.id = p.user_id WHERE u.status = 'active' AND p.published_at IS NOT NULL ORDER BY p.created_at DESC LIMIT 10;`

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('sql')







const formatSQL = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    return
  }

  try {
    outputValue.value = format(inputValue.value, {
      language: 'sql',
      tabWidth: 2,
      useTabs: false,
      keywordCase: 'upper',
      linesBetweenQueries: 2
    })
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.sql.formatError')}: ${error.message}`
    outputValue.value = ''
  }
}

const minifySQL = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    return
  }

  try {
    // 简单的压缩：移除多余空格和换行
    const minified = inputValue.value
      .replace(/\s+/g, ' ')
      .replace(/\s*([(),;])\s*/g, '$1')
      .trim()
    
    outputValue.value = minified
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.sql.formatError')}: ${error.message}`
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleSQL
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
