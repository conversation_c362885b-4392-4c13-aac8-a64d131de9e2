<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.json2yaml.title') }}</h2>
    <p>{{ $t('tools.json2yaml.description') }}</p>

    <div class="form-item">
      <label>{{ $t('common.input') }}</label>
      <textarea
        v-model="inputValue"
        rows="8"
        :placeholder="$t('tools.json2yaml.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace;"
      />
    </div>

    <div class="btn-group">
      <button @click="convertToYAML" class="btn btn-primary">
        {{ $t('tools.json2yaml.toYaml') }}
      </button>
      <button @click="convertToJSON" class="btn btn-info">
        {{ $t('tools.json2yaml.toJson') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('common.output') }}</label>
      <textarea
        v-model="outputValue"
        rows="8"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import YAML from 'yaml'

const { copyToClipboard } = useUtils()
const { t } = useI18n()

// SEO 配置
useSeoMeta({
  title: t('tools.json2yaml.seo_title'),
  description: t('tools.json2yaml.seo_description'),
  keywords: t('tools.json2yaml.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const errorMessage = ref('')
const exampleJSON = `{
  "name": "工具迷",
  "description": "下一代在线效率工具",
  "features": [
    "多语言支持",
    "SEO友好",
    "响应式设计"
  ],
  "config": {
    "version": "1.0.0",
    "author": "ToolMi Team"
  }
}`

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: 'JSON 转 YAML 工具',
  description: 'JSON 和 YAML 都是常用的数据交换格式。JSON 更适合程序处理和 API 传输，YAML 更适合人类阅读和配置文件编写。本工具提供两种格式的双向转换功能，帮助开发者在不同场景下选择合适的数据格式。',
  features: [
    '🔄 **双向转换**：支持 JSON 转 YAML 和 YAML 转 JSON',
    '✅ **语法验证**：自动检测格式错误并提供详细提示',
    '🎨 **格式美化**：自动格式化输出结果，提升可读性',
    '📋 **一键复制**：转换结果可直接复制使用',
    '🔧 **错误处理**：友好的错误提示和修复建议'
  ],
  examples: [
    {
      title: 'JSON 转 YAML 示例',
      input: `{
  "app": {
    "name": "工具迷",
    "version": "1.0.0"
  },
  "features": ["格式化", "转换", "验证"]
}`,
      output: `app:
  name: 工具迷
  version: 1.0.0
features:
  - 格式化
  - 转换
  - 验证`,
      language: 'yaml'
    },
    {
      title: 'YAML 转 JSON 示例',
      input: `database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret`,
      output: `{
  "database": {
    "host": "localhost",
    "port": 5432,
    "credentials": {
      "username": "admin",
      "password": "secret"
    }
  }
}`,
      language: 'json'
    }
  ],
  useCases: [
    {
      title: '配置文件转换',
      description: '在不同项目间迁移配置文件格式：',
      code: `# 原始 YAML 配置 (config.yml)
server:
  host: 0.0.0.0
  port: 3000
  ssl:
    enabled: true
    cert: /path/to/cert.pem

database:
  type: postgresql
  host: localhost
  port: 5432
  name: myapp
  pool:
    min: 2
    max: 10

# 转换为 JSON 配置 (config.json)
{
  "server": {
    "host": "0.0.0.0",
    "port": 3000,
    "ssl": {
      "enabled": true,
      "cert": "/path/to/cert.pem"
    }
  },
  "database": {
    "type": "postgresql",
    "host": "localhost",
    "port": 5432,
    "name": "myapp",
    "pool": {
      "min": 2,
      "max": 10
    }
  }
}`,
      language: 'yaml'
    },
    {
      title: 'API 文档编写',
      description: '将 JSON Schema 转换为更易读的 YAML 格式：',
      code: `# OpenAPI 规范 YAML 格式
openapi: 3.0.0
info:
  title: 工具迷 API
  version: 1.0.0
  description: 在线工具平台 API

paths:
  /tools:
    get:
      summary: 获取工具列表
      parameters:
        - name: category
          in: query
          schema:
            type: string
      responses:
        '200':
          description: 成功返回工具列表
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    name:
                      type: string
                    category:
                      type: string`,
      language: 'yaml'
    },
    {
      title: 'CI/CD 配置迁移',
      description: '在不同 CI/CD 平台间迁移配置：',
      code: `# GitHub Actions (YAML)
name: Build and Test
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test

# 转换为 JSON 格式供其他工具使用
{
  "name": "Build and Test",
  "on": {
    "push": {
      "branches": ["main"]
    },
    "pull_request": {
      "branches": ["main"]
    }
  },
  "jobs": {
    "test": {
      "runs-on": "ubuntu-latest",
      "steps": [
        {
          "uses": "actions/checkout@v3"
        },
        {
          "name": "Setup Node.js",
          "uses": "actions/setup-node@v3",
          "with": {
            "node-version": "18"
          }
        }
      ]
    }
  }
}`,
      language: 'yaml'
    },
    {
      title: '数据迁移',
      description: '在数据库迁移或数据导入时转换格式：',
      code: `# 用户数据 YAML 格式
users:
  - id: 1
    username: john_doe
    email: <EMAIL>
    profile:
      firstName: John
      lastName: Doe
      age: 30
    preferences:
      theme: dark
      language: en
  - id: 2
    username: jane_smith
    email: <EMAIL>
    profile:
      firstName: Jane
      lastName: Smith
      age: 28
    preferences:
      theme: light
      language: zh

# 转换为 JSON 供 API 使用
{
  "users": [
    {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "profile": {
        "firstName": "John",
        "lastName": "Doe",
        "age": 30
      },
      "preferences": {
        "theme": "dark",
        "language": "en"
      }
    }
  ]
}`,
      language: 'yaml'
    }
  ],
  technicalDetails: [
    {
      title: '格式特点对比',
      content: `JSON 和 YAML 的主要区别：

**JSON 特点：**
- 语法严格，使用大括号和方括号
- 所有字符串必须使用双引号
- 不支持注释
- 文件体积相对较小
- 解析速度快
- 广泛支持，几乎所有编程语言都支持

**YAML 特点：**
- 使用缩进表示层级关系
- 字符串通常不需要引号
- 支持注释（使用 # 符号）
- 可读性更好，更适合人类编辑
- 支持多行字符串
- 支持更复杂的数据类型

**选择建议：**
- API 传输：使用 JSON
- 配置文件：使用 YAML
- 数据存储：根据需求选择`
    },
    {
      title: '转换规则',
      content: `格式转换遵循以下规则：

**数据类型映射：**
- 字符串：JSON 字符串 ↔ YAML 字符串
- 数字：JSON 数字 ↔ YAML 数字
- 布尔值：JSON true/false ↔ YAML true/false
- 空值：JSON null ↔ YAML null
- 数组：JSON 数组 ↔ YAML 列表
- 对象：JSON 对象 ↔ YAML 映射

**特殊处理：**
- JSON 中的转义字符会被正确处理
- YAML 中的多行字符串转换为 JSON 字符串
- YAML 注释在转换为 JSON 时会丢失
- 数字和布尔值的类型会被保持

**格式化选项：**
- JSON 输出使用 2 个空格缩进
- YAML 输出使用标准缩进格式
- 保持数据的原始类型不变`
    }
  ],
  tips: [
    '**语法检查**：转换前会自动验证输入格式的正确性',
    '**注释处理**：YAML 转 JSON 时注释会丢失，转换前请备份',
    '**缩进规范**：YAML 使用空格缩进，避免使用 Tab 字符',
    '**引号使用**：包含特殊字符的 YAML 字符串建议使用引号'
  ],
  warnings: [
    '**注释丢失**：YAML 转 JSON 时会丢失所有注释内容',
    '**格式验证**：确保输入的格式正确，否则转换会失败',
    '**数据类型**：注意布尔值和字符串的区别（true vs "true"）',
    '**文件编码**：确保文件使用 UTF-8 编码，避免中文乱码'
  ]
})

const convertToYAML = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    return
  }

  try {
    const jsonData = JSON.parse(inputValue.value)
    outputValue.value = YAML.stringify(jsonData)
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.json2yaml.convertError')}: ${error.message}`
    outputValue.value = ''
  }
}

const convertToJSON = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    return
  }

  try {
    const yamlData = YAML.parse(inputValue.value)
    outputValue.value = JSON.stringify(yamlData, null, 2)
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.json2yaml.convertError')}: ${error.message}`
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleJSON
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
