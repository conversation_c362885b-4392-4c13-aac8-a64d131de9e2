<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.json2yaml.title') }}</h2>
    <p>{{ $t('tools.json2yaml.description') }}</p>

    <div class="form-item">
      <label>{{ $t('common.input') }}</label>
      <textarea
        v-model="inputValue"
        rows="8"
        :placeholder="$t('tools.json2yaml.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace;"
      />
    </div>

    <div class="btn-group">
      <button @click="convertToYAML" class="btn btn-primary">
        {{ $t('tools.json2yaml.toYaml') }}
      </button>
      <button @click="convertToJSON" class="btn btn-info">
        {{ $t('tools.json2yaml.toJson') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('common.output') }}</label>
      <textarea
        v-model="outputValue"
        rows="8"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import YAML from 'yaml'

const { copyToClipboard } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.json2yaml.seo_title'),
  description: t('tools.json2yaml.seo_description'),
  keywords: t('tools.json2yaml.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const errorMessage = ref('')
const exampleJSON = `{
  "name": "工具迷",
  "description": "下一代在线效率工具",
  "features": [
    "多语言支持",
    "SEO友好",
    "响应式设计"
  ],
  "config": {
    "version": "1.0.0",
    "author": "ToolMi Team"
  }
}`

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('json2yaml')





const convertToYAML = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    return
  }

  try {
    const jsonData = JSON.parse(inputValue.value)
    outputValue.value = YAML.stringify(jsonData)
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.json2yaml.convertError')}: ${error.message}`
    outputValue.value = ''
  }
}

const convertToJSON = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    return
  }

  try {
    const yamlData = YAML.parse(inputValue.value)
    outputValue.value = JSON.stringify(yamlData, null, 2)
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.json2yaml.convertError')}: ${error.message}`
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleJSON
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
