
<template>
  <div class="container">
    <h1>{{ $t('tools.jsonToPhpArrayCode.title') }}</h1>

    <div class="form-item">
      <label>{{ $t('tools.jsonToPhpArrayCode.inputLabel') }}</label>
      <textarea
        v-model="inputValue"
        rows="8"
        :placeholder="$t('tools.jsonToPhpArrayCode.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace;"
      />
    </div>

    <div class="btn-group">
      <button @click="convertToPhpArray" class="btn btn-primary">{{ $t('tools.jsonToPhpArrayCode.convert') }}</button>
      <button @click="copyOutput" class="btn btn-secondary">{{ $t('tools.jsonToPhpArrayCode.copyResult') }}</button>
      <button @click="loadExample" class="btn btn-secondary">{{ $t('tools.jsonToPhpArrayCode.loadExample') }}</button>
    </div>

    <div class="form-item">
      <label>{{ $t('tools.jsonToPhpArrayCode.outputLabel') }}</label>
      <textarea
        v-model="outputValue"
        rows="8"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard, isValidJSON } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.jsonToPhpArrayCode.seo_title'),
  description: t('tools.jsonToPhpArrayCode.seo_description'),
  keywords: t('tools.jsonToPhpArrayCode.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const errorMessage = ref('')
const exampleJSON = `{
  "name": "工具迷",
  "description": "下一代在线效率工具",
  "features": [
    "多语言支持",
    "SEO友好",
    "响应式设计"
  ],
  "config": {
    "version": "1.0.0",
    "author": "ToolMi Team"
  }
}`

// Markdown 内容
const markdownContent = useToolMarkdown('jsonToPhpArrayCode')




// 将 JSON 转换为 PHP 数组代码
const jsonToPhpArray = (data: any, indent = 0): string => {
  const spaces = ' '.repeat(indent)
  
  if (data === null) {
    return 'null';
  } else if (typeof data === 'boolean') {
    return data ? 'true' : 'false';
  } else if (typeof data === 'number') {
    return data.toString();
  } else if (typeof data === 'string') {
    // 处理字符串中的特殊字符
    const escaped = data
      .replace(/\\/g, '\\\\')
      .replace(/'/g, "\\'")
      .replace(/\r/g, '\\r')
      .replace(/\n/g, '\\n')
      .replace(/\t/g, '\\t');
    return `'${escaped}'`;
  } else if (Array.isArray(data)) {
    if (data.length === 0) {
      return '[]';
    }
    
    let result = '[\n';
    data.forEach((item, index) => {
      result += `${spaces}  ${jsonToPhpArray(item, indent + 2)}`;
      if (index < data.length - 1) {
        result += ',';
      }
      result += '\n';
    });
    result += `${spaces}]`;
    return result;
  } else if (typeof data === 'object') {
    const keys = Object.keys(data);
    if (keys.length === 0) {
      return '[]';
    }
    
    let result = '[\n';
    keys.forEach((key, index) => {
      const value = data[key];
      result += `${spaces}  '${key}' => ${jsonToPhpArray(value, indent + 2)}`;
      if (index < keys.length - 1) {
        result += ',';
      }
      result += '\n';
    });
    result += `${spaces}]`;
    return result;
  }
  
  return '';
}

const convertToPhpArray = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    outputValue.value = ''
    return
  }

  try {
    if (!isValidJSON(inputValue.value)) {
      throw new Error('无效的 JSON 格式')
    }
    
    const jsonData = JSON.parse(inputValue.value)
    const phpArray = jsonToPhpArray(jsonData)
    outputValue.value = `<?php\n\n$data = ${phpArray};\n`
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.jsonToPhpArrayCode.convertError')}: ${error.message}`
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleJSON
  convertToPhpArray()
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  color: #ff4d4f;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
}
</style>

