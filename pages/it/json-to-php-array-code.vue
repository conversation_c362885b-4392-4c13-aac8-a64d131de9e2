
<template>
  <div class="container">
    <h1>JSON 转 PHP 数组代码</h1>
    
    <div class="form-item">
      <label>输入 JSON</label>
      <textarea
        v-model="inputValue"
        rows="8"
        placeholder="请输入 JSON 数据"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-family: monospace;"
      />
    </div>

    <div class="btn-group">
      <button @click="convertToPhpArray" class="btn btn-primary">转换</button>
      <button @click="copyOutput" class="btn btn-secondary">复制结果</button>
      <button @click="loadExample" class="btn btn-secondary">加载示例</button>
    </div>

    <div class="form-item">
      <label>PHP 数组代码</label>
      <textarea
        v-model="outputValue"
        rows="8"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9; font-family: monospace;"
      />
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard, isValidJSON } = useUtils()

// SEO 配置
useSeoMeta({
  title: 'JSON 转 PHP 数组代码 - 在线转换工具 | 工具迷',
  description: 'JSON 转 PHP 数组代码在线工具，支持嵌套结构、中文字符、特殊字符转义。一键将 JSON 数据转换为格式规范的 PHP 数组代码，适用于配置文件、API Mock 数据、数据库种子文件等场景。免费在线使用，无需下载安装。',
  keywords: 'JSON转PHP,PHP数组,JSON转换,PHP代码生成,在线工具,配置文件,API Mock,数据转换,PHP开发工具,JSON解析'
})

const inputValue = ref('')
const outputValue = ref('')
const errorMessage = ref('')
const exampleJSON = `{
  "name": "工具迷",
  "description": "下一代在线效率工具",
  "features": [
    "多语言支持",
    "SEO友好",
    "响应式设计"
  ],
  "config": {
    "version": "1.0.0",
    "author": "ToolMi Team"
  }
}`

// Markdown 内容
const markdownContent = `# JSON 转 PHP 数组代码工具

这个工具可以将 **JSON 数据** 快速转换为 **PHP 数组代码**，方便在 PHP 项目中直接使用。支持嵌套结构、中文字符、布尔值、数字等各种数据类型。

## ✨ 主要特性

- 🚀 **一键转换**：粘贴 JSON 内容，立即生成 PHP 数组代码
- 🎯 **完美格式**：生成的代码格式规范，可直接复制使用
- 🌐 **多类型支持**：支持字符串、数字、布尔值、数组、对象等所有 JSON 类型
- 🔧 **特殊字符处理**：自动转义单引号、换行符等特殊字符
- 📱 **响应式设计**：支持手机、平板等各种设备

## 📖 使用示例

### 基础示例

假设你有如下 JSON 数据：

\`\`\`json
{
  "name": "ChatGPT",
  "features": ["AI", "NLP", "Code"],
  "active": true,
  "version": 4.0
}
\`\`\`

转换后的 PHP 数组代码：

\`\`\`php
<?php

$data = [
  'name' => 'ChatGPT',
  'features' => [
    'AI',
    'NLP',
    'Code'
  ],
  'active' => true,
  'version' => 4.0
];
\`\`\`

### 复杂嵌套示例

对于复杂的嵌套结构：

\`\`\`json
{
  "user": {
    "id": 123,
    "profile": {
      "name": "张三",
      "email": "<EMAIL>",
      "preferences": {
        "theme": "dark",
        "notifications": true
      }
    },
    "roles": ["admin", "editor"]
  }
}
\`\`\`

转换结果：

\`\`\`php
<?php

$data = [
  'user' => [
    'id' => 123,
    'profile' => [
      'name' => '张三',
      'email' => '<EMAIL>',
      'preferences' => [
        'theme' => 'dark',
        'notifications' => true
      ]
    ],
    'roles' => [
      'admin',
      'editor'
    ]
  ]
];
\`\`\`

## 🎯 应用场景

### 1. 配置文件生成

将 JSON 配置转换为 PHP 配置数组：

\`\`\`php
<?php
// config/app.php
return [
  'name' => 'My Application',
  'debug' => true,
  'timezone' => 'Asia/Shanghai'
];
\`\`\`

### 2. API Mock 数据

快速生成测试数据：

\`\`\`php
<?php
// 模拟 API 返回数据
$mockData = [
  'status' => 'success',
  'data' => [
    'users' => [
      ['id' => 1, 'name' => '用户1'],
      ['id' => 2, 'name' => '用户2']
    ]
  ]
];
\`\`\`

### 3. 数据库种子文件

生成数据库初始化数据：

\`\`\`php
<?php
// database/seeders/UserSeeder.php
$users = [
  [
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'role' => 'admin'
  ],
  [
    'name' => 'Regular User',
    'email' => '<EMAIL>',
    'role' => 'user'
  ]
];
\`\`\`

## 🔧 技术细节

### 数据类型转换

| JSON 类型 | PHP 类型 | 示例 |
|-----------|----------|------|
| string | string | \`"hello"\` → \`'hello'\` |
| number | int/float | \`42\` → \`42\`, \`3.14\` → \`3.14\` |
| boolean | boolean | \`true\` → \`true\`, \`false\` → \`false\` |
| null | null | \`null\` → \`null\` |
| array | array | \`[1,2,3]\` → \`[1, 2, 3]\` |
| object | array | \`{"key": "value"}\` → \`['key' => 'value']\` |

### 特殊字符处理

工具会自动处理以下特殊字符：

- **单引号**：\`'\` → \`\\'\`
- **反斜杠**：\`\\\` → \`\\\\\`
- **换行符**：\`\\n\` → \`\\\\n\`
- **制表符**：\`\\t\` → \`\\\\t\`
- **回车符**：\`\\r\` → \`\\\\r\`

## 💡 使用技巧

1. **复制粘贴**：可以直接从 API 响应、配置文件等地方复制 JSON 内容
2. **格式验证**：工具会自动验证 JSON 格式，提示错误信息
3. **一键复制**：生成的 PHP 代码可以一键复制到剪贴板
4. **示例学习**：点击"加载示例"查看更多使用案例

## 🚀 开始使用

1. 在上方输入框中粘贴或输入 JSON 数据
2. 点击"转换"按钮
3. 复制生成的 PHP 数组代码
4. 粘贴到你的 PHP 项目中使用

> **提示**：确保输入的是有效的 JSON 格式，工具会自动检测并提示格式错误。`

// 将 JSON 转换为 PHP 数组代码
const jsonToPhpArray = (data: any, indent = 0): string => {
  const spaces = ' '.repeat(indent)
  
  if (data === null) {
    return 'null';
  } else if (typeof data === 'boolean') {
    return data ? 'true' : 'false';
  } else if (typeof data === 'number') {
    return data.toString();
  } else if (typeof data === 'string') {
    // 处理字符串中的特殊字符
    const escaped = data
      .replace(/\\/g, '\\\\')
      .replace(/'/g, "\\'")
      .replace(/\r/g, '\\r')
      .replace(/\n/g, '\\n')
      .replace(/\t/g, '\\t');
    return `'${escaped}'`;
  } else if (Array.isArray(data)) {
    if (data.length === 0) {
      return '[]';
    }
    
    let result = '[\n';
    data.forEach((item, index) => {
      result += `${spaces}  ${jsonToPhpArray(item, indent + 2)}`;
      if (index < data.length - 1) {
        result += ',';
      }
      result += '\n';
    });
    result += `${spaces}]`;
    return result;
  } else if (typeof data === 'object') {
    const keys = Object.keys(data);
    if (keys.length === 0) {
      return '[]';
    }
    
    let result = '[\n';
    keys.forEach((key, index) => {
      const value = data[key];
      result += `${spaces}  '${key}' => ${jsonToPhpArray(value, indent + 2)}`;
      if (index < keys.length - 1) {
        result += ',';
      }
      result += '\n';
    });
    result += `${spaces}]`;
    return result;
  }
  
  return '';
}

const convertToPhpArray = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = '请输入 JSON 数据'
    outputValue.value = ''
    return
  }

  try {
    if (!isValidJSON(inputValue.value)) {
      throw new Error('无效的 JSON 格式')
    }
    
    const jsonData = JSON.parse(inputValue.value)
    const phpArray = jsonToPhpArray(jsonData)
    outputValue.value = `<?php\n\n$data = ${phpArray};\n`
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `转换失败: ${error.message}`
    outputValue.value = ''
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const loadExample = () => {
  inputValue.value = exampleJSON
  convertToPhpArray()
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-message {
  color: #ff4d4f;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
}
</style>

