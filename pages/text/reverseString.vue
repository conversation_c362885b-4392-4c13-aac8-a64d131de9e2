<template>
  <div class="simple-textarea">
    <h2>字符串反转</h2>
    <p>字符串反转和文本处理工具</p>
    
    <div class="form-item">
      <label>输入文本</label>
      <textarea
        v-model="inputValue"
        rows="6"
        placeholder="请输入要反转的文本"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="form-item">
      <label>反转选项</label>
      <div class="options-group">
        <label class="option-item">
          <input v-model="reverseType" type="radio" value="characters" />
          按字符反转
        </label>
        <label class="option-item">
          <input v-model="reverseType" type="radio" value="words" />
          按单词反转
        </label>
        <label class="option-item">
          <input v-model="reverseType" type="radio" value="lines" />
          按行反转
        </label>
      </div>
    </div>

    <div class="btn-group">
      <button @click="reverseText" class="btn btn-primary">
        反转
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        复制
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        载入示例
      </button>
      <button @click="clearAll" class="btn btn-danger">
        清空
      </button>
    </div>

    <div class="form-item">
      <label>反转结果</label>
      <textarea
        v-model="outputValue"
        rows="6"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- 文本统计 -->
    <div v-if="inputValue" class="text-stats">
      <h3>文本统计</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">字符数：</span>
          <span class="stat-value">{{ textStats.characters }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">字符数（不含空格）：</span>
          <span class="stat-value">{{ textStats.charactersNoSpaces }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">单词数：</span>
          <span class="stat-value">{{ textStats.words }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">行数：</span>
          <span class="stat-value">{{ textStats.lines }}</span>
        </div>
      </div>
    </div>

    <!-- 反转示例 -->
    <div class="reverse-examples">
      <h3>反转示例</h3>
      <div class="example-grid">
        <div class="example-item">
          <h4>按字符反转</h4>
          <p><strong>原文：</strong>Hello World</p>
          <p><strong>结果：</strong>dlroW olleH</p>
        </div>
        <div class="example-item">
          <h4>按单词反转</h4>
          <p><strong>原文：</strong>Hello World</p>
          <p><strong>结果：</strong>World Hello</p>
        </div>
        <div class="example-item">
          <h4>按行反转</h4>
          <p><strong>原文：</strong>第一行<br>第二行</p>
          <p><strong>结果：</strong>第二行<br>第一行</p>
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard } = useUtils()

// SEO 配置
useSeoMeta({
  title: '字符串反转工具 - 在线文本反转处理 | 工具迷',
  description: '字符串反转在线工具，支持按字符、单词、行反转文本。提供文本统计功能，显示字符数、单词数、行数等信息。适用于文本处理、数据转换、编程练习等场景。',
  keywords: '字符串反转,文本反转,文本处理,文本统计,字符统计,单词统计,行反转,在线工具'
})

const inputValue = ref('')
const outputValue = ref('')
const reverseType = ref('characters')

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: '字符串反转工具',
  description: '字符串反转工具可以按照不同的方式反转文本内容，包括按字符、按单词、按行反转。同时提供详细的文本统计功能，帮助分析文本的字符数、单词数和行数等信息。',
  features: [
    '🔄 **多种反转模式**：支持按字符、单词、行三种反转方式',
    '📊 **文本统计**：实时显示字符数、单词数、行数等统计信息',
    '⚡ **实时处理**：输入文本即时显示反转结果',
    '🌐 **多语言支持**：完美支持中文、英文、数字、符号等各种字符',
    '📋 **一键复制**：反转结果可直接复制使用'
  ],
  examples: [
    {
      title: '按字符反转',
      input: 'Hello World!',
      output: '!dlroW olleH',
      language: 'text'
    },
    {
      title: '按单词反转',
      input: 'Hello World 工具迷',
      output: '工具迷 World Hello',
      language: 'text'
    },
    {
      title: '按行反转',
      input: `第一行
第二行
第三行`,
      output: `第三行
第二行
第一行`,
      language: 'text'
    }
  ],
  useCases: [
    {
      title: '文本处理',
      description: '在文本编辑和数据处理中使用字符串反转：',
      code: `# 密码混淆
原始密码：password123
反转后：321drowssap

# 文件名处理
原始文件名：document_final_v2.pdf
按字符反转：fdp.2v_lanif_tnemucod
按单词反转：v2.pdf final document

# 数据脱敏
原始数据：张三 13800138000
反转处理：三张 00083100831

# 文本艺术
原始文本：WELCOME
反转文本：EMOCLEW`,
      language: 'text'
    },
    {
      title: '编程练习',
      description: '字符串反转是常见的编程练习题目：',
      code: `// JavaScript 实现字符串反转
function reverseString(str) {
  // 方法1：使用内置方法
  return str.split('').reverse().join('');
}

// 方法2：使用循环
function reverseStringLoop(str) {
  let result = '';
  for (let i = str.length - 1; i >= 0; i--) {
    result += str[i];
  }
  return result;
}

// 方法3：使用递归
function reverseStringRecursive(str) {
  if (str === '') return '';
  return reverseStringRecursive(str.substr(1)) + str.charAt(0);
}

// 单词反转
function reverseWords(str) {
  return str.split(' ').reverse().join(' ');
}

// 行反转
function reverseLines(str) {
  return str.split('\\n').reverse().join('\\n');
}

// 使用示例
console.log(reverseString('Hello')); // 'olleH'
console.log(reverseWords('Hello World')); // 'World Hello'`,
      language: 'javascript'
    },
    {
      title: '数据转换',
      description: '在数据处理和格式转换中应用字符串反转：',
      code: `# CSV 数据处理
原始数据：
姓名,年龄,城市
张三,25,北京
李四,30,上海

按行反转后：
李四,30,上海
张三,25,北京
姓名,年龄,城市

# 日志分析
原始日志：
2024-01-01 10:00:00 INFO 用户登录
2024-01-01 10:05:00 WARN 密码错误
2024-01-01 10:10:00 ERROR 系统异常

按行反转（最新在前）：
2024-01-01 10:10:00 ERROR 系统异常
2024-01-01 10:05:00 WARN 密码错误
2024-01-01 10:00:00 INFO 用户登录

# 配置文件处理
原始配置：
database.host=localhost
database.port=5432
database.name=myapp

按单词反转：
localhost database.host=
5432 database.port=
myapp database.name=`,
      language: 'text'
    },
    {
      title: '创意应用',
      description: '在创意设计和艺术创作中使用字符串反转：',
      code: `# 回文检测
原始文本：level
反转文本：level
结果：是回文

原始文本：hello
反转文本：olleh
结果：不是回文

# 镜像文本效果
原始：HELLO WORLD
反转：DLROW OLLEH
组合：HELLO WORLD | DLROW OLLEH

# 诗歌创作
原始诗句：
春眠不觉晓
处处闻啼鸟

反转创作：
鸟啼闻处处
晓觉不眠春

# 品牌名称创意
原始：GOOGLE
反转：ELGOOG
创意：可以作为镜像品牌或子品牌名称

# 密码生成
基础词：security
反转：ytiruces
组合：security2024ytiruces`,
      language: 'text'
    }
  ],
  technicalDetails: [
    {
      title: '反转算法',
      content: `不同反转模式的实现原理：

**按字符反转：**
1. 将字符串转换为字符数组
2. 使用双指针或内置方法反转数组
3. 将字符数组重新组合为字符串

**按单词反转：**
1. 使用空格分割字符串为单词数组
2. 反转单词数组的顺序
3. 使用空格重新连接单词

**按行反转：**
1. 使用换行符分割字符串为行数组
2. 反转行数组的顺序
3. 使用换行符重新连接各行

**时间复杂度：**
- 字符反转：O(n)
- 单词反转：O(n)
- 行反转：O(n)

其中 n 为字符串长度`
    },
    {
      title: '文本统计',
      content: `工具提供的文本统计功能：

**字符统计：**
- 总字符数：包括所有字符（空格、换行等）
- 不含空格字符数：排除空格字符的计数
- 支持 Unicode 字符正确计数

**单词统计：**
- 使用空白字符分割单词
- 自动处理多个连续空格
- 支持中英文混合文本

**行数统计：**
- 使用换行符分割计算行数
- 空行也计入总行数
- 支持不同操作系统的换行符

**实时更新：**
- 输入内容变化时自动更新统计
- 无需手动触发计算
- 性能优化，大文本也能流畅处理`
    }
  ],
  tips: [
    '**反转模式**：根据需求选择合适的反转模式，字符反转最常用',
    '**文本统计**：利用统计功能了解文本特征，辅助文本处理',
    '**实时预览**：输入文本时可以实时查看反转效果',
    '**复制功能**：处理完成后使用复制按钮快速获取结果'
  ],
  warnings: [
    '**字符编码**：确保文本使用正确的字符编码，避免乱码',
    '**大文本处理**：处理超大文本时可能影响浏览器性能',
    '**格式保持**：某些反转操作可能改变原有的文本格式',
    '**数据备份**：重要文本建议先备份再进行反转处理'
  ]
})

const exampleText = `工具迷是一个在线工具网站
提供各种实用的开发工具
支持多语言和SEO优化`

const textStats = computed(() => {
  if (!inputValue.value) {
    return { characters: 0, charactersNoSpaces: 0, words: 0, lines: 0 }
  }

  const text = inputValue.value
  const characters = text.length
  const charactersNoSpaces = text.replace(/\s/g, '').length
  const words = text.trim() ? text.trim().split(/\s+/).length : 0
  const lines = text.split('\n').length

  return { characters, charactersNoSpaces, words, lines }
})

const reverseText = () => {
  if (!inputValue.value.trim()) {
    outputValue.value = ''
    return
  }

  const text = inputValue.value

  switch (reverseType.value) {
    case 'characters':
      // 按字符反转
      outputValue.value = text.split('').reverse().join('')
      break
    
    case 'words':
      // 按单词反转
      outputValue.value = text.split(/\s+/).reverse().join(' ')
      break
    
    case 'lines':
      // 按行反转
      outputValue.value = text.split('\n').reverse().join('\n')
      break
    
    default:
      outputValue.value = text.split('').reverse().join('')
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const loadExample = () => {
  inputValue.value = exampleText
}

const clearAll = () => {
  inputValue.value = ''
  outputValue.value = ''
}

// 监听输入变化，自动反转
watch([inputValue, reverseType], () => {
  if (inputValue.value.trim()) {
    reverseText()
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.options-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: normal;
  cursor: pointer;
}

.option-item input[type="radio"] {
  margin: 0;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.text-stats {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.text-stats h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #374151;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.stat-value {
  color: #374151;
  font-weight: 600;
  font-size: 14px;
}

.reverse-examples {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.reverse-examples h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #374151;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.example-item {
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.example-item h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.example-item p {
  margin-bottom: 4px;
  font-size: 12px;
  color: #6b7280;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
