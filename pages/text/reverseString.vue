<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.reverseString.title') }}</h2>
    <p>{{ $t('tools.reverseString.description') }}</p>

    <div class="form-item">
      <label>{{ $t('tools.reverseString.inputLabel') }}</label>
      <textarea
        v-model="inputValue"
        rows="6"
        :placeholder="$t('tools.reverseString.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="form-item">
      <label>{{ $t('tools.reverseString.optionsLabel') }}</label>
      <div class="options-group">
        <label class="option-item">
          <input v-model="reverseType" type="radio" value="characters" />
          {{ $t('tools.reverseString.byCharacters') }}
        </label>
        <label class="option-item">
          <input v-model="reverseType" type="radio" value="words" />
          {{ $t('tools.reverseString.byWords') }}
        </label>
        <label class="option-item">
          <input v-model="reverseType" type="radio" value="lines" />
          {{ $t('tools.reverseString.byLines') }}
        </label>
      </div>
    </div>

    <div class="btn-group">
      <button @click="reverseText" class="btn btn-primary">
        {{ $t('tools.reverseString.reverse') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
      <button @click="clearAll" class="btn btn-danger">
        {{ $t('tools.reverseString.clear') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('tools.reverseString.resultLabel') }}</label>
      <textarea
        v-model="outputValue"
        rows="6"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- 文本统计 -->
    <div v-if="inputValue" class="text-stats">
      <h3>文本统计</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">字符数：</span>
          <span class="stat-value">{{ textStats.characters }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">字符数（不含空格）：</span>
          <span class="stat-value">{{ textStats.charactersNoSpaces }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">单词数：</span>
          <span class="stat-value">{{ textStats.words }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">行数：</span>
          <span class="stat-value">{{ textStats.lines }}</span>
        </div>
      </div>
    </div>

    <!-- 反转示例 -->
    <div class="reverse-examples">
      <h3>反转示例</h3>
      <div class="example-grid">
        <div class="example-item">
          <h4>按字符反转</h4>
          <p><strong>原文：</strong>Hello World</p>
          <p><strong>结果：</strong>dlroW olleH</p>
        </div>
        <div class="example-item">
          <h4>按单词反转</h4>
          <p><strong>原文：</strong>Hello World</p>
          <p><strong>结果：</strong>World Hello</p>
        </div>
        <div class="example-item">
          <h4>按行反转</h4>
          <p><strong>原文：</strong>第一行<br>第二行</p>
          <p><strong>结果：</strong>第二行<br>第一行</p>
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.reverseString.seo_title'),
  description: t('tools.reverseString.seo_description'),
  keywords: t('tools.reverseString.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const reverseType = ref('characters')

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = useToolMarkdown('reverseString')

const exampleText = `工具迷是一个在线工具网站
提供各种实用的开发工具
支持多语言和SEO优化`

const textStats = computed(() => {
  if (!inputValue.value) {
    return { characters: 0, charactersNoSpaces: 0, words: 0, lines: 0 }
  }

  const text = inputValue.value
  const characters = text.length
  const charactersNoSpaces = text.replace(/\s/g, '').length
  const words = text.trim() ? text.trim().split(/\s+/).length : 0
  const lines = text.split('\n').length

  return { characters, charactersNoSpaces, words, lines }
})

const reverseText = () => {
  if (!inputValue.value.trim()) {
    outputValue.value = ''
    return
  }

  const text = inputValue.value

  switch (reverseType.value) {
    case 'characters':
      // 按字符反转
      outputValue.value = text.split('').reverse().join('')
      break
    
    case 'words':
      // 按单词反转
      outputValue.value = text.split(/\s+/).reverse().join(' ')
      break
    
    case 'lines':
      // 按行反转
      outputValue.value = text.split('\n').reverse().join('\n')
      break
    
    default:
      outputValue.value = text.split('').reverse().join('')
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleText
}

const clearAll = () => {
  inputValue.value = ''
  outputValue.value = ''
}

// 监听输入变化，自动反转
watch([inputValue, reverseType], () => {
  if (inputValue.value.trim()) {
    reverseText()
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.options-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: normal;
  cursor: pointer;
}

.option-item input[type="radio"] {
  margin: 0;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.text-stats {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.text-stats h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #374151;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.stat-value {
  color: #374151;
  font-weight: 600;
  font-size: 14px;
}

.reverse-examples {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f8f9fa;
}

.reverse-examples h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #374151;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.example-item {
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.example-item h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.example-item p {
  margin-bottom: 4px;
  font-size: 12px;
  color: #6b7280;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
