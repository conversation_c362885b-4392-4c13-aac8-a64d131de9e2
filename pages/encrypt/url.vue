<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.urlencode.title') }}</h2>
    <p>{{ $t('tools.urlencode.description') }}</p>

    <div class="form-item">
      <label>{{ $t('common.input') }}</label>
      <textarea
        v-model="inputValue"
        rows="5"
        :placeholder="$t('tools.urlencode.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="btn-group">
      <button @click="encodeURL" class="btn btn-primary">
        {{ $t('tools.urlencode.encode') }}
      </button>
      <button @click="decodeURL" class="btn btn-secondary">
        {{ $t('tools.urlencode.decode') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('common.output') }}</label>
      <textarea
        v-model="outputValue"
        rows="5"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard } = useUtils()
const { t } = useI18n()

// SEO 配置
useSeoMeta({
  title: t('tools.urlencode.seo_title'),
  description: t('tools.urlencode.seo_description'),
  keywords: t('tools.urlencode.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const exampleText = 'https://example.com/search?q=你好世界&type=1'

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: 'URL 编码解码工具',
  description: 'URL 编码（百分号编码）是一种在 URL 中安全传输数据的编码机制。当 URL 包含特殊字符、中文字符或空格时，需要进行编码以确保正确传输。本工具提供快速的 URL 编码和解码功能。',
  features: [
    '🔄 **双向转换**：支持 URL 编码和解码操作',
    '🌐 **中文支持**：完美处理中文字符编码',
    '⚡ **即时转换**：输入内容立即显示转换结果',
    '🔧 **特殊字符**：处理空格、符号等特殊字符',
    '📋 **一键复制**：转换结果可直接复制使用'
  ],
  examples: [
    {
      title: '中文字符编码',
      input: 'https://example.com/search?q=你好世界',
      output: 'https%3A//example.com/search%3Fq%3D%E4%BD%A0%E5%A5%BD%E4%B8%96%E7%95%8C',
      language: 'text'
    },
    {
      title: '特殊字符编码',
      input: 'name=张三&age=25&email=<EMAIL>',
      output: 'name%3D%E5%BC%A0%E4%B8%89%26age%3D25%26email%3Dtest%40example.com',
      language: 'text'
    },
    {
      title: '空格和符号编码',
      input: 'Hello World! #tag @user',
      output: 'Hello%20World!%20%23tag%20%40user',
      language: 'text'
    }
  ],
  useCases: [
    {
      title: 'Web 表单提交',
      description: '处理表单数据中的特殊字符，确保正确提交：',
      code: `// JavaScript 表单数据编码
const formData = {
  name: '张三',
  message: '你好，世界！',
  email: '<EMAIL>'
}

// 编码表单数据
const encodedData = Object.keys(formData)
  .map(key => \`\${encodeURIComponent(key)}=\${encodeURIComponent(formData[key])}\`)
  .join('&')

console.log(encodedData)
// name=%E5%BC%A0%E4%B8%89&message=%E4%BD%A0%E5%A5%BD%EF%BC%8C%E4%B8%96%E7%95%8C%EF%BC%81&email=test%40example.com`,
      language: 'javascript'
    },
    {
      title: 'API 请求参数',
      description: '在 API 调用中正确编码查询参数：',
      code: `// 构建 API 请求 URL
const baseUrl = 'https://api.example.com/search'
const params = {
  q: '工具迷 在线工具',
  category: 'web开发',
  sort: 'created_at desc'
}

// 编码查询参数
const queryString = Object.keys(params)
  .map(key => \`\${key}=\${encodeURIComponent(params[key])}\`)
  .join('&')

const fullUrl = \`\${baseUrl}?\${queryString}\`
console.log(fullUrl)
// https://api.example.com/search?q=%E5%B7%A5%E5%85%B7%E8%BF%B7%20%E5%9C%A8%E7%BA%BF%E5%B7%A5%E5%85%B7&category=web%E5%BC%80%E5%8F%91&sort=created_at%20desc`,
      language: 'javascript'
    },
    {
      title: '文件下载链接',
      description: '处理包含中文文件名的下载链接：',
      code: `// 生成文件下载链接
const fileName = '工具迷使用手册.pdf'
const encodedFileName = encodeURIComponent(fileName)

const downloadUrl = \`https://example.com/download?file=\${encodedFileName}\`
console.log(downloadUrl)
// https://example.com/download?file=%E5%B7%A5%E5%85%B7%E8%BF%B7%E4%BD%BF%E7%94%A8%E6%89%8B%E5%86%8C.pdf

// HTML 下载链接
const downloadLink = \`<a href="\${downloadUrl}" download="\${fileName}">下载文件</a>\``,
      language: 'javascript'
    },
    {
      title: '搜索功能实现',
      description: '实现带中文关键词的搜索功能：',
      code: `// 搜索功能
function performSearch(keyword) {
  const encodedKeyword = encodeURIComponent(keyword)
  const searchUrl = \`/search?q=\${encodedKeyword}\`

  // 更新浏览器地址栏
  window.history.pushState({}, '', searchUrl)

  // 发送搜索请求
  fetch(\`/api/search?q=\${encodedKeyword}\`)
    .then(response => response.json())
    .then(data => {
      // 处理搜索结果
      displaySearchResults(data)
    })
}

// 使用示例
performSearch('Vue.js 教程')
// 生成 URL: /search?q=Vue.js%20%E6%95%99%E7%A8%8B`,
      language: 'javascript'
    }
  ],
  technicalDetails: [
    {
      title: '编码规则',
      content: `URL 编码遵循以下规则：

**保留字符（不编码）：**
- 字母：A-Z, a-z
- 数字：0-9
- 特殊字符：- _ . ~

**编码字符：**
- 空格：编码为 %20 或 +
- 中文字符：编码为 UTF-8 字节序列
- 特殊符号：编码为对应的十六进制值

**编码格式：**
- 使用百分号 (%) 后跟两位十六进制数字
- 例如：空格 → %20，@ → %40，中文"你" → %E4%BD%A0`
    },
    {
      title: 'JavaScript 实现',
      content: `JavaScript 提供了内置的编码解码函数：

**encodeURIComponent()：**
- 编码除字母、数字、- _ . ~ 之外的所有字符
- 适用于编码 URL 参数值

**encodeURI()：**
- 编码除 URL 保留字符之外的字符
- 适用于编码完整的 URL

**decodeURIComponent()：**
- 解码由 encodeURIComponent 编码的字符串

**decodeURI()：**
- 解码由 encodeURI 编码的字符串

**使用建议：**
- 编码 URL 参数值时使用 encodeURIComponent
- 编码完整 URL 时使用 encodeURI`
    }
  ],
  tips: [
    '**参数编码**：URL 参数值必须使用 encodeURIComponent 编码',
    '**中文处理**：中文字符会被编码为多个百分号序列',
    '**空格处理**：空格可以编码为 %20 或 +，推荐使用 %20',
    '**解码验证**：解码后检查结果是否符合预期格式'
  ],
  warnings: [
    '**编码范围**：只对需要编码的部分进行编码，避免重复编码',
    '**字符集**：确保使用 UTF-8 字符集处理中文字符',
    '**URL 长度**：编码后的 URL 长度会增加，注意浏览器限制',
    '**安全性**：URL 编码不是加密，敏感信息不应放在 URL 中'
  ]
})

const encodeURL = () => {
  if (!inputValue.value.trim()) {
    return
  }
  try {
    outputValue.value = encodeURIComponent(inputValue.value)
  } catch (error) {
    console.error('URL编码失败:', error)
  }
}

const decodeURL = () => {
  if (!inputValue.value.trim()) {
    return
  }
  try {
    outputValue.value = decodeURIComponent(inputValue.value)
  } catch (error) {
    console.error('URL解码失败:', error)
    alert(t('tools.urlencode.decodeError'))
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleText
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
