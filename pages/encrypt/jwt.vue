<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.jwtParse.title') }}</h2>
    <p>{{ $t('tools.jwtParse.description') }}</p>

    <div class="form-item">
      <label>JWT <PERSON>ken</label>
      <textarea
        v-model="inputValue"
        rows="4"
        :placeholder="$t('tools.jwtParse.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="btn-group">
      <button @click="parseJWT" class="btn btn-primary">
        {{ $t('tools.jwtParse.decode') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('tools.jwtParse.copyResult') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <!-- 解析结果 -->
    <div v-if="parsedResult" class="jwt-result">
      <div class="result-section">
        <h3>{{ $t('tools.jwtParse.header') }}</h3>
        <pre class="json-output">{{ JSON.stringify(parsedResult.header, null, 2) }}</pre>
      </div>

      <div class="result-section">
        <h3>{{ $t('tools.jwtParse.payload') }}</h3>
        <pre class="json-output">{{ JSON.stringify(parsedResult.payload, null, 2) }}</pre>
      </div>

      <div class="result-section">
        <h3>{{ $t('tools.jwtParse.signature') }}</h3>
        <pre class="signature-output">{{ parsedResult.signature }}</pre>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.jwtParse.seo_title'),
  description: t('tools.jwtParse.seo_description'),
  keywords: t('tools.jwtParse.seo_keywords')
})

const inputValue = ref('')
const parsedResult = ref(null)
const errorMessage = ref('')
const exampleJWT = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('jwt')




const parseJWT = () => {
  if (!inputValue.value.trim()) {
    errorMessage.value = t('common.inputRequired')
    parsedResult.value = null
    return
  }

  try {
    const token = inputValue.value.trim()
    const parts = token.split('.')
    
    if (parts.length !== 3) {
      throw new Error('无效的JWT格式')
    }

    // 解析Header
    const header = JSON.parse(atob(parts[0]))
    
    // 解析Payload
    const payload = JSON.parse(atob(parts[1]))
    
    // 获取Signature
    const signature = parts[2]

    parsedResult.value = {
      header,
      payload,
      signature
    }
    
    errorMessage.value = ''
  } catch (error) {
    errorMessage.value = `${t('tools.jwtParse.parseError')}: ${error.message}`
    parsedResult.value = null
  }
}

const copyOutput = async () => {
  if (!parsedResult.value) {
    return
  }
  
  const output = JSON.stringify(parsedResult.value, null, 2)
  const success = await copyToClipboard(output)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleJWT
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.jwt-result {
  margin: 20px 0;
}

.result-section {
  margin-bottom: 20px;
}

.result-section h3 {
  margin-bottom: 8px;
  color: #374151;
  font-size: 16px;
}

.json-output {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
}

.signature-output {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  word-break: break-all;
}

.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  padding: 12px;
  color: #c33;
  margin: 16px 0;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
