<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.base64.title') }}</h2>
    <p>{{ $t('tools.base64.description') }}</p>
    
    <div class="form-item">
      <label>{{ $t('common.input') }}</label>
      <textarea
        v-model="inputValue"
        rows="5"
        :placeholder="$t('common.inputRequired')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="btn-group">
      <button @click="encodeBase64" class="btn btn-primary">
        {{ $t('tools.base64.encode') }}
      </button>
      <button @click="decodeBase64" class="btn btn-secondary">
        {{ $t('tools.base64.decode') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="downloadOutput" class="btn btn-secondary">
        {{ $t('common.download') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('common.output') }}</label>
      <textarea
        v-model="outputValue"
        rows="5"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- Markdown 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import { encode, decode } from 'js-base64'

const { copyToClipboard, downloadText } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.base64.seo_title'),
  description: t('tools.base64.seo_description'),
  keywords: t('tools.base64.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const exampleText = 'Hello World! 你好世界！'

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('base64')

const encodeBase64 = () => {
  if (!inputValue.value.trim()) {
    return
  }
  try {
    outputValue.value = encode(inputValue.value)
  } catch (error) {
    console.error('Base64 编码失败:', error)
  }
}

const decodeBase64 = () => {
  if (!inputValue.value.trim()) {
    return
  }
  try {
    outputValue.value = decode(inputValue.value)
  } catch (error) {
    console.error('Base64 解码失败:', error)
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const downloadOutput = () => {
  if (!outputValue.value) {
    return
  }
  downloadText(outputValue.value, 'base64_output.txt')
}

const loadExample = () => {
  inputValue.value = exampleText
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
