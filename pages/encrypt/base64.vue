<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.base64.title') }}</h2>
    <p>{{ $t('tools.base64.description') }}</p>
    
    <div class="form-item">
      <label>{{ $t('common.input') }}</label>
      <textarea
        v-model="inputValue"
        rows="5"
        :placeholder="$t('common.inputRequired')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="btn-group">
      <button @click="encodeBase64" class="btn btn-primary">
        {{ $t('tools.base64.encode') }}
      </button>
      <button @click="decodeBase64" class="btn btn-secondary">
        {{ $t('tools.base64.decode') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="downloadOutput" class="btn btn-secondary">
        {{ $t('common.download') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('common.output') }}</label>
      <textarea
        v-model="outputValue"
        rows="5"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- Markdown 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import { encode, decode } from 'js-base64'

const { copyToClipboard, downloadText } = useUtils()
const { t } = useI18n()

// SEO 配置
useSeoMeta({
  title: t('tools.base64.seo_title'),
  description: t('tools.base64.seo_description'),
  keywords: t('tools.base64.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const exampleText = 'Hello World! 你好世界！'

// Markdown 内容
const markdownContent = `# Base64 编码解码工具

Base64 是一种基于 64 个可打印字符来表示二进制数据的编码方式。它常用于在需要存储和传输二进制数据的场合，特别是在文本协议中传输二进制数据。

## 🎯 什么是 Base64？

Base64 编码是一种**二进制到文本**的编码方案，它将二进制数据转换为 ASCII 字符串格式。编码后的数据比原始数据大约增加 33% 的大小。

### 字符集

Base64 使用以下 64 个字符：
- **A-Z**：26 个大写字母
- **a-z**：26 个小写字母
- **0-9**：10 个数字
- **+** 和 **/**：2 个特殊字符
- **=**：填充字符

## ✨ 主要特性

- 🚀 **双向转换**：支持编码和解码操作
- 📱 **多格式支持**：支持文本、URL、文件等多种数据格式
- 🔒 **安全可靠**：使用标准 Base64 算法，确保数据完整性
- 💾 **文件下载**：支持将结果保存为文件
- 🌐 **中文支持**：完美支持中文字符编码解码

## 📖 使用示例

### 文本编码示例

**原始文本：**
\`\`\`
Hello World! 你好世界！
\`\`\`

**Base64 编码后：**
\`\`\`
SGVsbG8gV29ybGQhIOS9oOWlveS4lueVjO+8gQ==
\`\`\`

### URL 编码示例

**原始 URL：**
\`\`\`
https://www.gongjumi.com/zh/encrypt/base64?text=测试
\`\`\`

**Base64 编码后：**
\`\`\`
aHR0cHM6Ly93d3cuZ29uZ2p1bWkuY29tL3poL2VuY3J5cHQvYmFzZTY0P3RleHQ95rWL6K+V
\`\`\`

### JSON 数据编码

**原始 JSON：**
\`\`\`json
{
  "name": "工具迷",
  "type": "在线工具",
  "features": ["编码", "解码", "格式化"]
}
\`\`\`

**Base64 编码后：**
\`\`\`
ewogICJuYW1lIjogIuW3peWFt+i/tyIsCiAgInR5cGUiOiAi5Zyo57q/5bel5YW3IiwKICAiZmVhdHVyZXMiOiBbIue8lueggiIsICLop6PnoIIiLCAi5qC85byP5YyWIl0KfQ==
\`\`\`

## 🎯 应用场景

### 1. 数据传输

在 HTTP 协议中传输二进制数据：

\`\`\`javascript
// 将图片转换为 Base64 用于传输
const imageBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
\`\`\`

### 2. 邮件附件

电子邮件系统使用 Base64 编码附件：

\`\`\`
Content-Type: image/jpeg
Content-Transfer-Encoding: base64

/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQY...
\`\`\`

### 3. 数据存储

在 JSON 或 XML 中存储二进制数据：

\`\`\`json
{
  "user": {
    "name": "张三",
    "avatar": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD..."
  }
}
\`\`\`

### 4. API 接口

RESTful API 中传输文件数据：

\`\`\`javascript
// 上传文件到服务器
const uploadData = {
  filename: "document.pdf",
  content: "JVBERi0xLjQKJcOkw7zDtsO8CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo..."
}
\`\`\`

## 🔧 技术原理

### 编码过程

1. **分组**：将输入数据按 3 字节（24 位）分组
2. **转换**：将每组 24 位分成 4 个 6 位的块
3. **映射**：将每个 6 位值映射到 Base64 字符表
4. **填充**：如果最后一组不足 3 字节，用 \`=\` 填充

### 解码过程

1. **移除填充**：去掉末尾的 \`=\` 字符
2. **反向映射**：将 Base64 字符转换回 6 位值
3. **重组**：将 4 个 6 位值重组为 3 个 8 位字节
4. **输出**：得到原始二进制数据

## 💡 使用技巧

### 编码技巧
- **大文件处理**：对于大文件，建议分块处理避免内存溢出
- **URL 安全**：如果用于 URL，考虑使用 URL 安全的 Base64 变体
- **性能优化**：频繁编码解码时，可以考虑缓存结果

### 解码注意事项
- **格式验证**：确保输入是有效的 Base64 格式
- **字符检查**：移除非 Base64 字符（空格、换行等）
- **填充处理**：正确处理填充字符

## ⚠️ 注意事项

1. **不是加密**：Base64 是编码方式，不是加密算法，不提供安全性
2. **大小增加**：编码后数据大小会增加约 33%
3. **字符限制**：只能包含 Base64 字符集中的字符
4. **换行处理**：某些系统会在长 Base64 字符串中插入换行符

## 🚀 开始使用

1. **编码**：在输入框中输入要编码的文本，点击"Base64编码"
2. **解码**：在输入框中输入 Base64 字符串，点击"Base64解码"
3. **复制**：点击"复制"按钮将结果复制到剪贴板
4. **下载**：点击"下载"按钮将结果保存为文件
5. **示例**：点击"载入示例"查看演示数据

> **提示**：工具支持中文字符，会自动处理 UTF-8 编码转换。`

const encodeBase64 = () => {
  if (!inputValue.value.trim()) {
    return
  }
  try {
    outputValue.value = encode(inputValue.value)
  } catch (error) {
    console.error('Base64 编码失败:', error)
  }
}

const decodeBase64 = () => {
  if (!inputValue.value.trim()) {
    return
  }
  try {
    outputValue.value = decode(inputValue.value)
  } catch (error) {
    console.error('Base64 解码失败:', error)
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const downloadOutput = () => {
  if (!outputValue.value) {
    return
  }
  downloadText(outputValue.value, 'base64_output.txt')
}

const loadExample = () => {
  inputValue.value = exampleText
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
