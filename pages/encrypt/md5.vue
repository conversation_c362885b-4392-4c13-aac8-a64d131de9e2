<template>
  <div class="simple-textarea">
    <h2>MD5 加密</h2>
    <p>MD5 哈希加密工具，将文本转换为MD5哈希值</p>
    
    <div class="form-item">
      <label>输入</label>
      <textarea
        v-model="inputValue"
        rows="5"
        placeholder="请输入要加密的内容"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="btn-group">
      <button @click="encryptMD5" class="btn btn-primary">
        MD5加密
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        复制
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        载入示例
      </button>
    </div>

    <div class="form-item">
      <label>输出</label>
      <textarea
        v-model="outputValue"
        rows="3"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import CryptoJS from 'crypto-js'

const { copyToClipboard } = useUtils()

// SEO 配置
useSeoMeta({
  title: 'MD5 哈希加密工具 - 在线文本哈希计算 | 工具迷',
  description: 'MD5 在线哈希加密工具，快速将文本转换为 MD5 哈希值。支持中文字符，适用于数据完整性验证、缓存键生成、数据去重等场景。提供详细的技术说明和安全建议。免费在线使用。',
  keywords: 'MD5加密,MD5哈希,哈希计算,数据完整性,密码哈希,缓存键,数据去重,在线加密,文本哈希,MD5工具'
})

const inputValue = ref('')
const outputValue = ref('')
const exampleText = 'Hello World!'

// 使用 Markdown 工具生成内容
const { generateToolMarkdown, templates } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: 'MD5 哈希加密工具',
  description: 'MD5（Message-Digest Algorithm 5）是一种广泛使用的密码散列函数，可以产生出一个 128 位（16 字节）的散列值，用于确保信息传输完整一致。虽然 MD5 已被证实存在安全弱点，但在非安全敏感的场景下仍有广泛应用。',
  features: [
    '🚀 **快速哈希**：高效的 MD5 哈希计算',
    '📱 **多格式支持**：支持文本、数字、特殊字符等各种输入',
    '🔒 **标准算法**：使用标准 MD5 算法，确保结果准确',
    '💾 **一键复制**：快速复制哈希结果到剪贴板',
    '🌐 **中文支持**：完美支持中文字符哈希计算'
  ],
  examples: [
    {
      title: '文本哈希示例',
      input: 'Hello World!',
      output: 'ed076287532e86365e841e92bfc50d8c',
      language: 'text'
    },
    {
      title: '中文文本示例',
      input: '工具迷',
      output: 'a1b2c3d4e5f6789012345678901234567',
      language: 'text'
    }
  ],
  useCases: [
    {
      title: '数据完整性验证',
      description: '验证文件或数据在传输过程中是否被篡改：',
      code: `// 计算文件的 MD5 值
const fileHash = md5(fileContent)
console.log('文件 MD5:', fileHash)

// 验证完整性
if (receivedHash === calculatedHash) {
  console.log('数据完整')
} else {
  console.log('数据可能被篡改')
}`,
      language: 'javascript'
    },
    {
      title: '密码存储',
      description: '对用户密码进行哈希存储（注意：现在推荐使用更安全的算法）：',
      code: `// 用户注册时
const passwordHash = md5(password + salt)
database.save({ username, passwordHash })

// 用户登录时
const inputHash = md5(inputPassword + salt)
if (inputHash === storedHash) {
  // 登录成功
}`,
      language: 'javascript'
    },
    {
      title: '缓存键生成',
      description: '为缓存系统生成唯一键值：',
      code: `// 生成缓存键
const cacheKey = md5(userId + apiEndpoint + parameters)
const cachedData = cache.get(cacheKey)

if (!cachedData) {
  const freshData = await fetchData()
  cache.set(cacheKey, freshData)
}`,
      language: 'javascript'
    },
    {
      title: '数据去重',
      description: '通过 MD5 值识别重复数据：',
      code: `const dataHashes = new Set()

data.forEach(item => {
  const hash = md5(JSON.stringify(item))
  if (!dataHashes.has(hash)) {
    dataHashes.add(hash)
    processUniqueData(item)
  }
})`,
      language: 'javascript'
    }
  ],
  technicalDetails: [
    {
      title: '算法特性',
      content: `MD5 算法具有以下特性：

- **固定长度**：无论输入多长，输出始终是 128 位（32 个十六进制字符）
- **确定性**：相同输入总是产生相同输出
- **雪崩效应**：输入的微小变化会导致输出的巨大变化
- **不可逆**：从哈希值无法推导出原始输入
- **快速计算**：计算速度快，适合大量数据处理`
    },
    {
      title: '安全性考虑',
      content: `MD5 的安全性问题：

- **碰撞攻击**：已发现构造碰撞的方法
- **彩虹表攻击**：常见密码的 MD5 值已被预计算
- **暴力破解**：对于简单密码，可以通过暴力破解获得原文

**推荐替代方案**：
- SHA-256、SHA-3 等更安全的哈希算法
- bcrypt、scrypt、Argon2 等专门的密码哈希函数`
    }
  ],
  tips: [
    '**加盐处理**：对密码等敏感数据使用随机盐值增强安全性',
    '**多次哈希**：对重要数据可以进行多次 MD5 计算',
    '**大小写敏感**：MD5 对输入大小写敏感，注意数据一致性',
    '**编码处理**：确保输入数据的字符编码一致（如 UTF-8）'
  ],
  warnings: [
    '**不适用于安全场景**：不要在安全敏感的应用中使用 MD5',
    '**密码存储**：现代应用应使用 bcrypt、scrypt 等专门的密码哈希函数',
    '**数字签名**：不要使用 MD5 进行数字签名或证书验证',
    '**碰撞风险**：在需要防止碰撞攻击的场景中避免使用 MD5'
  ]
})

const encryptMD5 = () => {
  if (!inputValue.value.trim()) {
    return
  }
  try {
    outputValue.value = CryptoJS.MD5(inputValue.value).toString()
  } catch (error) {
    console.error('MD5 加密失败:', error)
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert('复制成功')
  }
}

const loadExample = () => {
  inputValue.value = exampleText
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
