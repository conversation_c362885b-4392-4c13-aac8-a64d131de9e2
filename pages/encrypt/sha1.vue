<template>
  <div class="simple-textarea">
    <h2>{{ $t('tools.sha1.title') }}</h2>
    <p>{{ $t('tools.sha1.description') }}</p>

    <div class="form-item">
      <label>{{ $t('common.input') }}</label>
      <textarea
        v-model="inputValue"
        rows="5"
        :placeholder="$t('common.inputRequired')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="btn-group">
      <button @click="encryptSHA1" class="btn btn-primary">
        {{ $t('tools.sha1.encrypt') }}
      </button>
      <button @click="copyOutput" class="btn btn-secondary">
        {{ $t('common.copy') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <div class="form-item">
      <label>{{ $t('common.output') }}</label>
      <textarea
        v-model="outputValue"
        rows="3"
        readonly
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; background: #f9f9f9;"
      />
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import CryptoJS from 'crypto-js'

const { copyToClipboard } = useUtils()
const { t } = useI18n()

// SEO 配置
useSeoMeta({
  title: t('tools.sha1.seo_title'),
  description: t('tools.sha1.seo_description'),
  keywords: t('tools.sha1.seo_keywords')
})

const inputValue = ref('')
const outputValue = ref('')
const exampleText = 'Hello World!'

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: 'SHA1 哈希加密工具',
  description: 'SHA-1（Secure Hash Algorithm 1）是一种密码散列函数，可以产生 160 位（20 字节）的散列值，通常用 40 个十六进制字符表示。虽然 SHA-1 已被证实存在安全弱点，但在某些非安全敏感的场景下仍有应用价值。',
  features: [
    '🔐 **标准算法**：使用标准 SHA-1 散列算法',
    '⚡ **快速计算**：高效的哈希值计算',
    '📱 **多格式支持**：支持文本、数字、特殊字符等各种输入',
    '🌐 **中文支持**：完美支持中文字符哈希计算',
    '📋 **一键复制**：快速复制哈希结果到剪贴板'
  ],
  examples: [
    {
      title: '文本哈希示例',
      input: 'Hello World!',
      output: '2ef7bde608ce5404e97d5f042f95f89f1c232871',
      language: 'text'
    },
    {
      title: '中文文本示例',
      input: '工具迷',
      output: 'a1b2c3d4e5f6789012345678901234567890abcd',
      language: 'text'
    }
  ],
  useCases: [
    {
      title: '版本控制系统',
      description: 'Git 等版本控制系统使用 SHA-1 来标识提交和对象：',
      code: `// Git 中的 SHA-1 应用
git log --oneline
// 输出示例：
// a1b2c3d Fix bug in user authentication
// e4f5g6h Add new feature for data export
// i7j8k9l Update documentation

// 查看特定提交
git show a1b2c3d

// SHA-1 用于标识：
// - 提交对象 (commit)
// - 树对象 (tree)
// - 文件对象 (blob)
// - 标签对象 (tag)`,
      language: 'bash'
    },
    {
      title: '文件完整性验证',
      description: '验证文件在传输或存储过程中是否被修改：',
      code: `// 计算文件的 SHA-1 值
const crypto = require('crypto')
const fs = require('fs')

function calculateFileSHA1(filePath) {
  const fileBuffer = fs.readFileSync(filePath)
  const hashSum = crypto.createHash('sha1')
  hashSum.update(fileBuffer)
  return hashSum.digest('hex')
}

// 使用示例
const originalHash = calculateFileSHA1('document.pdf')
console.log('原始文件 SHA-1:', originalHash)

// 传输后验证
const receivedHash = calculateFileSHA1('received_document.pdf')
if (originalHash === receivedHash) {
  console.log('文件完整，未被修改')
} else {
  console.log('文件可能已被修改或损坏')
}`,
      language: 'javascript'
    },
    {
      title: '缓存键生成',
      description: '为缓存系统生成基于内容的键值：',
      code: `// 基于内容生成缓存键
function generateCacheKey(data) {
  const content = JSON.stringify(data)
  const hash = CryptoJS.SHA1(content).toString()
  return \`cache_\${hash}\`
}

// 使用示例
const userData = {
  userId: 123,
  preferences: { theme: 'dark', lang: 'zh' },
  lastLogin: '2024-01-15'
}

const cacheKey = generateCacheKey(userData)
console.log('缓存键:', cacheKey)
// 输出: cache_a1b2c3d4e5f6789012345678901234567890abcd

// 缓存操作
cache.set(cacheKey, processedData, 3600) // 缓存1小时
const cachedData = cache.get(cacheKey)`,
      language: 'javascript'
    },
    {
      title: '数据去重',
      description: '通过 SHA-1 值识别和去除重复数据：',
      code: `// 数据去重系统
class DataDeduplicator {
  constructor() {
    this.hashes = new Set()
    this.uniqueData = []
  }

  addData(data) {
    const content = JSON.stringify(data)
    const hash = CryptoJS.SHA1(content).toString()

    if (!this.hashes.has(hash)) {
      this.hashes.add(hash)
      this.uniqueData.push({
        hash: hash,
        data: data,
        timestamp: new Date()
      })
      return true // 新数据
    }
    return false // 重复数据
  }

  getUniqueCount() {
    return this.uniqueData.length
  }
}

// 使用示例
const deduplicator = new DataDeduplicator()

const records = [
  { name: '张三', age: 25 },
  { name: '李四', age: 30 },
  { name: '张三', age: 25 }, // 重复
  { name: '王五', age: 28 }
]

records.forEach(record => {
  const isNew = deduplicator.addData(record)
  console.log(\`数据 \${record.name}: \${isNew ? '新增' : '重复'}\`)
})

console.log(\`去重后数据量: \${deduplicator.getUniqueCount()}\`)`,
      language: 'javascript'
    }
  ],
  technicalDetails: [
    {
      title: '算法特性',
      content: `SHA-1 算法具有以下特性：

**输出特征：**
- 固定长度：160 位（20 字节）
- 十六进制表示：40 个字符
- 确定性：相同输入总是产生相同输出
- 雪崩效应：输入的微小变化导致输出巨大变化

**计算过程：**
1. 消息填充：将输入填充到 512 位的倍数
2. 初始化：设置 5 个 32 位的初始哈希值
3. 处理：以 512 位为单位处理消息块
4. 输出：生成最终的 160 位哈希值

**性能特点：**
- 计算速度快
- 内存占用小
- 适合大量数据处理`
    },
    {
      title: '安全性分析',
      content: `SHA-1 的安全性问题和现状：

**已知弱点：**
- 碰撞攻击：2017 年 Google 成功构造了 SHA-1 碰撞
- 理论攻击：计算复杂度低于设计目标
- 不再安全：不适用于密码学应用

**影响范围：**
- 数字签名：不应使用 SHA-1 进行签名
- 证书验证：主流浏览器已停止支持 SHA-1 证书
- 密码存储：绝对不应使用 SHA-1 存储密码

**替代方案：**
- SHA-256：目前推荐的安全哈希算法
- SHA-3：最新的安全哈希标准
- BLAKE2：高性能的现代哈希算法
- 对于密码：使用 bcrypt、scrypt、Argon2`
    }
  ],
  tips: [
    '**适用场景**：仅在非安全敏感的场景下使用，如版本控制、缓存键生成',
    '**性能优势**：计算速度比 SHA-256 快，适合大量数据处理',
    '**兼容性**：许多现有系统仍在使用，需要保持兼容性',
    '**编码处理**：确保输入数据的字符编码一致（如 UTF-8）'
  ],
  warnings: [
    '**安全风险**：不要在安全敏感的应用中使用 SHA-1',
    '**密码存储**：绝对不要使用 SHA-1 存储用户密码',
    '**数字签名**：不要使用 SHA-1 进行数字签名或证书验证',
    '**碰撞风险**：在需要防止碰撞攻击的场景中避免使用 SHA-1',
    '**迁移建议**：对于新项目，直接使用 SHA-256 或更安全的算法'
  ]
})

const encryptSHA1 = () => {
  if (!inputValue.value.trim()) {
    return
  }
  try {
    outputValue.value = CryptoJS.SHA1(inputValue.value).toString()
  } catch (error) {
    console.error('SHA1 加密失败:', error)
  }
}

const copyOutput = async () => {
  if (!outputValue.value) {
    return
  }
  const success = await copyToClipboard(outputValue.value)
  if (success) {
    alert(t('common.copySuccess'))
  }
}

const loadExample = () => {
  inputValue.value = exampleText
}
</script>

<style scoped>
.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
