<template>
  <div class="qr-generator">
    <h2>二维码生成器</h2>
    <p>在线二维码生成工具，支持文本、URL等内容</p>
    
    <div class="form-item">
      <label>输入内容</label>
      <textarea
        v-model="inputValue"
        rows="4"
        placeholder="请输入要生成二维码的内容（文本、URL等）"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="btn-group">
      <button @click="generateQR" class="btn btn-primary">
        生成二维码
      </button>
      <button @click="downloadQR" class="btn btn-secondary" :disabled="!qrDataUrl">
        下载二维码
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        载入示例
      </button>
    </div>

    <!-- 二维码显示区域 -->
    <div v-if="qrDataUrl" class="qr-result">
      <h3>生成的二维码</h3>
      <div class="qr-image-container">
        <img :src="qrDataUrl" alt="Generated QR Code" class="qr-image" />
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import QRCode from 'qrcode'

// SEO 配置
useSeoMeta({
  title: '二维码生成器 - 在线 QR Code 制作工具 | 工具迷',
  description: '二维码在线生成器，支持文本、网址、联系方式、WiFi 密码等内容生成二维码。高清输出，支持下载保存。适用于营销推广、产品标识、信息分享等场景。',
  keywords: '二维码生成器,QR码制作,在线二维码,二维码工具,营销推广,产品标识,信息分享,移动支付,联系方式'
})

const inputValue = ref('')
const qrDataUrl = ref('')
const exampleText = 'https://www.gongjumi.com'

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: '二维码生成器',
  description: '二维码（QR Code）是一种矩阵式二维条码，由日本 Denso Wave 公司于 1994 年发明。QR 来自英文 Quick Response 的缩写，即快速响应的意思。本工具可以将文本、网址、联系方式等信息快速生成为二维码图片。',
  features: [
    '📱 **多内容支持**：支持文本、网址、联系方式、WiFi 密码等',
    '🎨 **高清输出**：生成高质量的二维码图片',
    '💾 **一键下载**：支持 PNG 格式图片下载',
    '⚡ **即时生成**：输入内容立即生成二维码',
    '🔧 **自定义设置**：支持尺寸和样式调整'
  ],
  examples: [
    {
      title: '网址二维码',
      input: 'https://www.gongjumi.com',
      output: '生成包含网址的二维码，扫描后直接打开网页',
      language: 'text'
    },
    {
      title: '文本信息',
      input: '欢迎使用工具迷在线工具平台！',
      output: '生成包含文本信息的二维码，扫描后显示文字内容',
      language: 'text'
    },
    {
      title: '联系方式',
      input: 'BEGIN:VCARD\nVERSION:3.0\nFN:张三\nTEL:13800138000\nEMAIL:<EMAIL>\nEND:VCARD',
      output: '生成联系人信息二维码，扫描后可添加到通讯录',
      language: 'text'
    }
  ],
  useCases: [
    {
      title: '营销推广',
      description: '在宣传材料中使用二维码，方便用户快速访问：',
      code: `# 营销场景应用
1. 产品包装：添加产品详情页二维码
   内容：https://product.example.com/details/12345

2. 宣传海报：添加活动报名二维码
   内容：https://event.example.com/register

3. 名片设计：添加个人主页二维码
   内容：https://profile.example.com/john-doe

4. 店铺推广：添加优惠券二维码
   内容：https://coupon.example.com/discount-50

5. 社交媒体：添加关注二维码
   内容：https://weixin.qq.com/r/follow-account`,
      language: 'text'
    },
    {
      title: '产品标识',
      description: '在产品上添加二维码，提供产品信息和服务：',
      code: `# 产品标识应用
1. 产品溯源：
   内容：https://trace.example.com/product/ABC123456

2. 使用说明：
   内容：https://manual.example.com/product/model-x

3. 售后服务：
   内容：https://support.example.com/warranty/SN789012

4. 产品认证：
   内容：https://cert.example.com/verify/CERT456789

5. 防伪验证：
   内容：https://auth.example.com/verify/ANTI123456`,
      language: 'text'
    },
    {
      title: '信息分享',
      description: '快速分享各种信息，无需手动输入：',
      code: `# WiFi 密码分享
WIFI:T:WPA;S:MyWiFiNetwork;P:password123;H:false;;

# 联系方式分享
BEGIN:VCARD
VERSION:3.0
FN:张三
ORG:工具迷科技
TITLE:产品经理
TEL:+86-138-0013-8000
EMAIL:<EMAIL>
URL:https://www.gongjumi.com
END:VCARD

# 地理位置分享
geo:39.9042,116.4074?q=北京天安门

# 短信模板
sms:13800138000:您好，这是来自工具迷的消息

# 邮件模板
mailto:<EMAIL>?subject=咨询&body=您好，我想了解更多信息`,
      language: 'text'
    },
    {
      title: '移动支付',
      description: '生成支付二维码，方便用户扫码付款：',
      code: `# 支付宝收款码格式示例
https://qr.alipay.com/fkx12345678901234567890

# 微信收款码格式示例
wxp://f2f0abcdefghijklmnopqrstuvwxyz123456

# 银行转账信息
收款人：工具迷科技有限公司
账号：1234567890123456789
开户行：中国工商银行北京分行
金额：￥100.00
用途：产品购买

# 数字货币地址
BTC: **********************************
ETH: ******************************************`,
      language: 'text'
    }
  ],
  technicalDetails: [
    {
      title: '二维码规格',
      content: `二维码的技术规格和特点：

**存储容量：**
- 数字：最多 7,089 个字符
- 字母数字：最多 4,296 个字符
- 二进制：最多 2,953 字节
- 汉字：最多 1,817 个字符

**版本规格：**
- 版本 1：21×21 模块
- 版本 40：177×177 模块
- 每个版本增加 4 个模块

**纠错级别：**
- L 级：约 7% 的错误修正能力
- M 级：约 15% 的错误修正能力
- Q 级：约 25% 的错误修正能力
- H 级：约 30% 的错误修正能力

**结构组成：**
- 位置探测图形：用于定位
- 分隔符：分隔不同区域
- 格式信息：包含纠错级别和掩码信息
- 版本信息：标识二维码版本
- 数据和纠错码字：实际存储的信息`
    },
    {
      title: '生成参数',
      content: `本工具的生成参数设置：

**图片规格：**
- 尺寸：256×256 像素
- 格式：PNG
- 背景色：白色 (#FFFFFF)
- 前景色：黑色 (#000000)

**边距设置：**
- 边距：2 个模块宽度
- 确保扫描设备能正确识别

**纠错级别：**
- 默认使用 M 级纠错
- 平衡存储容量和纠错能力

**编码方式：**
- 自动选择最优编码方式
- 支持 UTF-8 字符集
- 兼容各种扫描设备

**输出质量：**
- 矢量化生成，确保清晰度
- 适合打印和数字显示
- 支持高分辨率输出`
    }
  ],
  tips: [
    '**内容长度**：内容越短，生成的二维码越简单，扫描成功率越高',
    '**测试扫描**：生成后建议用多种设备测试扫描效果',
    '**打印质量**：打印时确保二维码清晰，避免模糊影响扫描',
    '**尺寸选择**：根据使用场景选择合适的二维码尺寸'
  ],
  warnings: [
    '**内容限制**：避免在二维码中包含过多信息，影响扫描成功率',
    '**隐私保护**：不要在二维码中包含敏感个人信息',
    '**链接安全**：确保二维码中的链接安全可信，避免恶意网站',
    '**版权注意**：商业使用时注意二维码技术的相关版权规定'
  ]
})

const generateQR = async () => {
  if (!inputValue.value.trim()) {
    return
  }
  
  try {
    const dataUrl = await QRCode.toDataURL(inputValue.value, {
      width: 256,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    qrDataUrl.value = dataUrl
  } catch (error) {
    console.error('二维码生成失败:', error)
    alert('二维码生成失败，请检查输入内容')
  }
}

const downloadQR = () => {
  if (!qrDataUrl.value) {
    return
  }
  
  const link = document.createElement('a')
  link.download = 'qrcode.png'
  link.href = qrDataUrl.value
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const loadExample = () => {
  inputValue.value = exampleText
}
</script>

<style scoped>
.qr-generator {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn-group button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.qr-result {
  margin: 32px 0;
  text-align: center;
}

.qr-result h3 {
  margin-bottom: 16px;
  color: #374151;
}

.qr-image-container {
  display: inline-block;
  padding: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.qr-image {
  display: block;
  max-width: 256px;
  height: auto;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
