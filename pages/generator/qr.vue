<template>
  <div class="qr-generator">
    <h2>{{ $t('tools.qr.title') }}</h2>
    <p>{{ $t('tools.qr.description') }}</p>

    <div class="form-item">
      <label>{{ $t('tools.qr.inputLabel') }}</label>
      <textarea
        v-model="inputValue"
        rows="4"
        :placeholder="$t('tools.qr.inputPlaceholder')"
        style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>

    <div class="btn-group">
      <button @click="generateQR" class="btn btn-primary">
        {{ $t('tools.qr.generate') }}
      </button>
      <button @click="downloadQR" class="btn btn-secondary" :disabled="!qrDataUrl">
        {{ $t('tools.qr.download') }}
      </button>
      <button @click="loadExample" class="btn btn-secondary">
        {{ $t('common.example') }}
      </button>
    </div>

    <!-- 二维码显示区域 -->
    <div v-if="qrDataUrl" class="qr-result">
      <h3>{{ $t('tools.qr.resultTitle') }}</h3>
      <div class="qr-image-container">
        <img :src="qrDataUrl" alt="Generated QR Code" class="qr-image" />
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
import QRCode from 'qrcode'

const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.qr.seo_title'),
  description: t('tools.qr.seo_description'),
  keywords: t('tools.qr.seo_keywords')
})

const inputValue = ref('')
const qrDataUrl = ref('')
const exampleText = 'https://www.gongjumi.com'

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('qr')







const generateQR = async () => {
  if (!inputValue.value.trim()) {
    return
  }
  
  try {
    const dataUrl = await QRCode.toDataURL(inputValue.value, {
      width: 256,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    qrDataUrl.value = dataUrl
  } catch (error) {
    console.error('二维码生成失败:', error)
    alert('二维码生成失败，请检查输入内容')
  }
}

const downloadQR = () => {
  if (!qrDataUrl.value) {
    return
  }
  
  const link = document.createElement('a')
  link.download = 'qrcode.png'
  link.href = qrDataUrl.value
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const loadExample = () => {
  inputValue.value = exampleText
}
</script>

<style scoped>
.qr-generator {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  margin: 16px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn-group button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.qr-result {
  margin: 32px 0;
  text-align: center;
}

.qr-result h3 {
  margin-bottom: 16px;
  color: #374151;
}

.qr-image-container {
  display: inline-block;
  padding: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.qr-image {
  display: block;
  max-width: 256px;
  height: auto;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
