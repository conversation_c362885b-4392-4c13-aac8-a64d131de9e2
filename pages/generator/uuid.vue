<template>
  <div class="uuid-generator">
    <h2>UUID 生成器</h2>
    <p>UUID 唯一标识符生成工具</p>
    
    <div class="generator-controls">
      <div class="form-item">
        <label>生成数量</label>
        <input
          v-model.number="generateCount"
          type="number"
          min="1"
          max="100"
          style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
        />
      </div>

      <div class="btn-group">
        <button @click="generateUUIDs" class="btn btn-primary">
          生成UUID
        </button>
        <button @click="copyAllUUIDs" class="btn btn-secondary">
          复制全部
        </button>
        <button @click="clearUUIDs" class="btn btn-danger">
          清空
        </button>
      </div>
    </div>

    <!-- UUID列表 -->
    <div v-if="uuidList.length > 0" class="uuid-list">
      <h3>生成的UUID ({{ uuidList.length }}个)</h3>
      <div class="uuid-items">
        <div
          v-for="(uuid, index) in uuidList"
          :key="index"
          class="uuid-item"
        >
          <span class="uuid-text">{{ uuid }}</span>
          <button 
            @click="copyUUID(uuid)"
            class="copy-btn"
          >
            复制
          </button>
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard, generateId } = useUtils()

// SEO 配置
useSeoMeta({
  title: 'UUID 生成器 - 在线唯一标识符生成工具 | 工具迷',
  description: 'UUID 在线生成器，快速生成全球唯一标识符。支持批量生成、一键复制。适用于数据库主键、分布式系统、API 开发等场景。符合 RFC 4122 标准。',
  keywords: 'UUID生成器,唯一标识符,GUID,数据库主键,分布式系统,API开发,RFC4122,在线生成'
})

const generateCount = ref(5)
const uuidList = ref([])

// 使用 Markdown 工具生成内容
const { generateToolMarkdown } = useMarkdown()

const markdownContent = generateToolMarkdown({
  title: 'UUID 生成器',
  description: 'UUID（Universally Unique Identifier）是通用唯一识别码，是一种软件建构的标准，用于分布式计算环境中的唯一性标识信息。本工具可以快速生成符合 RFC 4122 标准的 UUID v4，确保全球唯一性。',
  features: [
    '🎯 **全球唯一**：生成符合 RFC 4122 标准的 UUID v4',
    '📊 **批量生成**：支持一次生成多个 UUID',
    '📋 **一键复制**：单个或批量复制 UUID',
    '🔄 **实时生成**：点击即可生成新的 UUID',
    '💾 **历史记录**：保留生成历史，方便查看和使用'
  ],
  examples: [
    {
      title: 'UUID v4 格式',
      input: '点击生成按钮',
      output: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      language: 'text'
    },
    {
      title: '批量生成示例',
      input: '设置生成数量为 3',
      output: `f47ac10b-58cc-4372-a567-0e02b2c3d479
6ba7b810-9dad-11d1-80b4-00c04fd430c8
6ba7b811-9dad-11d1-80b4-00c04fd430c8`,
      language: 'text'
    }
  ],
  useCases: [
    {
      title: '数据库主键',
      description: '在数据库设计中使用 UUID 作为主键，避免 ID 冲突：',
      code: `-- MySQL 数据库示例
CREATE TABLE users (
  id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
  username VARCHAR(50) NOT NULL,
  email VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO users (id, username, email)
VALUES ('f47ac10b-58cc-4372-a567-0e02b2c3d479', 'john_doe', '<EMAIL>');

-- PostgreSQL 示例
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  price DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT NOW()
);`,
      language: 'sql'
    },
    {
      title: '分布式系统',
      description: '在微服务架构中使用 UUID 确保跨服务的唯一性：',
      code: `// Node.js 微服务示例
const { v4: uuidv4 } = require('uuid');

// 订单服务
class OrderService {
  async createOrder(userId, items) {
    const orderId = uuidv4(); // 生成唯一订单ID

    const order = {
      id: orderId,
      userId: userId,
      items: items,
      status: 'pending',
      createdAt: new Date(),
      // 关联其他服务的ID也使用UUID
      paymentId: uuidv4(),
      shippingId: uuidv4()
    };

    await this.saveOrder(order);

    // 发送事件到其他服务
    await this.eventBus.publish('order.created', {
      orderId: orderId,
      userId: userId,
      timestamp: new Date()
    });

    return order;
  }
}

// API 网关中的请求追踪
app.use((req, res, next) => {
  req.traceId = uuidv4(); // 为每个请求生成追踪ID
  res.setHeader('X-Trace-ID', req.traceId);
  console.log(\`[\${req.traceId}] \${req.method} \${req.path}\`);
  next();
});`,
      language: 'javascript'
    },
    {
      title: 'API 开发',
      description: '在 RESTful API 中使用 UUID 作为资源标识符：',
      code: `// Express.js API 示例
const express = require('express');
const { v4: uuidv4 } = require('uuid');
const app = express();

// 创建资源
app.post('/api/articles', async (req, res) => {
  const article = {
    id: uuidv4(), // 使用UUID作为文章ID
    title: req.body.title,
    content: req.body.content,
    authorId: req.user.id,
    slug: generateSlug(req.body.title),
    createdAt: new Date(),
    updatedAt: new Date()
  };

  await Article.create(article);

  res.status(201).json({
    success: true,
    data: article
  });
});

// 获取资源
app.get('/api/articles/:id', async (req, res) => {
  const { id } = req.params;

  // 验证UUID格式
  if (!isValidUUID(id)) {
    return res.status(400).json({
      error: 'Invalid article ID format'
    });
  }

  const article = await Article.findById(id);

  if (!article) {
    return res.status(404).json({
      error: 'Article not found'
    });
  }

  res.json({
    success: true,
    data: article
  });
});

// UUID 格式验证函数
function isValidUUID(str) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}`,
      language: 'javascript'
    },
    {
      title: '文件系统',
      description: '在文件上传和管理中使用 UUID 避免文件名冲突：',
      code: `// 文件上传处理
const multer = require('multer');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 配置文件存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    // 使用UUID生成唯一文件名
    const fileId = uuidv4();
    const extension = path.extname(file.originalname);
    const filename = \`\${fileId}\${extension}\`;

    // 保存文件信息到数据库
    const fileRecord = {
      id: fileId,
      originalName: file.originalname,
      filename: filename,
      mimetype: file.mimetype,
      uploadedBy: req.user.id,
      uploadedAt: new Date()
    };

    req.fileRecord = fileRecord;
    cb(null, filename);
  }
});

const upload = multer({ storage: storage });

// 文件上传接口
app.post('/api/upload', upload.single('file'), async (req, res) => {
  try {
    await File.create(req.fileRecord);

    res.json({
      success: true,
      data: {
        fileId: req.fileRecord.id,
        originalName: req.fileRecord.originalName,
        downloadUrl: \`/api/files/\${req.fileRecord.id}\`
      }
    });
  } catch (error) {
    res.status(500).json({
      error: 'File upload failed'
    });
  }
});

// 文件下载接口
app.get('/api/files/:id', async (req, res) => {
  const fileRecord = await File.findById(req.params.id);

  if (!fileRecord) {
    return res.status(404).json({
      error: 'File not found'
    });
  }

  const filePath = path.join(__dirname, 'uploads', fileRecord.filename);
  res.download(filePath, fileRecord.originalName);
});`,
      language: 'javascript'
    }
  ],
  technicalDetails: [
    {
      title: 'UUID 版本',
      content: `UUID 有多个版本，各有不同的生成方式：

**UUID v1（基于时间）：**
- 基于时间戳和 MAC 地址
- 可以推断生成时间和位置
- 存在隐私泄露风险

**UUID v2（DCE 安全）：**
- 基于时间戳、MAC 地址和用户 ID
- 很少使用

**UUID v3（基于名称的 MD5）：**
- 基于命名空间和名称的 MD5 哈希
- 相同输入产生相同 UUID

**UUID v4（随机）：**
- 基于随机数或伪随机数
- 本工具使用的版本
- 最常用，安全性高

**UUID v5（基于名称的 SHA-1）：**
- 基于命名空间和名称的 SHA-1 哈希
- 比 v3 更安全`
    },
    {
      title: 'UUID v4 格式',
      content: `UUID v4 的格式和特点：

**格式结构：**
\`xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\`

**各部分说明：**
- 第 1 部分：8 个十六进制字符（32 位）
- 第 2 部分：4 个十六进制字符（16 位）
- 第 3 部分：4 个十六进制字符，第一个字符固定为 4
- 第 4 部分：4 个十六进制字符，第一个字符为 8、9、a 或 b
- 第 5 部分：12 个十六进制字符（48 位）

**随机性：**
- 总共 128 位
- 122 位是随机的
- 6 位用于版本和变体标识

**唯一性保证：**
- 理论上可以生成 2^122 个不同的 UUID
- 碰撞概率极低，可以忽略不计`
    },
    {
      title: '生成算法',
      content: `本工具使用的 UUID v4 生成算法：

**步骤说明：**
1. 生成 128 位随机数
2. 设置版本位（第 13 个十六进制字符为 4）
3. 设置变体位（第 17 个十六进制字符的前两位）
4. 格式化为标准的 8-4-4-4-12 格式

**JavaScript 实现：**
\`\`\`javascript
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
\`\`\`

**随机数来源：**
- 使用 JavaScript 的 Math.random()
- 在生产环境中建议使用加密安全的随机数生成器
- 如 crypto.getRandomValues() 或 Node.js 的 crypto 模块`
    }
  ],
  tips: [
    '**批量生成**：可以一次生成多个 UUID，提高工作效率',
    '**格式验证**：使用 UUID 时建议验证格式的正确性',
    '**存储优化**：数据库中可以使用 BINARY(16) 存储 UUID 节省空间',
    '**索引性能**：UUID 作为主键时注意数据库索引性能'
  ],
  warnings: [
    '**随机性依赖**：UUID v4 的唯一性依赖于随机数生成器的质量',
    '**存储空间**：UUID 比整数 ID 占用更多存储空间',
    '**性能考虑**：在高并发场景下，UUID 生成可能影响性能',
    '**排序问题**：UUID 是无序的，不适合需要按时间排序的场景'
  ]
})

// 生成UUID v4
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

const generateUUIDs = () => {
  const count = Math.min(Math.max(generateCount.value, 1), 100)
  const newUUIDs = []
  
  for (let i = 0; i < count; i++) {
    newUUIDs.push(generateUUID())
  }
  
  uuidList.value = [...uuidList.value, ...newUUIDs]
}

const copyUUID = async (uuid: string) => {
  const success = await copyToClipboard(uuid)
  if (success) {
    alert('UUID已复制')
  }
}

const copyAllUUIDs = async () => {
  if (uuidList.value.length === 0) {
    return
  }
  
  const allUUIDs = uuidList.value.join('\n')
  const success = await copyToClipboard(allUUIDs)
  if (success) {
    alert('所有UUID已复制')
  }
}

const clearUUIDs = () => {
  uuidList.value = []
}

// 页面加载时生成一些UUID
onMounted(() => {
  generateUUIDs()
})
</script>

<style scoped>
.uuid-generator {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.generator-controls {
  margin-bottom: 24px;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.uuid-list {
  margin: 24px 0;
}

.uuid-list h3 {
  margin-bottom: 16px;
  color: #374151;
}

.uuid-items {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  max-height: 400px;
  overflow-y: auto;
}

.uuid-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  background: white;
}

.uuid-item:last-child {
  border-bottom: none;
}

.uuid-item:hover {
  background: #f9fafb;
}

.uuid-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: #374151;
  flex: 1;
  margin-right: 12px;
}

.copy-btn {
  padding: 4px 8px;
  background: #00b96b;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s;
}

.copy-btn:hover {
  background: #009456;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
