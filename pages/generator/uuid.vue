<template>
  <div class="uuid-generator">
    <h2>{{ $t('tools.uuid.title') }}</h2>
    <p>{{ $t('tools.uuid.description') }}</p>

    <div class="generator-controls">
      <div class="form-item">
        <label>{{ $t('tools.uuid.countLabel') }}</label>
        <input
          v-model.number="generateCount"
          type="number"
          min="1"
          max="100"
          style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
        />
      </div>

      <div class="btn-group">
        <button @click="generateUUIDs" class="btn btn-primary">
          {{ $t('tools.uuid.generate') }}
        </button>
        <button @click="copyAllUUIDs" class="btn btn-secondary">
          {{ $t('tools.uuid.copyAll') }}
        </button>
        <button @click="clearUUIDs" class="btn btn-danger">
          {{ $t('tools.uuid.clear') }}
        </button>
      </div>
    </div>

    <!-- UUID列表 -->
    <div v-if="uuidList.length > 0" class="uuid-list">
      <h3>{{ $t('tools.uuid.resultTitle') }} ({{ uuidList.length }}{{ $t('tools.uuid.countSuffix') }})</h3>
      <div class="uuid-items">
        <div
          v-for="(uuid, index) in uuidList"
          :key="index"
          class="uuid-item"
        >
          <span class="uuid-text">{{ uuid }}</span>
          <button
            @click="copyUUID(uuid)"
            class="copy-btn"
          >
            {{ $t('common.copy') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 文档 -->
    <MarkdownRenderer :content="markdownContent" />
  </div>
</template>

<script setup lang="ts">
const { copyToClipboard, generateId } = useUtils()
const { t } = useI18n()
const { useToolMarkdown } = useMarkdownContent()

// SEO 配置
useSeoMeta({
  title: t('tools.uuid.seo_title'),
  description: t('tools.uuid.seo_description'),
  keywords: t('tools.uuid.seo_keywords')
})

const generateCount = ref(5)
const uuidList = ref([])

// 动态加载 Markdown 内容
const markdownContent = useToolMarkdown('uuid')






// 生成UUID v4
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

const generateUUIDs = () => {
  const count = Math.min(Math.max(generateCount.value, 1), 100)
  const newUUIDs = []
  
  for (let i = 0; i < count; i++) {
    newUUIDs.push(generateUUID())
  }
  
  uuidList.value = [...uuidList.value, ...newUUIDs]
}

const copyUUID = async (uuid: string) => {
  const success = await copyToClipboard(uuid)
  if (success) {
    alert('UUID已复制')
  }
}

const copyAllUUIDs = async () => {
  if (uuidList.value.length === 0) {
    return
  }
  
  const allUUIDs = uuidList.value.join('\n')
  const success = await copyToClipboard(allUUIDs)
  if (success) {
    alert('所有UUID已复制')
  }
}

const clearUUIDs = () => {
  uuidList.value = []
}

// 页面加载时生成一些UUID
onMounted(() => {
  generateUUIDs()
})
</script>

<style scoped>
.uuid-generator {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.generator-controls {
  margin-bottom: 24px;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.btn-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.uuid-list {
  margin: 24px 0;
}

.uuid-list h3 {
  margin-bottom: 16px;
  color: #374151;
}

.uuid-items {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  max-height: 400px;
  overflow-y: auto;
}

.uuid-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  background: white;
}

.uuid-item:last-child {
  border-bottom: none;
}

.uuid-item:hover {
  background: #f9fafb;
}

.uuid-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: #374151;
  flex: 1;
  margin-right: 12px;
}

.copy-btn {
  padding: 4px 8px;
  background: #00b96b;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s;
}

.copy-btn:hover {
  background: #009456;
}

.markdown-body {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.markdown-body h3 {
  margin-top: 0;
  color: #374151;
}

.markdown-body p {
  line-height: 1.6;
  color: #6b7280;
}
</style>
