<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          {{ $t('color.title') }}
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          {{ $t('color.description') }}
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 左侧：颜色选择器 -->
        <div class="lg:col-span-1">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              {{ $t('color.picker.title') }}
            </h2>
            
            <!-- 颜色预览 -->
            <div class="mb-6">
              <div 
                class="w-full h-32 rounded-lg border-2 border-gray-200 dark:border-gray-600 mb-4 cursor-pointer relative overflow-hidden"
                :style="{ backgroundColor: currentColor }"
                @click="showColorPicker = !showColorPicker"
              >
                <div class="absolute inset-0 bg-gradient-to-br from-transparent to-black/10"></div>
                <div class="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-sm">
                  {{ currentColor }}
                </div>
              </div>
              
              <!-- HTML5 颜色选择器 -->
              <input
                v-model="currentColor"
                type="color"
                class="w-full h-12 rounded-lg border-2 border-gray-200 dark:border-gray-600 cursor-pointer"
                @input="updateColor"
              />
            </div>

            <!-- 颜色输入 -->
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  HEX
                </label>
                <input
                  v-model="colorFormats.hex"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  @input="updateFromHex"
                  placeholder="#3498db"
                />
              </div>

              <div class="grid grid-cols-3 gap-2">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    R
                  </label>
                  <input
                    v-model.number="colorFormats.rgb.r"
                    type="number"
                    min="0"
                    max="255"
                    class="w-full px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                    @input="updateFromRgb"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    G
                  </label>
                  <input
                    v-model.number="colorFormats.rgb.g"
                    type="number"
                    min="0"
                    max="255"
                    class="w-full px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                    @input="updateFromRgb"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    B
                  </label>
                  <input
                    v-model.number="colorFormats.rgb.b"
                    type="number"
                    min="0"
                    max="255"
                    class="w-full px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                    @input="updateFromRgb"
                  />
                </div>
              </div>

              <div class="grid grid-cols-3 gap-2">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    H
                  </label>
                  <input
                    v-model.number="colorFormats.hsl.h"
                    type="number"
                    min="0"
                    max="360"
                    class="w-full px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                    @input="updateFromHsl"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    S%
                  </label>
                  <input
                    v-model.number="colorFormats.hsl.s"
                    type="number"
                    min="0"
                    max="100"
                    class="w-full px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                    @input="updateFromHsl"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    L%
                  </label>
                  <input
                    v-model.number="colorFormats.hsl.l"
                    type="number"
                    min="0"
                    max="100"
                    class="w-full px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                    @input="updateFromHsl"
                  />
                </div>
              </div>
            </div>

            <!-- 颜色历史 -->
            <div class="mt-6">
              <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                {{ $t('color.history.title') }}
              </h3>
              <div class="grid grid-cols-8 gap-2">
                <div
                  v-for="(color, index) in colorHistory"
                  :key="index"
                  class="w-8 h-8 rounded border-2 border-gray-200 dark:border-gray-600 cursor-pointer hover:scale-110 transition-transform"
                  :style="{ backgroundColor: color }"
                  @click="selectHistoryColor(color)"
                  :title="color"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间：颜色格式和调色板 -->
        <div class="lg:col-span-1">
          <!-- 颜色格式 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              {{ $t('color.formats.title') }}
            </h2>
            
            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">HEX:</span>
                <div class="flex items-center space-x-2">
                  <code class="text-sm font-mono text-gray-900 dark:text-white">{{ colorFormats.hex }}</code>
                  <button
                    @click="copyToClipboard(colorFormats.hex)"
                    class="p-1 text-gray-500 hover:text-blue-500 transition-colors"
                    :title="$t('common.copy')"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">RGB:</span>
                <div class="flex items-center space-x-2">
                  <code class="text-sm font-mono text-gray-900 dark:text-white">{{ rgbString }}</code>
                  <button
                    @click="copyToClipboard(rgbString)"
                    class="p-1 text-gray-500 hover:text-blue-500 transition-colors"
                    :title="$t('common.copy')"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">HSL:</span>
                <div class="flex items-center space-x-2">
                  <code class="text-sm font-mono text-gray-900 dark:text-white">{{ hslString }}</code>
                  <button
                    @click="copyToClipboard(hslString)"
                    class="p-1 text-gray-500 hover:text-blue-500 transition-colors"
                    :title="$t('common.copy')"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">HSV:</span>
                <div class="flex items-center space-x-2">
                  <code class="text-sm font-mono text-gray-900 dark:text-white">{{ hsvString }}</code>
                  <button
                    @click="copyToClipboard(hsvString)"
                    class="p-1 text-gray-500 hover:text-blue-500 transition-colors"
                    :title="$t('common.copy')"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 调色板生成 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              {{ $t('color.palette.title') }}
            </h2>
            
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {{ $t('color.palette.type') }}
              </label>
              <select
                v-model="paletteType"
                @change="generatePalette"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="complementary">{{ $t('color.palette.complementary') }}</option>
                <option value="analogous">{{ $t('color.palette.analogous') }}</option>
                <option value="triadic">{{ $t('color.palette.triadic') }}</option>
                <option value="monochromatic">{{ $t('color.palette.monochromatic') }}</option>
              </select>
            </div>

            <div class="grid grid-cols-5 gap-2">
              <div
                v-for="(color, index) in currentPalette"
                :key="index"
                class="aspect-square rounded-lg border-2 border-gray-200 dark:border-gray-600 cursor-pointer hover:scale-105 transition-transform relative group"
                :style="{ backgroundColor: color }"
                @click="selectPaletteColor(color)"
              >
                <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                <div class="absolute bottom-1 left-1 right-1 text-xs text-white bg-black/50 rounded px-1 py-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
                  {{ color }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：对比度分析和预设 -->
        <div class="lg:col-span-1">
          <!-- 对比度分析 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              {{ $t('color.contrast.title') }}
            </h2>
            
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {{ $t('color.contrast.background') }}
              </label>
              <div class="flex space-x-2">
                <input
                  v-model="contrastBackground"
                  type="color"
                  class="w-12 h-10 rounded border-2 border-gray-200 dark:border-gray-600 cursor-pointer"
                  @input="calculateContrast"
                />
                <input
                  v-model="contrastBackground"
                  type="text"
                  class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  @input="calculateContrast"
                  placeholder="#ffffff"
                />
              </div>
            </div>

            <!-- 对比度预览 -->
            <div class="mb-4 p-4 rounded-lg" :style="{ backgroundColor: contrastBackground, color: currentColor }">
              <p class="text-lg font-semibold">{{ $t('color.contrast.sample') }}</p>
              <p class="text-sm">{{ $t('color.contrast.description') }}</p>
            </div>

            <!-- 对比度结果 -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700 dark:text-gray-300">{{ $t('color.contrast.ratio') }}:</span>
                <span class="font-mono text-sm">{{ contrastRatio.toFixed(2) }}:1</span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700 dark:text-gray-300">WCAG AA:</span>
                <span class="text-sm" :class="contrastAA ? 'text-green-600' : 'text-red-600'">
                  {{ contrastAA ? '✓ ' + $t('color.contrast.pass') : '✗ ' + $t('color.contrast.fail') }}
                </span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700 dark:text-gray-300">WCAG AAA:</span>
                <span class="text-sm" :class="contrastAAA ? 'text-green-600' : 'text-red-600'">
                  {{ contrastAAA ? '✓ ' + $t('color.contrast.pass') : '✗ ' + $t('color.contrast.fail') }}
                </span>
              </div>
            </div>
          </div>

          <!-- 预设颜色 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              {{ $t('color.presets.title') }}
            </h2>
            
            <div class="grid grid-cols-6 gap-2">
              <div
                v-for="preset in colorPresets"
                :key="preset"
                class="aspect-square rounded-lg border-2 border-gray-200 dark:border-gray-600 cursor-pointer hover:scale-105 transition-transform"
                :style="{ backgroundColor: preset }"
                @click="selectPresetColor(preset)"
                :title="preset"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 复制成功提示 -->
    <div
      v-if="showCopySuccess"
      class="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-300"
    >
      {{ $t('common.copied') }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

// 页面元数据
useHead({
  title: 'Color Picker Tool - ToolMi',
  meta: [
    { name: 'description', content: 'Professional color picker tool with format conversion, palette generation, and contrast analysis.' }
  ]
})

// 响应式数据
const currentColor = ref('#3498db')
const showColorPicker = ref(false)
const paletteType = ref('complementary')
const contrastBackground = ref('#ffffff')
const showCopySuccess = ref(false)

// 颜色格式
const colorFormats = ref({
  hex: '#3498db',
  rgb: { r: 52, g: 152, b: 219 },
  hsl: { h: 204, s: 70, l: 53 },
  hsv: { h: 204, s: 76, v: 86 }
})

// 颜色历史
const colorHistory = ref([
  '#3498db', '#e74c3c', '#2ecc71', '#f39c12',
  '#9b59b6', '#1abc9c', '#34495e', '#95a5a6'
])

// 当前调色板
const currentPalette = ref([])

// 对比度数据
const contrastRatio = ref(4.5)
const contrastAA = ref(true)
const contrastAAA = ref(false)

// 预设颜色
const colorPresets = ref([
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
  '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA',
  '#F1948A', '#85C1E9', '#F4D03F', '#A569BD', '#5DADE2', '#58D68D',
  '#EC7063', '#AF7AC5', '#5499C7', '#52C41A', '#FA8C16', '#722ED1',
  '#13C2C2', '#52C41A', '#FAAD14', '#F759AB', '#2F54EB', '#FA541C'
])

// 计算属性
const rgbString = computed(() => {
  const { r, g, b } = colorFormats.value.rgb
  return `rgb(${r}, ${g}, ${b})`
})

const hslString = computed(() => {
  const { h, s, l } = colorFormats.value.hsl
  return `hsl(${h}, ${s}%, ${l}%)`
})

const hsvString = computed(() => {
  const { h, s, v } = colorFormats.value.hsv
  return `hsv(${h}, ${s}%, ${v}%)`
})

// 颜色转换工具函数
const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

const rgbToHex = (r, g, b) => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

const rgbToHsl = (r, g, b) => {
  r /= 255
  g /= 255
  b /= 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h, s, l = (max + min) / 2

  if (max === min) {
    h = s = 0
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  }
}

const hslToRgb = (h, s, l) => {
  h /= 360
  s /= 100
  l /= 100

  const hue2rgb = (p, q, t) => {
    if (t < 0) t += 1
    if (t > 1) t -= 1
    if (t < 1/6) return p + (q - p) * 6 * t
    if (t < 1/2) return q
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
    return p
  }

  let r, g, b

  if (s === 0) {
    r = g = b = l
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1/3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1/3)
  }

  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  }
}

const rgbToHsv = (r, g, b) => {
  r /= 255
  g /= 255
  b /= 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  const diff = max - min

  let h = 0
  const s = max === 0 ? 0 : diff / max
  const v = max

  if (diff !== 0) {
    switch (max) {
      case r: h = (g - b) / diff + (g < b ? 6 : 0); break
      case g: h = (b - r) / diff + 2; break
      case b: h = (r - g) / diff + 4; break
    }
    h /= 6
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    v: Math.round(v * 100)
  }
}

// 更新颜色函数
const updateColor = () => {
  const rgb = hexToRgb(currentColor.value)
  if (rgb) {
    colorFormats.value.hex = currentColor.value
    colorFormats.value.rgb = rgb
    colorFormats.value.hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
    colorFormats.value.hsv = rgbToHsv(rgb.r, rgb.g, rgb.b)

    addToHistory(currentColor.value)
    generatePalette()
    calculateContrast()
  }
}

const updateFromHex = () => {
  let hex = colorFormats.value.hex
  if (!hex.startsWith('#')) {
    hex = '#' + hex
  }

  if (/^#[0-9A-F]{6}$/i.test(hex)) {
    currentColor.value = hex
    updateColor()
  }
}

const updateFromRgb = () => {
  const { r, g, b } = colorFormats.value.rgb
  if (r >= 0 && r <= 255 && g >= 0 && g <= 255 && b >= 0 && b <= 255) {
    const hex = rgbToHex(r, g, b)
    currentColor.value = hex
    colorFormats.value.hex = hex
    colorFormats.value.hsl = rgbToHsl(r, g, b)
    colorFormats.value.hsv = rgbToHsv(r, g, b)

    addToHistory(hex)
    generatePalette()
    calculateContrast()
  }
}

const updateFromHsl = () => {
  const { h, s, l } = colorFormats.value.hsl
  if (h >= 0 && h <= 360 && s >= 0 && s <= 100 && l >= 0 && l <= 100) {
    const rgb = hslToRgb(h, s, l)
    const hex = rgbToHex(rgb.r, rgb.g, rgb.b)

    currentColor.value = hex
    colorFormats.value.hex = hex
    colorFormats.value.rgb = rgb
    colorFormats.value.hsv = rgbToHsv(rgb.r, rgb.g, rgb.b)

    addToHistory(hex)
    generatePalette()
    calculateContrast()
  }
}

// 颜色历史管理
const addToHistory = (color) => {
  if (!colorHistory.value.includes(color)) {
    colorHistory.value.unshift(color)
    if (colorHistory.value.length > 16) {
      colorHistory.value.pop()
    }
    // 保存到本地存储
    localStorage.setItem('colorHistory', JSON.stringify(colorHistory.value))
  }
}

const selectHistoryColor = (color) => {
  currentColor.value = color
  updateColor()
}

// 调色板生成
const generatePalette = () => {
  const baseColor = currentColor.value
  const rgb = hexToRgb(baseColor)
  if (!rgb) return

  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)

  switch (paletteType.value) {
    case 'complementary':
      currentPalette.value = generateComplementaryPalette(hsl)
      break
    case 'analogous':
      currentPalette.value = generateAnalogousPalette(hsl)
      break
    case 'triadic':
      currentPalette.value = generateTriadicPalette(hsl)
      break
    case 'monochromatic':
      currentPalette.value = generateMonochromaticPalette(hsl)
      break
  }
}

const generateComplementaryPalette = (hsl) => {
  const complementHue = (hsl.h + 180) % 360
  return [
    currentColor.value,
    hslToHexString({ ...hsl, l: Math.max(10, hsl.l - 20) }),
    hslToHexString({ ...hsl, l: Math.min(90, hsl.l + 20) }),
    hslToHexString({ h: complementHue, s: hsl.s, l: hsl.l }),
    hslToHexString({ h: complementHue, s: hsl.s, l: Math.max(10, hsl.l - 20) })
  ]
}

const generateAnalogousPalette = (hsl) => {
  return [
    hslToHexString({ h: (hsl.h - 60 + 360) % 360, s: hsl.s, l: hsl.l }),
    hslToHexString({ h: (hsl.h - 30 + 360) % 360, s: hsl.s, l: hsl.l }),
    currentColor.value,
    hslToHexString({ h: (hsl.h + 30) % 360, s: hsl.s, l: hsl.l }),
    hslToHexString({ h: (hsl.h + 60) % 360, s: hsl.s, l: hsl.l })
  ]
}

const generateTriadicPalette = (hsl) => {
  return [
    currentColor.value,
    hslToHexString({ h: (hsl.h + 120) % 360, s: hsl.s, l: hsl.l }),
    hslToHexString({ h: (hsl.h + 240) % 360, s: hsl.s, l: hsl.l }),
    hslToHexString({ ...hsl, l: Math.max(10, hsl.l - 20) }),
    hslToHexString({ ...hsl, l: Math.min(90, hsl.l + 20) })
  ]
}

const generateMonochromaticPalette = (hsl) => {
  return [
    hslToHexString({ ...hsl, l: 20 }),
    hslToHexString({ ...hsl, l: 40 }),
    currentColor.value,
    hslToHexString({ ...hsl, l: 70 }),
    hslToHexString({ ...hsl, l: 90 })
  ]
}

const hslToHexString = (hsl) => {
  const rgb = hslToRgb(hsl.h, hsl.s, hsl.l)
  return rgbToHex(rgb.r, rgb.g, rgb.b)
}

const selectPaletteColor = (color) => {
  currentColor.value = color
  updateColor()
}

// 预设颜色选择
const selectPresetColor = (color) => {
  currentColor.value = color
  updateColor()
}

// 对比度计算
const calculateContrast = () => {
  const color1Luminance = getLuminance(currentColor.value)
  const color2Luminance = getLuminance(contrastBackground.value)

  const lighter = Math.max(color1Luminance, color2Luminance)
  const darker = Math.min(color1Luminance, color2Luminance)

  contrastRatio.value = (lighter + 0.05) / (darker + 0.05)
  contrastAA.value = contrastRatio.value >= 4.5
  contrastAAA.value = contrastRatio.value >= 7
}

const getLuminance = (hex) => {
  const rgb = hexToRgb(hex)
  if (!rgb) return 0

  const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
    c = c / 255
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
  })

  return 0.2126 * r + 0.7152 * g + 0.0722 * b
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    showCopySuccess.value = true
    setTimeout(() => {
      showCopySuccess.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy: ', err)
  }
}

// 监听颜色变化
watch(currentColor, updateColor)
watch(contrastBackground, calculateContrast)

// 组件挂载时初始化
onMounted(() => {
  // 从本地存储加载颜色历史
  const savedHistory = localStorage.getItem('colorHistory')
  if (savedHistory) {
    try {
      colorHistory.value = JSON.parse(savedHistory)
    } catch (e) {
      console.error('Failed to load color history:', e)
    }
  }

  // 初始化
  updateColor()
  generatePalette()
  calculateContrast()
})
</script>
