<template>
  <div class="category-page">
    <div class="category-header">
      <h1>{{ $t('common.category') }}</h1>
      <p v-if="categoryName">{{ categoryTools.length }} {{ $t('common.tools') }}</p>
    </div>

    <!-- 分类列表 -->
    <div v-if="!categoryName" class="all-categories">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
        <NuxtLink
          v-for="(category, key) in allCategories"
          class="category-card"
          :to="`/category?name=${category.name}`"
        >
          <h3>{{ $t(category.name) }}</h3>
          <p>{{ category.num }} {{ $t('common.tools')}}</p>
        </NuxtLink>
      </div>
    </div>

    <!-- 特定分类的工具列表 -->
    <div v-else class="category-tools">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
        <NuxtLink
          v-for="tool in categoryTools"
          class="tool-card"
          :to="`${tool.route}`"
        >
          <h3>{{ $t(`${toolLocalesKey(tool.name)}.title`) }}</h3>
          <p>{{ $t(`${toolLocalesKey(tool.name)}.description`) }}</p>
          <button class="tool-button">
            {{ $t('common.useTool') }} →
          </button>
        </NuxtLink>
      </div>

      <!-- 返回按钮 -->
      <div class="back-button">
        <NuxtLink :to="`/category`" class="btn btn-secondary" >
          ← {{ $t('common.backToCategoryList') }}
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const { toolLocalesKey, getAllCategoryList, getToolsByCategory } = useTools()

const categoryName = ref<string>('')

const allCategories = computed(() => getAllCategoryList())
const categoryTools = computed(() => {
  if (!categoryName.value) return []
  return getToolsByCategory(categoryName.value)
})

// SEO 配置
useSeoMeta({
  title: () => categoryName.value 
    ? `${categoryName.value} - 工具迷`
    : `工具分类 - 工具迷`,
  description: () => categoryName.value 
    ? `浏览 ${categoryName.value} 分类下的所有工具`
    : '浏览所有工具分类',
  keywords: () => categoryName.value 
    ? `${categoryName.value},工具,在线工具`
    : '工具分类,在线工具,工具迷'
})

// 监听路由查询参数
watch(() => route.query.name, (name) => {
  categoryName.value = name as string || ''
}, { immediate: true })
</script>

<style scoped>
.category-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.category-header {
  text-align: center;
  margin-bottom: 32px;
}

.category-header h1 {
  font-size: 32px;
  margin-bottom: 8px;
  color: #374151;
}

.category-header p {
  color: #6b7280;
  font-size: 16px;
}

.category-card {
  padding: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.category-card:hover {
  border-color: #00b96b;
  box-shadow: 0 4px 12px rgba(0, 185, 107, 0.15);
  transform: translateY(-2px);
}

.category-card h3 {
  margin-bottom: 8px;
  color: #374151;
  font-size: 18px;
}

.category-card p {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.tool-card {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.tool-card:hover {
  border-color: #00b96b;
  box-shadow: 0 4px 12px rgba(0, 185, 107, 0.15);
  transform: translateY(-2px);
}

.tool-card h3 {
  margin-bottom: 8px;
  color: #374151;
  font-size: 18px;
  font-weight: 600;
}

.tool-card p {
  color: #6b7280;
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 1.5;
}

.tool-button {
  background: #00b96b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.tool-button:hover {
  background: #009456;
}

.back-button {
  margin-top: 32px;
  text-align: center;
}
</style>
