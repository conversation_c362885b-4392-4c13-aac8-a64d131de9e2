<template>
  <div class="index">
    <h1 class="site-name">{{ $t("common.appName") }}</h1>
    <div class="slogan">
      <i>{{ $t('common.slogan') }}</i>
    </div>
    
    <!-- 搜索框 -->
    <div class="search-input">
      <input
        v-model="searchWords"
        type="text"
        :placeholder="$t('common.searchPlaceholder', getAll().length)"
        @input="onSearch"
        style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 6px; font-size: 16px;"
      />
      
      <!-- 搜索结果 -->
      <div v-if="searchResults.length > 0" class="search-results">
        <div
          v-for="tool in searchResults"
          :key="tool.route"
          class="search-item"
          @click="goToTool(tool.route)"
        >
          {{ tool.title }}
        </div>
      </div>
    </div>

    <!-- 分类列表 -->
    <div class="category-list">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
        <NuxtLink
          v-for="(category, key) in categoryList"
          :key="key"
          :to="`/category?name=${category.name}`"
          class="category-item"
        >
          <h3 class="name">{{ $t(category.name) }}</h3>
          <p class="desc">{{ category.num }} {{$t('common.tools')}}</p>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { getAll, getAllCategoryList } = useTools()

// SEO 配置
useSeoMeta({
  title: '工具迷 - 下一代在线效率工具',
  description: 'Next generation online tools - 提供编码解码、格式化、生成器等多种在线工具',
  keywords: '在线工具,编码解码,格式化,生成器,Base64,JSON,工具迷'
})

const searchWords = ref('')
const searchResults = ref([])

// 获取所有工具和分类
const allTools = computed(() => getAll())
const categoryList = computed(() => getAllCategoryList())

// 搜索功能
const onSearch = () => {
  if (!searchWords.value) {
    searchResults.value = []
    return
  }

  searchResults.value = allTools.value.filter(tool =>
    tool.title.toLowerCase().includes(searchWords.value.toLowerCase())
  ).slice(0, 5) // 限制显示5个结果
}

// 跳转到工具页面
const goToTool = (route: string) => {
  navigateTo(`/zh${route}`)
}
</script>

<style scoped>
.site-name {
  text-align: center;
  font-size: 34px;
  margin-top: 20px;
}

.slogan {
  text-align: center;
  color: #7e7e7e;
}

.search-input {
  margin: 30px 0;
  position: relative;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.search-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
}

.search-item:hover {
  background: #f9fafb;
}

.search-item:last-child {
  border-bottom: none;
}

.category-list .category-item {
  padding: 40px 20px;
  border-radius: 4px;
  text-align: center;
  box-shadow: 0 0 0 1px rgb(0 0 0 / 7%), 0 2px 4px rgb(0 0 0 / 5%),
    0 12px 24px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  color: #374151;
  font-size: 20px;
  display: block;
  text-decoration: none;
}

.category-list .category-item:hover {
  background: radial-gradient(
    circle at 811.59px 102.194px,
    rgb(192 255 1 / 33%),
    rgba(0, 0, 0, 0.0588235294)
  );
  color: #00b96b;
  transform: translateY(-2px);
}

.category-list .category-item .name {
  margin: 0 0 8px 0;
  font-weight: 600;
}

.category-list .category-item .desc {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
}

.category-list .category-item:hover .desc {
  color: #00b96b;
}
</style>
