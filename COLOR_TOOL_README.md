# 颜色选择器工具 (Color Picker Tool)

## 🎨 功能概述

这是一个专业的颜色选择和管理工具，为设计师和开发者提供全面的颜色处理功能。

## ✨ 主要特性

### 1. 颜色选择器
- **可视化选择器**: HTML5 原生颜色选择器
- **多格式输入**: 支持 HEX、RGB、HSL 格式输入
- **实时预览**: 颜色变化实时显示
- **精确调节**: 数值输入精确控制

### 2. 格式转换
- **HEX**: #3498db
- **RGB**: rgb(52, 152, 219)
- **HSL**: hsl(204, 70%, 53%)
- **HSV**: hsv(204, 76%, 86%)
- **一键复制**: 点击即可复制任意格式

### 3. 调色板生成
- **互补色 (Complementary)**: 色轮上相对的颜色
- **类似色 (Analogous)**: 色轮上相邻的颜色
- **三角色 (Triadic)**: 色轮上等距的三个颜色
- **单色 (Monochromatic)**: 同一颜色的不同明度变化

### 4. 对比度分析
- **WCAG 标准**: 符合 Web 内容无障碍指南
- **AA 级别**: 4.5:1 对比度比例
- **AAA 级别**: 7:1 对比度比例
- **实时检测**: 自动计算并显示对比度结果

### 5. 颜色历史
- **自动保存**: 最近使用的 16 种颜色
- **本地存储**: 浏览器本地保存，下次访问自动加载
- **快速选择**: 点击历史颜色快速应用

### 6. 预设颜色
- **30+ 预设**: 精心挑选的常用颜色
- **分类齐全**: 涵盖各种设计场景
- **快速应用**: 一键选择预设颜色

## 🛠️ 技术实现

### 前端技术栈
- **Vue 3**: 响应式框架
- **Nuxt 3**: 全栈框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架

### 核心算法
```javascript
// 颜色格式转换
const hexToRgb = (hex) => { /* ... */ }
const rgbToHsl = (r, g, b) => { /* ... */ }
const hslToRgb = (h, s, l) => { /* ... */ }

// 对比度计算
const getLuminance = (hex) => { /* ... */ }
const calculateContrast = (color1, color2) => { /* ... */ }

// 调色板生成
const generateComplementaryPalette = (hsl) => { /* ... */ }
const generateAnalogousPalette = (hsl) => { /* ... */ }
```

### 响应式设计
- **移动端优化**: 适配各种屏幕尺寸
- **触摸友好**: 支持触摸操作
- **深色模式**: 自动适配系统主题

## 🎯 使用场景

### 1. 网页设计
- 选择网站主题色
- 生成配色方案
- 检查文本可读性

### 2. UI/UX 设计
- 界面配色设计
- 品牌色彩管理
- 无障碍设计验证

### 3. 前端开发
- CSS 颜色值获取
- 设计系统构建
- 主题色彩定义

### 4. 品牌设计
- 品牌色彩选择
- 视觉识别系统
- 营销物料配色

## 📱 界面布局

### 桌面端 (3列布局)
```
┌─────────────┬─────────────┬─────────────┐
│  颜色选择器  │  格式转换    │  对比度分析  │
│  颜色历史    │  调色板生成  │  预设颜色    │
└─────────────┴─────────────┴─────────────┘
```

### 移动端 (单列布局)
```
┌─────────────┐
│  颜色选择器  │
├─────────────┤
│  格式转换    │
├─────────────┤
│  调色板生成  │
├─────────────┤
│  对比度分析  │
├─────────────┤
│  预设颜色    │
└─────────────┘
```

## 🌐 国际化支持

### 支持语言
- 🇨🇳 中文 (简体)
- 🇺🇸 英文
- 🇯🇵 日文
- 🇪🇸 西班牙语
- 🇫🇷 法语
- 🇧🇷 葡萄牙语
- 🇷🇺 俄语

### 翻译内容
- 界面文本
- 功能说明
- 错误提示
- SEO 元数据

## 🔧 开发指南

### 本地运行
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问颜色工具
http://localhost:3000/color
```

### 文件结构
```
pages/
├── color.vue              # 颜色工具主页面
├── test-color.vue         # 测试页面
locales/
├── zh.json               # 中文翻译
├── en.json               # 英文翻译
composables/
├── useTools.ts           # 工具配置
public/mds/
├── zh/color.md           # 中文文档
├── en/color.md           # 英文文档
```

### 添加新功能
1. 在 `pages/color.vue` 中添加 UI 组件
2. 在 `<script setup>` 中添加逻辑函数
3. 在 `locales/*.json` 中添加翻译文本
4. 在 `public/mds/*/color.md` 中更新文档

## 🚀 部署说明

### 生产构建
```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 性能优化
- **代码分割**: 按需加载组件
- **图片优化**: WebP 格式支持
- **缓存策略**: 静态资源缓存
- **CDN 加速**: 全球内容分发

## 📊 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 88+ | ✅ 完全支持 |
| Firefox | 85+ | ✅ 完全支持 |
| Safari | 14+ | ✅ 完全支持 |
| Edge | 88+ | ✅ 完全支持 |
| IE | - | ❌ 不支持 |

## 🔮 未来规划

### 短期目标
- [ ] 添加渐变色生成器
- [ ] 支持颜色盲模拟
- [ ] 添加更多预设调色板
- [ ] 支持颜色导出为图片

### 长期目标
- [ ] AI 智能配色建议
- [ ] 团队协作功能
- [ ] 设计系统集成
- [ ] 移动端 App

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📞 联系我们

- 网站: [https://toolmi.com](https://toolmi.com)
- 邮箱: <EMAIL>
- GitHub: [https://github.com/toolmi](https://github.com/toolmi)

---

**ToolMi** - 让工具更简单，让开发更高效！ 🚀
