<template>
  <div>
    <!-- 头部导航 -->
    <header class="header">
      <div class="nav-link">
        <NuxtLink :to="'/'" class="logo">
          {{ $t('common.appName') }}
        </NuxtLink>
        <NuxtLink :to="'/about'">
          {{ $t('common.about') }}
        </NuxtLink>
        <a href="https://support.qq.com/product/290995" target="_blank">
          {{ $t('common.feedback')}}
        </a>

        <!-- 语言切换器 -->
        <div class="language-switcher">
          <select v-model="selectedLocale">
            <option v-for="locale in availableLocales" :key="locale.code" :value="locale.code">{{ getFlag(locale.code) + ' ' + locale.name }}</option>
          </select>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="content-wrapper">
      <div class="main-content">
        <!-- 推广横幅 -->
        <!-- 页面内容 -->
        <NuxtPage />
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      {{ new Date().getFullYear() }} {{ $t('footer.copyright') }}
    </footer>
  </div>
</template>

<script setup>
const { $i18n } = useNuxtApp()
const switchLocalePath = useSwitchLocalePath()

// Reactive state
const selectedLocale = ref($i18n.locale.value)

// Available locales from i18n config
const availableLocales = computed(() => $i18n.locales.value)


// 可以直接侦听一个 ref
watch(selectedLocale, async (newLocale, _) => {
  await navigateTo(switchLocalePath(newLocale))
})

// Get flag emoji for locale
const getFlag = (localeCode) => {
  const flags = {
    'zh': '🇨🇳',
    'en': '🇺🇸',
    'ja': '🇯🇵',
    'ko': '🇰🇷',
    'fr': '🇫🇷',
    'de': '🇩🇪',
    'es': '🇪🇸',
    'it': '🇮🇹',
    'pt': '🇵🇹',
    'ru': '🇷🇺'
  }
  return flags[localeCode] || '🌐'
}

// Close dropdown when clicking escape key
onMounted(() => {
  const handleEscape = (event) => {
    if (event.key === 'Escape') {
      closeDropdown()
    }
  }
  
  document.addEventListener('keydown', handleEscape)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleEscape)
  })
})
</script>

<style scoped>
.main-content {
  padding: 24px 0;
  min-height: calc(100vh - 200px);
}

.promo-banner {
  display: block;
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.promo-banner:hover {
  transform: translateY(-2px);
}

.promo-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.footer {
  text-align: center;
  padding: 24px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 14px;
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px 0;
  }

  .footer {
    padding: 16px;
    font-size: 12px;
  }
}
</style>
