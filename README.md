# Nuxt i18n 演示项目

这是一个展示 Nuxt 3 国际化功能的演示项目，包含完整的 i18n 标准目录结构。

## 项目结构

```
gongjumi-i18n/
├── components/
│   └── LanguageSwitcher.vue    # 语言切换组件
├── lang/                       # 语言文件目录
│   ├── en.json                 # 英文翻译
│   └── zh.json                 # 中文翻译
├── locales/                    # 备用语言文件目录
│   ├── en.json
│   └── zh.json
├── pages/                      # 页面目录
│   ├── index.vue               # 首页
│   ├── about.vue               # 关于页面
│   └── contact.vue             # 联系页面
├── app.vue                     # 根组件
├── nuxt.config.ts              # Nuxt 配置文件
└── package.json                # 项目依赖
```

## 功能特性

### 🌍 国际化功能
- **多语言支持**: 支持中文和英文
- **路由国际化**: 使用 `prefix_except_default` 策略
- **语言检测**: 自动检测浏览器语言
- **Cookie 持久化**: 记住用户的语言选择

### 🎨 用户界面
- **响应式设计**: 使用 Tailwind CSS
- **语言切换器**: 下拉菜单式语言选择
- **多页面演示**: 首页、关于、联系页面
- **美观的 UI**: 现代化的界面设计

### 📱 页面功能
- **首页**: 展示项目介绍和功能特性
- **关于页面**: 团队、使命、愿景展示
- **联系页面**: 联系表单和信息展示

## 技术栈

- **Nuxt 3**: Vue.js 全栈框架
- **@nuxtjs/i18n**: 国际化模块
- **Tailwind CSS**: 原子化 CSS 框架
- **Vue 3**: 渐进式 JavaScript 框架
- **TypeScript**: 类型安全的 JavaScript

## 安装和运行

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000 查看演示。

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## i18n 配置说明

### 基本配置
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  modules: ['@nuxtjs/i18n', '@nuxtjs/tailwindcss'],
  i18n: {
    locales: [
      { code: 'en', name: 'English', file: 'en.json' },
      { code: 'zh', name: '中文', file: 'zh.json' }
    ],
    defaultLocale: 'zh',
    langDir: 'lang',
    strategy: 'prefix_except_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  }
})
```

### 语言文件结构
语言文件采用嵌套的 JSON 结构，便于组织和维护：

```json
{
  "welcome": {
    "title": "欢迎使用 Nuxt i18n 演示",
    "subtitle": "这是一个国际化演示项目"
  },
  "navigation": {
    "home": "首页",
    "about": "关于"
  },
  "common": {
    "language": "语言",
    "save": "保存"
  }
}
```

## 使用方法

### 在组件中使用翻译
```vue
<template>
  <div>
    <h1>{{ $t('welcome.title') }}</h1>
    <p>{{ $t('welcome.subtitle') }}</p>
  </div>
</template>
```

### 语言切换
```vue
<script setup>
const { $i18n } = useNuxtApp()
const switchLocalePath = useSwitchLocalePath()

const switchLanguage = (locale) => {
  navigateTo(switchLocalePath(locale))
}
</script>
```

### 路由国际化
- 默认语言 (中文): `/`
- 英文: `/en`
- 其他页面: `/en/about`, `/en/contact`

## 开发说明

### 添加新语言
1. 在 `lang/` 目录下创建新的语言文件
2. 在 `nuxt.config.ts` 中添加语言配置
3. 更新 `LanguageSwitcher.vue` 组件

### 添加新翻译
1. 在对应的语言文件中添加翻译键值对
2. 在组件中使用 `$t('key')` 调用

### 自定义样式
项目使用 Tailwind CSS，可以直接在组件中使用原子化类名。

## 注意事项

- 确保所有语言文件的键结构保持一致
- 使用语义化的翻译键名
- 为 SEO 优化设置正确的页面标题和描述
- 测试所有语言的显示效果

## 许可证

MIT License
