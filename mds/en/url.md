# URL Encode/Decode Tool

URL encoding (also known as percent encoding) is a mechanism for encoding information in URLs by replacing special characters with percent signs followed by hexadecimal digits. This tool provides bidirectional URL encoding and decoding functionality for web development and data processing.

## ✨ Key Features

- 🔄 **Bidirectional Conversion**: Support both URL encoding and decoding operations
- 🌐 **International Support**: Handle Unicode characters and international text
- 📝 **Query Parameter Processing**: Perfect for handling form data and query strings
- ⚡ **Real-time Processing**: Instant encoding/decoding as you type
- 📋 **One-Click Copy**: Copy results directly to clipboard

## 📖 Usage Examples

### Basic URL Encoding Example
**Input:**
\`\`\`
https://example.com/search?q=hello world&category=tech news
\`\`\`

**Encoded Output:**
\`\`\`
https%3A//example.com/search%3Fq%3Dhello%20world%26category%3Dtech%20news
\`\`\`

### Query Parameters Example
**Input:**
\`\`\`
name=<PERSON>&email=<EMAIL>&message=Hello, how are you?
\`\`\`

**Encoded Output:**
\`\`\`
name%3DJohn%20Doe%26email%3Djohn%40example.com%26message%3DHello%2C%20how%20are%20you%3F
\`\`\`

### International Characters Example
**Input:**
\`\`\`
search=北京&language=中文&type=新闻
\`\`\`

**Encoded Output:**
\`\`\`
search%3D%E5%8C%97%E4%BA%AC%26language%3D%E4%B8%AD%E6%96%87%26type%3D%E6%96%B0%E9%97%BB
\`\`\`

## 🎯 Use Cases

### 1. Web Development
Handle form data and query parameters in web applications:

\`\`\`javascript
// Frontend form submission
const formData = {
  username: 'john doe',
  email: '<EMAIL>',
  message: 'Hello & welcome!'
};

// Encode for URL transmission
const queryString = Object.entries(formData)
  .map(([key, value]) => \`\${encodeURIComponent(key)}=\${encodeURIComponent(value)}\`)
  .join('&');

console.log(queryString);
// Output: username=john%20doe&email=john%40example.com&message=Hello%20%26%20welcome!

// Send GET request with encoded parameters
fetch(\`/api/users?\${queryString}\`)
  .then(response => response.json())
  .then(data => console.log(data));
\`\`\`

### 2. API Testing
Encode parameters for API requests and testing:

\`\`\`bash
# cURL example with encoded parameters
curl -X GET "https://api.example.com/search?q=hello%20world&type=news&lang=en"

# POST request with encoded form data
curl -X POST "https://api.example.com/submit" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -d "name=John%20Doe&email=john%40example.com&message=Hello%2C%20world%21"
\`\`\`

### 3. Data Transmission
Safely transmit data containing special characters:

\`\`\`python
import urllib.parse

# Python example for URL encoding
data = {
    'search_query': 'machine learning & AI',
    'category': 'tech/programming',
    'date_range': '2024-01-01 to 2024-12-31'
}

# Encode data for URL transmission
encoded_data = urllib.parse.urlencode(data)
print(encoded_data)
# Output: search_query=machine+learning+%26+AI&category=tech%2Fprogramming&date_range=2024-01-01+to+2024-12-31

# Build complete URL
base_url = 'https://api.example.com/search'
full_url = f"{base_url}?{encoded_data}"
print(full_url)
\`\`\`

### 4. Database Query Processing
Handle user input for database queries:

\`\`\`php
<?php
// PHP example for safe query parameter handling
$search_term = $_GET['q'] ?? '';
$category = $_GET['category'] ?? '';

// Decode URL-encoded parameters
$decoded_search = urldecode($search_term);
$decoded_category = urldecode($category);

// Safely use in database query (with proper escaping)
$sql = "SELECT * FROM articles WHERE title LIKE ? AND category = ?";
$stmt = $pdo->prepare($sql);
$stmt->execute(["%{$decoded_search}%", $decoded_category]);
$results = $stmt->fetchAll();
?>
\`\`\`

## 🔧 Technical Details

### Encoding Rules
URL encoding follows RFC 3986 standards:

**Reserved Characters:**
- \`: / ? # [ ] @\` - General delimiters
- \`! $ & ' ( ) * + , ; =\` - Sub-delimiters

**Encoding Format:**
- Format: \`%XX\` where XX is hexadecimal representation
- Example: Space character → \`%20\`
- Example: @ symbol → \`%40\`

**Safe Characters:**
- Alphanumeric: \`A-Z a-z 0-9\`
- Unreserved: \`- . _ ~\`

### Common Encodings
| Character | Encoded | Description |
|-----------|---------|-------------|
| Space | \`%20\` or \`+\` | Space character |
| \`!\` | \`%21\` | Exclamation mark |
| \`"\` | \`%22\` | Quotation mark |
| \`#\` | \`%23\` | Hash/pound sign |
| \`$\` | \`%24\` | Dollar sign |
| \`%\` | \`%25\` | Percent sign |
| \`&\` | \`%26\` | Ampersand |
| \`+\` | \`%2B\` | Plus sign |
| \`=\` | \`%3D\` | Equals sign |
| \`?\` | \`%3F\` | Question mark |
| \`@\` | \`%40\` | At symbol |

## 💡 Usage Tips

- **Form Data**: Use URL encoding for form submissions and query parameters
- **API Requests**: Always encode user input before including in URLs
- **International Text**: Tool properly handles Unicode characters
- **Debugging**: Use decoding to understand encoded URLs and parameters
- **Security**: Proper encoding helps prevent URL injection attacks

## ⚠️ Important Notes

- **Double Encoding**: Avoid encoding already encoded strings
- **Context Awareness**: Different parts of URLs have different encoding rules
- **Plus vs %20**: In query strings, \`+\` and \`%20\` both represent spaces
- **Fragment Identifiers**: Hash fragments (#) have special encoding considerations
- **Length Limits**: Very long URLs may hit browser or server limits

## 🚀 Getting Started

1. Enter text or URL in the input field above
2. Click "URL Encode" to encode special characters
3. Click "URL Decode" to decode percent-encoded text
4. Use "Copy" to copy the result to clipboard
5. Click "Load Example" to see sample data

> **Tip**: The tool automatically handles UTF-8 encoding for international characters and provides proper percent encoding for all special characters.