# Base64 Encode/Decode Tool

Base64 is a binary-to-text encoding scheme that represents binary data in an ASCII string format. It's commonly used for encoding data that needs to be stored and transferred over media designed to deal with text, especially in web protocols and email systems.

## ✨ Key Features

- 🚀 **Bidirectional Conversion**: Support both encoding and decoding operations
- 📱 **Multiple Format Support**: Handle text, URL, file data and various input formats
- 🔒 **Secure & Reliable**: Uses standard Base64 algorithm ensuring data integrity
- 💾 **File Download**: Support saving results as files
- 🌐 **Unicode Support**: Perfect support for Chinese characters and international text

## 📖 Usage Examples

### Text Encoding Example

**Original Text:**
\`\`\`
Hello World! 你好世界！
\`\`\`

**Base64 Encoded:**
\`\`\`
SGVsbG8gV29ybGQhIOS9oOWlveS4lueVjO+8gQ==
\`\`\`

### URL Encoding Example

**Original URL:**
\`\`\`
https://www.toolmi.com/en/encrypt/base64?text=test
\`\`\`

**Base64 Encoded:**
\`\`\`
aHR0cHM6Ly93d3cudG9vbG1pLmNvbS9lbi9lbmNyeXB0L2Jhc2U2ND90ZXh0PXRlc3Q=
\`\`\`

### JSON Data Encoding

**Original JSON:**
\`\`\`json
{
  "name": "ToolMi",
  "type": "Online Tools",
  "features": ["encoding", "decoding", "formatting"]
}
\`\`\`

**Base64 Encoded:**
\`\`\`
ewogICJuYW1lIjogIlRvb2xNaSIsCiAgInR5cGUiOiAiT25saW5lIFRvb2xzIiwKICAiZmVhdHVyZXMiOiBbImVuY29kaW5nIiwgImRlY29kaW5nIiwgImZvcm1hdHRpbmciXQp9
\`\`\`

## 🎯 Use Cases

### 1. Data Transmission

Transmit binary data in HTTP protocols:

\`\`\`javascript
// Convert image to Base64 for transmission
const imageBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
\`\`\`

### 2. Email Attachments

Email systems use Base64 encoding for attachments:

\`\`\`
Content-Type: image/jpeg
Content-Transfer-Encoding: base64

/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQY...
\`\`\`

### 3. Data Storage

Store binary data in JSON or XML:

\`\`\`json
{
  "user": {
    "name": "John Doe",
    "avatar": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD..."
  }
}
\`\`\`

### 4. API Interfaces

Transfer file data in RESTful APIs:

\`\`\`javascript
// Upload file to server
const uploadData = {
  filename: "document.pdf",
  content: "JVBERi0xLjQKJcOkw7zDtsO8CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo..."
}
\`\`\`

## 🔧 Technical Details

### Encoding Process

1. **Grouping**: Divide input data into 3-byte (24-bit) groups
2. **Conversion**: Split each 24-bit group into four 6-bit chunks
3. **Mapping**: Map each 6-bit value to Base64 character table
4. **Padding**: Add \`=\` padding if the last group is less than 3 bytes

### Decoding Process

1. **Remove Padding**: Strip trailing \`=\` characters
2. **Reverse Mapping**: Convert Base64 characters back to 6-bit values
3. **Reassemble**: Combine four 6-bit values into three 8-bit bytes
4. **Output**: Get original binary data

### Character Set

Base64 uses 64 characters:
- **A-Z**: 26 uppercase letters
- **a-z**: 26 lowercase letters
- **0-9**: 10 digits
- **+** and **/**: 2 special characters
- **=**: Padding character

## 💡 Usage Tips

### Encoding Tips
- **Large Files**: For large files, consider chunked processing to avoid memory overflow
- **URL Safety**: For URLs, consider using URL-safe Base64 variant
- **Performance**: For frequent encoding/decoding, consider caching results

### Decoding Notes
- **Format Validation**: Ensure input is valid Base64 format
- **Character Check**: Remove non-Base64 characters (spaces, newlines, etc.)
- **Padding Handling**: Properly handle padding characters

## ⚠️ Important Notes

1. **Not Encryption**: Base64 is encoding, not encryption - it provides no security
2. **Size Increase**: Encoded data is approximately 33% larger than original
3. **Character Limitation**: Can only contain Base64 character set
4. **Line Breaks**: Some systems insert line breaks in long Base64 strings

## 🚀 Getting Started

1. **Encoding**: Enter text in the input box, click "Base64 Encode"
2. **Decoding**: Enter Base64 string in the input box, click "Base64 Decode"
3. **Copy**: Click "Copy" button to copy result to clipboard
4. **Download**: Click "Download" button to save result as file
5. **Example**: Click "Load Example" to see demo data

> **Tip**: The tool supports Chinese characters and automatically handles UTF-8 encoding conversion.