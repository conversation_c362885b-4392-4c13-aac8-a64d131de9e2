/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const __wbg_parseresult_free: (a: number, b: number) => void;
export const __wbg_get_parseresult_programJson: (a: number, b: number) => void;
export const __wbg_get_parseresult_comments: (a: number, b: number) => void;
export const __wbg_get_parseresult_errors: (a: number, b: number) => void;
export const parseSync: (a: number, b: number, c: number, d: number) => void;
export const __wbindgen_export_0: (a: number) => void;
export const __wbindgen_export_1: (a: number, b: number) => number;
export const __wbindgen_export_2: (a: number, b: number, c: number, d: number) => number;
export const __wbindgen_add_to_stack_pointer: (a: number) => number;
export const __wbindgen_export_3: (a: number, b: number, c: number) => void;
