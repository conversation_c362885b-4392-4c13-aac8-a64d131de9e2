{"name": "@oxc-parser/wasm", "version": "0.60.0", "description": "Wasm target for the oxc parser.", "keywords": ["JavaScript", "TypeScript", "parser"], "author": "Boshen and oxc contributors", "license": "MIT", "homepage": "https://oxc.rs", "repository": {"type": "git", "url": "https://github.com/oxc-project/oxc", "directory": "wasm/parser"}, "funding": {"url": "https://github.com/sponsors/Boshen"}, "main": "./node/oxc_parser_wasm.js", "browser": "./web/oxc_parser_wasm.js", "types": "./node/oxc_parser_wasm.d.ts", "files": ["node", "web"], "dependencies": {"@oxc-project/types": "^0.60.0"}, "scripts": {"build": "pnpm run build-node && pnpm run build-web && pnpm run update-bindings && pnpm run copy-files && pnpm run clean-files", "build-node": "pnpm run build-base --target nodejs --out-dir ../../npm/parser-wasm/node .", "build-web": "pnpm run build-base --target web --out-dir ../../npm/parser-wasm/web .", "build-base": "wasm-pack build --release --no-pack", "update-bindings": "node ./update-bindings.mjs", "copy-files": "cp ./package.json ../../npm/parser-wasm/package.json && cp ./README.md ../../npm/parser-wasm/README.md", "clean-files": "rm ../../npm/parser-wasm/*/.gitignore", "test": "vitest ./test", "check": "tsc --lib es2020,dom ./node/oxc_parser_wasm.d.ts"}}