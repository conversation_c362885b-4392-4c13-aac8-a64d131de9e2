"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0; // https://nuxt.com/docs/api/configuration/nuxt-config
var _default = exports.default = defineNuxtConfig({
  devtools: { enabled: true },
  modules: [
  '@nuxtjs/i18n',
  '@nuxtjs/tailwindcss'],

  i18n: {
    locales: ['en', 'zh'],
    defaultLocale: 'zh',
    strategy: 'prefix_except_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  }
}); /* v9-ff8cd21aa620fd28 */
