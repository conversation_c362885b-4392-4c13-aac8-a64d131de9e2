/*!
  * vue-i18n v10.0.7
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{createVNode,Text,computed,watch,getCurrentInstance,ref,shallowRef,Fragment,defineComponent,h,effectScope,inject,onMounted,onUnmounted,isRef}from"vue";const inBrowser="undefined"!=typeof window,makeSymbol=(e,t=!1)=>t?Symbol.for(e):Symbol(e),generateFormatCacheKey=(e,t,a)=>friendlyJSONstringify({l:e,k:t,s:a}),friendlyJSONstringify=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),isNumber=e=>"number"==typeof e&&isFinite(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isEmptyObject=e=>isPlainObject(e)&&0===Object.keys(e).length,assign=Object.assign,_create=Object.create,create=(e=null)=>_create(e);function escapeHtml(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(e,t){return hasOwnProperty.call(e,t)}const isArray=Array.isArray,isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>isObject(e)&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),isPlainObject=e=>"[object Object]"===toTypeString(e),toDisplayString=e=>null==e?"":isArray(e)||isPlainObject(e)&&e.toString===objectToString?JSON.stringify(e,null,2):String(e);function join(e,t=""){return e.reduce(((e,a,r)=>0===r?e+a:e+t+a),"")}function warn(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const isNotObjectOrIsArray=e=>!isObject(e)||isArray(e);function deepCopy(e,t){if(isNotObjectOrIsArray(e)||isNotObjectOrIsArray(t))throw new Error("Invalid value");const a=[{src:e,des:t}];for(;a.length;){const{src:e,des:t}=a.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(isObject(e[r])&&!isObject(t[r])&&(t[r]=Array.isArray(e[r])?[]:create()),isNotObjectOrIsArray(t[r])||isNotObjectOrIsArray(e[r])?t[r]=e[r]:a.push({src:e[r],des:t[r]}))}))}}const COMPILE_ERROR_CODES_EXTEND_POINT=17;function isMessageAST(e){return isObject(e)&&0===resolveType(e)&&(hasOwn(e,"b")||hasOwn(e,"body"))}const PROPS_BODY=["b","body"];function resolveBody(e){return resolveProps(e,PROPS_BODY)}const PROPS_CASES=["c","cases"];function resolveCases(e){return resolveProps(e,PROPS_CASES,[])}const PROPS_STATIC=["s","static"];function resolveStatic(e){return resolveProps(e,PROPS_STATIC)}const PROPS_ITEMS=["i","items"];function resolveItems(e){return resolveProps(e,PROPS_ITEMS,[])}const PROPS_TYPE=["t","type"];function resolveType(e){return resolveProps(e,PROPS_TYPE)}const PROPS_VALUE=["v","value"];function resolveValue$1(e,t){const a=resolveProps(e,PROPS_VALUE);if(null!=a)return a;throw createUnhandleNodeError(t)}const PROPS_MODIFIER=["m","modifier"];function resolveLinkedModifier(e){return resolveProps(e,PROPS_MODIFIER)}const PROPS_KEY=["k","key"];function resolveLinkedKey(e){const t=resolveProps(e,PROPS_KEY);if(t)return t;throw createUnhandleNodeError(6)}function resolveProps(e,t,a){for(let r=0;r<t.length;r++){const a=t[r];if(hasOwn(e,a)&&null!=e[a])return e[a]}return a}const AST_NODE_PROPS_KEYS=[...PROPS_BODY,...PROPS_CASES,...PROPS_STATIC,...PROPS_ITEMS,...PROPS_KEY,...PROPS_MODIFIER,...PROPS_VALUE,...PROPS_TYPE];function createUnhandleNodeError(e){return new Error(`unhandled node type: ${e}`)}function format(e){return t=>formatParts(t,e)}function formatParts(e,t){const a=resolveBody(t);if(null==a)throw createUnhandleNodeError(0);if(1===resolveType(a)){const t=resolveCases(a);return e.plural(t.reduce(((t,a)=>[...t,formatMessageParts(e,a)]),[]))}return formatMessageParts(e,a)}function formatMessageParts(e,t){const a=resolveStatic(t);if(null!=a)return"text"===e.type?a:e.normalize([a]);{const a=resolveItems(t).reduce(((t,a)=>[...t,formatMessagePart(e,a)]),[]);return e.normalize(a)}}function formatMessagePart(e,t){const a=resolveType(t);switch(a){case 3:case 9:case 7:case 8:return resolveValue$1(t,a);case 4:{const r=t;if(hasOwn(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(hasOwn(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw createUnhandleNodeError(a)}case 5:{const r=t;if(hasOwn(r,"i")&&isNumber(r.i))return e.interpolate(e.list(r.i));if(hasOwn(r,"index")&&isNumber(r.index))return e.interpolate(e.list(r.index));throw createUnhandleNodeError(a)}case 6:{const a=t,r=resolveLinkedModifier(a),n=resolveLinkedKey(a);return e.linked(formatMessagePart(e,n),r?formatMessagePart(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${a}`)}}let compileCache=create();function compile(e,t){{const t=e.cacheKey;if(t){const a=compileCache[t];return a||(compileCache[t]=format(e))}return format(e)}}const CoreErrorCodes={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},CORE_ERROR_CODES_EXTEND_POINT=24;function getLocale(e,t){return null!=t.locale?resolveLocale(t.locale):resolveLocale(e.locale)}let _resolveLocale;function resolveLocale(e){if(isString(e))return e;if(isFunction(e)){if(e.resolvedOnce&&null!=_resolveLocale)return _resolveLocale;if("Function"===e.constructor.name){const t=e();if(isPromise(t))throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return _resolveLocale=t}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE)}function fallbackWithSimple(e,t,a){return[...new Set([a,...isArray(t)?t:isObject(t)?Object.keys(t):isString(t)?[t]:[a]])]}function fallbackWithLocaleChain(e,t,a){const r=isString(a)?a:DEFAULT_LOCALE,n=e;n.__localeChainCache||(n.__localeChainCache=new Map);let s=n.__localeChainCache.get(r);if(!s){s=[];let e=[a];for(;isArray(e);)e=appendBlockToChain(s,e,t);const o=isArray(t)||!isPlainObject(t)?t:t.default?t.default:null;e=isString(o)?[o]:o,isArray(e)&&appendBlockToChain(s,e,!1),n.__localeChainCache.set(r,s)}return s}function appendBlockToChain(e,t,a){let r=!0;for(let n=0;n<t.length&&isBoolean(r);n++){const s=t[n];isString(s)&&(r=appendLocaleToChain(e,t[n],a))}return r}function appendLocaleToChain(e,t,a){let r;const n=t.split("-");do{r=appendItemToChain(e,n.join("-"),a),n.splice(-1,1)}while(n.length&&!0===r);return r}function appendItemToChain(e,t,a){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const n=t.replace(/!/g,"");e.push(n),(isArray(a)||isPlainObject(a))&&a[n]&&(r=a[n])}return r}const pathStateMachine=[];pathStateMachine[0]={w:[0],i:[3,0],"[":[4],o:[7]},pathStateMachine[1]={w:[1],".":[2],"[":[4],o:[7]},pathStateMachine[2]={w:[2],i:[3,0],0:[3,0]},pathStateMachine[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},pathStateMachine[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},pathStateMachine[5]={"'":[4,0],o:8,l:[5,0]},pathStateMachine[6]={'"':[4,0],o:8,l:[6,0]};const literalValueRE=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function isLiteral(e){return literalValueRE.test(e)}function stripQuotes(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}function getPathCharType(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function formatSubPath(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(isLiteral(t)?stripQuotes(t):"*"+t)}function parse(e){const t=[];let a,r,n,s,o,l,i,c=-1,u=0,m=0;const g=[];function p(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,n="\\"+t,g[0](),!0}for(g[0]=()=>{void 0===r?r=n:r+=n},g[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},g[2]=()=>{g[0](),m++},g[3]=()=>{if(m>0)m--,u=4,g[0]();else{if(m=0,void 0===r)return!1;if(r=formatSubPath(r),!1===r)return!1;g[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!p()){if(s=getPathCharType(a),i=pathStateMachine[u],o=i[s]||i.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(l=g[o[1]],l&&(n=a,!1===l())))return;if(7===u)return t}}const cache=new Map;function resolveWithKeyValue(e,t){return isObject(e)?e[t]:null}function resolveValue(e,t){if(!isObject(e))return null;let a=cache.get(t);if(a||(a=parse(t),a&&cache.set(t,a)),!a)return null;const r=a.length;let n=e,s=0;for(;s<r;){const e=a[s];if(AST_NODE_PROPS_KEYS.includes(e)&&isMessageAST(n))return null;const t=n[e];if(void 0===t)return null;if(isFunction(n))return null;n=t,s++}return n}const VERSION$1="10.0.7",NOT_REOSLVED=-1,DEFAULT_LOCALE="en-US",MISSING_RESOLVE_VALUE="",capitalize=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function getDefaultLinkedModifiers(){return{upper:(e,t)=>"text"===t&&isString(e)?e.toUpperCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&isString(e)?e.toLowerCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&isString(e)?capitalize(e):"vnode"===t&&isObject(e)&&"__v_isVNode"in e?capitalize(e.children):e}}let _compiler,_resolver,_fallbacker;function registerMessageCompiler(e){_compiler=e}function registerMessageResolver(e){_resolver=e}function registerLocaleFallbacker(e){_fallbacker=e}const setAdditionalMeta=e=>{};let _fallbackContext=null;const setFallbackContext=e=>{_fallbackContext=e},getFallbackContext=()=>_fallbackContext;let _cid=0;function createCoreContext(e={}){const t=isFunction(e.onWarn)?e.onWarn:warn,a=isString(e.version)?e.version:VERSION$1,r=isString(e.locale)||isFunction(e.locale)?e.locale:DEFAULT_LOCALE,n=isFunction(r)?DEFAULT_LOCALE:r,s=isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||isString(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,o=isPlainObject(e.messages)?e.messages:createResources(n),l=isPlainObject(e.datetimeFormats)?e.datetimeFormats:createResources(n),i=isPlainObject(e.numberFormats)?e.numberFormats:createResources(n),c=assign(create(),e.modifiers,getDefaultLinkedModifiers()),u=e.pluralRules||create(),m=isFunction(e.missing)?e.missing:null,g=!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,p=!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,f=!!e.fallbackFormat,_=!!e.unresolving,b=isFunction(e.postTranslation)?e.postTranslation:null,d=isPlainObject(e.processor)?e.processor:null,E=!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter,S=isFunction(e.messageCompiler)?e.messageCompiler:_compiler,h=isFunction(e.messageResolver)?e.messageResolver:_resolver||resolveWithKeyValue,T=isFunction(e.localeFallbacker)?e.localeFallbacker:_fallbacker||fallbackWithSimple,F=isObject(e.fallbackContext)?e.fallbackContext:void 0,P=e,y=isObject(P.__datetimeFormatters)?P.__datetimeFormatters:new Map,I=isObject(P.__numberFormatters)?P.__numberFormatters:new Map,N=isObject(P.__meta)?P.__meta:{};_cid++;const R={version:a,cid:_cid,locale:r,fallbackLocale:s,messages:o,modifiers:c,pluralRules:u,missing:m,missingWarn:g,fallbackWarn:p,fallbackFormat:f,unresolving:_,postTranslation:b,processor:d,warnHtmlMessage:E,escapeParameter:O,messageCompiler:S,messageResolver:h,localeFallbacker:T,fallbackContext:F,onWarn:t,__meta:N};return R.datetimeFormats=l,R.numberFormats=i,R.__datetimeFormatters=y,R.__numberFormatters=I,R}const createResources=e=>({[e]:create()});function handleMissing(e,t,a,r,n){const{missing:s,onWarn:o}=e;if(null!==s){const r=s(e,a,t,n);return isString(r)?r:t}return t}function updateFallbackLocale(e,t,a){e.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}function isAlmostSameLocale(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function isImplicitFallback(e,t){const a=t.indexOf(e);if(-1===a)return!1;for(let r=a+1;r<t.length;r++)if(isAlmostSameLocale(e,t[r]))return!0;return!1}function datetime(e,...t){const{datetimeFormats:a,unresolving:r,fallbackLocale:n,onWarn:s,localeFallbacker:o}=e,{__datetimeFormatters:l}=e,[i,c,u,m]=parseDateTimeArgs(...t),g=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,f=getLocale(e,u),_=o(e,n,f);if(!isString(i)||""===i)return new Intl.DateTimeFormat(f,m).format(c);let b,d={},E=null;for(let h=0;h<_.length&&(b=_[h],d=a[b]||{},E=d[i],!isPlainObject(E));h++)handleMissing(e,i,b,g,"datetime format");if(!isPlainObject(E)||!isString(b))return r?NOT_REOSLVED:i;let O=`${b}__${i}`;isEmptyObject(m)||(O=`${O}__${JSON.stringify(m)}`);let S=l.get(O);return S||(S=new Intl.DateTimeFormat(b,assign({},E,m)),l.set(O,S)),p?S.formatToParts(c):S.format(c)}const DATETIME_FORMAT_OPTIONS_KEYS=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function parseDateTimeArgs(...e){const[t,a,r,n]=e,s=create();let o,l=create();if(isString(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);const a=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();o=new Date(a);try{o.toISOString()}catch{throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT)}}else if(isDate(t)){if(isNaN(t.getTime()))throw Error(CoreErrorCodes.INVALID_DATE_ARGUMENT);o=t}else{if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);o=t}return isString(a)?s.key=a:isPlainObject(a)&&Object.keys(a).forEach((e=>{DATETIME_FORMAT_OPTIONS_KEYS.includes(e)?l[e]=a[e]:s[e]=a[e]})),isString(r)?s.locale=r:isPlainObject(r)&&(l=r),isPlainObject(n)&&(l=n),[s.key||"",o,s,l]}function clearDateTimeFormat(e,t,a){const r=e;for(const n in a){const e=`${t}__${n}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function number(e,...t){const{numberFormats:a,unresolving:r,fallbackLocale:n,onWarn:s,localeFallbacker:o}=e,{__numberFormatters:l}=e,[i,c,u,m]=parseNumberArgs(...t),g=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,f=getLocale(e,u),_=o(e,n,f);if(!isString(i)||""===i)return new Intl.NumberFormat(f,m).format(c);let b,d={},E=null;for(let h=0;h<_.length&&(b=_[h],d=a[b]||{},E=d[i],!isPlainObject(E));h++)handleMissing(e,i,b,g,"number format");if(!isPlainObject(E)||!isString(b))return r?NOT_REOSLVED:i;let O=`${b}__${i}`;isEmptyObject(m)||(O=`${O}__${JSON.stringify(m)}`);let S=l.get(O);return S||(S=new Intl.NumberFormat(b,assign({},E,m)),l.set(O,S)),p?S.formatToParts(c):S.format(c)}const NUMBER_FORMAT_OPTIONS_KEYS=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function parseNumberArgs(...e){const[t,a,r,n]=e,s=create();let o=create();if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const l=t;return isString(a)?s.key=a:isPlainObject(a)&&Object.keys(a).forEach((e=>{NUMBER_FORMAT_OPTIONS_KEYS.includes(e)?o[e]=a[e]:s[e]=a[e]})),isString(r)?s.locale=r:isPlainObject(r)&&(o=r),isPlainObject(n)&&(o=n),[s.key||"",l,s,o]}function clearNumberFormat(e,t,a){const r=e;for(const n in a){const e=`${t}__${n}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const DEFAULT_MODIFIER=e=>e,DEFAULT_MESSAGE=e=>"",DEFAULT_MESSAGE_DATA_TYPE="text",DEFAULT_NORMALIZE=e=>0===e.length?"":join(e),DEFAULT_INTERPOLATE=toDisplayString;function pluralDefault(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function getPluralIndex(e){const t=isNumber(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(isNumber(e.named.count)||isNumber(e.named.n))?isNumber(e.named.count)?e.named.count:isNumber(e.named.n)?e.named.n:t:t}function normalizeNamed(e,t){t.count||(t.count=e),t.n||(t.n=e)}function createMessageContext(e={}){const t=e.locale,a=getPluralIndex(e),r=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?e.pluralRules[t]:pluralDefault,n=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?pluralDefault:void 0,s=e.list||[],o=e.named||create();isNumber(e.pluralIndex)&&normalizeNamed(a,o);function l(t,a){const r=isFunction(e.messages)?e.messages(t,!!a):!!isObject(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):DEFAULT_MESSAGE)}const i=isPlainObject(e.processor)&&isFunction(e.processor.normalize)?e.processor.normalize:DEFAULT_NORMALIZE,c=isPlainObject(e.processor)&&isFunction(e.processor.interpolate)?e.processor.interpolate:DEFAULT_INTERPOLATE,u={list:e=>s[e],named:e=>o[e],plural:e=>e[r(a,e.length,n)],linked:(t,...a)=>{const[r,n]=a;let s="text",o="";1===a.length?isObject(r)?(o=r.modifier||o,s=r.type||s):isString(r)&&(o=r||o):2===a.length&&(isString(r)&&(o=r||o),isString(n)&&(s=n||s));const i=l(t,!0)(u),c="vnode"===s&&isArray(i)&&o?i[0]:i;return o?(m=o,e.modifiers?e.modifiers[m]:DEFAULT_MODIFIER)(c,s):c;var m},message:l,type:isPlainObject(e.processor)&&isString(e.processor.type)?e.processor.type:DEFAULT_MESSAGE_DATA_TYPE,interpolate:c,normalize:i,values:assign(create(),s,o)};return u}const NOOP_MESSAGE_FUNCTION=()=>"",isMessageFunction=e=>isFunction(e);function translate(e,...t){const{fallbackFormat:a,postTranslation:r,unresolving:n,messageCompiler:s,fallbackLocale:o,messages:l}=e,[i,c]=parseTranslateArgs(...t),u=isBoolean(c.missingWarn)?c.missingWarn:e.missingWarn,m=isBoolean(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,g=isBoolean(c.escapeParameter)?c.escapeParameter:e.escapeParameter,p=!!c.resolvedMessage,f=isString(c.default)||isBoolean(c.default)?isBoolean(c.default)?s?i:()=>i:c.default:a?s?i:()=>i:null,_=a||null!=f&&(isString(f)||isFunction(f)),b=getLocale(e,c);g&&escapeParams(c);let[d,E,O]=p?[i,b,l[b]||create()]:resolveMessageFormat(e,i,b,o,m,u),S=d,h=i;if(p||isString(S)||isMessageAST(S)||isMessageFunction(S)||_&&(S=f,h=S),!(p||(isString(S)||isMessageAST(S)||isMessageFunction(S))&&isString(E)))return n?NOT_REOSLVED:i;let T=!1;const F=isMessageFunction(S)?S:compileMessageFormat(e,i,E,S,h,(()=>{T=!0}));if(T)return S;const P=evaluateMessage(e,F,createMessageContext(getMessageContextOptions(e,E,O,c)));return r?r(P,i):P}function escapeParams(e){isArray(e.list)?e.list=e.list.map((e=>isString(e)?escapeHtml(e):e)):isObject(e.named)&&Object.keys(e.named).forEach((t=>{isString(e.named[t])&&(e.named[t]=escapeHtml(e.named[t]))}))}function resolveMessageFormat(e,t,a,r,n,s){const{messages:o,onWarn:l,messageResolver:i,localeFallbacker:c}=e,u=c(e,r,a);let m,g=create(),p=null;for(let f=0;f<u.length&&(m=u[f],g=o[m]||create(),null===(p=i(g,t))&&(p=g[t]),!(isString(p)||isMessageAST(p)||isMessageFunction(p)));f++)if(!isImplicitFallback(m,u)){const a=handleMissing(e,t,m,s,"translate");a!==t&&(p=a)}return[p,m,g]}function compileMessageFormat(e,t,a,r,n,s){const{messageCompiler:o,warnHtmlMessage:l}=e;if(isMessageFunction(r)){const e=r;return e.locale=e.locale||a,e.key=e.key||t,e}if(null==o){const e=()=>r;return e.locale=a,e.key=t,e}const i=o(r,getCompileContext(e,a,n,r,l,s));return i.locale=a,i.key=t,i.source=r,i}function evaluateMessage(e,t,a){return t(a)}function parseTranslateArgs(...e){const[t,a,r]=e,n=create();if(!(isString(t)||isNumber(t)||isMessageFunction(t)||isMessageAST(t)))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const s=isNumber(t)?String(t):(isMessageFunction(t),t);return isNumber(a)?n.plural=a:isString(a)?n.default=a:isPlainObject(a)&&!isEmptyObject(a)?n.named=a:isArray(a)&&(n.list=a),isNumber(r)?n.plural=r:isString(r)?n.default=r:isPlainObject(r)&&assign(n,r),[s,n]}function getCompileContext(e,t,a,r,n,s){return{locale:t,key:a,warnHtmlMessage:n,onError:e=>{throw s&&s(e),e},onCacheKey:e=>generateFormatCacheKey(t,a,e)}}function getMessageContextOptions(e,t,a,r){const{modifiers:n,pluralRules:s,messageResolver:o,fallbackLocale:l,fallbackWarn:i,missingWarn:c,fallbackContext:u}=e,m={locale:t,modifiers:n,pluralRules:s,messages:(r,n)=>{let s=o(a,r);if(null==s&&(u||n)){const[,,a]=resolveMessageFormat(u||e,r,t,l,i,c);s=o(a,r)}if(isString(s)||isMessageAST(s)){let a=!1;const n=compileMessageFormat(e,r,t,s,r,(()=>{a=!0}));return a?NOOP_MESSAGE_FUNCTION:n}return isMessageFunction(s)?s:NOOP_MESSAGE_FUNCTION}};return e.processor&&(m.processor=e.processor),r.list&&(m.list=r.list),r.named&&(m.named=r.named),isNumber(r.plural)&&(m.pluralIndex=r.plural),m}const VERSION="10.0.7",I18nErrorCodes={UNEXPECTED_RETURN_TYPE:24,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34},TranslateVNodeSymbol=makeSymbol("__translateVNode"),DatetimePartsSymbol=makeSymbol("__datetimeParts"),NumberPartsSymbol=makeSymbol("__numberParts"),SetPluralRulesSymbol=makeSymbol("__setPluralRules"),InejctWithOptionSymbol=makeSymbol("__injectWithOption"),DisposeSymbol=makeSymbol("__dispose");function handleFlatJson(e){if(!isObject(e))return e;if(isMessageAST(e))return e;for(const t in e)if(hasOwn(e,t))if(t.includes(".")){const a=t.split("."),r=a.length-1;let n=e,s=!1;for(let e=0;e<r;e++){if("__proto__"===a[e])throw new Error(`unsafe key: ${a[e]}`);if(a[e]in n||(n[a[e]]=create()),!isObject(n[a[e]])){s=!0;break}n=n[a[e]]}if(s||(isMessageAST(n)?AST_NODE_PROPS_KEYS.includes(a[r])||delete e[t]:(n[a[r]]=e[t],delete e[t])),!isMessageAST(n)){const e=n[a[r]];isObject(e)&&handleFlatJson(e)}}else isObject(e[t])&&handleFlatJson(e[t]);return e}function getLocaleMessages(e,t){const{messages:a,__i18n:r,messageResolver:n,flatJson:s}=t,o=isPlainObject(a)?a:isArray(r)?create():{[e]:create()};if(isArray(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:a}=e;t?(o[t]=o[t]||create(),deepCopy(a,o[t])):deepCopy(a,o)}else isString(e)&&deepCopy(JSON.parse(e),o)})),null==n&&s)for(const l in o)hasOwn(o,l)&&handleFlatJson(o[l]);return o}function getComponentOptions(e){return e.type}function adjustI18nResources(e,t,a){let r=isObject(t.messages)?t.messages:create();"__i18nGlobal"in a&&(r=getLocaleMessages(e.locale.value,{messages:r,__i18n:a.__i18nGlobal}));const n=Object.keys(r);if(n.length&&n.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),isObject(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach((a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])}))}if(isObject(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach((a=>{e.mergeNumberFormat(a,t.numberFormats[a])}))}}function createTextNode(e){return createVNode(Text,null,e,0)}const DEVTOOLS_META="__INTLIFY_META__",NOOP_RETURN_ARRAY=()=>[],NOOP_RETURN_FALSE=()=>!1;let composerID=0;function defineCoreMissingHandler(e){return(t,a,r,n)=>e(a,r,getCurrentInstance()||void 0,n)}const getMetaInfo=()=>{const e=getCurrentInstance();let t=null;return e&&(t=getComponentOptions(e)[DEVTOOLS_META])?{[DEVTOOLS_META]:t}:null};function createComposer(e={}){const{__root:t,__injectWithOption:a}=e,r=void 0===t,n=e.flatJson,s=inBrowser?ref:shallowRef;let o=!isBoolean(e.inheritLocale)||e.inheritLocale;const l=s(t&&o?t.locale.value:isString(e.locale)?e.locale:DEFAULT_LOCALE),i=s(t&&o?t.fallbackLocale.value:isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:l.value),c=s(getLocaleMessages(l.value,e)),u=s(isPlainObject(e.datetimeFormats)?e.datetimeFormats:{[l.value]:{}}),m=s(isPlainObject(e.numberFormats)?e.numberFormats:{[l.value]:{}});let g=t?t.missingWarn:!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,p=t?t.fallbackWarn:!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,f=t?t.fallbackRoot:!isBoolean(e.fallbackRoot)||e.fallbackRoot,_=!!e.fallbackFormat,b=isFunction(e.missing)?e.missing:null,d=isFunction(e.missing)?defineCoreMissingHandler(e.missing):null,E=isFunction(e.postTranslation)?e.postTranslation:null,O=t?t.warnHtmlMessage:!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,S=!!e.escapeParameter;const h=t?t.modifiers:isPlainObject(e.modifiers)?e.modifiers:{};let T,F=e.pluralRules||t&&t.pluralRules;T=(()=>{r&&setFallbackContext(null);const t={version:VERSION,locale:l.value,fallbackLocale:i.value,messages:c.value,modifiers:h,pluralRules:F,missing:null===d?void 0:d,missingWarn:g,fallbackWarn:p,fallbackFormat:_,unresolving:!0,postTranslation:null===E?void 0:E,warnHtmlMessage:O,escapeParameter:S,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=u.value,t.numberFormats=m.value,t.__datetimeFormatters=isPlainObject(T)?T.__datetimeFormatters:void 0,t.__numberFormatters=isPlainObject(T)?T.__numberFormatters:void 0;const a=createCoreContext(t);return r&&setFallbackContext(a),a})(),updateFallbackLocale(T,l.value,i.value);const P=computed({get:()=>l.value,set:e=>{l.value=e,T.locale=l.value}}),y=computed({get:()=>i.value,set:e=>{i.value=e,T.fallbackLocale=i.value,updateFallbackLocale(T,l.value,e)}}),I=computed((()=>c.value)),N=computed((()=>u.value)),R=computed((()=>m.value));const v=(e,a,n,s,o,g)=>{let p;l.value,i.value,c.value,u.value,m.value;try{0,r||(T.fallbackContext=t?getFallbackContext():void 0),p=e(T)}finally{r||(T.fallbackContext=void 0)}if("translate exists"!==n&&isNumber(p)&&p===NOT_REOSLVED||"translate exists"===n&&!p){const[e,r]=a();return t&&f?s(t):o(e)}if(g(p))return p;throw Error(I18nErrorCodes.UNEXPECTED_RETURN_TYPE)};function k(...e){return v((t=>Reflect.apply(translate,null,[t,...e])),(()=>parseTranslateArgs(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>isString(e)))}const L={normalize:function(e){return e.map((e=>isString(e)||isNumber(e)||isBoolean(e)?createTextNode(String(e)):e))},interpolate:e=>e,type:"vnode"};function C(e){return c.value[e]||{}}composerID++,t&&inBrowser&&(watch(t.locale,(e=>{o&&(l.value=e,T.locale=e,updateFallbackLocale(T,l.value,i.value))})),watch(t.fallbackLocale,(e=>{o&&(i.value=e,T.fallbackLocale=e,updateFallbackLocale(T,l.value,i.value))})));const A={id:composerID,locale:P,fallbackLocale:y,get inheritLocale(){return o},set inheritLocale(e){o=e,e&&t&&(l.value=t.locale.value,i.value=t.fallbackLocale.value,updateFallbackLocale(T,l.value,i.value))},get availableLocales(){return Object.keys(c.value).sort()},messages:I,get modifiers(){return h},get pluralRules(){return F||{}},get isGlobal(){return r},get missingWarn(){return g},set missingWarn(e){g=e,T.missingWarn=g},get fallbackWarn(){return p},set fallbackWarn(e){p=e,T.fallbackWarn=p},get fallbackRoot(){return f},set fallbackRoot(e){f=e},get fallbackFormat(){return _},set fallbackFormat(e){_=e,T.fallbackFormat=_},get warnHtmlMessage(){return O},set warnHtmlMessage(e){O=e,T.warnHtmlMessage=e},get escapeParameter(){return S},set escapeParameter(e){S=e,T.escapeParameter=e},t:k,getLocaleMessage:C,setLocaleMessage:function(e,t){if(n){const a={[e]:t};for(const e in a)hasOwn(a,e)&&handleFlatJson(a[e]);t=a[e]}c.value[e]=t,T.messages=c.value},mergeLocaleMessage:function(e,t){c.value[e]=c.value[e]||{};const a={[e]:t};if(n)for(const r in a)hasOwn(a,r)&&handleFlatJson(a[r]);deepCopy(t=a[e],c.value[e]),T.messages=c.value},getPostTranslationHandler:function(){return isFunction(E)?E:null},setPostTranslationHandler:function(e){E=e,T.postTranslation=e},getMissingHandler:function(){return b},setMissingHandler:function(e){null!==e&&(d=defineCoreMissingHandler(e)),b=e,T.missing=d},[SetPluralRulesSymbol]:function(e){F=e,T.pluralRules=F}};return A.datetimeFormats=N,A.numberFormats=R,A.rt=function(...e){const[t,a,r]=e;if(r&&!isObject(r))throw Error(I18nErrorCodes.INVALID_ARGUMENT);return k(t,a,assign({resolvedMessage:!0},r||{}))},A.te=function(e,t){return v((()=>{if(!e)return!1;const a=C(isString(t)?t:l.value),r=T.messageResolver(a,e);return isMessageAST(r)||isMessageFunction(r)||isString(r)}),(()=>[e]),"translate exists",(a=>Reflect.apply(a.te,a,[e,t])),NOOP_RETURN_FALSE,(e=>isBoolean(e)))},A.tm=function(e){const a=function(e){let t=null;const a=fallbackWithLocaleChain(T,i.value,l.value);for(let r=0;r<a.length;r++){const n=c.value[a[r]]||{},s=T.messageResolver(n,e);if(null!=s){t=s;break}}return t}(e);return null!=a?a:t&&t.tm(e)||{}},A.d=function(...e){return v((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},A.n=function(...e){return v((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},A.getDateTimeFormat=function(e){return u.value[e]||{}},A.setDateTimeFormat=function(e,t){u.value[e]=t,T.datetimeFormats=u.value,clearDateTimeFormat(T,e,t)},A.mergeDateTimeFormat=function(e,t){u.value[e]=assign(u.value[e]||{},t),T.datetimeFormats=u.value,clearDateTimeFormat(T,e,t)},A.getNumberFormat=function(e){return m.value[e]||{}},A.setNumberFormat=function(e,t){m.value[e]=t,T.numberFormats=m.value,clearNumberFormat(T,e,t)},A.mergeNumberFormat=function(e,t){m.value[e]=assign(m.value[e]||{},t),T.numberFormats=m.value,clearNumberFormat(T,e,t)},A[InejctWithOptionSymbol]=a,A[TranslateVNodeSymbol]=function(...e){return v((t=>{let a;const r=t;try{r.processor=L,a=Reflect.apply(translate,null,[r,...e])}finally{r.processor=null}return a}),(()=>parseTranslateArgs(...e)),"translate",(t=>t[TranslateVNodeSymbol](...e)),(e=>[createTextNode(e)]),(e=>isArray(e)))},A[DatetimePartsSymbol]=function(...e){return v((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>t[DatetimePartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},A[NumberPartsSymbol]=function(...e){return v((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>t[NumberPartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},A}function convertComposerOptions(e){const t=isString(e.locale)?e.locale:DEFAULT_LOCALE,a=isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=isFunction(e.missing)?e.missing:void 0,n=!isBoolean(e.silentTranslationWarn)&&!isRegExp(e.silentTranslationWarn)||!e.silentTranslationWarn,s=!isBoolean(e.silentFallbackWarn)&&!isRegExp(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!isBoolean(e.fallbackRoot)||e.fallbackRoot,l=!!e.formatFallbackMessages,i=isPlainObject(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=isFunction(e.postTranslation)?e.postTranslation:void 0,m=!isString(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,g=!!e.escapeParameterHtml,p=!isBoolean(e.sync)||e.sync;let f=e.messages;if(isPlainObject(e.sharedMessages)){const t=e.sharedMessages;f=Object.keys(t).reduce(((e,a)=>{const r=e[a]||(e[a]={});return assign(r,t[a]),e}),f||{})}const{__i18n:_,__root:b,__injectWithOption:d}=e,E=e.datetimeFormats,O=e.numberFormats;return{locale:t,fallbackLocale:a,messages:f,flatJson:e.flatJson,datetimeFormats:E,numberFormats:O,missing:r,missingWarn:n,fallbackWarn:s,fallbackRoot:o,fallbackFormat:l,modifiers:i,pluralRules:c,postTranslation:u,warnHtmlMessage:m,escapeParameter:g,messageResolver:e.messageResolver,inheritLocale:p,__i18n:_,__root:b,__injectWithOption:d}}function createVueI18n(e={}){const t=createComposer(convertComposerOptions(e)),{__extender:a}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return isBoolean(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=isBoolean(e)?!e:e},get silentFallbackWarn(){return isBoolean(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=isBoolean(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[a,r,n]=e,s={plural:1};let o=null,l=null;if(!isString(a))throw Error(I18nErrorCodes.INVALID_ARGUMENT);const i=a;return isString(r)?s.locale=r:isNumber(r)?s.plural=r:isArray(r)?o=r:isPlainObject(r)&&(l=r),isString(n)?s.locale=n:isArray(n)?o=n:isPlainObject(n)&&(l=n),Reflect.apply(t.t,t,[i,o||l||{},s])},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)}};return r.__extender=a,r}function defineMixin(e,t,a){return{beforeCreate(){const r=getCurrentInstance();if(!r)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const n=this.$options;if(n.i18n){const r=n.i18n;if(n.__i18n&&(r.__i18n=n.__i18n),r.__root=t,this===this.$root)this.$i18n=mergeToGlobal(e,r);else{r.__injectWithOption=!0,r.__extender=a.__vueI18nExtend,this.$i18n=createVueI18n(r);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(n.__i18n)if(this===this.$root)this.$i18n=mergeToGlobal(e,n);else{this.$i18n=createVueI18n({__i18n:n.__i18n,__injectWithOption:!0,__extender:a.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;n.__i18nGlobal&&adjustI18nResources(t,n,n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),a.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const e=getCurrentInstance();if(!e)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),a.__deleteInstance(e),delete this.$i18n}}}function mergeToGlobal(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[SetPluralRulesSymbol](t.pluralizationRules||e.pluralizationRules);const a=getLocaleMessages(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}const baseFormatProps={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function getInterpolateArg({slots:e},t){if(1===t.length&&"default"===t[0]){return(e.default?e.default():[]).reduce(((e,t)=>[...e,...t.type===Fragment?t.children:[t]]),[])}return t.reduce(((t,a)=>{const r=e[a];return r&&(t[a]=r()),t}),create())}function getFragmentableTag(){return Fragment}const TranslationImpl=defineComponent({name:"i18n-t",props:assign({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>isNumber(e)||!isNaN(e)}},baseFormatProps),setup(e,t){const{slots:a,attrs:r}=t,n=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(a).filter((e=>"_"!==e)),o=create();e.locale&&(o.locale=e.locale),void 0!==e.plural&&(o.plural=isString(e.plural)?+e.plural:e.plural);const l=getInterpolateArg(t,s),i=n[TranslateVNodeSymbol](e.keypath,l,o),c=assign(create(),r),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,i)}}}),Translation=TranslationImpl,I18nT=Translation;function isVNode(e){return isArray(e)&&!isString(e[0])}function renderFormatter(e,t,a,r){const{slots:n,attrs:s}=t;return()=>{const t={part:!0};let o=create();e.locale&&(t.locale=e.locale),isString(e.format)?t.key=e.format:isObject(e.format)&&(isString(e.format.key)&&(t.key=e.format.key),o=Object.keys(e.format).reduce(((t,r)=>a.includes(r)?assign(create(),t,{[r]:e.format[r]}):t),create()));const l=r(e.value,t,o);let i=[t.key];isArray(l)?i=l.map(((e,t)=>{const a=n[e.type],r=a?a({[e.type]:e.value,index:t,parts:l}):[e.value];return isVNode(r)&&(r[0].key=`${e.type}-${t}`),r})):isString(l)&&(i=[l]);const c=assign(create(),s),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,i)}}const NumberFormatImpl=defineComponent({name:"i18n-n",props:assign({value:{type:Number,required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const a=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return renderFormatter(e,t,NUMBER_FORMAT_OPTIONS_KEYS,((...e)=>a[NumberPartsSymbol](...e)))}}),NumberFormat=NumberFormatImpl,I18nN=NumberFormat,DatetimeFormatImpl=defineComponent({name:"i18n-d",props:assign({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const a=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return renderFormatter(e,t,DATETIME_FORMAT_OPTIONS_KEYS,((...e)=>a[DatetimePartsSymbol](...e)))}}),DatetimeFormat=DatetimeFormatImpl,I18nD=DatetimeFormat;function getComposer$1(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const r=a.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}function vTDirective(e){const t=t=>{const{instance:a,value:r}=t;if(!a||!a.$)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const n=getComposer$1(e,a.$),s=parseValue(r);return[Reflect.apply(n.t,n,[...makeParams(s)]),n]};return{created:(a,r)=>{const[n,s]=t(r);inBrowser&&e.global===s&&(a.__i18nWatcher=watch(s.locale,(()=>{r.instance&&r.instance.$forceUpdate()}))),a.__composer=s,a.textContent=n},unmounted:e=>{inBrowser&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const a=e.__composer,r=parseValue(t);e.textContent=Reflect.apply(a.t,a,[...makeParams(r)])}},getSSRProps:e=>{const[a]=t(e);return{textContent:a}}}}function parseValue(e){if(isString(e))return{path:e};if(isPlainObject(e)){if(!("path"in e))throw Error(I18nErrorCodes.REQUIRED_VALUE,"path");return e}throw Error(I18nErrorCodes.INVALID_VALUE)}function makeParams(e){const{path:t,locale:a,args:r,choice:n,plural:s}=e,o={},l=r||{};return isString(a)&&(o.locale=a),isNumber(n)&&(o.plural=n),isNumber(s)&&(o.plural=s),[t,l,o]}function apply(e,t,...a){const r=isPlainObject(a[0])?a[0]:{};(!isBoolean(r.globalInstall)||r.globalInstall)&&([Translation.name,"I18nT"].forEach((t=>e.component(t,Translation))),[NumberFormat.name,"I18nN"].forEach((t=>e.component(t,NumberFormat))),[DatetimeFormat.name,"I18nD"].forEach((t=>e.component(t,DatetimeFormat)))),e.directive("t",vTDirective(t))}const I18nInjectionKey=makeSymbol("global-vue-i18n");function createI18n(e={},t){const a=!isBoolean(e.legacy)||e.legacy,r=!isBoolean(e.globalInjection)||e.globalInjection,n=new Map,[s,o]=createGlobal(e,a),l=makeSymbol("");const i={get mode(){return a?"legacy":"composition"},async install(e,...t){if(e.__VUE_I18N_SYMBOL__=l,e.provide(e.__VUE_I18N_SYMBOL__,i),isPlainObject(t[0])){const e=t[0];i.__composerExtend=e.__composerExtend,i.__vueI18nExtend=e.__vueI18nExtend}let n=null;!a&&r&&(n=injectGlobalFields(e,i.global)),apply(e,i,...t),a&&e.mixin(defineMixin(o,o.__composer,i));const s=e.unmount;e.unmount=()=>{n&&n(),i.dispose(),s()}},get global(){return o},dispose(){s.stop()},__instances:n,__getInstance:function(e){return n.get(e)||null},__setInstance:function(e,t){n.set(e,t)},__deleteInstance:function(e){n.delete(e)}};return i}function useI18n(e={}){const t=getCurrentInstance();if(null==t)throw Error(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Error(I18nErrorCodes.NOT_INSTALLED);const a=getI18nInstance(t),r=getGlobalComposer(a),n=getComponentOptions(t),s=getScope(e,n);if("global"===s)return adjustI18nResources(r,e,n),r;if("parent"===s){let n=getComposer(a,t,e.__useComponent);return null==n&&(n=r),n}const o=a;let l=o.__getInstance(t);if(null==l){const a=assign({},e);"__i18n"in n&&(a.__i18n=n.__i18n),r&&(a.__root=r),l=createComposer(a),o.__composerExtend&&(l[DisposeSymbol]=o.__composerExtend(l)),setupLifeCycle(o,t,l),o.__setInstance(t,l)}return l}function createGlobal(e,t,a){const r=effectScope(),n=t?r.run((()=>createVueI18n(e))):r.run((()=>createComposer(e)));if(null==n)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);return[r,n]}function getI18nInstance(e){const t=inject(e.isCE?I18nInjectionKey:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Error(e.isCE?I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE:I18nErrorCodes.UNEXPECTED_ERROR);return t}function getScope(e,t){return isEmptyObject(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function getGlobalComposer(e){return"composition"===e.mode?e.global:e.global.__composer}function getComposer(e,t,a=!1){let r=null;const n=t.root;let s=getParentComponentInstance(t,a);for(;null!=s;){const t=e;if("composition"===e.mode)r=t.__getInstance(s);else{const e=t.__getInstance(s);null!=e&&(r=e.__composer,a&&r&&!r[InejctWithOptionSymbol]&&(r=null))}if(null!=r)break;if(n===s)break;s=s.parent}return r}function getParentComponentInstance(e,t=!1){return null==e?null:t&&e.vnode.ctx||e.parent}function setupLifeCycle(e,t,a){onMounted((()=>{}),t),onUnmounted((()=>{const r=a;e.__deleteInstance(t);const n=r[DisposeSymbol];n&&(n(),delete r[DisposeSymbol])}),t)}const globalExportProps=["locale","fallbackLocale","availableLocales"],globalExportMethods=["t","rt","d","n","tm","te"];function injectGlobalFields(e,t){const a=Object.create(null);globalExportProps.forEach((e=>{const r=Object.getOwnPropertyDescriptor(t,e);if(!r)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const n=isRef(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(a,e,n)})),e.config.globalProperties.$i18n=a,globalExportMethods.forEach((a=>{const r=Object.getOwnPropertyDescriptor(t,a);if(!r||!r.value)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${a}`,r)}));return()=>{delete e.config.globalProperties.$i18n,globalExportMethods.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))}}registerMessageCompiler(compile),registerMessageResolver(resolveValue),registerLocaleFallbacker(fallbackWithLocaleChain);export{DatetimeFormat,I18nD,I18nInjectionKey,I18nN,I18nT,NumberFormat,Translation,VERSION,createI18n,useI18n,vTDirective};
