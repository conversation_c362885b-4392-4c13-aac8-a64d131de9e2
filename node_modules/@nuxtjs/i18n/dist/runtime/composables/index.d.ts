import type { Ref } from 'vue';
import type { Locale } from 'vue-i18n';
import type { resolveRoute } from '../routing/routing.js';
import type { I18nHeadMetaInfo, I18nHeadOptions, SeoAttributesOptions } from '#internal-i18n-types';
import type { RouteLocationAsRelativeI18n, RouteLocationRaw, RouteLocationResolvedI18n, RouteMap, RouteMapI18n } from 'vue-router';
import type { RouteLocationGenericPath, I18nRouteMeta } from '../types.js';
export * from 'vue-i18n';
export * from './shared.js';
/**
 * Used to set i18n params for the current route.
 *
 * @params params - an object with {@link Locale} keys with localized parameters
 */
export type SetI18nParamsFunction = (params: I18nRouteMeta) => void;
/**
 * Returns a {@link SetI18nParamsFunction} used to set i18n params for the current route.
 *
 * @param options - An options object, see {@link SeoAttributesOptions}.
 *
 * @returns a {@link SetI18nParamsFunction}.
 */
export declare function useSetI18nParams(seo?: SeoAttributesOptions): SetI18nParamsFunction;
/**
 * Returns localized head properties for locale-related aspects.
 *
 * @param options - An options object, see {@link I18nHeadOptions}.
 *
 * @returns The localized head properties.
 */
export type LocaleHeadFunction = (options: I18nHeadOptions) => I18nHeadMetaInfo;
/**
 * Returns localized head properties for locale-related aspects.
 *
 * @param options - An options object, see {@link I18nHeadOptions}
 *
 * @returns The localized {@link I18nHeadMetaInfo | head properties} with Vue `ref`.
 */
export declare function useLocaleHead({ dir, lang, seo, key }?: I18nHeadOptions): Ref<I18nHeadMetaInfo>;
/**
 * NOTE: regarding composables accepting narrowed route arguments
 * route path string autocompletion is disabled as this can break depending on `strategy`
 * if route resolve is improved to work regardless of strategy this can be enabled again
 *
 * the following would be the complete narrowed type
 * route: Name | RouteLocationAsRelativeI18n | RouteLocationAsStringI18n | RouteLocationAsPathI18n
 */
type RouteLocationI18nGenericPath = Omit<RouteLocationAsRelativeI18n, 'path'> & {
    path?: string;
};
/**
 * Revoles a localized route object for the passed route.
 *
 * @param route - a route name or route object.
 * @param locale - (default: current locale).
 *
 * @returns Localized route object
 *
 * @deprecated use {@link useLocalePath}/{@link LocalePathFunction $localePath} or {@link useLocaleRoute}/{@link LocaleRouteFunction $localeRoute} instead
 */
export type ResolveRouteFunction = (route: RouteLocationRaw, locale?: Locale) => ReturnType<typeof resolveRoute>;
/**
 * Resolves the route base name for the given route.
 *
 * @param route - a route name or route object.
 *
 * @returns Route base name (without localization suffix) or `undefined` if no name was found.
 */
export type RouteBaseNameFunction = <Name extends keyof RouteMap = keyof RouteMap>(route: Name | RouteLocationGenericPath) => string | undefined;
/**
 * Returns a {@link RouteBaseNameFunction} used get the base name of a route.
 */
export declare function useRouteBaseName(): RouteBaseNameFunction;
/**
 * Resolves a localized path for the given route.
 *
 * @param route - a route name or route object.
 * @param locale - (default: current locale).
 *
 * @returns Returns the localized URL for a given route.
 */
export type LocalePathFunction = <Name extends keyof RouteMapI18n = keyof RouteMapI18n>(route: Name | RouteLocationI18nGenericPath, locale?: Locale) => string;
/**
 * Returns a {@link LocalePathFunction} used to resolve a localized path.
 */
export declare function useLocalePath(): LocalePathFunction;
/**
 * Resolves a localized route object for the given route.
 *
 * @param route - a route name or route object.
 * @param locale - (default: current locale).
 *
 * @returns A route. if cannot resolve, `undefined` is returned.
 */
export type LocaleRouteFunction = <Name extends keyof RouteMapI18n = keyof RouteMapI18n>(route: Name | RouteLocationI18nGenericPath, locale?: Locale) => RouteLocationResolvedI18n<Name> | undefined;
/**
 * Returns a {@link LocaleRouteFunction} used to resolve localized route objects.
 */
export declare function useLocaleRoute(): LocaleRouteFunction;
/**
 * Resolves a localized variant of the passed route.
 *
 * @param route - a route name or route object.
 * @param locale - (default: current locale).
 *
 * @returns A resolved route object
 *
 * @deprecated use {@link useLocaleRoute}/{@link LocaleRouteFunction $localeRoute} instead
 */
export type LocaleLocationFunction = <Name extends keyof RouteMapI18n = keyof RouteMapI18n>(route: Name | RouteLocationI18nGenericPath, locale?: Locale) => RouteLocationResolvedI18n<Name> | undefined;
/**
 * Returns a {@link LocaleLocationFunction} used to resolve localized route objects.
 *
 * @deprecated use {@link useLocaleRoute}/{@link LocaleRouteFunction $localeRoute} instead
 */
export declare function useLocaleLocation(): LocaleLocationFunction;
/**
 * Resolves a localized variant of the current path.
 *
 * @param locale - (default: current locale).
 */
export type SwitchLocalePathFunction = (locale: Locale) => string;
/**
 * Returns a {@link SwitchLocalePathFunction} used to resolve a localized variant of the current path.
 */
export declare function useSwitchLocalePath(): SwitchLocalePathFunction;
/**
 * Return the browser locale based on `navigator.languages` (client-side) or `accept-language` header (server-side).
 *
 * @returns the browser locale, if not detected, return `null`.
 */
export declare function useBrowserLocale(): string | null;
/**
 * Returns the locale cookie based on `document.cookie` (client-side) or `cookie` header (server-side).
 *
 * @returns a `Ref<string>` with the detected cookie or an empty string if none is detected or if `detectBrowserLanguage.useCookie` is disabled.
 */
export declare function useCookieLocale(): Ref<string>;
/**
 * The i18n custom route for page components
 */
export interface I18nRoute {
    /**
     * Customize page component routes per locale.
     *
     * @description You can specify static and dynamic paths for vue-router.
     */
    paths?: Partial<Record<Locale, `/${string}`>>;
    /**
     * Locales in which the page component should be localized.
     */
    locales?: Locale[];
}
/**
 * Define custom route for page component
 *
 * @param route - The custom route
 */
export declare function defineI18nRoute(route: I18nRoute | false): void;
