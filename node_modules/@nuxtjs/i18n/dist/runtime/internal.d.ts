import type { Locale } from 'vue-i18n';
import type { DetectBrowserLanguageOptions, I18nPublicRuntimeConfig } from '#internal-i18n-types';
import type { CookieRef, NuxtApp } from 'nuxt/app';
import type { CompatRoute } from './types.js';
import type { CommonComposableOptions } from './utils.js';
type TailParameters<T> = T extends (first: CommonComposableOptions, ...rest: infer R) => unknown ? R : never;
export declare function wrapComposable<F extends (common: CommonComposableOptions, ...args: never[]) => ReturnType<F>>(fn: F, common?: CommonComposableOptions): (...args: TailParameters<F>) => ReturnType<F>;
export declare function getBrowserLocale(): string | undefined;
export declare function createI18nCookie(): CookieRef<string | null | undefined>;
export declare function getLocaleCookie(cookieRef: CookieRef<string | null | undefined>, detect: false | DetectBrowserLanguageOptions, defaultLocale: string): string | undefined;
type DetectBrowserLanguageResult = {
    locale: string;
    from?: 'cookie' | 'navigator_or_header' | 'fallback';
    error?: 'not_found_match' | 'first_access_only' | 'not_redirect_on_root' | 'not_redirect_on_no_prefix' | 'detect_ignore_on_ssg' | 'disabled';
};
export declare function detectBrowserLanguage(nuxtApp: NuxtApp, route: string | CompatRoute, localeCookie: string | undefined, locale?: Locale): DetectBrowserLanguageResult;
export declare function runtimeDetectBrowserLanguage(opts?: I18nPublicRuntimeConfig): false | DetectBrowserLanguageOptions;
export {};
