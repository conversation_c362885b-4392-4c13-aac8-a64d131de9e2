import type { Locale } from 'vue-i18n';
import type { LocaleObject } from '#internal-i18n-types';
import type { I18nPublicRuntimeConfig } from '#internal-i18n-types';
import type { CompatRoute } from './types.js';
export declare function getHost(): string;
export declare function getLocaleDomain(locales: LocaleObject[], strategy: string, route: string | CompatRoute): string;
export declare function getDomainFromLocale(localeCode: Locale): string | undefined;
/**
 * Removes default routes depending on domain
 */
export declare function setupMultiDomainLocales(runtimeI18n: I18nPublicRuntimeConfig, defaultLocaleDomain: string): void;
/**
 * Returns default locale for the current domain, returns `defaultLocale` by default
 */
export declare function getDefaultLocaleForDomain(runtimeI18n: I18nPublicRuntimeConfig): any;
