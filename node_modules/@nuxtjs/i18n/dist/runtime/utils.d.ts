import type { I18n, Locale } from 'vue-i18n';
import type { NuxtApp } from '#app';
import type { Ref } from '#imports';
import type { Router } from '#vue-router';
import type { RuntimeConfig } from 'nuxt/schema';
import type { I18nPublicRuntimeConfig, Strategies } from '#internal-i18n-types';
import type { CompatRoute, I18nRouteMeta } from './types.js';
export declare function formatMessage(message: string): string;
/**
 * Common options used internally by composable functions, these
 * are initialized when calling a wrapped composable function.
 *
 * @internal
 */
export type CommonComposableOptions = {
    router: Router;
    i18n: I18n;
    runtimeConfig: RuntimeConfig & {
        public: {
            i18n: I18nPublicRuntimeConfig;
        };
    };
    metaState: Ref<I18nRouteMeta>;
};
export declare function initCommonComposableOptions(i18n?: I18n): CommonComposableOptions;
export declare function loadAndSetLocale(nuxtApp: NuxtApp, newLocale: Locale, initial?: boolean): Promise<boolean>;
export declare function detectLocale(nuxtApp: NuxtApp, route: string | CompatRoute, routeLocale: string, currentLocale: string | undefined, localeCookie: string | undefined): string;
type DetectRedirectOptions = {
    nuxtApp: NuxtApp;
    to: CompatRoute;
    from?: CompatRoute;
    /**
     * The locale we want to navigate to
     */
    locale: Locale;
    /**
     * Locale detected from route
     */
    routeLocale: string;
};
/**
 * Returns a localized path to redirect to, or an empty string if no redirection should occur
 *
 * @param inMiddleware - whether this is called during navigation middleware
 */
export declare function detectRedirect({ to, nuxtApp, from, locale, routeLocale }: DetectRedirectOptions, inMiddleware?: boolean): string;
type NavigateArgs = {
    nuxt: NuxtApp;
    redirectPath: string;
    locale: string;
    route: CompatRoute;
};
export declare function navigate({ nuxt, locale, route, redirectPath }: NavigateArgs, enableNavigate?: boolean): Promise<string | false | void | import("vue-router").RouteLocationAsRelativeGeneric | import("vue-router").RouteLocationAsPathGeneric | import("vue-router").NavigationFailure>;
export declare function prefixable(currentLocale: string, defaultLocale: string, strategy: Strategies): boolean;
export declare function extendBaseUrl(ctx: NuxtApp): () => string;
export declare function createNuxtI18nDev(): {
    resetI18nProperties: (locale?: string) => Promise<void>;
};
export declare function toArray<T>(value: T | T[]): T[];
export {};
