import { deepCopy, isArray, isFunction, isObject } from "@intlify/shared";
import { vueI18nConfigs, localeLoaders, nuxtI18nOptions, normalizedLocales } from "#internal/i18n/options.mjs";
import { dtsFile } from "#internal/i18n-type-generation-options";
import { loadLocale, loadVueI18nOptions } from "../messages.js";
import { nuxtMock } from "./utils.js";
import { writeFile } from "fs/promises";
export default async () => {
  const targetLocales = [];
  if (nuxtI18nOptions.experimental.typedOptionsAndMessages === "default" && nuxtI18nOptions.defaultLocale != null) {
    targetLocales.push(nuxtI18nOptions.defaultLocale);
  } else if (nuxtI18nOptions.experimental.typedOptionsAndMessages === "all") {
    targetLocales.push(...normalizedLocales.map((x) => x.code));
  }
  const merged = {
    messages: {},
    datetimeFormats: {},
    numberFormats: {}
  };
  const vueI18nConfig = await loadVueI18nOptions(vueI18nConfigs, nuxtMock);
  for (const locale of targetLocales) {
    deepCopy(vueI18nConfig.messages?.[locale] || {}, merged.messages);
    deepCopy(vueI18nConfig.numberFormats?.[locale] || {}, merged.numberFormats);
    deepCopy(vueI18nConfig.datetimeFormats?.[locale] || {}, merged.datetimeFormats);
  }
  const loaderPromises = [];
  for (const locale in localeLoaders) {
    if (!targetLocales.includes(locale)) continue;
    async function loader() {
      const setter = (_, message) => {
        deepCopy(message, merged.messages);
      };
      await loadLocale(locale, localeLoaders, setter, nuxtMock);
    }
    loaderPromises.push(loader());
  }
  await Promise.all(loaderPromises);
  await writeFile(dtsFile, generateTypeCode(merged), "utf-8");
};
function generateInterface(obj, indentLevel = 1) {
  const indent = "  ".repeat(indentLevel);
  let str = "";
  for (const key in obj) {
    if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;
    if (isObject(obj[key]) && obj[key] !== null && !isArray(obj[key])) {
      str += `${indent}"${key}": {
`;
      str += generateInterface(obj[key], indentLevel + 1);
      str += `${indent}};
`;
    } else {
      let propertyType = isArray(obj[key]) ? "unknown[]" : typeof obj[key];
      if (isFunction(propertyType)) {
        propertyType = "() => string";
      }
      str += `${indent}"${key}": ${propertyType};
`;
    }
  }
  return str;
}
function generateTypeCode(res) {
  return `// generated by @nuxtjs/i18n
import type { DateTimeFormatOptions, NumberFormatOptions, SpecificNumberFormatOptions, CurrencyNumberFormatOptions } from '@intlify/core'

interface GeneratedLocaleMessage {
  ${generateInterface(res.messages || {}).trim()}
}

interface GeneratedDateTimeFormat {
  ${Object.keys(res.datetimeFormats || {}).map((k) => `${k}: DateTimeFormatOptions;`).join(`
  `)}
}

interface GeneratedNumberFormat {
  ${Object.entries(res.numberFormats || {}).map(([k]) => `${k}: NumberFormatOptions;`).join(`
  `)}
}

declare module 'vue-i18n' {
  export interface DefineLocaleMessage extends GeneratedLocaleMessage {}
  export interface DefineDateTimeFormat extends GeneratedDateTimeFormat {}
  export interface DefineNumberFormat extends GeneratedNumberFormat {}
}

declare module '@intlify/core' {
  export interface DefineCoreLocaleMessage extends GeneratedLocaleMessage {}
}

export {}`;
}
