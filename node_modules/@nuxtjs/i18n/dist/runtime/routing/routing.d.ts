import { type CommonComposableOptions } from '../utils.js';
import type { Locale } from 'vue-i18n';
import type { RouteLocationRaw, RouteMap } from 'vue-router';
import type { CompatRoute, RouteLocationGenericPath } from '../types.js';
/**
 * Returns base name of current (if argument not provided) or passed in route.
 *
 * @remarks
 * Base name is name of the route without locale suffix and other metadata added by nuxt i18n module
 */
export declare function getRouteBaseName<Name extends keyof RouteMap = keyof RouteMap>(common: CommonComposableOptions, route: Name | RouteLocationGenericPath | null): string | undefined;
/**
 * Resolves a localized path of the passed in route.
 */
export declare function localePath(common: CommonComposableOptions, route: RouteLocationRaw, locale?: Locale): string;
/**
 * Resolves a localized variant of the passed route.
 */
export declare function localeRoute(common: CommonComposableOptions, route: RouteLocationRaw, locale?: Locale): import("vue-router").RouteLocationResolvedGeneric | undefined;
export declare function resolveRoute(common: CommonComposableOptions, route: RouteLocationRaw, locale?: Locale): import("vue-router").RouteLocationResolvedGeneric | null | undefined;
/**
 * Resolve the localized path of the current route.
 */
export declare function switchLocalePath(common: CommonComposableOptions, locale: Locale, _route?: CompatRoute): string;
