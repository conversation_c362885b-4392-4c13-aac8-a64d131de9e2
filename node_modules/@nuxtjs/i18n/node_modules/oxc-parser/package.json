{"name": "oxc-parser", "version": "0.70.0", "main": "index.js", "browser": "wasm.mjs", "engines": {"node": ">=14.0.0"}, "description": "Oxc Parser Node API", "keywords": ["oxc", "parser"], "author": "Boshen and oxc contributors", "license": "MIT", "homepage": "https://oxc.rs", "bugs": "https://github.com/oxc-project/oxc/issues", "repository": {"type": "git", "url": "https://github.com/oxc-project/oxc.git", "directory": "napi/parser"}, "funding": {"url": "https://github.com/sponsors/Boshen"}, "files": ["index.d.ts", "index.js", "wrap.cjs", "wrap.mjs", "wasm.mjs", "bindings.js", "webcontainer-fallback.js", "generated/deserialize/js.js", "generated/deserialize/ts.js"], "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "dependencies": {"@oxc-project/types": "^0.70.0"}, "devDependencies": {"@codspeed/vitest-plugin": "^4.0.0", "@napi-rs/wasm-runtime": "^0.2.7", "@vitest/browser": "3.1.3", "esbuild": "^0.25.0", "playwright": "^1.51.0", "vitest": "3.1.3", "typescript": "5.8.3"}, "napi": {"binaryName": "parser", "packageName": "@oxc-parser/binding", "targets": ["x86_64-pc-windows-msvc", "aarch64-pc-windows-msvc", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl", "x86_64-unknown-freebsd", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-unknown-linux-gnueabihf", "armv7-unknown-linux-musleabihf", "s390x-unknown-linux-gnu", "riscv64gc-unknown-linux-gnu", "x86_64-apple-darwin", "aarch64-apple-darwin", "wasm32-wasip1-threads"], "wasm": {"browser": {"fs": false}}, "dtsHeaderFile": "header.js"}, "optionalDependencies": {"@oxc-parser/binding-win32-x64-msvc": "0.70.0", "@oxc-parser/binding-win32-arm64-msvc": "0.70.0", "@oxc-parser/binding-linux-x64-gnu": "0.70.0", "@oxc-parser/binding-linux-x64-musl": "0.70.0", "@oxc-parser/binding-freebsd-x64": "0.70.0", "@oxc-parser/binding-linux-arm64-gnu": "0.70.0", "@oxc-parser/binding-linux-arm64-musl": "0.70.0", "@oxc-parser/binding-linux-arm-gnueabihf": "0.70.0", "@oxc-parser/binding-linux-arm-musleabihf": "0.70.0", "@oxc-parser/binding-linux-s390x-gnu": "0.70.0", "@oxc-parser/binding-linux-riscv64-gnu": "0.70.0", "@oxc-parser/binding-darwin-x64": "0.70.0", "@oxc-parser/binding-darwin-arm64": "0.70.0", "@oxc-parser/binding-wasm32-wasi": "0.70.0"}, "scripts": {"build-dev": "napi build --no-dts-cache --platform --js bindings.js", "build": "pnpm run build-dev --features allocator --release", "postbuild-dev": "node patch.mjs", "build-wasi": "pnpm run build-dev --release --target wasm32-wasip1-threads", "build-npm-dir": "rm -rf npm-dir && napi create-npm-dirs --npm-dir npm-dir && pnpm napi artifacts --npm-dir npm-dir --output-dir .", "build-browser-bundle": "node build-browser-bundle.mjs", "test": "tsc && pnpm run test-node run", "test-node": "vitest --dir ./test", "test-browser": "vitest -c vitest.config.browser.mts", "bench": "vitest bench --run ./bench.bench.mjs"}}