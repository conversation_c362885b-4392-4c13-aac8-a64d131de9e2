/* auto-generated by NAPI-RS */
/* eslint-disable */

export * from '@oxc-project/types';
export declare class ParseResult {
  get program(): import("@oxc-project/types").Program
  get module(): EcmaScriptModule
  get comments(): Array<Comment>
  get errors(): Array<OxcError>
}

export interface Comment {
  type: 'Line' | 'Block'
  value: string
  start: number
  end: number
}

export interface DynamicImport {
  start: number
  end: number
  moduleRequest: Span
}

export interface EcmaScriptModule {
  /**
   * Has ESM syntax.
   *
   * i.e. `import` and `export` statements, and `import.meta`.
   *
   * Dynamic imports `import('foo')` are ignored since they can be used in non-ESM files.
   */
  hasModuleSyntax: boolean
  /** Import statements. */
  staticImports: Array<StaticImport>
  /** Export statements. */
  staticExports: Array<StaticExport>
  /** Dynamic import expressions. */
  dynamicImports: Array<DynamicImport>
  /** Span positions` of `import.meta` */
  importMetas: Array<Span>
}

export interface ErrorLabel {
  message?: string
  start: number
  end: number
}

export interface ExportExportName {
  kind: ExportExportNameKind
  name?: string
  start?: number
  end?: number
}

export declare const enum ExportExportNameKind {
  /** `export { name } */
  Name = 'Name',
  /** `export default expression` */
  Default = 'Default',
  /** `export * from "mod" */
  None = 'None'
}

export interface ExportImportName {
  kind: ExportImportNameKind
  name?: string
  start?: number
  end?: number
}

export declare const enum ExportImportNameKind {
  /** `export { name } */
  Name = 'Name',
  /** `export * as ns from "mod"` */
  All = 'All',
  /** `export * from "mod"` */
  AllButDefault = 'AllButDefault',
  /** Does not have a specifier. */
  None = 'None'
}

export interface ExportLocalName {
  kind: ExportLocalNameKind
  name?: string
  start?: number
  end?: number
}

export declare const enum ExportLocalNameKind {
  /** `export { name } */
  Name = 'Name',
  /** `export default expression` */
  Default = 'Default',
  /**
   * If the exported value is not locally accessible from within the module.
   * `export default function () {}`
   */
  None = 'None'
}

/**
 * Get offset within a `Uint8Array` which is aligned on 4 GiB.
 *
 * Does not check that the offset is within bounds of `buffer`.
 * To ensure it always is, provide a `Uint8Array` of at least 4 GiB size.
 */
export declare function getBufferOffset(buffer: Uint8Array): number

export interface ImportName {
  kind: ImportNameKind
  name?: string
  start?: number
  end?: number
}

export declare const enum ImportNameKind {
  /** `import { x } from "mod"` */
  Name = 'Name',
  /** `import * as ns from "mod"` */
  NamespaceObject = 'NamespaceObject',
  /** `import defaultExport from "mod"` */
  Default = 'Default'
}

export interface OxcError {
  severity: Severity
  message: string
  labels: Array<ErrorLabel>
  helpMessage?: string
  codeframe?: string
}

/**
 * Parse asynchronously.
 *
 * Note: This function can be slower than `parseSync` due to the overhead of spawning a thread.
 */
export declare function parseAsync(filename: string, sourceText: string, options?: ParserOptions | undefined | null): Promise<ParseResult>

export interface ParserOptions {
  /** Treat the source text as `js`, `jsx`, `ts`, or `tsx`. */
  lang?: 'js' | 'jsx' | 'ts' | 'tsx'
  /** Treat the source text as `script` or `module` code. */
  sourceType?: 'script' | 'module' | 'unambiguous' | undefined
  /**
   * Return an AST which includes TypeScript-related properties, or excludes them.
   *
   * `'js'` is default for JS / JSX files.
   * `'ts'` is default for TS / TSX files.
   * The type of the file is determined from `lang` option, or extension of provided `filename`.
   */
  astType?: 'js' | 'ts'
  /**
   * Emit `ParenthesizedExpression` and `TSParenthesizedType` in AST.
   *
   * If this option is true, parenthesized expressions are represented by
   * (non-standard) `ParenthesizedExpression` and `TSParenthesizedType` nodes that
   * have a single `expression` property containing the expression inside parentheses.
   *
   * @default true
   */
  preserveParens?: boolean
  /**
   * Produce semantic errors with an additional AST pass.
   * Semantic errors depend on symbols and scopes, where the parser does not construct.
   * This adds a small performance overhead.
   *
   * @default false
   */
  showSemanticErrors?: boolean
}

/** Parse synchronously. */
export declare function parseSync(filename: string, sourceText: string, options?: ParserOptions | undefined | null): ParseResult

/**
 * Parses AST into provided `Uint8Array` buffer.
 *
 * Source text must be written into the start of the buffer, and its length (in UTF-8 bytes)
 * provided as `source_len`.
 *
 * This function will parse the source, and write the AST into the buffer, starting at the end.
 *
 * It also writes to the very end of the buffer the offset of `Program` within the buffer.
 *
 * Caller can deserialize data from the buffer on JS side.
 *
 * # SAFETY
 *
 * Caller must ensure:
 * * Source text is written into start of the buffer.
 * * Source text's UTF-8 byte length is `source_len`.
 * * The 1st `source_len` bytes of the buffer comprises a valid UTF-8 string.
 *
 * If source text is originally a JS string on JS side, and converted to a buffer with
 * `Buffer.from(str)` or `new TextEncoder().encode(str)`, this guarantees it's valid UTF-8.
 *
 * # Panics
 *
 * Panics if source text is too long, or AST takes more memory than is available in the buffer.
 */
export declare function parseSyncRaw(filename: string, buffer: Uint8Array, sourceLen: number, options?: ParserOptions | undefined | null): void

/** Returns `true` if raw transfer is supported on this platform. */
export declare function rawTransferSupported(): boolean

export declare const enum Severity {
  Error = 'Error',
  Warning = 'Warning',
  Advice = 'Advice'
}

export interface Span {
  start: number
  end: number
}

export interface StaticExport {
  start: number
  end: number
  entries: Array<StaticExportEntry>
}

export interface StaticExportEntry {
  start: number
  end: number
  moduleRequest?: ValueSpan
  /** The name under which the desired binding is exported by the module`. */
  importName: ExportImportName
  /** The name used to export this binding by this module. */
  exportName: ExportExportName
  /** The name that is used to locally access the exported value from within the importing module. */
  localName: ExportLocalName
  /**
   * Whether the export is a TypeScript `export type`.
   *
   * Examples:
   *
   * ```ts
   * export type * from 'mod';
   * export type * as ns from 'mod';
   * export type { foo };
   * export { type foo }:
   * export type { foo } from 'mod';
   * ```
   */
  isType: boolean
}

export interface StaticImport {
  /** Start of import statement. */
  start: number
  /** End of import statement. */
  end: number
  /**
   * Import source.
   *
   * ```js
   * import { foo } from "mod";
   * //                   ^^^
   * ```
   */
  moduleRequest: ValueSpan
  /**
   * Import specifiers.
   *
   * Empty for `import "mod"`.
   */
  entries: Array<StaticImportEntry>
}

export interface StaticImportEntry {
  /**
   * The name under which the desired binding is exported by the module.
   *
   * ```js
   * import { foo } from "mod";
   * //       ^^^
   * import { foo as bar } from "mod";
   * //       ^^^
   * ```
   */
  importName: ImportName
  /**
   * The name that is used to locally access the imported value from within the importing module.
   * ```js
   * import { foo } from "mod";
   * //       ^^^
   * import { foo as bar } from "mod";
   * //              ^^^
   * ```
   */
  localName: ValueSpan
  /**
   * Whether this binding is for a TypeScript type-only import.
   *
   * `true` for the following imports:
   * ```ts
   * import type { foo } from "mod";
   * import { type foo } from "mod";
   * ```
   */
  isType: boolean
}

export interface ValueSpan {
  value: string
  start: number
  end: number
}
