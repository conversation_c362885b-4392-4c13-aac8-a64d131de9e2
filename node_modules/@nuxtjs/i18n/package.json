{"name": "@nuxtjs/i18n", "type": "module", "version": "9.5.6", "packageManager": "pnpm@10.6.5", "description": "Internationalization for Nuxt", "contributors": [{"name": "<PERSON>lan<PERSON> (@paulgv)"}, {"name": "<PERSON><PERSON><PERSON> (@rchl)"}, {"name": "<PERSON><PERSON><PERSON> (@kazupon)"}, {"name": "<PERSON><PERSON> (@BobbieGoede)"}], "license": "MIT", "homepage": "https://i18n.nuxtjs.org", "funding": "https://github.com/sponsors/bobbiegoede", "repository": {"type": "git", "url": "git+https://github.com/nuxt-modules/i18n.git"}, "bugs": {"url": "https://github.com/nuxt-modules/i18n/issues"}, "keywords": ["nuxt", "i18n", "internationalization", "nuxt-module"], "exports": {".": {"types": "./dist/types.d.mts", "default": "./dist/module.mjs"}, "./package.json": "./package.json"}, "imports": {"#i18n": "./dist/runtime/composables/index.js"}, "main": "./dist/module.mjs", "types": "./dist/types.d.ts", "files": ["dist", "internals.d.ts"], "scripts": {"prepare": "git config --local core.hooksPath .githooks", "build": "nuxt-module-build prepare && nuxt-module-build build .", "stub": "nuxt-module-build build --stub .", "prepack": "pnpm build", "release": "bumpp --commit \"release: v%s\" --push --tag", "changelog": "gh-changelogen --repo=nuxt-community/i18n-module", "dev": "pnpm dev:prepare && pnpm build --sourcemap && nuxi dev playground", "dev:prepare": "nuxt-module-build build --stub && nuxt-module-build prepare", "dev:build": "nuxi build playground", "dev:generate": "nuxi generate playground", "dev:preview": "nuxi preview playground", "dev:clean": "rm -rf playground/.nuxt playground/dist playground/.output", "docs:dev": "nuxi dev docs", "docs:build": "nuxi build docs", "docs:generate": "nuxi prepare && nuxi generate docs", "docs:preview": "nuxi preview docs", "fix": "run-s \"fix:*\"", "fix:prettier": "prettier . --write", "fix:eslint": "pnpm lint:eslint --fix", "lint": "run-p \"lint:* {@}\" --", "lint:prettier": "prettier . --check", "lint:eslint": "eslint .", "lint:knip": "pnpm knip --workspace .", "test": "pnpm build && run-s test:types test:unit test:spec", "test:types": "tsc --noEmit", "test:unit": "vitest run test -c vitest.config.test.ts", "test:spec": "vitest run specs"}, "pnpm": {"overrides": {"@nuxtjs/i18n": "link:.", "unimport": "4.1.2", "rollup": "4.37.0"}}, "dependencies": {"@intlify/h3": "^0.6.1", "@intlify/shared": "^10.0.7", "@intlify/unplugin-vue-i18n": "^6.0.8", "@intlify/utils": "^0.13.0", "@miyaneee/rollup-plugin-json5": "^1.2.0", "@nuxt/kit": "^3.17.2", "@oxc-parser/wasm": "^0.60.0", "@rollup/plugin-yaml": "^4.1.2", "@vue/compiler-sfc": "^3.5.13", "debug": "^4.4.0", "defu": "^6.1.4", "esbuild": "^0.25.1", "estree-walker": "^3.0.3", "h3": "^1.15.1", "knitwork": "^1.2.0", "magic-string": "^0.30.17", "mlly": "^1.7.4", "oxc-parser": "^0.70.0", "pathe": "^2.0.3", "typescript": "^5.6.2", "ufo": "^1.5.4", "unplugin": "^2.2.2", "unplugin-vue-router": "^0.12.0", "vue-i18n": "^10.0.7", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/types": "^7.27.0", "@eslint/js": "9.23.0", "@nuxt/module-builder": "^0.8.4", "@nuxt/schema": "^3.17.2", "@rspack/core": "^1.2.8", "@types/debug": "^4.1.12", "@types/estree": "^1.0.7", "@types/jsdom": "^21.1.7", "bumpp": "^10.1.0", "changelogithub": "^13.13.0", "consola": "^3.4.2", "eslint": "^9.23.0", "eslint-config-prettier": "^9.1.0", "get-port-please": "^3.1.2", "gh-changelogen": "^0.2.8", "globals": "^16.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "knip": "^5.46.1", "lint-staged": "^15.5.0", "nitropack": "^2.11.7", "npm-run-all2": "^6.2.6", "nuxt": "^3.17.2", "ofetch": "^1.4.1", "playwright-core": "^1.51.1", "prettier": "^3.5.3", "scule": "^1.3.0", "tinyexec": "^1.0.0", "ts-essentials": "^9.4.2", "typescript-eslint": "^7.14.0", "unbuild": "^2.0.0", "undici": "^7.5.0", "vitest": "^3.0.9", "vue": "^3.5.13", "webpack": "^5.98.0"}, "lint-staged": {"*.{json,md,yml}": ["prettier --write"], "*.{js,vue}": ["prettier --write", "eslint --fix"], "*.ts?(x)": ["prettier --parser=typescript --write", "eslint --fix"]}, "publishConfig": {"access": "public"}, "engines": {"node": ">=18.12.0"}}