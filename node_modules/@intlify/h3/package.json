{"name": "@intlify/h3", "version": "0.6.1", "description": "Internationalization middleware & utilities for h3", "keywords": ["i18n", "h3", "internationalization", "intlify", "utilities", "middleware"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "funding": "https://github.com/sponsors/kazupon", "bugs": {"url": "https://github.com/intlify/h3/issues"}, "repository": {"type": "git", "url": "git+https://github.com/intlify/h3.git"}, "homepage": "https://github.com/intlify/h3#readme", "files": ["dist"], "publishConfig": {"access": "public"}, "engines": {"node": ">= 18"}, "sideEffects": false, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "scripts": {"prepare": "git config --local core.hooksPath .githooks", "changelog": "gh-changelogen --repo=intlify/h3", "release": "bumpp --commit \"release: v%s\" --push --tag", "lint": "deno lint", "format": "deno fmt", "build": "unbuild", "play:basic": "bun run ./playground/basic/index.ts", "test": "npm run test:type && npm run test:unit", "test:type": "vitest run --typecheck", "test:unit": "vitest run", "test:coverage": "npm test -- --reporter verbose --coverage"}, "lint-staged": {"*.{js,ts,jsx,tsx,json,jsonc}": ["deno fmt"], "*.{js,ts,jsx,tsx}": ["deno lint"]}, "devDependencies": {"@types/node": "^20.6.0", "@types/supertest": "^2.0.12", "@vitest/coverage-v8": "^1.3.0", "bumpp": "^9.2.0", "gh-changelogen": "^0.2.8", "h3": "^1.11.0", "lint-staged": "^15.0.2", "supertest": "^6.3.3", "typescript": "^5.3.0", "unbuild": "^2.0.0", "vitest": "^1.3.0"}, "dependencies": {"@intlify/core": "^10.0.3", "@intlify/utils": "^0.13.0"}, "packageManager": "npm@9.5.1+sha512.33350b9bd78458fb8fc879910718dc2a6e3b28a6184f582d7953973e53496df75a5cdb69fac3b8cb65f7bf9837ef92ae7440062550d50a816e93b6c59eebaf36"}