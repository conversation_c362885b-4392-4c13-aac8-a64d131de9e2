/*!
  * message-compiler v11.1.7
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var IntlifyMessageCompiler=function(e){"use strict";function t(e,t,n){return{line:e,column:t,offset:n}}function n(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const r=Object.assign,c=e=>"string"==typeof e;function o(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}const s={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,<PERSON>MPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},u={[s.EXPECTED_TOKEN]:"Expected token: '{0}'",[s.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[s.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[s.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[s.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[s.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[s.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[s.EMPTY_PLACEHOLDER]:"Empty placeholder",[s.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[s.INVALID_LINKED_FORMAT]:"Invalid linked format",[s.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[s.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[s.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[s.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[s.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[s.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function a(e,t,n={}){const{domain:r,messages:c,args:o}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}const i=/<\/?[\w\s="/.':;#-\/]+>/,l=" ",E="\r",f="\n",L=String.fromCharCode(8232),d=String.fromCharCode(8233);function N(e){const t=e;let n=0,r=1,c=1,o=0;const s=e=>t[e]===E&&t[e+1]===f,u=e=>t[e]===d,a=e=>t[e]===L,i=e=>s(e)||(e=>t[e]===f)(e)||u(e)||a(e),l=e=>s(e)||u(e)||a(e)?f:t[e];function N(){return o=0,i(n)&&(r++,c=0),s(n)&&n++,n++,c++,t[n]}return{index:()=>n,line:()=>r,column:()=>c,peekOffset:()=>o,charAt:l,currentChar:()=>l(n),currentPeek:()=>l(n+o),next:N,peek:function(){return s(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,c=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)N();o=0}}}const _=void 0,p="'";function C(e,r={}){const c=!1!==r.location,o=N(e),u=()=>o.index(),a=()=>t(o.line(),o.column(),o.index()),i=a(),E=u(),L={currentType:13,offset:E,startLoc:i,endLoc:i,lastType:13,lastOffset:E,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},d=()=>L,{onError:C}=r;function A(e,t,r){e.endLoc=a(),e.currentType=t;const o={type:t};return c&&(o.loc=n(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const T=e=>A(e,13);function k(e,t){return e.currentChar()===t?(e.next(),t):(s.EXPECTED_TOKEN,a(),"")}function I(e){let t="";for(;e.currentPeek()===l||e.currentPeek()===f;)t+=e.currentPeek(),e.peek();return t}function h(e){const t=I(e);return e.skipToPeek(),t}function P(e){if(e===_)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function S(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=function(e){if(e===_)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function y(e){I(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function D(e,t=!0){const n=(t=!1,r="")=>{const c=e.currentPeek();return"{"===c?t:"@"!==c&&c?"|"===c?!(r===l||r===f):c===l?(e.peek(),n(!0,l)):c!==f||(e.peek(),n(!0,f)):t},r=n();return t&&e.resetPeek(),r}function O(e,t){const n=e.currentChar();return n===_?_:t(n)?(e.next(),n):null}function m(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function b(e){return O(e,m)}function U(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function x(e){return O(e,U)}function R(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function v(e){return O(e,R)}function M(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function g(e){return O(e,M)}function X(e){let t="",n="";for(;t=v(e);)n+=t;return n}function Y(e){return e!==p&&e!==f}function K(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return w(e,t,4);case"U":return w(e,t,6);default:return s.UNKNOWN_ESCAPE_SEQUENCE,a(),""}}function w(e,t,n){k(e,t);let r="";for(let c=0;c<n;c++){const t=g(e);if(!t){s.INVALID_UNICODE_ESCAPE_SEQUENCE,a(),e.currentChar();break}r+=t}return`\\${t}${r}`}function H(e){return"{"!==e&&"}"!==e&&e!==l&&e!==f}function G(e){h(e);const t=k(e,"|");return h(e),t}function $(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(s.NOT_ALLOW_NEST_PLACEHOLDER,a()),e.next(),n=A(t,2,"{"),h(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(s.EMPTY_PLACEHOLDER,a()),e.next(),n=A(t,3,"}"),t.braceNest--,t.braceNest>0&&h(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(s.UNTERMINATED_CLOSING_BRACE,a()),n=B(e,t)||T(t),t.braceNest=0,n;default:{let r=!0,c=!0,o=!0;if(y(e))return t.braceNest>0&&(s.UNTERMINATED_CLOSING_BRACE,a()),n=A(t,1,G(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return s.UNTERMINATED_CLOSING_BRACE,a(),t.braceNest=0,V(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=P(e.currentPeek());return e.resetPeek(),r}(e,t))return n=A(t,4,function(e){h(e);let t="",n="";for(;t=x(e);)n+=t;return e.currentChar()===_&&(s.UNTERMINATED_CLOSING_BRACE,a()),n}(e)),h(e),n;if(c=S(e,t))return n=A(t,5,function(e){h(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${X(e)}`):t+=X(e),e.currentChar()===_&&(s.UNTERMINATED_CLOSING_BRACE,a()),t}(e)),h(e),n;if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=e.currentPeek()===p;return e.resetPeek(),r}(e,t))return n=A(t,6,function(e){h(e),k(e,"'");let t="",n="";for(;t=O(e,Y);)n+="\\"===t?K(e):t;const r=e.currentChar();return r===f||r===_?(s.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,a(),r===f&&(e.next(),k(e,"'")),n):(k(e,"'"),n)}(e)),h(e),n;if(!r&&!c&&!o)return n=A(t,12,function(e){h(e);let t="",n="";for(;t=O(e,H);)n+=t;return n}(e)),s.INVALID_TOKEN_IN_PLACEHOLDER,a(),n.value,h(e),n;break}}return n}function B(e,t){const{currentType:n}=t;let r=null;const c=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||c!==f&&c!==l||(s.INVALID_LINKED_FORMAT,a()),c){case"@":return e.next(),r=A(t,7,"@"),t.inLinked=!0,r;case".":return h(e),e.next(),A(t,8,".");case":":return h(e),e.next(),A(t,9,":");default:return y(e)?(r=A(t,1,G(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;I(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;I(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(h(e),B(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;I(e);const r=P(e.currentPeek());return e.resetPeek(),r}(e,t)?(h(e),A(t,11,function(e){let t="",n="";for(;t=b(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?P(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===l||!t)&&(t===f?(e.peek(),r()):D(e,!1))},c=r();return e.resetPeek(),c}(e,t)?(h(e),"{"===c?$(e,t)||r:A(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===l?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&(s.INVALID_LINKED_FORMAT,a()),t.braceNest=0,t.inLinked=!1,V(e,t))}}function V(e,t){let n={type:13};if(t.braceNest>0)return $(e,t)||T(t);if(t.inLinked)return B(e,t)||T(t);switch(e.currentChar()){case"{":return $(e,t)||T(t);case"}":return s.UNBALANCED_CLOSING_BRACE,a(),e.next(),A(t,3,"}");case"@":return B(e,t)||T(t);default:if(y(e))return n=A(t,1,G(e)),t.braceNest=0,t.inLinked=!1,n;if(D(e))return A(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===l||n===f)if(D(e))t+=n,e.next();else{if(y(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:r}=L;return L.lastType=e,L.lastOffset=t,L.lastStartLoc=n,L.lastEndLoc=r,L.offset=u(),L.startLoc=a(),o.currentChar()===_?A(L,13):V(o,L)},currentOffset:u,currentPosition:a,context:d}}const A="parser",T=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function k(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function I(e={}){const t=!1!==e.location,{onError:n}=e;function c(e,n,r){const c={type:e};return t&&(c.start=n,c.end=n,c.loc={start:r,end:r}),c}function o(e,n,r,c){t&&(e.end=n,e.loc&&(e.loc.end=r))}function u(e,t){const n=e.context(),r=c(3,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(5,r,s);return u.index=parseInt(t,10),e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function i(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(4,r,s);return u.key=t,e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function l(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(9,r,s);return u.value=t.replace(T,k),e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function E(e){const t=e.context(),n=c(6,t.offset,t.startLoc);let r=e.nextToken();if(8===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:u}=n,a=c(8,r,u);return 11!==t.type?(s.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,a.value="",o(a,r,u),{nextConsumeToken:t,node:a}):(null==t.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,h(t)),a.value=t.value||"",o(a,e.currentOffset(),e.currentPosition()),{node:a})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(9!==r.type&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 10:null==r.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(r)),n.key=function(e,t){const n=e.context(),r=c(7,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 4:null==r.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(r)),n.key=i(e,r.value||"");break;case 5:null==r.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(r)),n.key=a(e,r.value||"");break;case 6:null==r.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(r)),n.key=l(e,r.value||"");break;default:{s.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const u=e.context(),a=c(7,u.offset,u.startLoc);return a.value="",o(a,u.offset,u.startLoc),n.key=a,o(n,u.offset,u.startLoc),{nextConsumeToken:r,node:n}}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=c(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null;do{const c=r||e.nextToken();switch(r=null,c.type){case 0:null==c.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(c)),n.items.push(u(e,c.value||""));break;case 5:null==c.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(c)),n.items.push(a(e,c.value||""));break;case 4:null==c.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(c)),n.items.push(i(e,c.value||""));break;case 6:null==c.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,h(c)),n.items.push(l(e,c.value||""));break;case 7:{const t=E(e);n.items.push(t.node),r=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function L(e){const t=e.context(),{offset:n,startLoc:r}=t,u=f(e);return 13===t.currentType?u:function(e,t,n,r){const u=e.context();let a=0===r.items.length;const i=c(1,t,n);i.cases=[],i.cases.push(r);do{const t=f(e);a||(a=0===t.items.length),i.cases.push(t)}while(13!==u.currentType);return a&&s.MUST_HAVE_MESSAGES_IN_PLURAL,o(i,e.currentOffset(),e.currentPosition()),i}(e,n,r,u)}return{parse:function(n){const u=C(n,r({},e)),a=u.context(),i=c(0,a.offset,a.startLoc);return t&&i.loc&&(i.loc.source=n),i.body=L(u),e.onCacheKey&&(i.cacheKey=e.onCacheKey(n)),13!==a.currentType&&(s.UNEXPECTED_LEXICAL_ANALYSIS,a.lastStartLoc,n[a.offset]),o(i,u.currentOffset(),u.currentPosition()),i}}}function h(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function P(e,t){for(let n=0;n<e.length;n++)S(e[n],t)}function S(e,t){switch(e.type){case 1:P(e.cases,t),t.helper("plural");break;case 2:P(e.items,t);break;case 6:S(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function y(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&S(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function D(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=o(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function O(e){switch(e.t=e.type,e.type){case 0:{const t=e;O(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)O(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)O(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;O(t.key),t.k=t.key,delete t.key,t.modifier&&(O(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function m(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?m(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const c=t.cases.length;for(let n=0;n<c&&(m(e,t.cases[n]),n!==c-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const c=t.items.length;for(let o=0;o<c&&(m(e,t.items[o]),o!==c-1);o++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),m(e,t.key),t.modifier?(e.push(", "),m(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}return e.COMPILE_ERROR_CODES_EXTEND_POINT=17,e.CompileErrorCodes=s,e.ERROR_DOMAIN=A,e.LOCATION_STUB={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}},e.baseCompile=function(e,t={}){const n=r({},t),s=!!n.jit,u=!!n.minify,a=null==n.optimize||n.optimize,i=I(n).parse(e);return s?(a&&function(e){const t=e.body;2===t.type?D(t):t.cases.forEach((e=>D(e)))}(i),u&&O(i),{ast:i,code:""}):(y(i,n),((e,t={})=>{const n=c(t.mode)?t.mode:"normal",r=c(t.filename)?t.filename:"message.intl",s=!!t.sourceMap,u=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",a=t.needIndent?t.needIndent:"arrow"!==n,i=e.helpers||[],l=function(e,t){const{sourceMap:n,filename:r,breakLineCode:c,needIndent:o}=t,s=!1!==t.location,u={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:c,needIndent:o,indentLevel:0};function a(e,t){u.code+=e}function i(e,t=!0){const n=t?c:"";a(o?n+"  ".repeat(e):n)}return s&&e.loc&&(u.source=e.loc.source),{context:()=>u,push:a,indent:function(e=!0){const t=++u.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--u.indentLevel;e&&i(t)},newline:function(){i(u.indentLevel)},helper:e=>`_${e}`,needIndent:()=>u.needIndent}}(e,{mode:n,filename:r,sourceMap:s,breakLineCode:u,needIndent:a});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(a),i.length>0&&(l.push(`const { ${o(i.map((e=>`${e}: _${e}`)),", ")} } = ctx`),l.newline()),l.push("return "),m(l,e),l.deindent(a),l.push("}"),delete e.helpers;const{code:E,map:f}=l.context();return{ast:e,code:E,map:f?f.toJSON():void 0}})(i,n))},e.createCompileError=a,e.createLocation=n,e.createParser=I,e.createPosition=t,e.defaultOnError=function(e){throw e},e.detectHtmlTag=e=>i.test(e),e.errorMessages=u,e}({});
