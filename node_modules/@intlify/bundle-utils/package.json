{"name": "@intlify/bundle-utils", "type": "module", "description": "Bundle utilities for Intlify project", "version": "10.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/intlify/bundle-tools/issues"}, "peerDependenciesMeta": {"petite-vue-i18n": {"optional": true}, "vue-i18n": {"optional": true}}, "dependencies": {"@intlify/message-compiler": "^11.1.2", "@intlify/shared": "^11.1.2", "acorn": "^8.8.2", "escodegen": "^2.1.0", "estree-walker": "^2.0.2", "jsonc-eslint-parser": "^2.3.0", "mlly": "^1.2.0", "source-map-js": "^1.0.1", "yaml-eslint-parser": "^1.2.2"}, "devDependencies": {"@types/escodegen": "^0.0.10", "@types/estree": "^1.0.0", "unbuild": "^2.0.0"}, "engines": {"node": ">= 18"}, "files": ["lib", "index.mjs"], "homepage": "https://github.com/intlify/bundle-tools/blob/main/packages/bundle-utils/README.md", "license": "MIT", "main": "lib/index.cjs", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.mjs", "require": "./lib/index.cjs"}, "./lib/*": "./lib/*", "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/intlify/bundle-tools.git", "directory": "packages/bundle-utils"}, "scripts": {"build": "unbuild", "clean": "run-p \"clean:*\"", "clean:lib": "rm -rf ./lib", "changelog": "jiti ../../scripts/changelog.ts", "release": "jiti ../../scripts/release.ts"}}