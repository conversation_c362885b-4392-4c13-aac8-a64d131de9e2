{"name": "@intlify/unplugin-vue-i18n", "type": "module", "version": "6.0.8", "description": "unplugin for Vue I18n", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/intlify/bundle-tools/issues"}, "peerDependencies": {"petite-vue-i18n": "*", "vue": "^3.2.25", "vue-i18n": "*"}, "peerDependenciesMeta": {"petite-vue-i18n": {"optional": true}, "vue-i18n": {"optional": true}}, "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "@intlify/bundle-utils": "^10.0.1", "@intlify/shared": "^11.1.2", "@intlify/vue-i18n-extensions": "^8.0.0", "@rollup/pluginutils": "^5.1.0", "@typescript-eslint/scope-manager": "^8.13.0", "@typescript-eslint/typescript-estree": "^8.13.0", "debug": "^4.3.3", "fast-glob": "^3.2.12", "js-yaml": "^4.1.0", "json5": "^2.2.3", "pathe": "^1.0.0", "picocolors": "^1.0.0", "source-map-js": "^1.0.2", "unplugin": "^1.1.0", "vue": "^3.4"}, "devDependencies": {"unbuild": "^2.0.0", "@types/node": "^20.14.8"}, "engines": {"node": ">= 18"}, "files": ["lib", "*.d.ts"], "homepage": "https://github.com/intlify/bundle-tools/blob/main/packages/unplugin-vue-i18n/README.md", "keywords": ["i18n", "plugin", "unplugin", "transform", "webpack", "vite", "vue", "vue-i18n"], "license": "MIT", "main": "lib/index.cjs", "module": "./lib/index.mjs", "types": "./index.d.ts", "exports": {".": {"import": "./lib/index.mjs", "require": "./lib/index.cjs"}, "./vite": {"import": "./lib/vite.mjs", "require": "./lib/vite.cjs"}, "./webpack": {"import": "./lib/webpack.mjs", "require": "./lib/webpack.cjs"}, "./types": {"import": {"types": "./lib/types.d.mts"}, "require": {"types": "./lib/types.d.cts"}}, "./messages": {"types": "./messages.d.ts"}, "./lib/*": "./lib/*", "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/intlify/bundle-tools.git", "directory": "packages/unplugin-vue-i18n"}, "scripts": {"build": "unbuild", "clean": "run-p \"clean:*\"", "clean:lib": "rm -rf ./lib", "changelog": "jiti ../../scripts/changelog.ts", "release": "jiti ../../scripts/release.ts"}}