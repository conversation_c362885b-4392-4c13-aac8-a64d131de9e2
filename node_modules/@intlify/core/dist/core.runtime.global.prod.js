/*!
  * core v10.0.7
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var IntlifyCore=function(e){"use strict";const t=/\{([0-9a-zA-Z]+)\}/g;const n=(e,t,n)=>r({l:e,k:t,s:n}),r=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),a=e=>"number"==typeof e&&isFinite(e),o=e=>"[object Date]"===O(e),l=e=>"[object RegExp]"===O(e),s=e=>C(e)&&0===Object.keys(e).length,i=Object.assign,c=Object.create,u=(e=null)=>c(e);function m(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const f=Object.prototype.hasOwnProperty;function _(e,t){return f.call(e,t)}const E=Array.isArray,g=e=>"function"==typeof e,p=e=>"string"==typeof e,d=e=>"boolean"==typeof e,T=e=>null!==e&&"object"==typeof e,N=e=>T(e)&&g(e.then)&&g(e.catch),A=Object.prototype.toString,O=e=>A.call(e),C=e=>"[object Object]"===O(e);function L(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const h={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16};function b(e,t,n={}){const{domain:r,messages:a,args:o}=n,l=new SyntaxError(String(e));return l.code=e,t&&(l.location=t),l.domain=r,l}function I(e){return T(e)&&0===y(e)&&(_(e,"b")||_(e,"body"))}const R=["b","body"];const S=["c","cases"];const k=["s","static"];const D=["i","items"];const M=["t","type"];function y(e){return v(e,M)}const F=["v","value"];function P(e,t){const n=v(e,F);if(null!=n)return n;throw x(t)}const U=["m","modifier"];const w=["k","key"];function v(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(_(e,n)&&null!=e[n])return e[n]}return n}const W=[...R,...S,...k,...D,...w,...U,...F,...M];function x(e){return new Error(`unhandled node type: ${e}`)}function V(e){return t=>function(e,t){const n=(r=t,v(r,R));var r;if(null==n)throw x(0);if(1===y(n)){const t=function(e){return v(e,S,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,G(e,n)]),[]))}return G(e,n)}(t,e)}function G(e,t){const n=function(e){return v(e,k)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return v(e,D,[])}(t).reduce(((t,n)=>[...t,K(e,n)]),[]);return e.normalize(n)}}function K(e,t){const n=y(t);switch(n){case 3:case 9:case 7:case 8:return P(t,n);case 4:{const r=t;if(_(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(_(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw x(n)}case 5:{const r=t;if(_(r,"i")&&a(r.i))return e.interpolate(e.list(r.i));if(_(r,"index")&&a(r.index))return e.interpolate(e.list(r.index));throw x(n)}case 6:{const n=t,r=function(e){return v(e,U)}(n),a=function(e){const t=v(e,w);if(t)return t;throw x(6)}(n);return e.linked(K(e,a),r?K(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}let $=u();function Y(e,t){{const t=e.cacheKey;if(t){const n=$[t];return n||($[t]=V(e))}return V(e)}}let j=null;const B=H("function:translate");function H(e){return t=>j&&j.emit(e,t)}const X={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23};function z(e,t){return null!=t.locale?Z(t.locale):Z(e.locale)}let J;function Z(e){if(p(e))return e;if(g(e)){if(e.resolvedOnce&&null!=J)return J;if("Function"===e.constructor.name){const t=e();if(N(t))throw Error(X.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return J=t}throw Error(X.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(X.NOT_SUPPORT_LOCALE_TYPE)}function Q(e,t,n){return[...new Set([n,...E(t)?t:T(t)?Object.keys(t):p(t)?[t]:[n]])]}function q(e,t,n){const r=p(n)?n:Ee,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let o=a.__localeChainCache.get(r);if(!o){o=[];let e=[n];for(;E(e);)e=ee(o,e,t);const l=E(t)||!C(t)?t:t.default?t.default:null;e=p(l)?[l]:l,E(e)&&ee(o,e,!1),a.__localeChainCache.set(r,o)}return o}function ee(e,t,n){let r=!0;for(let a=0;a<t.length&&d(r);a++){const o=t[a];p(o)&&(r=te(e,t[a],n))}return r}function te(e,t,n){let r;const a=t.split("-");do{r=ne(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function ne(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(E(n)||C(n))&&n[a]&&(r=n[a])}return r}X.INVALID_ARGUMENT,X.INVALID_DATE_ARGUMENT,X.INVALID_ISO_DATE_ARGUMENT,X.NOT_SUPPORT_NON_STRING_MESSAGE,X.NOT_SUPPORT_LOCALE_PROMISE_VALUE,X.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION,X.NOT_SUPPORT_LOCALE_TYPE;const re=[];re[0]={w:[0],i:[3,0],"[":[4],o:[7]},re[1]={w:[1],".":[2],"[":[4],o:[7]},re[2]={w:[2],i:[3,0],0:[3,0]},re[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},re[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},re[5]={"'":[4,0],o:8,l:[5,0]},re[6]={'"':[4,0],o:8,l:[6,0]};const ae=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function oe(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function le(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,ae.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}function se(e){const t=[];let n,r,a,o,l,s,i,c=-1,u=0,m=0;const f=[];function _(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,a="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===r?r=a:r+=a},f[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===r)return!1;if(r=le(r),!1===r)return!1;f[1]()}};null!==u;)if(c++,n=e[c],"\\"!==n||!_()){if(o=oe(n),i=re[u],l=i[o]||i.l||8,8===l)return;if(u=l[0],void 0!==l[1]&&(s=f[l[1]],s&&(a=n,!1===s())))return;if(7===u)return t}}const ie=new Map;function ce(e,t){return T(e)?e[t]:null}function ue(e,t){if(!T(e))return null;let n=ie.get(t);if(n||(n=se(t),n&&ie.set(t,n)),!n)return null;const r=n.length;let a=e,o=0;for(;o<r;){const e=n[o];if(W.includes(e)&&I(a))return null;const t=a[e];if(void 0===t)return null;if(g(a))return null;a=t,o++}return a}const me={NOT_FOUND_KEY:1,FALLBACK_TO_TRANSLATE:2,CANNOT_FORMAT_NUMBER:3,FALLBACK_TO_NUMBER_FORMAT:4,CANNOT_FORMAT_DATE:5,FALLBACK_TO_DATE_FORMAT:6,EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:7},fe={[me.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[me.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[me.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[me.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[me.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[me.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale.",[me.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]:"This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future."};const _e="10.0.7",Ee="en-US",ge=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let pe,de,Te;function Ne(e){pe=e}function Ae(e){de=e}function Oe(e){Te=e}let Ce=null;let Le=null;let he=0;const be=e=>({[e]:u()});function Ie(e,t,n,r,a){const{missing:o,onWarn:l}=e;if(null!==o){const r=o(e,n,t,a);return p(r)?r:t}return t}function Re(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function Se(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(Re(e,t[r]))return!0;return!1}const ke=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function De(...e){const[t,n,r,l]=e,s=u();let i,c=u();if(p(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(X.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();i=new Date(n);try{i.toISOString()}catch{throw Error(X.INVALID_ISO_DATE_ARGUMENT)}}else if(o(t)){if(isNaN(t.getTime()))throw Error(X.INVALID_DATE_ARGUMENT);i=t}else{if(!a(t))throw Error(X.INVALID_ARGUMENT);i=t}return p(n)?s.key=n:C(n)&&Object.keys(n).forEach((e=>{ke.includes(e)?c[e]=n[e]:s[e]=n[e]})),p(r)?s.locale=r:C(r)&&(c=r),C(l)&&(c=l),[s.key||"",i,s,c]}const Me=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ye(...e){const[t,n,r,o]=e,l=u();let s=u();if(!a(t))throw Error(X.INVALID_ARGUMENT);const i=t;return p(n)?l.key=n:C(n)&&Object.keys(n).forEach((e=>{Me.includes(e)?s[e]=n[e]:l[e]=n[e]})),p(r)?l.locale=r:C(r)&&(s=r),C(o)&&(s=o),[l.key||"",i,l,s]}const Fe=e=>e,Pe=e=>"",Ue="text",we=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}(e),ve=e=>null==e?"":E(e)||C(e)&&e.toString===A?JSON.stringify(e,null,2):String(e);function We(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function xe(e={}){const t=e.locale,n=function(e){const t=a(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(a(e.named.count)||a(e.named.n))?a(e.named.count)?e.named.count:a(e.named.n)?e.named.n:t:t}(e),r=T(e.pluralRules)&&p(t)&&g(e.pluralRules[t])?e.pluralRules[t]:We,o=T(e.pluralRules)&&p(t)&&g(e.pluralRules[t])?We:void 0,l=e.list||[],s=e.named||u();a(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,s);function c(t,n){const r=g(e.messages)?e.messages(t,!!n):!!T(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):Pe)}const m=C(e.processor)&&g(e.processor.normalize)?e.processor.normalize:we,f=C(e.processor)&&g(e.processor.interpolate)?e.processor.interpolate:ve,_={list:e=>l[e],named:e=>s[e],plural:e=>e[r(n,e.length,o)],linked:(t,...n)=>{const[r,a]=n;let o="text",l="";1===n.length?T(r)?(l=r.modifier||l,o=r.type||o):p(r)&&(l=r||l):2===n.length&&(p(r)&&(l=r||l),p(a)&&(o=a||o));const s=c(t,!0)(_),i="vnode"===o&&E(s)&&l?s[0]:s;return l?(u=l,e.modifiers?e.modifiers[u]:Fe)(i,o):i;var u},message:c,type:C(e.processor)&&p(e.processor.type)?e.processor.type:Ue,interpolate:f,normalize:m,values:i(u(),l,s)};return _}const Ve=()=>"",Ge=e=>g(e);function Ke(e,t,n,r,a,o){const{messages:l,onWarn:s,messageResolver:i,localeFallbacker:c}=e,m=c(e,r,n);let f,_=u(),E=null;for(let g=0;g<m.length&&(f=m[g],_=l[f]||u(),null===(E=i(_,t))&&(E=_[t]),!(p(E)||I(E)||Ge(E)));g++)if(!Se(f,m)){const n=Ie(e,t,f,0,"translate");n!==t&&(E=n)}return[E,f,_]}function $e(e,t,r,a,o,l){const{messageCompiler:s,warnHtmlMessage:i}=e;if(Ge(a)){const e=a;return e.locale=e.locale||r,e.key=e.key||t,e}if(null==s){const e=()=>a;return e.locale=r,e.key=t,e}const c=s(a,function(e,t,r,a,o,l){return{locale:t,key:r,warnHtmlMessage:o,onError:e=>{throw l&&l(e),e},onCacheKey:e=>n(t,r,e)}}(0,r,o,0,i,l));return c.locale=r,c.key=t,c.source=a,c}function Ye(...e){const[t,n,r]=e,o=u();if(!(p(t)||a(t)||Ge(t)||I(t)))throw Error(X.INVALID_ARGUMENT);const l=a(t)?String(t):(Ge(t),t);return a(n)?o.plural=n:p(n)?o.default=n:C(n)&&!s(n)?o.named=n:E(n)&&(o.list=n),a(r)?o.plural=r:p(r)?o.default=r:C(r)&&i(o,r),[l,o]}return Ne(Y),Ae(ue),Oe(q),e.AST_NODE_PROPS_KEYS=W,e.CORE_ERROR_CODES_EXTEND_POINT=24,e.CORE_WARN_CODES_EXTEND_POINT=8,e.CompileErrorCodes=h,e.CoreErrorCodes=X,e.CoreWarnCodes=me,e.DATETIME_FORMAT_OPTIONS_KEYS=ke,e.DEFAULT_LOCALE=Ee,e.DEFAULT_MESSAGE_DATA_TYPE=Ue,e.MISSING_RESOLVE_VALUE="",e.NOT_REOSLVED=-1,e.NUMBER_FORMAT_OPTIONS_KEYS=Me,e.VERSION=_e,e.clearCompileCache=function(){$=u()},e.clearDateTimeFormat=function(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}},e.clearNumberFormat=function(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}},e.compile=Y,e.createCompileError=b,e.createCoreContext=function(e={}){const t=g(e.onWarn)?e.onWarn:L,n=p(e.version)?e.version:_e,r=p(e.locale)||g(e.locale)?e.locale:Ee,a=g(r)?Ee:r,o=E(e.fallbackLocale)||C(e.fallbackLocale)||p(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,s=C(e.messages)?e.messages:be(a),c=C(e.datetimeFormats)?e.datetimeFormats:be(a),m=C(e.numberFormats)?e.numberFormats:be(a),f=i(u(),e.modifiers,{upper:(e,t)=>"text"===t&&p(e)?e.toUpperCase():"vnode"===t&&T(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&p(e)?e.toLowerCase():"vnode"===t&&T(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&p(e)?ge(e):"vnode"===t&&T(e)&&"__v_isVNode"in e?ge(e.children):e}),_=e.pluralRules||u(),N=g(e.missing)?e.missing:null,A=!d(e.missingWarn)&&!l(e.missingWarn)||e.missingWarn,O=!d(e.fallbackWarn)&&!l(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,b=!!e.unresolving,I=g(e.postTranslation)?e.postTranslation:null,R=C(e.processor)?e.processor:null,S=!d(e.warnHtmlMessage)||e.warnHtmlMessage,k=!!e.escapeParameter,D=g(e.messageCompiler)?e.messageCompiler:pe,M=g(e.messageResolver)?e.messageResolver:de||ce,y=g(e.localeFallbacker)?e.localeFallbacker:Te||Q,F=T(e.fallbackContext)?e.fallbackContext:void 0,P=e,U=T(P.__datetimeFormatters)?P.__datetimeFormatters:new Map,w=T(P.__numberFormatters)?P.__numberFormatters:new Map,v=T(P.__meta)?P.__meta:{};he++;const W={version:n,cid:he,locale:r,fallbackLocale:o,messages:s,modifiers:f,pluralRules:_,missing:N,missingWarn:A,fallbackWarn:O,fallbackFormat:h,unresolving:b,postTranslation:I,processor:R,warnHtmlMessage:S,escapeParameter:k,messageCompiler:D,messageResolver:M,localeFallbacker:y,fallbackContext:F,onWarn:t,__meta:v};return W.datetimeFormats=c,W.numberFormats=m,W.__datetimeFormatters=U,W.__numberFormatters=w,W},e.createCoreError=function(e){return b(e,null,void 0)},e.createMessageContext=xe,e.datetime=function(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:l}=e,{__datetimeFormatters:c}=e,[u,m,f,_]=De(...t);d(f.missingWarn)?f.missingWarn:e.missingWarn,d(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const E=!!f.part,g=z(e,f),T=l(e,a,g);if(!p(u)||""===u)return new Intl.DateTimeFormat(g,_).format(m);let N,A={},O=null;for(let s=0;s<T.length&&(N=T[s],A=n[N]||{},O=A[u],!C(O));s++)Ie(e,u,N,0,"datetime format");if(!C(O)||!p(N))return r?-1:u;let L=`${N}__${u}`;s(_)||(L=`${L}__${JSON.stringify(_)}`);let h=c.get(L);return h||(h=new Intl.DateTimeFormat(N,i({},O,_)),c.set(L,h)),E?h.formatToParts(m):h.format(m)},e.fallbackWithLocaleChain=q,e.fallbackWithSimple=Q,e.getAdditionalMeta=()=>Ce,e.getDevToolsHook=function(){return j},e.getFallbackContext=()=>Le,e.getLocale=z,e.getWarnMessage=function(e,...n){return function(e,...n){return 1===n.length&&T(n[0])&&(n=n[0]),n&&n.hasOwnProperty||(n={}),e.replace(t,((e,t)=>n.hasOwnProperty(t)?n[t]:""))}(fe[e],...n)},e.handleMissing=Ie,e.initI18nDevTools=function(e,t,n){j&&j.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})},e.isAlmostSameLocale=Re,e.isImplicitFallback=Se,e.isMessageAST=I,e.isMessageFunction=Ge,e.isTranslateFallbackWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.isTranslateMissingWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.number=function(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:l}=e,{__numberFormatters:c}=e,[u,m,f,_]=ye(...t);d(f.missingWarn)?f.missingWarn:e.missingWarn,d(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const E=!!f.part,g=z(e,f),T=l(e,a,g);if(!p(u)||""===u)return new Intl.NumberFormat(g,_).format(m);let N,A={},O=null;for(let s=0;s<T.length&&(N=T[s],A=n[N]||{},O=A[u],!C(O));s++)Ie(e,u,N,0,"number format");if(!C(O)||!p(N))return r?-1:u;let L=`${N}__${u}`;s(_)||(L=`${L}__${JSON.stringify(_)}`);let h=c.get(L);return h||(h=new Intl.NumberFormat(N,i({},O,_)),c.set(L,h)),E?h.formatToParts(m):h.format(m)},e.parse=se,e.parseDateTimeArgs=De,e.parseNumberArgs=ye,e.parseTranslateArgs=Ye,e.registerLocaleFallbacker=Oe,e.registerMessageCompiler=Ne,e.registerMessageResolver=Ae,e.resolveLocale=Z,e.resolveValue=ue,e.resolveWithKeyValue=ce,e.setAdditionalMeta=e=>{Ce=e},e.setDevToolsHook=function(e){j=e},e.setFallbackContext=e=>{Le=e},e.translate=function(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:o,messageCompiler:l,fallbackLocale:s,messages:i}=e,[c,f]=Ye(...t),_=d(f.missingWarn)?f.missingWarn:e.missingWarn,N=d(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn,A=d(f.escapeParameter)?f.escapeParameter:e.escapeParameter,O=!!f.resolvedMessage,C=p(f.default)||d(f.default)?d(f.default)?l?c:()=>c:f.default:n?l?c:()=>c:null,L=n||null!=C&&(p(C)||g(C)),h=z(e,f);A&&function(e){E(e.list)?e.list=e.list.map((e=>p(e)?m(e):e)):T(e.named)&&Object.keys(e.named).forEach((t=>{p(e.named[t])&&(e.named[t]=m(e.named[t]))}))}(f);let[b,R,S]=O?[c,h,i[h]||u()]:Ke(e,c,h,s,N,_),k=b,D=c;if(O||p(k)||I(k)||Ge(k)||L&&(k=C,D=k),!(O||(p(k)||I(k)||Ge(k))&&p(R)))return o?-1:c;let M=!1;const y=Ge(k)?k:$e(e,c,R,k,D,(()=>{M=!0}));if(M)return k;const F=function(e,t,n,r){const{modifiers:o,pluralRules:l,messageResolver:s,fallbackLocale:i,fallbackWarn:c,missingWarn:u,fallbackContext:m}=e,f=(r,a)=>{let o=s(n,r);if(null==o&&(m||a)){const[,,n]=Ke(m||e,r,t,i,c,u);o=s(n,r)}if(p(o)||I(o)){let n=!1;const a=$e(e,r,t,o,r,(()=>{n=!0}));return n?Ve:a}return Ge(o)?o:Ve},_={locale:t,modifiers:o,pluralRules:l,messages:f};e.processor&&(_.processor=e.processor);r.list&&(_.list=r.list);r.named&&(_.named=r.named);a(r.plural)&&(_.pluralIndex=r.plural);return _}(e,R,S,f),P=function(e,t,n){const r=t(n);return r}(0,y,xe(F));return r?r(P,c):P},e.translateDevTools=B,e.updateFallbackLocale=function(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)},e}({});
