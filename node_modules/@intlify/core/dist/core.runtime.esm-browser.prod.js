/*!
  * core v10.0.7
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
const RE_ARGS=/\{([0-9a-zA-Z]+)\}/g;function format$1(e,...t){return 1===t.length&&isObject(t[0])&&(t=t[0]),t&&t.hasOwnProperty||(t={}),e.replace(RE_ARGS,((e,r)=>t.hasOwnProperty(r)?t[r]:""))}const generateFormatCacheKey=(e,t,r)=>friendlyJSONstringify({l:e,k:t,s:r}),friendlyJSONstringify=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),isNumber=e=>"number"==typeof e&&isFinite(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isEmptyObject=e=>isPlainObject(e)&&0===Object.keys(e).length,assign=Object.assign,_create=Object.create,create=(e=null)=>_create(e);function escapeHtml(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(e,t){return hasOwnProperty.call(e,t)}const isArray=Array.isArray,isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>isObject(e)&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),isPlainObject=e=>"[object Object]"===toTypeString(e),toDisplayString=e=>null==e?"":isArray(e)||isPlainObject(e)&&e.toString===objectToString?JSON.stringify(e,null,2):String(e);function join(e,t=""){return e.reduce(((e,r,n)=>0===n?e+r:e+t+r),"")}function warn(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const CompileErrorCodes={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},COMPILE_ERROR_CODES_EXTEND_POINT=17;function createCompileError(e,t,r={}){const{domain:n,messages:a,args:s}=r,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=n,o}function isMessageAST(e){return isObject(e)&&0===resolveType(e)&&(hasOwn(e,"b")||hasOwn(e,"body"))}const PROPS_BODY=["b","body"];function resolveBody(e){return resolveProps(e,PROPS_BODY)}const PROPS_CASES=["c","cases"];function resolveCases(e){return resolveProps(e,PROPS_CASES,[])}const PROPS_STATIC=["s","static"];function resolveStatic(e){return resolveProps(e,PROPS_STATIC)}const PROPS_ITEMS=["i","items"];function resolveItems(e){return resolveProps(e,PROPS_ITEMS,[])}const PROPS_TYPE=["t","type"];function resolveType(e){return resolveProps(e,PROPS_TYPE)}const PROPS_VALUE=["v","value"];function resolveValue$1(e,t){const r=resolveProps(e,PROPS_VALUE);if(null!=r)return r;throw createUnhandleNodeError(t)}const PROPS_MODIFIER=["m","modifier"];function resolveLinkedModifier(e){return resolveProps(e,PROPS_MODIFIER)}const PROPS_KEY=["k","key"];function resolveLinkedKey(e){const t=resolveProps(e,PROPS_KEY);if(t)return t;throw createUnhandleNodeError(6)}function resolveProps(e,t,r){for(let n=0;n<t.length;n++){const r=t[n];if(hasOwn(e,r)&&null!=e[r])return e[r]}return r}const AST_NODE_PROPS_KEYS=[...PROPS_BODY,...PROPS_CASES,...PROPS_STATIC,...PROPS_ITEMS,...PROPS_KEY,...PROPS_MODIFIER,...PROPS_VALUE,...PROPS_TYPE];function createUnhandleNodeError(e){return new Error(`unhandled node type: ${e}`)}function format(e){return t=>formatParts(t,e)}function formatParts(e,t){const r=resolveBody(t);if(null==r)throw createUnhandleNodeError(0);if(1===resolveType(r)){const t=resolveCases(r);return e.plural(t.reduce(((t,r)=>[...t,formatMessageParts(e,r)]),[]))}return formatMessageParts(e,r)}function formatMessageParts(e,t){const r=resolveStatic(t);if(null!=r)return"text"===e.type?r:e.normalize([r]);{const r=resolveItems(t).reduce(((t,r)=>[...t,formatMessagePart(e,r)]),[]);return e.normalize(r)}}function formatMessagePart(e,t){const r=resolveType(t);switch(r){case 3:case 9:case 7:case 8:return resolveValue$1(t,r);case 4:{const n=t;if(hasOwn(n,"k")&&n.k)return e.interpolate(e.named(n.k));if(hasOwn(n,"key")&&n.key)return e.interpolate(e.named(n.key));throw createUnhandleNodeError(r)}case 5:{const n=t;if(hasOwn(n,"i")&&isNumber(n.i))return e.interpolate(e.list(n.i));if(hasOwn(n,"index")&&isNumber(n.index))return e.interpolate(e.list(n.index));throw createUnhandleNodeError(r)}case 6:{const r=t,n=resolveLinkedModifier(r),a=resolveLinkedKey(r);return e.linked(formatMessagePart(e,a),n?formatMessagePart(e,n):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${r}`)}}let compileCache=create();function clearCompileCache(){compileCache=create()}function compile(e,t){{const t=e.cacheKey;if(t){const r=compileCache[t];return r||(compileCache[t]=format(e))}return format(e)}}let devtools=null;function setDevToolsHook(e){devtools=e}function getDevToolsHook(){return devtools}function initI18nDevTools(e,t,r){devtools&&devtools.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:r})}const translateDevTools=createDevToolsHook("function:translate");function createDevToolsHook(e){return t=>devtools&&devtools.emit(e,t)}const CoreErrorCodes={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},CORE_ERROR_CODES_EXTEND_POINT=24;function createCoreError(e){return createCompileError(e,null,void 0)}function getLocale(e,t){return null!=t.locale?resolveLocale(t.locale):resolveLocale(e.locale)}let _resolveLocale;function resolveLocale(e){if(isString(e))return e;if(isFunction(e)){if(e.resolvedOnce&&null!=_resolveLocale)return _resolveLocale;if("Function"===e.constructor.name){const t=e();if(isPromise(t))throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return _resolveLocale=t}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE)}function fallbackWithSimple(e,t,r){return[...new Set([r,...isArray(t)?t:isObject(t)?Object.keys(t):isString(t)?[t]:[r]])]}function fallbackWithLocaleChain(e,t,r){const n=isString(r)?r:DEFAULT_LOCALE,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let s=a.__localeChainCache.get(n);if(!s){s=[];let e=[r];for(;isArray(e);)e=appendBlockToChain(s,e,t);const o=isArray(t)||!isPlainObject(t)?t:t.default?t.default:null;e=isString(o)?[o]:o,isArray(e)&&appendBlockToChain(s,e,!1),a.__localeChainCache.set(n,s)}return s}function appendBlockToChain(e,t,r){let n=!0;for(let a=0;a<t.length&&isBoolean(n);a++){const s=t[a];isString(s)&&(n=appendLocaleToChain(e,t[a],r))}return n}function appendLocaleToChain(e,t,r){let n;const a=t.split("-");do{n=appendItemToChain(e,a.join("-"),r),a.splice(-1,1)}while(a.length&&!0===n);return n}function appendItemToChain(e,t,r){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(isArray(r)||isPlainObject(r))&&r[a]&&(n=r[a])}return n}CoreErrorCodes.INVALID_ARGUMENT,CoreErrorCodes.INVALID_DATE_ARGUMENT,CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT,CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE,CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE,CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION,CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE;const pathStateMachine=[];pathStateMachine[0]={w:[0],i:[3,0],"[":[4],o:[7]},pathStateMachine[1]={w:[1],".":[2],"[":[4],o:[7]},pathStateMachine[2]={w:[2],i:[3,0],0:[3,0]},pathStateMachine[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},pathStateMachine[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},pathStateMachine[5]={"'":[4,0],o:8,l:[5,0]},pathStateMachine[6]={'"':[4,0],o:8,l:[6,0]};const literalValueRE=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function isLiteral(e){return literalValueRE.test(e)}function stripQuotes(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}function getPathCharType(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function formatSubPath(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(isLiteral(t)?stripQuotes(t):"*"+t)}function parse(e){const t=[];let r,n,a,s,o,i,l,c=-1,u=0,m=0;const _=[];function E(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,a="\\"+t,_[0](),!0}for(_[0]=()=>{void 0===n?n=a:n+=a},_[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},_[2]=()=>{_[0](),m++},_[3]=()=>{if(m>0)m--,u=4,_[0]();else{if(m=0,void 0===n)return!1;if(n=formatSubPath(n),!1===n)return!1;_[1]()}};null!==u;)if(c++,r=e[c],"\\"!==r||!E()){if(s=getPathCharType(r),l=pathStateMachine[u],o=l[s]||l.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(i=_[o[1]],i&&(a=r,!1===i())))return;if(7===u)return t}}const cache=new Map;function resolveWithKeyValue(e,t){return isObject(e)?e[t]:null}function resolveValue(e,t){if(!isObject(e))return null;let r=cache.get(t);if(r||(r=parse(t),r&&cache.set(t,r)),!r)return null;const n=r.length;let a=e,s=0;for(;s<n;){const e=r[s];if(AST_NODE_PROPS_KEYS.includes(e)&&isMessageAST(a))return null;const t=a[e];if(void 0===t)return null;if(isFunction(a))return null;a=t,s++}return a}const CoreWarnCodes={NOT_FOUND_KEY:1,FALLBACK_TO_TRANSLATE:2,CANNOT_FORMAT_NUMBER:3,FALLBACK_TO_NUMBER_FORMAT:4,CANNOT_FORMAT_DATE:5,FALLBACK_TO_DATE_FORMAT:6,EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:7},CORE_WARN_CODES_EXTEND_POINT=8,warnMessages={[CoreWarnCodes.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[CoreWarnCodes.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[CoreWarnCodes.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[CoreWarnCodes.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale.",[CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]:"This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future."};function getWarnMessage(e,...t){return format$1(warnMessages[e],...t)}const VERSION="10.0.7",NOT_REOSLVED=-1,DEFAULT_LOCALE="en-US",MISSING_RESOLVE_VALUE="",capitalize=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function getDefaultLinkedModifiers(){return{upper:(e,t)=>"text"===t&&isString(e)?e.toUpperCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&isString(e)?e.toLowerCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&isString(e)?capitalize(e):"vnode"===t&&isObject(e)&&"__v_isVNode"in e?capitalize(e.children):e}}let _compiler,_resolver,_fallbacker;function registerMessageCompiler(e){_compiler=e}function registerMessageResolver(e){_resolver=e}function registerLocaleFallbacker(e){_fallbacker=e}let _additionalMeta=null;const setAdditionalMeta=e=>{_additionalMeta=e},getAdditionalMeta=()=>_additionalMeta;let _fallbackContext=null;const setFallbackContext=e=>{_fallbackContext=e},getFallbackContext=()=>_fallbackContext;let _cid=0;function createCoreContext(e={}){const t=isFunction(e.onWarn)?e.onWarn:warn,r=isString(e.version)?e.version:VERSION,n=isString(e.locale)||isFunction(e.locale)?e.locale:DEFAULT_LOCALE,a=isFunction(n)?DEFAULT_LOCALE:n,s=isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||isString(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,o=isPlainObject(e.messages)?e.messages:createResources(a),i=isPlainObject(e.datetimeFormats)?e.datetimeFormats:createResources(a),l=isPlainObject(e.numberFormats)?e.numberFormats:createResources(a),c=assign(create(),e.modifiers,getDefaultLinkedModifiers()),u=e.pluralRules||create(),m=isFunction(e.missing)?e.missing:null,_=!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,E=!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,g=!!e.fallbackFormat,O=!!e.unresolving,f=isFunction(e.postTranslation)?e.postTranslation:null,p=isPlainObject(e.processor)?e.processor:null,S=!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,T=!!e.escapeParameter,d=isFunction(e.messageCompiler)?e.messageCompiler:_compiler,A=isFunction(e.messageResolver)?e.messageResolver:_resolver||resolveWithKeyValue,C=isFunction(e.localeFallbacker)?e.localeFallbacker:_fallbacker||fallbackWithSimple,N=isObject(e.fallbackContext)?e.fallbackContext:void 0,P=e,b=isObject(P.__datetimeFormatters)?P.__datetimeFormatters:new Map,M=isObject(P.__numberFormatters)?P.__numberFormatters:new Map,L=isObject(P.__meta)?P.__meta:{};_cid++;const h={version:r,cid:_cid,locale:n,fallbackLocale:s,messages:o,modifiers:c,pluralRules:u,missing:m,missingWarn:_,fallbackWarn:E,fallbackFormat:g,unresolving:O,postTranslation:f,processor:p,warnHtmlMessage:S,escapeParameter:T,messageCompiler:d,messageResolver:A,localeFallbacker:C,fallbackContext:N,onWarn:t,__meta:L};return h.datetimeFormats=i,h.numberFormats=l,h.__datetimeFormatters=b,h.__numberFormatters=M,h}const createResources=e=>({[e]:create()});function isTranslateFallbackWarn(e,t){return e instanceof RegExp?e.test(t):e}function isTranslateMissingWarn(e,t){return e instanceof RegExp?e.test(t):e}function handleMissing(e,t,r,n,a){const{missing:s,onWarn:o}=e;if(null!==s){const n=s(e,r,t,a);return isString(n)?n:t}return t}function updateFallbackLocale(e,t,r){e.__localeChainCache=new Map,e.localeFallbacker(e,r,t)}function isAlmostSameLocale(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function isImplicitFallback(e,t){const r=t.indexOf(e);if(-1===r)return!1;for(let n=r+1;n<t.length;n++)if(isAlmostSameLocale(e,t[n]))return!0;return!1}function datetime(e,...t){const{datetimeFormats:r,unresolving:n,fallbackLocale:a,onWarn:s,localeFallbacker:o}=e,{__datetimeFormatters:i}=e,[l,c,u,m]=parseDateTimeArgs(...t),_=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const E=!!u.part,g=getLocale(e,u),O=o(e,a,g);if(!isString(l)||""===l)return new Intl.DateTimeFormat(g,m).format(c);let f,p={},S=null;for(let A=0;A<O.length&&(f=O[A],p=r[f]||{},S=p[l],!isPlainObject(S));A++)handleMissing(e,l,f,_,"datetime format");if(!isPlainObject(S)||!isString(f))return n?-1:l;let T=`${f}__${l}`;isEmptyObject(m)||(T=`${T}__${JSON.stringify(m)}`);let d=i.get(T);return d||(d=new Intl.DateTimeFormat(f,assign({},S,m)),i.set(T,d)),E?d.formatToParts(c):d.format(c)}const DATETIME_FORMAT_OPTIONS_KEYS=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function parseDateTimeArgs(...e){const[t,r,n,a]=e,s=create();let o,i=create();if(isString(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);const r=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();o=new Date(r);try{o.toISOString()}catch{throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT)}}else if(isDate(t)){if(isNaN(t.getTime()))throw Error(CoreErrorCodes.INVALID_DATE_ARGUMENT);o=t}else{if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);o=t}return isString(r)?s.key=r:isPlainObject(r)&&Object.keys(r).forEach((e=>{DATETIME_FORMAT_OPTIONS_KEYS.includes(e)?i[e]=r[e]:s[e]=r[e]})),isString(n)?s.locale=n:isPlainObject(n)&&(i=n),isPlainObject(a)&&(i=a),[s.key||"",o,s,i]}function clearDateTimeFormat(e,t,r){const n=e;for(const a in r){const e=`${t}__${a}`;n.__datetimeFormatters.has(e)&&n.__datetimeFormatters.delete(e)}}function number(e,...t){const{numberFormats:r,unresolving:n,fallbackLocale:a,onWarn:s,localeFallbacker:o}=e,{__numberFormatters:i}=e,[l,c,u,m]=parseNumberArgs(...t),_=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const E=!!u.part,g=getLocale(e,u),O=o(e,a,g);if(!isString(l)||""===l)return new Intl.NumberFormat(g,m).format(c);let f,p={},S=null;for(let A=0;A<O.length&&(f=O[A],p=r[f]||{},S=p[l],!isPlainObject(S));A++)handleMissing(e,l,f,_,"number format");if(!isPlainObject(S)||!isString(f))return n?-1:l;let T=`${f}__${l}`;isEmptyObject(m)||(T=`${T}__${JSON.stringify(m)}`);let d=i.get(T);return d||(d=new Intl.NumberFormat(f,assign({},S,m)),i.set(T,d)),E?d.formatToParts(c):d.format(c)}const NUMBER_FORMAT_OPTIONS_KEYS=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function parseNumberArgs(...e){const[t,r,n,a]=e,s=create();let o=create();if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const i=t;return isString(r)?s.key=r:isPlainObject(r)&&Object.keys(r).forEach((e=>{NUMBER_FORMAT_OPTIONS_KEYS.includes(e)?o[e]=r[e]:s[e]=r[e]})),isString(n)?s.locale=n:isPlainObject(n)&&(o=n),isPlainObject(a)&&(o=a),[s.key||"",i,s,o]}function clearNumberFormat(e,t,r){const n=e;for(const a in r){const e=`${t}__${a}`;n.__numberFormatters.has(e)&&n.__numberFormatters.delete(e)}}const DEFAULT_MODIFIER=e=>e,DEFAULT_MESSAGE=e=>"",DEFAULT_MESSAGE_DATA_TYPE="text",DEFAULT_NORMALIZE=e=>0===e.length?"":join(e),DEFAULT_INTERPOLATE=toDisplayString;function pluralDefault(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function getPluralIndex(e){const t=isNumber(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(isNumber(e.named.count)||isNumber(e.named.n))?isNumber(e.named.count)?e.named.count:isNumber(e.named.n)?e.named.n:t:t}function normalizeNamed(e,t){t.count||(t.count=e),t.n||(t.n=e)}function createMessageContext(e={}){const t=e.locale,r=getPluralIndex(e),n=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?e.pluralRules[t]:pluralDefault,a=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?pluralDefault:void 0,s=e.list||[],o=e.named||create();isNumber(e.pluralIndex)&&normalizeNamed(r,o);function i(t,r){const n=isFunction(e.messages)?e.messages(t,!!r):!!isObject(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):DEFAULT_MESSAGE)}const l=isPlainObject(e.processor)&&isFunction(e.processor.normalize)?e.processor.normalize:DEFAULT_NORMALIZE,c=isPlainObject(e.processor)&&isFunction(e.processor.interpolate)?e.processor.interpolate:DEFAULT_INTERPOLATE,u={list:e=>s[e],named:e=>o[e],plural:e=>e[n(r,e.length,a)],linked:(t,...r)=>{const[n,a]=r;let s="text",o="";1===r.length?isObject(n)?(o=n.modifier||o,s=n.type||s):isString(n)&&(o=n||o):2===r.length&&(isString(n)&&(o=n||o),isString(a)&&(s=a||s));const l=i(t,!0)(u),c="vnode"===s&&isArray(l)&&o?l[0]:l;return o?(m=o,e.modifiers?e.modifiers[m]:DEFAULT_MODIFIER)(c,s):c;var m},message:i,type:isPlainObject(e.processor)&&isString(e.processor.type)?e.processor.type:DEFAULT_MESSAGE_DATA_TYPE,interpolate:c,normalize:l,values:assign(create(),s,o)};return u}const NOOP_MESSAGE_FUNCTION=()=>"",isMessageFunction=e=>isFunction(e);function translate(e,...t){const{fallbackFormat:r,postTranslation:n,unresolving:a,messageCompiler:s,fallbackLocale:o,messages:i}=e,[l,c]=parseTranslateArgs(...t),u=isBoolean(c.missingWarn)?c.missingWarn:e.missingWarn,m=isBoolean(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,_=isBoolean(c.escapeParameter)?c.escapeParameter:e.escapeParameter,E=!!c.resolvedMessage,g=isString(c.default)||isBoolean(c.default)?isBoolean(c.default)?s?l:()=>l:c.default:r?s?l:()=>l:null,O=r||null!=g&&(isString(g)||isFunction(g)),f=getLocale(e,c);_&&escapeParams(c);let[p,S,T]=E?[l,f,i[f]||create()]:resolveMessageFormat(e,l,f,o,m,u),d=p,A=l;if(E||isString(d)||isMessageAST(d)||isMessageFunction(d)||O&&(d=g,A=d),!(E||(isString(d)||isMessageAST(d)||isMessageFunction(d))&&isString(S)))return a?-1:l;let C=!1;const N=isMessageFunction(d)?d:compileMessageFormat(e,l,S,d,A,(()=>{C=!0}));if(C)return d;const P=evaluateMessage(e,N,createMessageContext(getMessageContextOptions(e,S,T,c)));return n?n(P,l):P}function escapeParams(e){isArray(e.list)?e.list=e.list.map((e=>isString(e)?escapeHtml(e):e)):isObject(e.named)&&Object.keys(e.named).forEach((t=>{isString(e.named[t])&&(e.named[t]=escapeHtml(e.named[t]))}))}function resolveMessageFormat(e,t,r,n,a,s){const{messages:o,onWarn:i,messageResolver:l,localeFallbacker:c}=e,u=c(e,n,r);let m,_=create(),E=null;for(let g=0;g<u.length&&(m=u[g],_=o[m]||create(),null===(E=l(_,t))&&(E=_[t]),!(isString(E)||isMessageAST(E)||isMessageFunction(E)));g++)if(!isImplicitFallback(m,u)){const r=handleMissing(e,t,m,s,"translate");r!==t&&(E=r)}return[E,m,_]}function compileMessageFormat(e,t,r,n,a,s){const{messageCompiler:o,warnHtmlMessage:i}=e;if(isMessageFunction(n)){const e=n;return e.locale=e.locale||r,e.key=e.key||t,e}if(null==o){const e=()=>n;return e.locale=r,e.key=t,e}const l=o(n,getCompileContext(e,r,a,n,i,s));return l.locale=r,l.key=t,l.source=n,l}function evaluateMessage(e,t,r){return t(r)}function parseTranslateArgs(...e){const[t,r,n]=e,a=create();if(!(isString(t)||isNumber(t)||isMessageFunction(t)||isMessageAST(t)))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const s=isNumber(t)?String(t):(isMessageFunction(t),t);return isNumber(r)?a.plural=r:isString(r)?a.default=r:isPlainObject(r)&&!isEmptyObject(r)?a.named=r:isArray(r)&&(a.list=r),isNumber(n)?a.plural=n:isString(n)?a.default=n:isPlainObject(n)&&assign(a,n),[s,a]}function getCompileContext(e,t,r,n,a,s){return{locale:t,key:r,warnHtmlMessage:a,onError:e=>{throw s&&s(e),e},onCacheKey:e=>generateFormatCacheKey(t,r,e)}}function getMessageContextOptions(e,t,r,n){const{modifiers:a,pluralRules:s,messageResolver:o,fallbackLocale:i,fallbackWarn:l,missingWarn:c,fallbackContext:u}=e,m={locale:t,modifiers:a,pluralRules:s,messages:(n,a)=>{let s=o(r,n);if(null==s&&(u||a)){const[,,r]=resolveMessageFormat(u||e,n,t,i,l,c);s=o(r,n)}if(isString(s)||isMessageAST(s)){let r=!1;const a=compileMessageFormat(e,n,t,s,n,(()=>{r=!0}));return r?NOOP_MESSAGE_FUNCTION:a}return isMessageFunction(s)?s:NOOP_MESSAGE_FUNCTION}};return e.processor&&(m.processor=e.processor),n.list&&(m.list=n.list),n.named&&(m.named=n.named),isNumber(n.plural)&&(m.pluralIndex=n.plural),m}registerMessageCompiler(compile),registerMessageResolver(resolveValue),registerLocaleFallbacker(fallbackWithLocaleChain);export{AST_NODE_PROPS_KEYS,CORE_ERROR_CODES_EXTEND_POINT,CORE_WARN_CODES_EXTEND_POINT,CompileErrorCodes,CoreErrorCodes,CoreWarnCodes,DATETIME_FORMAT_OPTIONS_KEYS,DEFAULT_LOCALE,DEFAULT_MESSAGE_DATA_TYPE,MISSING_RESOLVE_VALUE,NOT_REOSLVED,NUMBER_FORMAT_OPTIONS_KEYS,VERSION,clearCompileCache,clearDateTimeFormat,clearNumberFormat,compile,createCompileError,createCoreContext,createCoreError,createMessageContext,datetime,fallbackWithLocaleChain,fallbackWithSimple,getAdditionalMeta,getDevToolsHook,getFallbackContext,getLocale,getWarnMessage,handleMissing,initI18nDevTools,isAlmostSameLocale,isImplicitFallback,isMessageAST,isMessageFunction,isTranslateFallbackWarn,isTranslateMissingWarn,number,parse,parseDateTimeArgs,parseNumberArgs,parseTranslateArgs,registerLocaleFallbacker,registerMessageCompiler,registerMessageResolver,resolveLocale,resolveValue,resolveWithKeyValue,setAdditionalMeta,setDevToolsHook,setFallbackContext,translate,translateDevTools,updateFallbackLocale};
