/*!
  * core v10.0.7
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
'use strict';

var coreBase = require('@intlify/core-base');

// register message compiler at @intlify/core
coreBase.registerMessageCompiler(coreBase.compile);
// register message resolver at @intlify/core
coreBase.registerMessageResolver(coreBase.resolveValue);
// register fallback locale at @intlify/core
coreBase.registerLocaleFallbacker(coreBase.fallbackWithLocaleChain);

Object.prototype.hasOwnProperty.call(coreBase, '__proto__') &&
  !Object.prototype.hasOwnProperty.call(exports, '__proto__') &&
  Object.defineProperty(exports, '__proto__', {
    enumerable: true,
    value: coreBase['__proto__']
  });

Object.keys(coreBase).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) exports[k] = coreBase[k];
});
