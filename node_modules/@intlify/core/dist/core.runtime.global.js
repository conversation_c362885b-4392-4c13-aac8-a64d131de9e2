/*!
  * core v10.0.7
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var IntlifyCore = (function (exports) {
  'use strict';

  /**
   * Original Utilities
   * written by ka<PERSON><PERSON> kawa<PERSON>
   */
  const inBrowser = typeof window !== 'undefined';
  let mark;
  let measure;
  {
      const perf = inBrowser && window.performance;
      if (perf &&
          perf.mark &&
          perf.measure &&
          perf.clearMarks &&
          // @ts-ignore browser compat
          perf.clearMeasures) {
          mark = (tag) => {
              perf.mark(tag);
          };
          measure = (name, startTag, endTag) => {
              perf.measure(name, startTag, endTag);
              perf.clearMarks(startTag);
              perf.clearMarks(endTag);
          };
      }
  }
  const RE_ARGS = /\{([0-9a-zA-Z]+)\}/g;
  /* eslint-disable */
  function format$1(message, ...args) {
      if (args.length === 1 && isObject(args[0])) {
          args = args[0];
      }
      if (!args || !args.hasOwnProperty) {
          args = {};
      }
      return message.replace(RE_ARGS, (match, identifier) => {
          return args.hasOwnProperty(identifier) ? args[identifier] : '';
      });
  }
  const generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });
  const friendlyJSONstringify = (json) => JSON.stringify(json)
      .replace(/\u2028/g, '\\u2028')
      .replace(/\u2029/g, '\\u2029')
      .replace(/\u0027/g, '\\u0027');
  const isNumber = (val) => typeof val === 'number' && isFinite(val);
  const isDate = (val) => toTypeString(val) === '[object Date]';
  const isRegExp = (val) => toTypeString(val) === '[object RegExp]';
  const isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;
  const assign = Object.assign;
  const _create = Object.create;
  const create = (obj = null) => _create(obj);
  function escapeHtml(rawText) {
      return rawText
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&apos;');
  }
  const hasOwnProperty = Object.prototype.hasOwnProperty;
  function hasOwn(obj, key) {
      return hasOwnProperty.call(obj, key);
  }
  /* eslint-enable */
  /**
   * Useful Utilities By Evan you
   * Modified by kazuya kawaguchi
   * MIT License
   * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/index.ts
   * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/codeframe.ts
   */
  const isArray = Array.isArray;
  const isFunction = (val) => typeof val === 'function';
  const isString = (val) => typeof val === 'string';
  const isBoolean = (val) => typeof val === 'boolean';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const isObject = (val) => val !== null && typeof val === 'object';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const isPromise = (val) => {
      return isObject(val) && isFunction(val.then) && isFunction(val.catch);
  };
  const objectToString = Object.prototype.toString;
  const toTypeString = (value) => objectToString.call(value);
  const isPlainObject = (val) => toTypeString(val) === '[object Object]';
  // for converting list and named values to displayed strings.
  const toDisplayString = (val) => {
      return val == null
          ? ''
          : isArray(val) || (isPlainObject(val) && val.toString === objectToString)
              ? JSON.stringify(val, null, 2)
              : String(val);
  };
  function join(items, separator = '') {
      return items.reduce((str, item, index) => (index === 0 ? str + item : str + separator + item), '');
  }
  const RANGE = 2;
  function generateCodeFrame(source, start = 0, end = source.length) {
      const lines = source.split(/\r?\n/);
      let count = 0;
      const res = [];
      for (let i = 0; i < lines.length; i++) {
          count += lines[i].length + 1;
          if (count >= start) {
              for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {
                  if (j < 0 || j >= lines.length)
                      continue;
                  const line = j + 1;
                  res.push(`${line}${' '.repeat(3 - String(line).length)}|  ${lines[j]}`);
                  const lineLength = lines[j].length;
                  if (j === i) {
                      // push underline
                      const pad = start - (count - lineLength) + 1;
                      const length = Math.max(1, end > count ? lineLength - pad : end - start);
                      res.push(`   |  ` + ' '.repeat(pad) + '^'.repeat(length));
                  }
                  else if (j > i) {
                      if (end > count) {
                          const length = Math.max(Math.min(end - count, lineLength), 1);
                          res.push(`   |  ` + '^'.repeat(length));
                      }
                      count += lineLength + 1;
                  }
              }
              break;
          }
      }
      return res.join('\n');
  }

  function warn(msg, err) {
      if (typeof console !== 'undefined') {
          console.warn(`[intlify] ` + msg);
          /* istanbul ignore if */
          if (err) {
              console.warn(err.stack);
          }
      }
  }

  const CompileErrorCodes = {
      // tokenizer error codes
      EXPECTED_TOKEN: 1,
      INVALID_TOKEN_IN_PLACEHOLDER: 2,
      UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,
      UNKNOWN_ESCAPE_SEQUENCE: 4,
      INVALID_UNICODE_ESCAPE_SEQUENCE: 5,
      UNBALANCED_CLOSING_BRACE: 6,
      UNTERMINATED_CLOSING_BRACE: 7,
      EMPTY_PLACEHOLDER: 8,
      NOT_ALLOW_NEST_PLACEHOLDER: 9,
      INVALID_LINKED_FORMAT: 10,
      // parser error codes
      MUST_HAVE_MESSAGES_IN_PLURAL: 11,
      UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,
      UNEXPECTED_EMPTY_LINKED_KEY: 13,
      UNEXPECTED_LEXICAL_ANALYSIS: 14,
      // generator error codes
      UNHANDLED_CODEGEN_NODE_TYPE: 15,
      // minifier error codes
      UNHANDLED_MINIFIER_NODE_TYPE: 16
  };
  // Special value for higher-order compilers to pick up the last code
  // to avoid collision of error codes.
  // This should always be kept as the last item.
  const COMPILE_ERROR_CODES_EXTEND_POINT = 17;
  /** @internal */
  const errorMessages$1 = {
      // tokenizer error messages
      [CompileErrorCodes.EXPECTED_TOKEN]: `Expected token: '{0}'`,
      [CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]: `Invalid token in placeholder: '{0}'`,
      [CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: `Unterminated single quote in placeholder`,
      [CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]: `Unknown escape sequence: \\{0}`,
      [CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]: `Invalid unicode escape sequence: {0}`,
      [CompileErrorCodes.UNBALANCED_CLOSING_BRACE]: `Unbalanced closing brace`,
      [CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]: `Unterminated closing brace`,
      [CompileErrorCodes.EMPTY_PLACEHOLDER]: `Empty placeholder`,
      [CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]: `Not allowed nest placeholder`,
      [CompileErrorCodes.INVALID_LINKED_FORMAT]: `Invalid linked format`,
      // parser error messages
      [CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]: `Plural must have messages`,
      [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]: `Unexpected empty linked modifier`,
      [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]: `Unexpected empty linked key`,
      [CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]: `Unexpected lexical analysis in token: '{0}'`,
      // generator error messages
      [CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]: `unhandled codegen node type: '{0}'`,
      // minimizer error messages
      [CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]: `unhandled mimifier node type: '{0}'`
  };
  function createCompileError(code, loc, options = {}) {
      const { domain, messages, args } = options;
      const msg = format$1((messages || errorMessages$1)[code] || '', ...(args || []))
          ;
      const error = new SyntaxError(String(msg));
      error.code = code;
      if (loc) {
          error.location = loc;
      }
      error.domain = domain;
      return error;
  }

  function isMessageAST(val) {
      return (isObject(val) &&
          resolveType(val) === 0 &&
          (hasOwn(val, 'b') || hasOwn(val, 'body')));
  }
  const PROPS_BODY = ['b', 'body'];
  function resolveBody(node) {
      return resolveProps(node, PROPS_BODY);
  }
  const PROPS_CASES = ['c', 'cases'];
  function resolveCases(node) {
      return resolveProps(node, PROPS_CASES, []);
  }
  const PROPS_STATIC = ['s', 'static'];
  function resolveStatic(node) {
      return resolveProps(node, PROPS_STATIC);
  }
  const PROPS_ITEMS = ['i', 'items'];
  function resolveItems(node) {
      return resolveProps(node, PROPS_ITEMS, []);
  }
  const PROPS_TYPE = ['t', 'type'];
  function resolveType(node) {
      return resolveProps(node, PROPS_TYPE);
  }
  const PROPS_VALUE = ['v', 'value'];
  function resolveValue$1(node, type) {
      const resolved = resolveProps(node, PROPS_VALUE);
      if (resolved != null) {
          return resolved;
      }
      else {
          throw createUnhandleNodeError(type);
      }
  }
  const PROPS_MODIFIER = ['m', 'modifier'];
  function resolveLinkedModifier(node) {
      return resolveProps(node, PROPS_MODIFIER);
  }
  const PROPS_KEY = ['k', 'key'];
  function resolveLinkedKey(node) {
      const resolved = resolveProps(node, PROPS_KEY);
      if (resolved) {
          return resolved;
      }
      else {
          throw createUnhandleNodeError(6 /* NodeTypes.Linked */);
      }
  }
  function resolveProps(node, props, defaultValue) {
      for (let i = 0; i < props.length; i++) {
          const prop = props[i];
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          if (hasOwn(node, prop) && node[prop] != null) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return node[prop];
          }
      }
      return defaultValue;
  }
  const AST_NODE_PROPS_KEYS = [
      ...PROPS_BODY,
      ...PROPS_CASES,
      ...PROPS_STATIC,
      ...PROPS_ITEMS,
      ...PROPS_KEY,
      ...PROPS_MODIFIER,
      ...PROPS_VALUE,
      ...PROPS_TYPE
  ];
  function createUnhandleNodeError(type) {
      return new Error(`unhandled node type: ${type}`);
  }

  function format(ast) {
      const msg = (ctx) => formatParts(ctx, ast);
      return msg;
  }
  function formatParts(ctx, ast) {
      const body = resolveBody(ast);
      if (body == null) {
          throw createUnhandleNodeError(0 /* NodeTypes.Resource */);
      }
      const type = resolveType(body);
      if (type === 1 /* NodeTypes.Plural */) {
          const plural = body;
          const cases = resolveCases(plural);
          return ctx.plural(cases.reduce((messages, c) => [
              ...messages,
              formatMessageParts(ctx, c)
          ], []));
      }
      else {
          return formatMessageParts(ctx, body);
      }
  }
  function formatMessageParts(ctx, node) {
      const static_ = resolveStatic(node);
      if (static_ != null) {
          return ctx.type === 'text'
              ? static_
              : ctx.normalize([static_]);
      }
      else {
          const messages = resolveItems(node).reduce((acm, c) => [...acm, formatMessagePart(ctx, c)], []);
          return ctx.normalize(messages);
      }
  }
  function formatMessagePart(ctx, node) {
      const type = resolveType(node);
      switch (type) {
          case 3 /* NodeTypes.Text */: {
              return resolveValue$1(node, type);
          }
          case 9 /* NodeTypes.Literal */: {
              return resolveValue$1(node, type);
          }
          case 4 /* NodeTypes.Named */: {
              const named = node;
              if (hasOwn(named, 'k') && named.k) {
                  return ctx.interpolate(ctx.named(named.k));
              }
              if (hasOwn(named, 'key') && named.key) {
                  return ctx.interpolate(ctx.named(named.key));
              }
              throw createUnhandleNodeError(type);
          }
          case 5 /* NodeTypes.List */: {
              const list = node;
              if (hasOwn(list, 'i') && isNumber(list.i)) {
                  return ctx.interpolate(ctx.list(list.i));
              }
              if (hasOwn(list, 'index') && isNumber(list.index)) {
                  return ctx.interpolate(ctx.list(list.index));
              }
              throw createUnhandleNodeError(type);
          }
          case 6 /* NodeTypes.Linked */: {
              const linked = node;
              const modifier = resolveLinkedModifier(linked);
              const key = resolveLinkedKey(linked);
              return ctx.linked(formatMessagePart(ctx, key), modifier ? formatMessagePart(ctx, modifier) : undefined, ctx.type);
          }
          case 7 /* NodeTypes.LinkedKey */: {
              return resolveValue$1(node, type);
          }
          case 8 /* NodeTypes.LinkedModifier */: {
              return resolveValue$1(node, type);
          }
          default:
              throw new Error(`unhandled node on format message part: ${type}`);
      }
  }

  let compileCache = create();
  function clearCompileCache() {
      compileCache = create();
  }
  /* #__NO_SIDE_EFFECTS__ */
  function compile(message, context) {
      {
          if (!isMessageAST(message)) {
              warn(`the message that is resolve with key '${context.key}' is not supported for jit compilation`);
              return (() => message);
          }
          // AST case (passed from bundler)
          const cacheKey = message.cacheKey;
          if (cacheKey) {
              const cached = compileCache[cacheKey];
              if (cached) {
                  return cached;
              }
              // compose message function from message (AST)
              return (compileCache[cacheKey] =
                  format(message));
          }
          else {
              return format(message);
          }
      }
  }

  let devtools = null;
  function setDevToolsHook(hook) {
      devtools = hook;
  }
  function getDevToolsHook() {
      return devtools;
  }
  function initI18nDevTools(i18n, version, meta) {
      // TODO: queue if devtools is undefined
      devtools &&
          devtools.emit('i18n:init', {
              timestamp: Date.now(),
              i18n,
              version,
              meta
          });
  }
  const translateDevTools = 
  /* #__PURE__*/ createDevToolsHook('function:translate');
  function createDevToolsHook(hook) {
      return (payloads) => devtools && devtools.emit(hook, payloads);
  }

  const CoreErrorCodes = {
      INVALID_ARGUMENT: COMPILE_ERROR_CODES_EXTEND_POINT, // 17
      INVALID_DATE_ARGUMENT: 18,
      INVALID_ISO_DATE_ARGUMENT: 19,
      NOT_SUPPORT_NON_STRING_MESSAGE: 20,
      NOT_SUPPORT_LOCALE_PROMISE_VALUE: 21,
      NOT_SUPPORT_LOCALE_ASYNC_FUNCTION: 22,
      NOT_SUPPORT_LOCALE_TYPE: 23
  };
  const CORE_ERROR_CODES_EXTEND_POINT = 24;
  function createCoreError(code) {
      return createCompileError(code, null, { messages: errorMessages } );
  }
  /** @internal */
  const errorMessages = {
      [CoreErrorCodes.INVALID_ARGUMENT]: 'Invalid arguments',
      [CoreErrorCodes.INVALID_DATE_ARGUMENT]: 'The date provided is an invalid Date object.' +
          'Make sure your Date represents a valid date.',
      [CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT]: 'The argument provided is not a valid ISO date string',
      [CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE]: 'Not support non-string message',
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE]: 'cannot support promise value',
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION]: 'cannot support async function',
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE]: 'cannot support locale type'
  };

  /** @internal */
  function getLocale(context, options) {
      return options.locale != null
          ? resolveLocale(options.locale)
          : resolveLocale(context.locale);
  }
  let _resolveLocale;
  /** @internal */
  function resolveLocale(locale) {
      if (isString(locale)) {
          return locale;
      }
      else {
          if (isFunction(locale)) {
              if (locale.resolvedOnce && _resolveLocale != null) {
                  return _resolveLocale;
              }
              else if (locale.constructor.name === 'Function') {
                  const resolve = locale();
                  if (isPromise(resolve)) {
                      throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);
                  }
                  return (_resolveLocale = resolve);
              }
              else {
                  throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION);
              }
          }
          else {
              throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE);
          }
      }
  }
  /**
   * Fallback with simple implemenation
   *
   * @remarks
   * A fallback locale function implemented with a simple fallback algorithm.
   *
   * Basically, it returns the value as specified in the `fallbackLocale` props, and is processed with the fallback inside intlify.
   *
   * @param ctx - A {@link CoreContext | context}
   * @param fallback - A {@link FallbackLocale | fallback locale}
   * @param start - A starting {@link Locale | locale}
   *
   * @returns Fallback locales
   *
   * @VueI18nGeneral
   */
  function fallbackWithSimple(ctx, fallback, start) {
      // prettier-ignore
      return [...new Set([
              start,
              ...(isArray(fallback)
                  ? fallback
                  : isObject(fallback)
                      ? Object.keys(fallback)
                      : isString(fallback)
                          ? [fallback]
                          : [start])
          ])];
  }
  /**
   * Fallback with locale chain
   *
   * @remarks
   * A fallback locale function implemented with a fallback chain algorithm. It's used in VueI18n as default.
   *
   * @param ctx - A {@link CoreContext | context}
   * @param fallback - A {@link FallbackLocale | fallback locale}
   * @param start - A starting {@link Locale | locale}
   *
   * @returns Fallback locales
   *
   * @VueI18nSee [Fallbacking](../guide/essentials/fallback)
   *
   * @VueI18nGeneral
   */
  function fallbackWithLocaleChain(ctx, fallback, start) {
      const startLocale = isString(start) ? start : DEFAULT_LOCALE;
      const context = ctx;
      if (!context.__localeChainCache) {
          context.__localeChainCache = new Map();
      }
      let chain = context.__localeChainCache.get(startLocale);
      if (!chain) {
          chain = [];
          // first block defined by start
          let block = [start];
          // while any intervening block found
          while (isArray(block)) {
              block = appendBlockToChain(chain, block, fallback);
          }
          // prettier-ignore
          // last block defined by default
          const defaults = isArray(fallback) || !isPlainObject(fallback)
              ? fallback
              : fallback['default']
                  ? fallback['default']
                  : null;
          // convert defaults to array
          block = isString(defaults) ? [defaults] : defaults;
          if (isArray(block)) {
              appendBlockToChain(chain, block, false);
          }
          context.__localeChainCache.set(startLocale, chain);
      }
      return chain;
  }
  function appendBlockToChain(chain, block, blocks) {
      let follow = true;
      for (let i = 0; i < block.length && isBoolean(follow); i++) {
          const locale = block[i];
          if (isString(locale)) {
              follow = appendLocaleToChain(chain, block[i], blocks);
          }
      }
      return follow;
  }
  function appendLocaleToChain(chain, locale, blocks) {
      let follow;
      const tokens = locale.split('-');
      do {
          const target = tokens.join('-');
          follow = appendItemToChain(chain, target, blocks);
          tokens.splice(-1, 1);
      } while (tokens.length && follow === true);
      return follow;
  }
  function appendItemToChain(chain, target, blocks) {
      let follow = false;
      if (!chain.includes(target)) {
          follow = true;
          if (target) {
              follow = target[target.length - 1] !== '!';
              const locale = target.replace(/!/g, '');
              chain.push(locale);
              if ((isArray(blocks) || isPlainObject(blocks)) &&
                  blocks[locale] // eslint-disable-line @typescript-eslint/no-explicit-any
              ) {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  follow = blocks[locale];
              }
          }
      }
      return follow;
  }

  const pathStateMachine = [];
  pathStateMachine[0 /* States.BEFORE_PATH */] = {
      ["w" /* PathCharTypes.WORKSPACE */]: [0 /* States.BEFORE_PATH */],
      ["i" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],
      ["[" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */],
      ["o" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */]
  };
  pathStateMachine[1 /* States.IN_PATH */] = {
      ["w" /* PathCharTypes.WORKSPACE */]: [1 /* States.IN_PATH */],
      ["." /* PathCharTypes.DOT */]: [2 /* States.BEFORE_IDENT */],
      ["[" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */],
      ["o" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */]
  };
  pathStateMachine[2 /* States.BEFORE_IDENT */] = {
      ["w" /* PathCharTypes.WORKSPACE */]: [2 /* States.BEFORE_IDENT */],
      ["i" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],
      ["0" /* PathCharTypes.ZERO */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */]
  };
  pathStateMachine[3 /* States.IN_IDENT */] = {
      ["i" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],
      ["0" /* PathCharTypes.ZERO */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],
      ["w" /* PathCharTypes.WORKSPACE */]: [1 /* States.IN_PATH */, 1 /* Actions.PUSH */],
      ["." /* PathCharTypes.DOT */]: [2 /* States.BEFORE_IDENT */, 1 /* Actions.PUSH */],
      ["[" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */, 1 /* Actions.PUSH */],
      ["o" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */, 1 /* Actions.PUSH */]
  };
  pathStateMachine[4 /* States.IN_SUB_PATH */] = {
      ["'" /* PathCharTypes.SINGLE_QUOTE */]: [5 /* States.IN_SINGLE_QUOTE */, 0 /* Actions.APPEND */],
      ["\"" /* PathCharTypes.DOUBLE_QUOTE */]: [6 /* States.IN_DOUBLE_QUOTE */, 0 /* Actions.APPEND */],
      ["[" /* PathCharTypes.LEFT_BRACKET */]: [
          4 /* States.IN_SUB_PATH */,
          2 /* Actions.INC_SUB_PATH_DEPTH */
      ],
      ["]" /* PathCharTypes.RIGHT_BRACKET */]: [1 /* States.IN_PATH */, 3 /* Actions.PUSH_SUB_PATH */],
      ["o" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,
      ["l" /* PathCharTypes.ELSE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */]
  };
  pathStateMachine[5 /* States.IN_SINGLE_QUOTE */] = {
      ["'" /* PathCharTypes.SINGLE_QUOTE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */],
      ["o" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,
      ["l" /* PathCharTypes.ELSE */]: [5 /* States.IN_SINGLE_QUOTE */, 0 /* Actions.APPEND */]
  };
  pathStateMachine[6 /* States.IN_DOUBLE_QUOTE */] = {
      ["\"" /* PathCharTypes.DOUBLE_QUOTE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */],
      ["o" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,
      ["l" /* PathCharTypes.ELSE */]: [6 /* States.IN_DOUBLE_QUOTE */, 0 /* Actions.APPEND */]
  };
  /**
   * Check if an expression is a literal value.
   */
  const literalValueRE = /^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;
  function isLiteral(exp) {
      return literalValueRE.test(exp);
  }
  /**
   * Strip quotes from a string
   */
  function stripQuotes(str) {
      const a = str.charCodeAt(0);
      const b = str.charCodeAt(str.length - 1);
      return a === b && (a === 0x22 || a === 0x27) ? str.slice(1, -1) : str;
  }
  /**
   * Determine the type of a character in a keypath.
   */
  function getPathCharType(ch) {
      if (ch === undefined || ch === null) {
          return "o" /* PathCharTypes.END_OF_FAIL */;
      }
      const code = ch.charCodeAt(0);
      switch (code) {
          case 0x5b: // [
          case 0x5d: // ]
          case 0x2e: // .
          case 0x22: // "
          case 0x27: // '
              return ch;
          case 0x5f: // _
          case 0x24: // $
          case 0x2d: // -
              return "i" /* PathCharTypes.IDENT */;
          case 0x09: // Tab (HT)
          case 0x0a: // Newline (LF)
          case 0x0d: // Return (CR)
          case 0xa0: // No-break space (NBSP)
          case 0xfeff: // Byte Order Mark (BOM)
          case 0x2028: // Line Separator (LS)
          case 0x2029: // Paragraph Separator (PS)
              return "w" /* PathCharTypes.WORKSPACE */;
      }
      return "i" /* PathCharTypes.IDENT */;
  }
  /**
   * Format a subPath, return its plain form if it is
   * a literal string or number. Otherwise prepend the
   * dynamic indicator (*).
   */
  function formatSubPath(path) {
      const trimmed = path.trim();
      // invalid leading 0
      if (path.charAt(0) === '0' && isNaN(parseInt(path))) {
          return false;
      }
      return isLiteral(trimmed)
          ? stripQuotes(trimmed)
          : "*" /* PathCharTypes.ASTARISK */ + trimmed;
  }
  /**
   * Parse a string path into an array of segments
   */
  function parse(path) {
      const keys = [];
      let index = -1;
      let mode = 0 /* States.BEFORE_PATH */;
      let subPathDepth = 0;
      let c;
      let key; // eslint-disable-line
      let newChar;
      let type;
      let transition;
      let action;
      let typeMap;
      const actions = [];
      actions[0 /* Actions.APPEND */] = () => {
          if (key === undefined) {
              key = newChar;
          }
          else {
              key += newChar;
          }
      };
      actions[1 /* Actions.PUSH */] = () => {
          if (key !== undefined) {
              keys.push(key);
              key = undefined;
          }
      };
      actions[2 /* Actions.INC_SUB_PATH_DEPTH */] = () => {
          actions[0 /* Actions.APPEND */]();
          subPathDepth++;
      };
      actions[3 /* Actions.PUSH_SUB_PATH */] = () => {
          if (subPathDepth > 0) {
              subPathDepth--;
              mode = 4 /* States.IN_SUB_PATH */;
              actions[0 /* Actions.APPEND */]();
          }
          else {
              subPathDepth = 0;
              if (key === undefined) {
                  return false;
              }
              key = formatSubPath(key);
              if (key === false) {
                  return false;
              }
              else {
                  actions[1 /* Actions.PUSH */]();
              }
          }
      };
      function maybeUnescapeQuote() {
          const nextChar = path[index + 1];
          if ((mode === 5 /* States.IN_SINGLE_QUOTE */ &&
              nextChar === "'" /* PathCharTypes.SINGLE_QUOTE */) ||
              (mode === 6 /* States.IN_DOUBLE_QUOTE */ &&
                  nextChar === "\"" /* PathCharTypes.DOUBLE_QUOTE */)) {
              index++;
              newChar = '\\' + nextChar;
              actions[0 /* Actions.APPEND */]();
              return true;
          }
      }
      while (mode !== null) {
          index++;
          c = path[index];
          if (c === '\\' && maybeUnescapeQuote()) {
              continue;
          }
          type = getPathCharType(c);
          typeMap = pathStateMachine[mode];
          transition = typeMap[type] || typeMap["l" /* PathCharTypes.ELSE */] || 8 /* States.ERROR */;
          // check parse error
          if (transition === 8 /* States.ERROR */) {
              return;
          }
          mode = transition[0];
          if (transition[1] !== undefined) {
              action = actions[transition[1]];
              if (action) {
                  newChar = c;
                  if (action() === false) {
                      return;
                  }
              }
          }
          // check parse finish
          if (mode === 7 /* States.AFTER_PATH */) {
              return keys;
          }
      }
  }
  // path token cache
  const cache = new Map();
  /**
   * key-value message resolver
   *
   * @remarks
   * Resolves messages with the key-value structure. Note that messages with a hierarchical structure such as objects cannot be resolved
   *
   * @param obj - A target object to be resolved with path
   * @param path - A {@link Path | path} to resolve the value of message
   *
   * @returns A resolved {@link PathValue | path value}
   *
   * @VueI18nGeneral
   */
  function resolveWithKeyValue(obj, path) {
      return isObject(obj) ? obj[path] : null;
  }
  /**
   * message resolver
   *
   * @remarks
   * Resolves messages. messages with a hierarchical structure such as objects can be resolved. This resolver is used in VueI18n as default.
   *
   * @param obj - A target object to be resolved with path
   * @param path - A {@link Path | path} to resolve the value of message
   *
   * @returns A resolved {@link PathValue | path value}
   *
   * @VueI18nGeneral
   */
  function resolveValue(obj, path) {
      // check object
      if (!isObject(obj)) {
          return null;
      }
      // parse path
      let hit = cache.get(path);
      if (!hit) {
          hit = parse(path);
          if (hit) {
              cache.set(path, hit);
          }
      }
      // check hit
      if (!hit) {
          return null;
      }
      // resolve path value
      const len = hit.length;
      let last = obj;
      let i = 0;
      while (i < len) {
          const key = hit[i];
          /**
           * NOTE:
           * if `key` is intlify message format AST node key and `last` is intlify message format AST, skip it.
           * because the AST node is not a key-value structure.
           */
          if (AST_NODE_PROPS_KEYS.includes(key) && isMessageAST(last)) {
              return null;
          }
          const val = last[key];
          if (val === undefined) {
              return null;
          }
          if (isFunction(last)) {
              return null;
          }
          last = val;
          i++;
      }
      return last;
  }

  const CoreWarnCodes = {
      NOT_FOUND_KEY: 1,
      FALLBACK_TO_TRANSLATE: 2,
      CANNOT_FORMAT_NUMBER: 3,
      FALLBACK_TO_NUMBER_FORMAT: 4,
      CANNOT_FORMAT_DATE: 5,
      FALLBACK_TO_DATE_FORMAT: 6,
      EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER: 7
  };
  const CORE_WARN_CODES_EXTEND_POINT = 8;
  /** @internal */
  const warnMessages = {
      [CoreWarnCodes.NOT_FOUND_KEY]: `Not found '{key}' key in '{locale}' locale messages.`,
      [CoreWarnCodes.FALLBACK_TO_TRANSLATE]: `Fall back to translate '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.CANNOT_FORMAT_NUMBER]: `Cannot format a number value due to not supported Intl.NumberFormat.`,
      [CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]: `Fall back to number format '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.CANNOT_FORMAT_DATE]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,
      [CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]: `Fall back to datetime format '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]: `This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future.`
  };
  function getWarnMessage(code, ...args) {
      return format$1(warnMessages[code], ...args);
  }

  /* eslint-disable @typescript-eslint/no-explicit-any */
  /**
   * Intlify core-base version
   * @internal
   */
  const VERSION = '10.0.7';
  const NOT_REOSLVED = -1;
  const DEFAULT_LOCALE = 'en-US';
  const MISSING_RESOLVE_VALUE = '';
  const capitalize = (str) => `${str.charAt(0).toLocaleUpperCase()}${str.substr(1)}`;
  function getDefaultLinkedModifiers() {
      return {
          upper: (val, type) => {
              // prettier-ignore
              return type === 'text' && isString(val)
                  ? val.toUpperCase()
                  : type === 'vnode' && isObject(val) && '__v_isVNode' in val
                      ? val.children.toUpperCase()
                      : val;
          },
          lower: (val, type) => {
              // prettier-ignore
              return type === 'text' && isString(val)
                  ? val.toLowerCase()
                  : type === 'vnode' && isObject(val) && '__v_isVNode' in val
                      ? val.children.toLowerCase()
                      : val;
          },
          capitalize: (val, type) => {
              // prettier-ignore
              return (type === 'text' && isString(val)
                  ? capitalize(val)
                  : type === 'vnode' && isObject(val) && '__v_isVNode' in val
                      ? capitalize(val.children)
                      : val);
          }
      };
  }
  let _compiler;
  function registerMessageCompiler(compiler) {
      _compiler = compiler;
  }
  let _resolver;
  /**
   * Register the message resolver
   *
   * @param resolver - A {@link MessageResolver} function
   *
   * @VueI18nGeneral
   */
  function registerMessageResolver(resolver) {
      _resolver = resolver;
  }
  let _fallbacker;
  /**
   * Register the locale fallbacker
   *
   * @param fallbacker - A {@link LocaleFallbacker} function
   *
   * @VueI18nGeneral
   */
  function registerLocaleFallbacker(fallbacker) {
      _fallbacker = fallbacker;
  }
  // Additional Meta for Intlify DevTools
  let _additionalMeta = null;
  /* #__NO_SIDE_EFFECTS__ */
  const setAdditionalMeta = (meta) => {
      _additionalMeta = meta;
  };
  /* #__NO_SIDE_EFFECTS__ */
  const getAdditionalMeta = () => _additionalMeta;
  let _fallbackContext = null;
  const setFallbackContext = (context) => {
      _fallbackContext = context;
  };
  const getFallbackContext = () => _fallbackContext;
  // ID for CoreContext
  let _cid = 0;
  function createCoreContext(options = {}) {
      // setup options
      const onWarn = isFunction(options.onWarn) ? options.onWarn : warn;
      const version = isString(options.version) ? options.version : VERSION;
      const locale = isString(options.locale) || isFunction(options.locale)
          ? options.locale
          : DEFAULT_LOCALE;
      const _locale = isFunction(locale) ? DEFAULT_LOCALE : locale;
      const fallbackLocale = isArray(options.fallbackLocale) ||
          isPlainObject(options.fallbackLocale) ||
          isString(options.fallbackLocale) ||
          options.fallbackLocale === false
          ? options.fallbackLocale
          : _locale;
      const messages = isPlainObject(options.messages)
          ? options.messages
          : createResources(_locale);
      const datetimeFormats = isPlainObject(options.datetimeFormats)
              ? options.datetimeFormats
              : createResources(_locale)
          ;
      const numberFormats = isPlainObject(options.numberFormats)
              ? options.numberFormats
              : createResources(_locale)
          ;
      const modifiers = assign(create(), options.modifiers, getDefaultLinkedModifiers());
      const pluralRules = options.pluralRules || create();
      const missing = isFunction(options.missing) ? options.missing : null;
      const missingWarn = isBoolean(options.missingWarn) || isRegExp(options.missingWarn)
          ? options.missingWarn
          : true;
      const fallbackWarn = isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn)
          ? options.fallbackWarn
          : true;
      const fallbackFormat = !!options.fallbackFormat;
      const unresolving = !!options.unresolving;
      const postTranslation = isFunction(options.postTranslation)
          ? options.postTranslation
          : null;
      const processor = isPlainObject(options.processor) ? options.processor : null;
      const warnHtmlMessage = isBoolean(options.warnHtmlMessage)
          ? options.warnHtmlMessage
          : true;
      const escapeParameter = !!options.escapeParameter;
      const messageCompiler = isFunction(options.messageCompiler)
          ? options.messageCompiler
          : _compiler;
      const messageResolver = isFunction(options.messageResolver)
          ? options.messageResolver
          : _resolver || resolveWithKeyValue;
      const localeFallbacker = isFunction(options.localeFallbacker)
          ? options.localeFallbacker
          : _fallbacker || fallbackWithSimple;
      const fallbackContext = isObject(options.fallbackContext)
          ? options.fallbackContext
          : undefined;
      // setup internal options
      const internalOptions = options;
      const __datetimeFormatters = isObject(internalOptions.__datetimeFormatters)
              ? internalOptions.__datetimeFormatters
              : new Map()
          ;
      const __numberFormatters = isObject(internalOptions.__numberFormatters)
              ? internalOptions.__numberFormatters
              : new Map()
          ;
      const __meta = isObject(internalOptions.__meta) ? internalOptions.__meta : {};
      _cid++;
      const context = {
          version,
          cid: _cid,
          locale,
          fallbackLocale,
          messages,
          modifiers,
          pluralRules,
          missing,
          missingWarn,
          fallbackWarn,
          fallbackFormat,
          unresolving,
          postTranslation,
          processor,
          warnHtmlMessage,
          escapeParameter,
          messageCompiler,
          messageResolver,
          localeFallbacker,
          fallbackContext,
          onWarn,
          __meta
      };
      {
          context.datetimeFormats = datetimeFormats;
          context.numberFormats = numberFormats;
          context.__datetimeFormatters = __datetimeFormatters;
          context.__numberFormatters = __numberFormatters;
      }
      // for vue-devtools timeline event
      {
          context.__v_emitter =
              internalOptions.__v_emitter != null
                  ? internalOptions.__v_emitter
                  : undefined;
      }
      // NOTE: experimental !!
      {
          initI18nDevTools(context, version, __meta);
      }
      return context;
  }
  const createResources = (locale) => ({ [locale]: create() });
  /** @internal */
  function isTranslateFallbackWarn(fallback, key) {
      return fallback instanceof RegExp ? fallback.test(key) : fallback;
  }
  /** @internal */
  function isTranslateMissingWarn(missing, key) {
      return missing instanceof RegExp ? missing.test(key) : missing;
  }
  /** @internal */
  function handleMissing(context, key, locale, missingWarn, type) {
      const { missing, onWarn } = context;
      // for vue-devtools timeline event
      {
          const emitter = context.__v_emitter;
          if (emitter) {
              emitter.emit('missing', {
                  locale,
                  key,
                  type,
                  groupId: `${type}:${key}`
              });
          }
      }
      if (missing !== null) {
          const ret = missing(context, locale, key, type);
          return isString(ret) ? ret : key;
      }
      else {
          if (isTranslateMissingWarn(missingWarn, key)) {
              onWarn(getWarnMessage(CoreWarnCodes.NOT_FOUND_KEY, { key, locale }));
          }
          return key;
      }
  }
  /** @internal */
  function updateFallbackLocale(ctx, locale, fallback) {
      const context = ctx;
      context.__localeChainCache = new Map();
      ctx.localeFallbacker(ctx, fallback, locale);
  }
  /** @internal */
  function isAlmostSameLocale(locale, compareLocale) {
      if (locale === compareLocale)
          return false;
      return locale.split('-')[0] === compareLocale.split('-')[0];
  }
  /** @internal */
  function isImplicitFallback(targetLocale, locales) {
      const index = locales.indexOf(targetLocale);
      if (index === -1) {
          return false;
      }
      for (let i = index + 1; i < locales.length; i++) {
          if (isAlmostSameLocale(targetLocale, locales[i])) {
              return true;
          }
      }
      return false;
  }
  /* eslint-enable @typescript-eslint/no-explicit-any */

  const intlDefined = typeof Intl !== 'undefined';
  const Availabilities = {
      dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== 'undefined',
      numberFormat: intlDefined && typeof Intl.NumberFormat !== 'undefined'
  };

  // implementation of `datetime` function
  function datetime(context, ...args) {
      const { datetimeFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;
      const { __datetimeFormatters } = context;
      if (!Availabilities.dateTimeFormat) {
          onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_DATE));
          return MISSING_RESOLVE_VALUE;
      }
      const [key, value, options, overrides] = parseDateTimeArgs(...args);
      const missingWarn = isBoolean(options.missingWarn)
          ? options.missingWarn
          : context.missingWarn;
      const fallbackWarn = isBoolean(options.fallbackWarn)
          ? options.fallbackWarn
          : context.fallbackWarn;
      const part = !!options.part;
      const locale = getLocale(context, options);
      const locales = localeFallbacker(context, // eslint-disable-line @typescript-eslint/no-explicit-any
      fallbackLocale, locale);
      if (!isString(key) || key === '') {
          return new Intl.DateTimeFormat(locale, overrides).format(value);
      }
      // resolve format
      let datetimeFormat = {};
      let targetLocale;
      let format = null;
      let from = locale;
      let to = null;
      const type = 'datetime format';
      for (let i = 0; i < locales.length; i++) {
          targetLocale = to = locales[i];
          if (locale !== targetLocale &&
              isTranslateFallbackWarn(fallbackWarn, key)) {
              onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_DATE_FORMAT, {
                  key,
                  target: targetLocale
              }));
          }
          // for vue-devtools timeline event
          if (locale !== targetLocale) {
              const emitter = context.__v_emitter;
              if (emitter) {
                  emitter.emit('fallback', {
                      type,
                      key,
                      from,
                      to,
                      groupId: `${type}:${key}`
                  });
              }
          }
          datetimeFormat =
              datetimeFormats[targetLocale] || {};
          format = datetimeFormat[key];
          if (isPlainObject(format))
              break;
          handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any
          from = to;
      }
      // checking format and target locale
      if (!isPlainObject(format) || !isString(targetLocale)) {
          return unresolving ? NOT_REOSLVED : key;
      }
      let id = `${targetLocale}__${key}`;
      if (!isEmptyObject(overrides)) {
          id = `${id}__${JSON.stringify(overrides)}`;
      }
      let formatter = __datetimeFormatters.get(id);
      if (!formatter) {
          formatter = new Intl.DateTimeFormat(targetLocale, assign({}, format, overrides));
          __datetimeFormatters.set(id, formatter);
      }
      return !part ? formatter.format(value) : formatter.formatToParts(value);
  }
  /** @internal */
  const DATETIME_FORMAT_OPTIONS_KEYS = [
      'localeMatcher',
      'weekday',
      'era',
      'year',
      'month',
      'day',
      'hour',
      'minute',
      'second',
      'timeZoneName',
      'formatMatcher',
      'hour12',
      'timeZone',
      'dateStyle',
      'timeStyle',
      'calendar',
      'dayPeriod',
      'numberingSystem',
      'hourCycle',
      'fractionalSecondDigits'
  ];
  /** @internal */
  function parseDateTimeArgs(...args) {
      const [arg1, arg2, arg3, arg4] = args;
      const options = create();
      let overrides = create();
      let value;
      if (isString(arg1)) {
          // Only allow ISO strings - other date formats are often supported,
          // but may cause different results in different browsers.
          const matches = arg1.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);
          if (!matches) {
              throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);
          }
          // Some browsers can not parse the iso datetime separated by space,
          // this is a compromise solution by replace the 'T'/' ' with 'T'
          const dateTime = matches[3]
              ? matches[3].trim().startsWith('T')
                  ? `${matches[1].trim()}${matches[3].trim()}`
                  : `${matches[1].trim()}T${matches[3].trim()}`
              : matches[1].trim();
          value = new Date(dateTime);
          try {
              // This will fail if the date is not valid
              value.toISOString();
          }
          catch {
              throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);
          }
      }
      else if (isDate(arg1)) {
          if (isNaN(arg1.getTime())) {
              throw createCoreError(CoreErrorCodes.INVALID_DATE_ARGUMENT);
          }
          value = arg1;
      }
      else if (isNumber(arg1)) {
          value = arg1;
      }
      else {
          throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
      }
      if (isString(arg2)) {
          options.key = arg2;
      }
      else if (isPlainObject(arg2)) {
          Object.keys(arg2).forEach(key => {
              if (DATETIME_FORMAT_OPTIONS_KEYS.includes(key)) {
                  overrides[key] = arg2[key];
              }
              else {
                  options[key] = arg2[key];
              }
          });
      }
      if (isString(arg3)) {
          options.locale = arg3;
      }
      else if (isPlainObject(arg3)) {
          overrides = arg3;
      }
      if (isPlainObject(arg4)) {
          overrides = arg4;
      }
      return [options.key || '', value, options, overrides];
  }
  /** @internal */
  function clearDateTimeFormat(ctx, locale, format) {
      const context = ctx;
      for (const key in format) {
          const id = `${locale}__${key}`;
          if (!context.__datetimeFormatters.has(id)) {
              continue;
          }
          context.__datetimeFormatters.delete(id);
      }
  }

  // implementation of `number` function
  function number(context, ...args) {
      const { numberFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;
      const { __numberFormatters } = context;
      if (!Availabilities.numberFormat) {
          onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_NUMBER));
          return MISSING_RESOLVE_VALUE;
      }
      const [key, value, options, overrides] = parseNumberArgs(...args);
      const missingWarn = isBoolean(options.missingWarn)
          ? options.missingWarn
          : context.missingWarn;
      const fallbackWarn = isBoolean(options.fallbackWarn)
          ? options.fallbackWarn
          : context.fallbackWarn;
      const part = !!options.part;
      const locale = getLocale(context, options);
      const locales = localeFallbacker(context, // eslint-disable-line @typescript-eslint/no-explicit-any
      fallbackLocale, locale);
      if (!isString(key) || key === '') {
          return new Intl.NumberFormat(locale, overrides).format(value);
      }
      // resolve format
      let numberFormat = {};
      let targetLocale;
      let format = null;
      let from = locale;
      let to = null;
      const type = 'number format';
      for (let i = 0; i < locales.length; i++) {
          targetLocale = to = locales[i];
          if (locale !== targetLocale &&
              isTranslateFallbackWarn(fallbackWarn, key)) {
              onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT, {
                  key,
                  target: targetLocale
              }));
          }
          // for vue-devtools timeline event
          if (locale !== targetLocale) {
              const emitter = context.__v_emitter;
              if (emitter) {
                  emitter.emit('fallback', {
                      type,
                      key,
                      from,
                      to,
                      groupId: `${type}:${key}`
                  });
              }
          }
          numberFormat =
              numberFormats[targetLocale] || {};
          format = numberFormat[key];
          if (isPlainObject(format))
              break;
          handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any
          from = to;
      }
      // checking format and target locale
      if (!isPlainObject(format) || !isString(targetLocale)) {
          return unresolving ? NOT_REOSLVED : key;
      }
      let id = `${targetLocale}__${key}`;
      if (!isEmptyObject(overrides)) {
          id = `${id}__${JSON.stringify(overrides)}`;
      }
      let formatter = __numberFormatters.get(id);
      if (!formatter) {
          formatter = new Intl.NumberFormat(targetLocale, assign({}, format, overrides));
          __numberFormatters.set(id, formatter);
      }
      return !part ? formatter.format(value) : formatter.formatToParts(value);
  }
  /** @internal */
  const NUMBER_FORMAT_OPTIONS_KEYS = [
      'localeMatcher',
      'style',
      'currency',
      'currencyDisplay',
      'currencySign',
      'useGrouping',
      'minimumIntegerDigits',
      'minimumFractionDigits',
      'maximumFractionDigits',
      'minimumSignificantDigits',
      'maximumSignificantDigits',
      'compactDisplay',
      'notation',
      'signDisplay',
      'unit',
      'unitDisplay',
      'roundingMode',
      'roundingPriority',
      'roundingIncrement',
      'trailingZeroDisplay'
  ];
  /** @internal */
  function parseNumberArgs(...args) {
      const [arg1, arg2, arg3, arg4] = args;
      const options = create();
      let overrides = create();
      if (!isNumber(arg1)) {
          throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
      }
      const value = arg1;
      if (isString(arg2)) {
          options.key = arg2;
      }
      else if (isPlainObject(arg2)) {
          Object.keys(arg2).forEach(key => {
              if (NUMBER_FORMAT_OPTIONS_KEYS.includes(key)) {
                  overrides[key] = arg2[key];
              }
              else {
                  options[key] = arg2[key];
              }
          });
      }
      if (isString(arg3)) {
          options.locale = arg3;
      }
      else if (isPlainObject(arg3)) {
          overrides = arg3;
      }
      if (isPlainObject(arg4)) {
          overrides = arg4;
      }
      return [options.key || '', value, options, overrides];
  }
  /** @internal */
  function clearNumberFormat(ctx, locale, format) {
      const context = ctx;
      for (const key in format) {
          const id = `${locale}__${key}`;
          if (!context.__numberFormatters.has(id)) {
              continue;
          }
          context.__numberFormatters.delete(id);
      }
  }

  const DEFAULT_MODIFIER = (str) => str;
  const DEFAULT_MESSAGE = (ctx) => ''; // eslint-disable-line
  const DEFAULT_MESSAGE_DATA_TYPE = 'text';
  const DEFAULT_NORMALIZE = (values) => values.length === 0 ? '' : join(values);
  const DEFAULT_INTERPOLATE = toDisplayString;
  function pluralDefault(choice, choicesLength) {
      choice = Math.abs(choice);
      if (choicesLength === 2) {
          // prettier-ignore
          return choice
              ? choice > 1
                  ? 1
                  : 0
              : 1;
      }
      return choice ? Math.min(choice, 2) : 0;
  }
  function getPluralIndex(options) {
      // prettier-ignore
      const index = isNumber(options.pluralIndex)
          ? options.pluralIndex
          : -1;
      // prettier-ignore
      return options.named && (isNumber(options.named.count) || isNumber(options.named.n))
          ? isNumber(options.named.count)
              ? options.named.count
              : isNumber(options.named.n)
                  ? options.named.n
                  : index
          : index;
  }
  function normalizeNamed(pluralIndex, props) {
      if (!props.count) {
          props.count = pluralIndex;
      }
      if (!props.n) {
          props.n = pluralIndex;
      }
  }
  function createMessageContext(options = {}) {
      const locale = options.locale;
      const pluralIndex = getPluralIndex(options);
      const pluralRule = isObject(options.pluralRules) &&
          isString(locale) &&
          isFunction(options.pluralRules[locale])
          ? options.pluralRules[locale]
          : pluralDefault;
      const orgPluralRule = isObject(options.pluralRules) &&
          isString(locale) &&
          isFunction(options.pluralRules[locale])
          ? pluralDefault
          : undefined;
      const plural = (messages) => {
          return messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];
      };
      const _list = options.list || [];
      const list = (index) => _list[index];
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const _named = options.named || create();
      isNumber(options.pluralIndex) && normalizeNamed(pluralIndex, _named);
      const named = (key) => _named[key];
      function message(key, useLinked) {
          // prettier-ignore
          const msg = isFunction(options.messages)
              ? options.messages(key, !!useLinked)
              : isObject(options.messages)
                  ? options.messages[key]
                  : false;
          return !msg
              ? options.parent
                  ? options.parent.message(key) // resolve from parent messages
                  : DEFAULT_MESSAGE
              : msg;
      }
      const _modifier = (name) => options.modifiers
          ? options.modifiers[name]
          : DEFAULT_MODIFIER;
      const normalize = isPlainObject(options.processor) && isFunction(options.processor.normalize)
          ? options.processor.normalize
          : DEFAULT_NORMALIZE;
      const interpolate = isPlainObject(options.processor) &&
          isFunction(options.processor.interpolate)
          ? options.processor.interpolate
          : DEFAULT_INTERPOLATE;
      const type = isPlainObject(options.processor) && isString(options.processor.type)
          ? options.processor.type
          : DEFAULT_MESSAGE_DATA_TYPE;
      const linked = (key, ...args) => {
          const [arg1, arg2] = args;
          let type = 'text';
          let modifier = '';
          if (args.length === 1) {
              if (isObject(arg1)) {
                  modifier = arg1.modifier || modifier;
                  type = arg1.type || type;
              }
              else if (isString(arg1)) {
                  modifier = arg1 || modifier;
              }
          }
          else if (args.length === 2) {
              if (isString(arg1)) {
                  modifier = arg1 || modifier;
              }
              if (isString(arg2)) {
                  type = arg2 || type;
              }
          }
          const ret = message(key, true)(ctx);
          const msg = 
          // The message in vnode resolved with linked are returned as an array by processor.nomalize
          type === 'vnode' && isArray(ret) && modifier
              ? ret[0]
              : ret;
          return modifier ? _modifier(modifier)(msg, type) : msg;
      };
      const ctx = {
          ["list" /* HelperNameMap.LIST */]: list,
          ["named" /* HelperNameMap.NAMED */]: named,
          ["plural" /* HelperNameMap.PLURAL */]: plural,
          ["linked" /* HelperNameMap.LINKED */]: linked,
          ["message" /* HelperNameMap.MESSAGE */]: message,
          ["type" /* HelperNameMap.TYPE */]: type,
          ["interpolate" /* HelperNameMap.INTERPOLATE */]: interpolate,
          ["normalize" /* HelperNameMap.NORMALIZE */]: normalize,
          ["values" /* HelperNameMap.VALUES */]: assign(create(), _list, _named)
      };
      return ctx;
  }

  const NOOP_MESSAGE_FUNCTION = () => '';
  const isMessageFunction = (val) => isFunction(val);
  // implementation of `translate` function
  function translate(context, ...args) {
      const { fallbackFormat, postTranslation, unresolving, messageCompiler, fallbackLocale, messages } = context;
      const [key, options] = parseTranslateArgs(...args);
      const missingWarn = isBoolean(options.missingWarn)
          ? options.missingWarn
          : context.missingWarn;
      const fallbackWarn = isBoolean(options.fallbackWarn)
          ? options.fallbackWarn
          : context.fallbackWarn;
      const escapeParameter = isBoolean(options.escapeParameter)
          ? options.escapeParameter
          : context.escapeParameter;
      const resolvedMessage = !!options.resolvedMessage;
      // prettier-ignore
      const defaultMsgOrKey = isString(options.default) || isBoolean(options.default) // default by function option
          ? !isBoolean(options.default)
              ? options.default
              : (!messageCompiler ? () => key : key)
          : fallbackFormat // default by `fallbackFormat` option
              ? (!messageCompiler ? () => key : key)
              : null;
      const enableDefaultMsg = fallbackFormat ||
          (defaultMsgOrKey != null &&
              (isString(defaultMsgOrKey) || isFunction(defaultMsgOrKey)));
      const locale = getLocale(context, options);
      // escape params
      escapeParameter && escapeParams(options);
      // resolve message format
      // eslint-disable-next-line prefer-const
      let [formatScope, targetLocale, message] = !resolvedMessage
          ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn)
          : [
              key,
              locale,
              messages[locale] || create()
          ];
      // NOTE:
      //  Fix to work around `ssrTransfrom` bug in Vite.
      //  https://github.com/vitejs/vite/issues/4306
      //  To get around this, use temporary variables.
      //  https://github.com/nuxt/framework/issues/1461#issuecomment-954606243
      let format = formatScope;
      // if you use default message, set it as message format!
      let cacheBaseKey = key;
      if (!resolvedMessage &&
          !(isString(format) ||
              isMessageAST(format) ||
              isMessageFunction(format))) {
          if (enableDefaultMsg) {
              format = defaultMsgOrKey;
              cacheBaseKey = format;
          }
      }
      // checking message format and target locale
      if (!resolvedMessage &&
          (!(isString(format) ||
              isMessageAST(format) ||
              isMessageFunction(format)) ||
              !isString(targetLocale))) {
          return unresolving ? NOT_REOSLVED : key;
      }
      // TODO: refactor
      if (isString(format) && context.messageCompiler == null) {
          warn(`The message format compilation is not supported in this build. ` +
              `Because message compiler isn't included. ` +
              `You need to pre-compilation all message format. ` +
              `So translate function return '${key}'.`);
          return key;
      }
      // setup compile error detecting
      let occurred = false;
      const onError = () => {
          occurred = true;
      };
      // compile message format
      const msg = !isMessageFunction(format)
          ? compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, onError)
          : format;
      // if occurred compile error, return the message format
      if (occurred) {
          return format;
      }
      // evaluate message with context
      const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);
      const msgContext = createMessageContext(ctxOptions);
      const messaged = evaluateMessage(context, msg, msgContext);
      // if use post translation option, proceed it with handler
      const ret = postTranslation
          ? postTranslation(messaged, key)
          : messaged;
      // NOTE: experimental !!
      {
          // prettier-ignore
          const payloads = {
              timestamp: Date.now(),
              key: isString(key)
                  ? key
                  : isMessageFunction(format)
                      ? format.key
                      : '',
              locale: targetLocale || (isMessageFunction(format)
                  ? format.locale
                  : ''),
              format: isString(format)
                  ? format
                  : isMessageFunction(format)
                      ? format.source
                      : '',
              message: ret
          };
          payloads.meta = assign({}, context.__meta, getAdditionalMeta() || {});
          translateDevTools(payloads);
      }
      return ret;
  }
  function escapeParams(options) {
      if (isArray(options.list)) {
          options.list = options.list.map(item => isString(item) ? escapeHtml(item) : item);
      }
      else if (isObject(options.named)) {
          Object.keys(options.named).forEach(key => {
              if (isString(options.named[key])) {
                  options.named[key] = escapeHtml(options.named[key]);
              }
          });
      }
  }
  function resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {
      const { messages, onWarn, messageResolver: resolveValue, localeFallbacker } = context;
      const locales = localeFallbacker(context, fallbackLocale, locale); // eslint-disable-line @typescript-eslint/no-explicit-any
      let message = create();
      let targetLocale;
      let format = null;
      let from = locale;
      let to = null;
      const type = 'translate';
      for (let i = 0; i < locales.length; i++) {
          targetLocale = to = locales[i];
          if (locale !== targetLocale &&
              !isAlmostSameLocale(locale, targetLocale) &&
              isTranslateFallbackWarn(fallbackWarn, key)) {
              onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_TRANSLATE, {
                  key,
                  target: targetLocale
              }));
          }
          // for vue-devtools timeline event
          if (locale !== targetLocale) {
              const emitter = context.__v_emitter;
              if (emitter) {
                  emitter.emit('fallback', {
                      type,
                      key,
                      from,
                      to,
                      groupId: `${type}:${key}`
                  });
              }
          }
          message =
              messages[targetLocale] || create();
          // for vue-devtools timeline event
          let start = null;
          let startTag;
          let endTag;
          if (inBrowser) {
              start = window.performance.now();
              startTag = 'intlify-message-resolve-start';
              endTag = 'intlify-message-resolve-end';
              mark && mark(startTag);
          }
          if ((format = resolveValue(message, key)) === null) {
              // if null, resolve with object key path
              format = message[key]; // eslint-disable-line @typescript-eslint/no-explicit-any
          }
          // for vue-devtools timeline event
          if (inBrowser) {
              const end = window.performance.now();
              const emitter = context.__v_emitter;
              if (emitter && start && format) {
                  emitter.emit('message-resolve', {
                      type: 'message-resolve',
                      key,
                      message: format,
                      time: end - start,
                      groupId: `${type}:${key}`
                  });
              }
              if (startTag && endTag && mark && measure) {
                  mark(endTag);
                  measure('intlify message resolve', startTag, endTag);
              }
          }
          if (isString(format) || isMessageAST(format) || isMessageFunction(format)) {
              break;
          }
          if (!isImplicitFallback(targetLocale, locales)) {
              const missingRet = handleMissing(context, // eslint-disable-line @typescript-eslint/no-explicit-any
              key, targetLocale, missingWarn, type);
              if (missingRet !== key) {
                  format = missingRet;
              }
          }
          from = to;
      }
      return [format, targetLocale, message];
  }
  function compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, onError) {
      const { messageCompiler, warnHtmlMessage } = context;
      if (isMessageFunction(format)) {
          const msg = format;
          msg.locale = msg.locale || targetLocale;
          msg.key = msg.key || key;
          return msg;
      }
      if (messageCompiler == null) {
          const msg = (() => format);
          msg.locale = targetLocale;
          msg.key = key;
          return msg;
      }
      // for vue-devtools timeline event
      let start = null;
      let startTag;
      let endTag;
      if (inBrowser) {
          start = window.performance.now();
          startTag = 'intlify-message-compilation-start';
          endTag = 'intlify-message-compilation-end';
          mark && mark(startTag);
      }
      const msg = messageCompiler(format, getCompileContext(context, targetLocale, cacheBaseKey, format, warnHtmlMessage, onError));
      // for vue-devtools timeline event
      if (inBrowser) {
          const end = window.performance.now();
          const emitter = context.__v_emitter;
          if (emitter && start) {
              emitter.emit('message-compilation', {
                  type: 'message-compilation',
                  message: format,
                  time: end - start,
                  groupId: `${'translate'}:${key}`
              });
          }
          if (startTag && endTag && mark && measure) {
              mark(endTag);
              measure('intlify message compilation', startTag, endTag);
          }
      }
      msg.locale = targetLocale;
      msg.key = key;
      msg.source = format;
      return msg;
  }
  function evaluateMessage(context, msg, msgCtx) {
      // for vue-devtools timeline event
      let start = null;
      let startTag;
      let endTag;
      if (inBrowser) {
          start = window.performance.now();
          startTag = 'intlify-message-evaluation-start';
          endTag = 'intlify-message-evaluation-end';
          mark && mark(startTag);
      }
      const messaged = msg(msgCtx);
      // for vue-devtools timeline event
      if (inBrowser) {
          const end = window.performance.now();
          const emitter = context.__v_emitter;
          if (emitter && start) {
              emitter.emit('message-evaluation', {
                  type: 'message-evaluation',
                  value: messaged,
                  time: end - start,
                  groupId: `${'translate'}:${msg.key}`
              });
          }
          if (startTag && endTag && mark && measure) {
              mark(endTag);
              measure('intlify message evaluation', startTag, endTag);
          }
      }
      return messaged;
  }
  /** @internal */
  function parseTranslateArgs(...args) {
      const [arg1, arg2, arg3] = args;
      const options = create();
      if (!isString(arg1) &&
          !isNumber(arg1) &&
          !isMessageFunction(arg1) &&
          !isMessageAST(arg1)) {
          throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
      }
      // prettier-ignore
      const key = isNumber(arg1)
          ? String(arg1)
          : isMessageFunction(arg1)
              ? arg1
              : arg1;
      if (isNumber(arg2)) {
          options.plural = arg2;
      }
      else if (isString(arg2)) {
          options.default = arg2;
      }
      else if (isPlainObject(arg2) && !isEmptyObject(arg2)) {
          options.named = arg2;
      }
      else if (isArray(arg2)) {
          options.list = arg2;
      }
      if (isNumber(arg3)) {
          options.plural = arg3;
      }
      else if (isString(arg3)) {
          options.default = arg3;
      }
      else if (isPlainObject(arg3)) {
          assign(options, arg3);
      }
      return [key, options];
  }
  function getCompileContext(context, locale, key, source, warnHtmlMessage, onError) {
      return {
          locale,
          key,
          warnHtmlMessage,
          onError: (err) => {
              onError && onError(err);
              {
                  const _source = getSourceForCodeFrame(source);
                  const message = `Message compilation error: ${err.message}`;
                  const codeFrame = err.location &&
                      _source &&
                      generateCodeFrame(_source, err.location.start.offset, err.location.end.offset);
                  const emitter = context.__v_emitter;
                  if (emitter && _source) {
                      emitter.emit('compile-error', {
                          message: _source,
                          error: err.message,
                          start: err.location && err.location.start.offset,
                          end: err.location && err.location.end.offset,
                          groupId: `${'translate'}:${key}`
                      });
                  }
                  console.error(codeFrame ? `${message}\n${codeFrame}` : message);
              }
          },
          onCacheKey: (source) => generateFormatCacheKey(locale, key, source)
      };
  }
  function getSourceForCodeFrame(source) {
      if (isString(source)) {
          return source;
      }
      else {
          if (source.loc && source.loc.source) {
              return source.loc.source;
          }
      }
  }
  function getMessageContextOptions(context, locale, message, options) {
      const { modifiers, pluralRules, messageResolver: resolveValue, fallbackLocale, fallbackWarn, missingWarn, fallbackContext } = context;
      const resolveMessage = (key, useLinked) => {
          let val = resolveValue(message, key);
          // fallback
          if (val == null && (fallbackContext || useLinked)) {
              const [, , message] = resolveMessageFormat(fallbackContext || context, // NOTE: if has fallbackContext, fallback to root, else if use linked, fallback to local context
              key, locale, fallbackLocale, fallbackWarn, missingWarn);
              val = resolveValue(message, key);
          }
          if (isString(val) || isMessageAST(val)) {
              let occurred = false;
              const onError = () => {
                  occurred = true;
              };
              const msg = compileMessageFormat(context, key, locale, val, key, onError);
              return !occurred
                  ? msg
                  : NOOP_MESSAGE_FUNCTION;
          }
          else if (isMessageFunction(val)) {
              return val;
          }
          else {
              // TODO: should be implemented warning message
              return NOOP_MESSAGE_FUNCTION;
          }
      };
      const ctxOptions = {
          locale,
          modifiers,
          pluralRules,
          messages: resolveMessage
      };
      if (context.processor) {
          ctxOptions.processor = context.processor;
      }
      if (options.list) {
          ctxOptions.list = options.list;
      }
      if (options.named) {
          ctxOptions.named = options.named;
      }
      if (isNumber(options.plural)) {
          ctxOptions.pluralIndex = options.plural;
      }
      return ctxOptions;
  }

  // register message compiler for jit compilation
  registerMessageCompiler(compile);
  // register message resolver at @intlify/core
  registerMessageResolver(resolveValue);
  // register fallback locale at @intlify/core
  registerLocaleFallbacker(fallbackWithLocaleChain);

  exports.AST_NODE_PROPS_KEYS = AST_NODE_PROPS_KEYS;
  exports.CORE_ERROR_CODES_EXTEND_POINT = CORE_ERROR_CODES_EXTEND_POINT;
  exports.CORE_WARN_CODES_EXTEND_POINT = CORE_WARN_CODES_EXTEND_POINT;
  exports.CompileErrorCodes = CompileErrorCodes;
  exports.CoreErrorCodes = CoreErrorCodes;
  exports.CoreWarnCodes = CoreWarnCodes;
  exports.DATETIME_FORMAT_OPTIONS_KEYS = DATETIME_FORMAT_OPTIONS_KEYS;
  exports.DEFAULT_LOCALE = DEFAULT_LOCALE;
  exports.DEFAULT_MESSAGE_DATA_TYPE = DEFAULT_MESSAGE_DATA_TYPE;
  exports.MISSING_RESOLVE_VALUE = MISSING_RESOLVE_VALUE;
  exports.NOT_REOSLVED = NOT_REOSLVED;
  exports.NUMBER_FORMAT_OPTIONS_KEYS = NUMBER_FORMAT_OPTIONS_KEYS;
  exports.VERSION = VERSION;
  exports.clearCompileCache = clearCompileCache;
  exports.clearDateTimeFormat = clearDateTimeFormat;
  exports.clearNumberFormat = clearNumberFormat;
  exports.compile = compile;
  exports.createCompileError = createCompileError;
  exports.createCoreContext = createCoreContext;
  exports.createCoreError = createCoreError;
  exports.createMessageContext = createMessageContext;
  exports.datetime = datetime;
  exports.fallbackWithLocaleChain = fallbackWithLocaleChain;
  exports.fallbackWithSimple = fallbackWithSimple;
  exports.getAdditionalMeta = getAdditionalMeta;
  exports.getDevToolsHook = getDevToolsHook;
  exports.getFallbackContext = getFallbackContext;
  exports.getLocale = getLocale;
  exports.getWarnMessage = getWarnMessage;
  exports.handleMissing = handleMissing;
  exports.initI18nDevTools = initI18nDevTools;
  exports.isAlmostSameLocale = isAlmostSameLocale;
  exports.isImplicitFallback = isImplicitFallback;
  exports.isMessageAST = isMessageAST;
  exports.isMessageFunction = isMessageFunction;
  exports.isTranslateFallbackWarn = isTranslateFallbackWarn;
  exports.isTranslateMissingWarn = isTranslateMissingWarn;
  exports.number = number;
  exports.parse = parse;
  exports.parseDateTimeArgs = parseDateTimeArgs;
  exports.parseNumberArgs = parseNumberArgs;
  exports.parseTranslateArgs = parseTranslateArgs;
  exports.registerLocaleFallbacker = registerLocaleFallbacker;
  exports.registerMessageCompiler = registerMessageCompiler;
  exports.registerMessageResolver = registerMessageResolver;
  exports.resolveLocale = resolveLocale;
  exports.resolveValue = resolveValue;
  exports.resolveWithKeyValue = resolveWithKeyValue;
  exports.setAdditionalMeta = setAdditionalMeta;
  exports.setDevToolsHook = setDevToolsHook;
  exports.setFallbackContext = setFallbackContext;
  exports.translate = translate;
  exports.translateDevTools = translateDevTools;
  exports.updateFallbackLocale = updateFallbackLocale;

  return exports;

})({});
