{"name": "@intlify/core", "version": "10.0.7", "description": "@intlify/core", "keywords": ["core", "fundamental", "i18n", "internationalization", "intlify"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n/tree/master/packages/core#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/intlify/vue-i18n/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/core.mjs", "unpkg": "dist/core.global.js", "jsdelivr": "dist/core.global.js", "types": "dist/core.d.ts", "dependencies": {"@intlify/core-base": "10.0.7", "@intlify/shared": "10.0.7"}, "engines": {"node": ">= 16"}, "buildOptions": {"name": "IntlifyCore", "formats": ["mjs", "mjs-node", "mjs-runtime", "mjs-node-runtime", "browser", "browser-runtime", "cjs", "global", "global-runtime"]}, "exports": {".": {"types": "./dist/core.d.ts", "import": "./dist/core.mjs", "browser": "./dist/core.esm-browser.js", "node": {"import": {"production": "./dist/core.prod.node.mjs", "development": "./dist/core.node.mjs", "default": "./dist/core.node.mjs"}, "require": {"production": "./dist/core.prod.cjs", "development": "./dist/core.cjs", "default": "./index.js"}}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "funding": "https://github.com/sponsors/kazupon", "publishConfig": {"access": "public"}, "sideEffects": false}