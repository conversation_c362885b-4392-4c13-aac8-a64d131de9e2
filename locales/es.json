{"it": "TI", "code": "Codificar", "format": "Formato", "generator": "Generador", "json": "JSON", "calc": "Calculadora", "text": "Texto", "tool": "Herramienta", "common": {"appName": "ToolMi", "slogan": "Herramientas de eficiencia en línea de próxima generación", "input": "Entrada", "output": "Salida", "copy": "Copiar", "download": "<PERSON><PERSON><PERSON>", "clear": "Limpiar", "example": "<PERSON><PERSON> eje<PERSON>lo", "search": "Buscar", "searchPlaceholder": "Ingrese palabras clave, actualmente hay {count} herram<PERSON>as", "about": "Acerca de este sitio", "feedback": "Comentarios", "category": "Categoría", "latest": "Último", "tools": "herramientas", "inputRequired": "Por favor ingrese contenido antes de continuar", "copySuccess": "Copiado exitosamente", "copyFailed": "Error al copiar", "languageSwitch": "Cambiar idioma", "useTool": "<PERSON><PERSON>", "backToCategoryList": "Volver a la lista de categorías"}, "nav": {"home": "<PERSON><PERSON>o", "encrypt": "Codificar", "format": "Formato", "generator": "Generador", "json": "JSON", "calc": "Calculadora", "text": "Texto", "table": "Tabla"}, "categories": {"encrypt": "Codificación", "format": "Formato", "generator": "Generador", "json": "Herramientas JSON", "calc": "Calculadora", "text": "Procesamiento de texto", "table": "Herramientas de tabla", "iframe": "Herramientas en línea", "math": "Herramientas matemáticas"}, "tools": {"base64": {"title": "Codificación y decodificación Base64", "description": "Herramienta de codificación y decodificación Base64, compatible con codificación y decodificación Base64 de texto y archivos", "encode": "Codificar Base64", "decode": "Decodificar Base64", "seo_title": "Herramienta de codificación y decodificación Base64 - Conversión de texto binario en línea | ToolMi", "seo_description": "Herramienta en línea de codificación y decodificación Base64, compatible con codificación y decodificación Base64 de texto, URL, JSON y otros formatos diversos. Totalmente compatible con caracteres en español, proporciona función de descarga de archivos. Aplicable a transferencia de datos, adjuntos de correo, interfaces API y otros escenarios. Uso gratuito en línea, sin instalación requerida.", "seo_keywords": "codificación Base64,decodificación Base64,codificación en línea,conversión de texto,codificación binaria,codificación URL,codificación JSON,transferencia de datos,adjuntos de correo,interfaz API,herramientas en línea", "inputPlaceholder": "Ingrese el texto o URL para codificar/decodificar", "decodeError": "Error en la decodificación Base64, verifique el formato de entrada"}, "md5": {"title": "Cifrado MD5", "description": "Herramienta de cifrado hash MD5", "encrypt": "Cifrar MD5", "seo_title": "Herramienta de cifrado MD5 - <PERSON><PERSON><PERSON><PERSON><PERSON> de hash de texto en línea | ToolMi", "seo_description": "Herramienta en línea de cifrado hash MD5, convierte rápidamente texto a valores hash MD5. Compatible con caracteres en español, aplicable a verificación de integridad de datos, generación de claves de caché, deduplicación de datos y otros escenarios. Proporciona explicaciones técnicas detalladas y sugerencias de seguridad. Uso gratuito en línea, sin instalación requerida.", "seo_keywords": "cifrado MD5,hash MD5,c<PERSON><PERSON><PERSON><PERSON> hash,integridad de datos,hash de contraseña,clave de caché,deduplicación de datos,cifrado en línea,hash de texto,herramienta MD5"}, "sha1": {"title": "Cifrado SHA1", "description": "Herramienta de cifrado hash SHA1, convierte texto a valores hash SHA1", "encrypt": "Cifrar SHA1", "seo_title": "Herramienta de cifrado hash SHA1 - Cálculo de hash SHA1 en línea | ToolMi", "seo_description": "Herramienta en línea de cifrado hash SHA1, convierte rápidamente texto a valores hash SHA1. Compatible con caracteres en español, aplicable a verificación de integridad de datos, control de versiones y otros escenarios. Proporciona explicaciones de seguridad detalladas y propuestas de alternativas.", "seo_keywords": "cifrado SHA1,hash SHA1,c<PERSON><PERSON><PERSON>lo hash,integridad de datos,control de versiones,Git,verificación de archivos,cifrado en línea,herramienta SHA1"}, "urlencode": {"title": "Codificación y decodificación URL", "description": "Herramienta de codificación y decodificación URL, maneja caracteres especiales en URLs", "encode": "Codificar URL", "decode": "Decodificar URL", "seo_title": "Herramienta de codificación y decodificación URL - Conversión de codificación porcentual en línea | ToolMi", "seo_description": "Herramienta en línea de codificación y decodificación URL, compatible con conversión de codificación porcentual de caracteres en español y símbolos especiales. Aplicable a desarrollo web, llamadas API, envío de formularios y otros escenarios. Maneja rápidamente problemas de codificación de parámetros URL, garantiza transmisión precisa de datos.", "seo_keywords": "codificación URL,decodificación URL,codificación porcentual,encodeURIComponent,desarrollo web,llamadas API,envío de formularios,caracteres especiales,codificación en español", "inputPlaceholder": "Ingrese URL o texto para codificar/decodificar", "decodeError": "Error en la decodificación URL. Verifique el formato de entrada"}, "jwtParse": {"title": "Análisis JWT", "description": "Herramienta de análisis de JWT Token, análisis y verificación de JSON Web Token", "decode": "Analizar J<PERSON>", "seo_title": "Herramienta de análisis JWT - Decodificador de JSON Web Token en línea | ToolMi", "seo_description": "Herramienta en línea de análisis JWT, analiza y verifica rápidamente JSON Web Token. Compatible con análisis de Header, Payload, Signature, proporciona explicaciones detalladas de estructura JWT y sugerencias de seguridad. Aplicable a depuración de desarrollo y verificación de Token.", "seo_keywords": "an<PERSON><PERSON><PERSON>,JSON Web Token,decodificación Token,verificación JWT,an<PERSON><PERSON><PERSON>,an<PERSON><PERSON><PERSON>load,depuración JWT,an<PERSON><PERSON><PERSON>", "inputPlaceholder": "Ingrese JWT Token", "copyResult": "Copiar resultado", "header": "Header", "payload": "Payload", "signature": "Signature", "parseError": "Error al analizar JWT. Verifique el formato del Token", "invalidFormat": "Formato JWT inválido"}, "json2yaml": {"title": "Conversión JSON→YAML", "description": "Herramienta de conversión mutua entre formatos JSON y YAML", "toYaml": "Convertir a YAML", "toJson": "Convertir a JSON", "seo_title": "Herramienta JSON→YAML - Convertidor de formato JSON YAML en línea | ToolMi", "seo_description": "Herramienta en línea de conversión JSON→YAML, compatible con conversión bidireccional entre formatos JSON y YAML. Totalmente compatible con caracteres en español, proporciona validación de formato y indicación de errores. Aplicable a conversión de archivos de configuración, creación de documentación API, conversión de formato de datos y otros escenarios.", "seo_keywords": "JSON→YAML,YAML→JSON,conversión de formato,archivo de configuración,documentación API,conversión de datos,conversión en línea,herramienta JSON,herramienta YAML", "inputPlaceholder": "Ingrese contenido JSON o YAML", "convertError": "Error en la conversión. Verifique el formato de entrada"}, "sql": {"title": "Formato SQL", "description": "Herramienta de formato y embellecimiento de declaraciones SQL", "format": "Formatear", "minify": "Comprimir", "seo_title": "Herramienta de formato SQL - Embellecimiento y compresión de declaraciones SQL en línea | ToolMi", "seo_description": "Herramienta en línea de formato SQL, compatible con formato, embellecimiento y compresión de declaraciones SQL. Proporciona resaltado de sintaxis, detección de errores, compatible con múltiples dialectos de base de datos. Aplicable a desarrollo de bases de datos, depuración SQL, revisión de código y otros escenarios.", "seo_keywords": "formato SQL,embellecimiento SQL,compresión SQL,base de datos,declaración SQL,formato de código,herramienta SQL,desarrollo de bases de datos", "inputLabel": "Entrada SQL", "outputLabel": "Salida formateada", "inputPlaceholder": "Ingrese declaración SQL", "formatError": "Error al formatear SQL. Verifique la sintaxis"}, "qr": {"title": "Generador de códigos QR", "description": "Herramienta en línea de generación de códigos QR, compatible con texto, URL y otros contenidos", "generate": "Generar código QR", "seo_title": "Generador de códigos QR - Herramienta de creación de QR Code en línea | ToolMi", "seo_description": "Generador en línea de códigos QR, genera códigos QR con contenido como texto, URL, información de contacto, contraseñas WiFi. Compatible con salida de alta resolución, descarga y guardado. Aplicable a promoción de marketing, identificación de productos, compartir información y otros escenarios.", "seo_keywords": "generador de códigos QR,creación de códigos QR,código QR en línea,herramienta de códigos QR,promoción de marketing,identificación de productos,compartir información,pago móvil,contacto", "inputLabel": "Entrada de contenido", "inputPlaceholder": "Ingrese el contenido para generar código QR (texto, URL, etc.)", "download": "Descargar código QR", "resultTitle": "Código QR generado", "generateError": "Error al generar código QR"}, "uuid": {"title": "Generador UUID", "description": "Herramienta de generación de identificadores únicos UUID", "generate": "Generar UUID", "seo_title": "Generador UUID - Herramienta de generación de identificadores únicos en línea | ToolMi", "seo_description": "Generador en línea UUID, genera rápidamente identificadores únicos. Compatible con generación por lotes, copia con un clic, aplicable a claves primarias de base de datos, identificación API, nomenclatura de archivos y otros escenarios. Cumple con el estándar RFC 4122, garantiza unicidad global.", "seo_keywords": "generador UUID,identificador único,generación GUID,clave primaria de base de datos,identificación API,nomenclatura de archivos,RFC4122,unicidad global", "countLabel": "Cantidad a generar", "copyAll": "<PERSON><PERSON><PERSON> todo", "clear": "Limpiar", "resultTitle": "UUID generados", "countSuffix": "unidades"}, "numberToZh": {"title": "Conversión de números a chino", "description": "Convierte números arábigos a números chinos, compatible con chino simplificado y tradicional", "convert": "Convertir", "seo_title": "Herramienta de números a chino - Convertidor de números arábigos a chino en línea | ToolMi", "seo_description": "Herramienta en línea de números a chino, compatible con conversión de números arábigos a chino simplificado, chino tradicional y cantidades en mayúsculas. Aplicable a informes financieros, contratos, emisión de facturas y otros escenarios. Compatible con conversión de enteros y decimales.", "seo_keywords": "números a chino,conversión de números arábigos,números chinos,cantidades en mayúsculas,informes financieros,contratos,emisión de facturas", "inputLabel": "Entrada de números", "inputPlaceholder": "Ingrese números, por ejemplo: 12345", "typeLabel": "Tipo de conversión", "simple": "Chino simplificado", "traditional": "Chino tradicional", "financial": "Cantidades en mayúsculas", "convertError": "Error al convertir números. Verifique el formato de entrada"}, "netscapeCookies": {"title": "Conversión de formato Netscape Cookies", "description": "Herramienta de conversión mutua entre formato Netscape Cookies y formato JSON", "convert": "Convertir", "seo_title": "Herramienta de conversión de formato Netscape Cookies - Convertidor de formato Cookies en línea | ToolMi", "seo_description": "Herramienta en línea de conversión de formato Netscape Cookies, compatible con conversión bidireccional entre formato Netscape Cookies y formato JSON. Aplicable a migración de datos de navegador, desarrollo de crawlers, pruebas web y otros escenarios.", "seo_keywords": "Netscape Cookies,conversión de formato Cookies,datos de navegador,desarrollo de crawlers,pruebas web,conversión JSON", "inputFormatLabel": "Formato de entrada", "netscapeFormat": "Formato Netscape", "jsonFormat": "Formato JSON", "inputLabel": "Entrada de contenido", "resultLabel": "Resultado de conversión", "clear": "Limpiar", "convertError": "Error en la conversión de formato. Verifique el formato de entrada"}, "yaml": {"title": "Formato YAML", "description": "Herramienta de formato, validación y embellecimiento YAML", "format": "Formatear", "validate": "Validar", "seo_title": "Herramienta de formato YAML - Validador y embellecedor YAML en línea | ToolMi", "seo_description": "Herramienta en línea de formato YAML, compatible con validación, embellecimiento y formato YAML. Proporciona verificación de sintaxis, indicación de errores, aplicable a creación de archivos de configuración, despliegue DevOps, documentación API y otros escenarios.", "seo_keywords": "formato YAML,validación YAML,embellecimiento YAML,archivo de configuración,DevOps,documentación API,verificación de sintaxis", "inputLabel": "Entrada YAML", "outputLabel": "Salida formateada", "inputPlaceholder": "Ingrese contenido YAML", "clear": "Limpiar", "validStatus": "✓ El formato YAML es correcto", "invalidStatus": "✗ Hay errores en el formato YAML", "formatError": "Error al formatear YAML. Verifique la sintaxis"}, "reverseString": {"title": "Inversión de cadenas", "description": "Herramienta de inversión de cadenas y procesamiento de texto", "reverse": "Invertir", "seo_title": "Herramienta de inversión de cadenas - Procesador de inversión de texto en línea | ToolMi", "seo_description": "Herramienta en línea de inversión de cadenas, compatible con inversión de texto por caracteres, palabras y líneas. Aplicable a procesamiento de texto, generación de contraseñas, análisis de datos y otros escenarios. Proporciona múltiples modos de inversión, operación simple y rápida.", "seo_keywords": "inversión de cadenas,inversión de texto,procesamiento de texto,generación de contraseñas,análisis de da<PERSON>,herramientas en línea", "inputLabel": "Entrada de texto", "inputPlaceholder": "Ingrese el texto a invertir", "optionsLabel": "Opciones de inversión", "byCharacters": "Invertir por caracteres", "byWords": "Invertir por palabras", "byLines": "Invertir por líneas", "resultLabel": "Resultado de inversión", "clear": "Limpiar", "reverseError": "Error al invertir texto"}, "jsonToPhpArrayCode": {"title": "JSON → Código de array PHP", "description": "Convierte JSON a código de array PHP, se puede copiar directamente y pegar en código PHP", "convert": "Convertir", "seo_title": "JSON → Código de array PHP - Herramienta de conversión en línea | ToolMi", "seo_description": "Herramienta en línea JSON → Código de array PHP, compatible con estructuras anidadas, caracteres en español, escape de caracteres especiales. Convierte con un clic datos JSON a código de array PHP con formato estándar, aplicable a archivos de configuración, datos Mock API, archivos seed de base de datos y otros escenarios.", "seo_keywords": "JSON→PHP,array PHP,conversión JSON,generación de código PHP,herramientas en línea,archivo de configuración,API Mock,conversión de datos,herramientas de desarrollo PHP,análisis JSON", "inputLabel": "Entrada JSON", "inputPlaceholder": "Ingrese datos JSON", "outputLabel": "Código de array PHP", "copyResult": "Copiar resultado", "loadExample": "<PERSON><PERSON> eje<PERSON>lo", "convertError": "Error al convertir JSON. Verifique el formato"}}, "footer": {"copyright": "ToolMi Construido con amor."}, "about": {"title": "Acerca de este sitio", "seo_title": "Acerca de este sitio - ToolMi", "seo_description": "Información detallada del sitio web ToolMi, stack tecnológico y características funcionales", "seo_keywords": "acerca de,Tool<PERSON>i,herramientas en línea,Nuxt3,stack tecnológico", "aboutSite": {"title": "Acerca de ToolMi", "description1": "ToolMi es un sitio web especializado en proporcionar herramientas en línea de alta calidad. Nos dedicamos a brindar servicios de herramientas en línea convenientes y eficientes para desarrolladores, diseñadores y usuarios generales.", "description2": "Nuestro objetivo es convertirnos en 'herramientas de eficiencia en línea de próxima generación'. A través de un stack tecnológico moderno y diseño de interfaz amigable, permitimos que todos puedan usar fácilmente diversas herramientas prácticas."}, "categories": {"title": "Categorías de herramientas", "toolsCount": "herramientas", "viewTools": "<PERSON><PERSON>"}, "contact": {"title": "Contacto", "description": "Si tiene sugerencias, preguntas o intenciones de cooperación, no dude en contactarnos a través de los siguientes métodos:", "feedback": "Sugerencias y comentarios", "website": "Sitio web"}}}