{"it": "IT", "code": "Code", "format": "Format", "generator": "Generator", "json": "JSON", "calc": "Calc", "text": "Text", "tool": "Tool", "common": {"appName": "ToolMi", "slogan": "Next generation online tools", "input": "Input", "output": "Output", "copy": "Copy", "download": "Download", "clear": "Clear", "example": "Load Example", "search": "Search", "searchPlaceholder": "Enter keywords, {count} tools available", "about": "About", "feedback": "<PERSON><PERSON><PERSON>", "category": "Category", "latest": "Latest", "tools": "tools", "inputRequired": "Please enter content before proceeding", "copySuccess": "<PERSON><PERSON>d successfully", "copyFailed": "Co<PERSON> failed", "languageSwitch": "Language", "useTool": "Use Tool", "backToCategoryList": "Back to Category List"}, "nav": {"home": "Home", "encrypt": "Encode", "format": "Format", "generator": "Generator", "json": "JSON", "calc": "Calculator", "text": "Text", "table": "Table"}, "categories": {"encrypt": "Encoding", "format": "Formatting", "generator": "Generator", "json": "JSON Tools", "calc": "Calculator", "text": "Text Processing", "table": "Table Tools", "iframe": "Online Tools", "math": "<PERSON> Tools"}, "tools": {"base64": {"title": "Base64 Encode/Decode", "description": "Base64 encoding and decoding tool, supports text and file formats", "encode": "Base64 Encode", "decode": "Base64 Decode", "seo_title": "Base64 Encode/Decode Tool - Online Text Binary Converter | ToolMi", "seo_description": "Base64 online encoding and decoding tool, supports text, URL, JSON and other formats for Base64 encoding and decoding. Perfect support for Chinese characters, provides file download function. Suitable for data transmission, email attachments, API interfaces and other scenarios. Free online use, no installation required.", "seo_keywords": "Base64 encoding,Base64 decoding,online encoding,text conversion,binary encoding,URL encoding,JSON encoding,data transmission,email attachments,API interface,online tools"}, "md5": {"title": "MD5 Encryption", "description": "MD5 hash encryption tool", "encrypt": "MD5 Encrypt", "seo_title": "MD5 Encryption Tool - Online Text Hash Calculator | ToolMi", "seo_description": "MD5 online hash encryption tool, quickly converts text to MD5 hash values. Supports Chinese characters, suitable for scenarios such as data integrity verification, cache key generation, data deduplication, etc. Provides detailed technical instructions and security suggestions. Free online use, no installation required.", "seo_keywords": "MD5 encryption,MD5 hash,hash calculation,data integrity,password hash,cache key,data deduplication,online encryption,text hash,MD5 tool"}, "sha1": {"title": "SHA1 Encryption", "description": "SHA1 hash encryption tool, converts text to SHA1 hash values", "encrypt": "SHA1 Encrypt", "seo_title": "SHA1 Hash Encryption Tool - Online SHA1 Hash Calculator | ToolMi", "seo_description": "SHA1 online hash encryption tool, quickly converts text to SHA1 hash values. Supports Chinese characters, suitable for data integrity verification, version control and other scenarios. Provides detailed security instructions and alternative solution recommendations.", "seo_keywords": "SHA1 encryption,SHA1 hash,hash calculation,data integrity,version control,Git,file verification,online encryption,SHA1 tool"}, "urlencode": {"title": "URL Encode/Decode", "description": "URL encoding and decoding tool, handles special characters in URLs", "encode": "URL Encode", "decode": "URL Decode", "seo_title": "URL Encode/Decode Tool - Online Percent Encoding Converter | ToolMi", "seo_description": "URL encoding and decoding online tool, supports Chinese characters and special symbols percent encoding conversion. Suitable for Web development, API calls, form submission and other scenarios. Quickly handle URL parameter encoding issues to ensure correct data transmission.", "seo_keywords": "URL encoding,URL decoding,percent encoding,encodeURIComponent,Web development,API calls,form submission,special characters,Chinese encoding", "inputPlaceholder": "Please enter URL or text to encode/decode", "decodeError": "URL decoding failed, please check input format"}, "jwtParse": {"title": "JWT Parser", "description": "JWT Token parsing tool, parse and verify JSON Web Token", "decode": "Parse JWT", "seo_title": "JWT Parser Tool - Online JSON Web Token Decoder | ToolMi", "seo_description": "JWT online parsing tool, quickly parse and verify JSON Web Token. Supports Header, Payload, Signature analysis, provides detailed JWT structure description and security recommendations. Suitable for development debugging and Token verification.", "seo_keywords": "JWT parser,JSON Web Token,Token decoder,JWT verification,Header parsing,Payload parsing,JWT debugging,Token analysis", "inputPlaceholder": "Please enter JWT Token", "copyResult": "<PERSON><PERSON> Result", "header": "Header", "payload": "Payload", "signature": "Signature", "parseError": "JWT parsing failed, please check Token format", "invalidFormat": "Invalid JWT format"}, "json2yaml": {"title": "JSON to YAML", "description": "JSON and YAML format conversion tool", "toYaml": "To YAML", "toJson": "To JSON", "seo_title": "JSON to YAML Tool - Online JSON YAML Format Converter | ToolMi", "seo_description": "JSON to YAML online conversion tool, supports bidirectional conversion between JSON and YAML formats. Perfect support for Chinese characters, provides format validation and error prompts. Suitable for configuration file conversion, API documentation writing, data format conversion and other scenarios.", "seo_keywords": "JSON to YAML,YAML to JSON,format conversion,configuration files,API documentation,data conversion,online converter,JSON tool,YAML tool", "inputPlaceholder": "Please enter JSON or YAML content", "convertError": "Conversion failed, please check input format"}, "SQLFormatter": {"title": "SQL Formatter", "description": "SQL statement formatting and beautifying tool", "format": "Format"}, "qrGenerator": {"title": "QR Code Generator", "description": "Online QR code generation tool", "generate": "Generate QR"}, "uuid": {"title": "UUID Generator", "description": "UUID unique identifier generation tool", "generate": "Generate UUID"}, "numberToZh": {"title": "Number to Chinese", "description": "Convert Arabic numerals to Chinese characters", "convert": "Convert"}, "netscapeCookies": {"title": "Netscape Cookies", "description": "Netscape Cookies format conversion tool", "convert": "Convert"}, "yaml": {"title": "YAML Formatter", "description": "YAML format conversion and validation tool", "format": "Format", "validate": "Validate"}, "reverseString": {"title": "Reverse String", "description": "String reversal and text processing tool", "reverse": "Reverse"}, "jsonToPhpArrayCode": {"title": "JSON to PHP Array Code", "description": "Convert JSON to PHP array code, directly copy and paste into PHP code", "convert": "Convert"}}, "footer": {"copyright": "ToolMi Build with love."}}