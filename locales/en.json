{"it": "IT", "code": "Code", "format": "Format", "generator": "Generator", "json": "JSON", "calc": "Calc", "text": "Text", "tool": "Tool", "common": {"appName": "ToolMi", "slogan": "Next generation online tools", "input": "Input", "output": "Output", "copy": "Copy", "download": "Download", "clear": "Clear", "example": "Load Example", "search": "Search", "searchPlaceholder": "Enter keywords, {count} tools available", "about": "About", "feedback": "<PERSON><PERSON><PERSON>", "category": "Category", "latest": "Latest", "tools": "tools", "inputRequired": "Please enter content before proceeding", "copySuccess": "<PERSON><PERSON>d successfully", "copyFailed": "Co<PERSON> failed", "languageSwitch": "Language", "useTool": "Use Tool", "backToCategoryList": "Back to Category List", "copied": "<PERSON>pied"}, "nav": {"home": "Home", "encrypt": "Encode", "format": "Format", "generator": "Generator", "json": "JSON", "calc": "Calculator", "text": "Text", "table": "Table"}, "categories": {"encrypt": "Encoding", "format": "Formatting", "generator": "Generator", "json": "JSON Tools", "calc": "Calculator", "text": "Text Processing", "table": "Table Tools", "iframe": "Online Tools", "math": "<PERSON> Tools"}, "tools": {"base64": {"title": "Base64 Encode/Decode", "description": "Base64 encoding and decoding tool, supports text and file formats", "encode": "Base64 Encode", "decode": "Base64 Decode", "seo_title": "Base64 Encode/Decode Tool - Online Text Binary Converter | ToolMi", "seo_description": "Base64 online encoding and decoding tool, supports text, URL, JSON and other formats for Base64 encoding and decoding. Perfect support for Chinese characters, provides file download function. Suitable for data transmission, email attachments, API interfaces and other scenarios. Free online use, no installation required.", "seo_keywords": "Base64 encoding,Base64 decoding,online encoding,text conversion,binary encoding,URL encoding,JSON encoding,data transmission,email attachments,API interface,online tools"}, "md5": {"title": "MD5 Encryption", "description": "MD5 hash encryption tool", "encrypt": "MD5 Encrypt", "seo_title": "MD5 Encryption Tool - Online Text Hash Calculator | ToolMi", "seo_description": "MD5 online hash encryption tool, quickly converts text to MD5 hash values. Supports Chinese characters, suitable for scenarios such as data integrity verification, cache key generation, data deduplication, etc. Provides detailed technical instructions and security suggestions. Free online use, no installation required.", "seo_keywords": "MD5 encryption,MD5 hash,hash calculation,data integrity,password hash,cache key,data deduplication,online encryption,text hash,MD5 tool"}, "sha1": {"title": "SHA1 Encryption", "description": "SHA1 hash encryption tool, converts text to SHA1 hash values", "encrypt": "SHA1 Encrypt", "seo_title": "SHA1 Hash Encryption Tool - Online SHA1 Hash Calculator | ToolMi", "seo_description": "SHA1 online hash encryption tool, quickly converts text to SHA1 hash values. Supports Chinese characters, suitable for data integrity verification, version control and other scenarios. Provides detailed security instructions and alternative solution recommendations.", "seo_keywords": "SHA1 encryption,SHA1 hash,hash calculation,data integrity,version control,Git,file verification,online encryption,SHA1 tool"}, "urlencode": {"title": "URL Encode/Decode", "description": "URL encoding and decoding tool, handles special characters in URLs", "encode": "URL Encode", "decode": "URL Decode", "seo_title": "URL Encode/Decode Tool - Online Percent Encoding Converter | ToolMi", "seo_description": "URL encoding and decoding online tool, supports Chinese characters and special symbols percent encoding conversion. Suitable for Web development, API calls, form submission and other scenarios. Quickly handle URL parameter encoding issues to ensure correct data transmission.", "seo_keywords": "URL encoding,URL decoding,percent encoding,encodeURIComponent,Web development,API calls,form submission,special characters,Chinese encoding", "inputPlaceholder": "Please enter URL or text to encode/decode", "decodeError": "URL decoding failed, please check input format"}, "jwtParse": {"title": "JWT Parser", "description": "JWT Token parsing tool, parse and verify JSON Web Token", "decode": "Parse JWT", "seo_title": "JWT Parser Tool - Online JSON Web Token Decoder | ToolMi", "seo_description": "JWT online parsing tool, quickly parse and verify JSON Web Token. Supports Header, Payload, Signature analysis, provides detailed JWT structure description and security recommendations. Suitable for development debugging and Token verification.", "seo_keywords": "JWT parser,JSON Web Token,Token decoder,JWT verification,Header parsing,Payload parsing,JWT debugging,Token analysis", "inputPlaceholder": "Please enter JWT Token", "copyResult": "<PERSON><PERSON> Result", "header": "Header", "payload": "Payload", "signature": "Signature", "parseError": "JWT parsing failed, please check Token format", "invalidFormat": "Invalid JWT format"}, "json2yaml": {"title": "JSON to YAML", "description": "JSON and YAML format conversion tool", "toYaml": "To YAML", "toJson": "To JSON", "seo_title": "JSON to YAML Tool - Online JSON YAML Format Converter | ToolMi", "seo_description": "JSON to YAML online conversion tool, supports bidirectional conversion between JSON and YAML formats. Perfect support for Chinese characters, provides format validation and error prompts. Suitable for configuration file conversion, API documentation writing, data format conversion and other scenarios.", "seo_keywords": "JSON to YAML,YAML to JSON,format conversion,configuration files,API documentation,data conversion,online converter,JSON tool,YAML tool", "inputPlaceholder": "Please enter JSON or YAML content", "convertError": "Conversion failed, please check input format"}, "sql": {"title": "SQL Formatter", "description": "SQL statement formatting and beautification tool", "format": "Format", "minify": "Minify", "seo_title": "SQL Formatter Tool - Online SQL Statement Beautifier Compressor | ToolMi", "seo_description": "SQL formatting online tool, supports SQL statement formatting, beautification and compression. Provides syntax highlighting, error detection, supports multiple database dialects. Suitable for database development, SQL debugging, code review and other scenarios.", "seo_keywords": "SQL formatter,SQL beautifier,SQL compressor,database,SQL statement,code formatting,SQL tool,database development", "inputLabel": "SQL Input", "outputLabel": "Formatted Output", "inputPlaceholder": "Please enter SQL statement", "formatError": "SQL formatting failed, please check syntax"}, "qr": {"title": "QR Code Generator", "description": "Online QR code generation tool, supports text, URL and other content", "generate": "Generate QR Code", "seo_title": "QR Code Generator - Online QR Code Maker Tool | ToolMi", "seo_description": "QR code online generator, supports text, URLs, contact information, WiFi passwords and other content to generate QR codes. High-definition output, supports download and save. Suitable for marketing promotion, product identification, information sharing and other scenarios.", "seo_keywords": "QR code generator,QR code maker,online QR code,QR code tool,marketing promotion,product identification,information sharing,mobile payment,contact information", "inputLabel": "Input Content", "inputPlaceholder": "Please enter the content to generate QR code (text, URL, etc.)", "download": "Download QR Code", "resultTitle": "Generated QR Code", "generateError": "QR code generation failed"}, "uuid": {"title": "UUID Generator", "description": "UUID unique identifier generation tool", "generate": "Generate UUID", "seo_title": "UUID Generator - Online Unique Identifier Generation Tool | ToolMi", "seo_description": "UUID online generator, quickly generate unique identifiers. Supports batch generation, one-click copy, suitable for database primary keys, API identifiers, file naming and other scenarios. Complies with RFC 4122 standard, ensuring global uniqueness.", "seo_keywords": "UUID generator,unique identifier,GUID generation,database primary key,API identifier,file naming,RFC4122,globally unique", "countLabel": "Generate Count", "copyAll": "Copy All", "clear": "Clear", "resultTitle": "Generated UUIDs", "countSuffix": ""}, "numberToZh": {"title": "Number to Chinese", "description": "Convert Arabic numerals to Chinese characters, supports simplified and traditional", "convert": "Convert", "seo_title": "Number to Chinese Tool - Online Arabic Numeral Chinese Converter | ToolMi", "seo_description": "Number to Chinese online tool, supports converting Arabic numerals to simplified Chinese, traditional Chinese and uppercase amounts. Suitable for financial statements, contract documents, invoice issuance and other scenarios. Supports integer and decimal conversion.", "seo_keywords": "number to Chinese,Arabic numeral conversion,Chinese numerals,uppercase amount,financial statements,contract documents,invoice issuance", "inputLabel": "Input Number", "inputPlaceholder": "Please enter a number, e.g.: 12345", "typeLabel": "Conversion Type", "simple": "Simplified Chinese", "traditional": "Traditional Chinese", "financial": "Uppercase Amount", "convertError": "Number conversion failed, please check input format"}, "netscapeCookies": {"title": "Netscape Cookies Format Converter", "description": "Netscape Cookies format and JSON format conversion tool", "convert": "Convert", "seo_title": "Netscape Cookies Format Converter - Online Cookies Format Converter | ToolMi", "seo_description": "Netscape Cookies format conversion online tool, supports bidirectional conversion between Netscape Cookies format and JSON format. Suitable for browser data migration, crawler development, Web testing and other scenarios.", "seo_keywords": "Netscape Cookies,Cookies format conversion,browser data,crawler development,Web testing,JSON conversion", "inputFormatLabel": "Input Format", "netscapeFormat": "Netscape Format", "jsonFormat": "JSON Format", "inputLabel": "Input Content", "resultLabel": "Conversion Result", "clear": "Clear", "convertError": "Format conversion failed, please check input format"}, "yaml": {"title": "YAML Formatter", "description": "YAML formatting, validation and beautification tool", "format": "Format", "validate": "Validate", "seo_title": "YAML Formatter Tool - Online YAML Validator Beautifier | ToolMi", "seo_description": "YAML formatting online tool, supports YAML format validation, beautification and formatting. Provides syntax checking, error prompts, suitable for configuration file writing, DevOps deployment, API documentation and other scenarios.", "seo_keywords": "YAML formatter,YAML validator,YAML beautifier,configuration files,DevOps,API documentation,syntax checking", "inputLabel": "YAML Input", "outputLabel": "Formatted Output", "inputPlaceholder": "Please enter YAML content", "clear": "Clear", "validStatus": "✓ YAML format is correct", "invalidStatus": "✗ YAML format error", "formatError": "YAML formatting failed, please check syntax"}, "reverseString": {"title": "Reverse String", "description": "String reversal and text processing tool", "reverse": "Reverse", "seo_title": "String Reverse Tool - Online Text Reversal Processor | ToolMi", "seo_description": "String reverse online tool, supports reversing text by characters, words, lines. Suitable for text processing, password generation, data analysis and other scenarios. Provides multiple reverse modes, simple and fast operation.", "seo_keywords": "string reverse,text reverse,text processing,password generation,data analysis,online tool", "inputLabel": "Input Text", "inputPlaceholder": "Please enter text to reverse", "optionsLabel": "Reverse Options", "byCharacters": "Reverse by Characters", "byWords": "Reverse by Words", "byLines": "Reverse by <PERSON>", "resultLabel": "Reverse Result", "clear": "Clear", "reverseError": "Text reversal failed"}, "jsonToPhpArrayCode": {"title": "JSON to PHP Array Code", "description": "Convert JSON to PHP array code, directly copy and paste into PHP code", "convert": "Convert", "seo_title": "JSON to PHP Array Code - Online Conversion Tool | ToolMi", "seo_description": "JSON to PHP array code online tool, supports nested structures, Chinese characters, special character escaping. One-click conversion of JSON data to well-formatted PHP array code, suitable for configuration files, API Mock data, database seed files and other scenarios.", "seo_keywords": "JSON to PHP,PHP array,JSON conversion,PHP code generation,online tool,configuration files,API Mock,data conversion,PHP development tools,JSON parsing", "inputLabel": "Input JSON", "inputPlaceholder": "Please enter JSON data", "outputLabel": "PHP Array Code", "copyResult": "<PERSON><PERSON> Result", "loadExample": "Load Example", "convertError": "JSON conversion failed, please check format"}, "color": {"title": "Color Picker", "description": "Professional color selection and management tool with format conversion, palette generation, and contrast analysis", "seo_title": "Color Picker Tool - Online Color Format Conversion Palette Generator | ToolMi", "seo_description": "Professional online color picker tool supporting HEX, RGB, HSL, HSV format conversion, automatic palette generation, WCAG contrast analysis. Perfect for web design, UI design, brand design and more.", "seo_keywords": "color picker,color conversion,color palette,contrast analysis,HEX to RGB,HSL conversion,web design,UI design,brand design,WCAG"}}, "footer": {"copyright": "ToolMi Build with love."}, "about": {"title": "About Us", "seo_title": "About Us - ToolMi", "seo_description": "Learn more about ToolMi website, technology stack and features", "seo_keywords": "about,ToolMi,online tools,Nuxt3,technology stack", "aboutSite": {"title": "About ToolMi", "description1": "ToolMi is a website dedicated to providing high-quality online tools. We are committed to providing convenient and efficient online tool services for developers, designers and general users.", "description2": "Our goal is to become the \"next generation online efficiency tools\", through modern technology stack and user-friendly interface design, so that everyone can easily use various practical tools."}, "categories": {"title": "Tool Categories", "toolsCount": " tools", "viewTools": "View Tools"}, "contact": {"title": "Contact Us", "description": "If you have any suggestions, questions or cooperation intentions, please feel free to contact us through the following ways:", "feedback": "<PERSON><PERSON><PERSON>", "website": "Website"}}, "color": {"title": "Color Picker", "description": "Professional color selection and management tool with format conversion, palette generation, and contrast analysis", "picker": {"title": "Color Picker"}, "history": {"title": "Color History"}, "formats": {"title": "Color Formats"}, "palette": {"title": "Palette Generation", "type": "Palette Type", "complementary": "Complementary", "analogous": "Analogous", "triadic": "Triadic", "monochromatic": "Monochromatic"}, "contrast": {"title": "Contrast Analysis", "background": "Background Color", "sample": "Sample Text", "description": "This is contrast test text for checking readability.", "ratio": "Contrast Ratio", "pass": "Pass", "fail": "Fail"}, "presets": {"title": "Preset Colors"}}}