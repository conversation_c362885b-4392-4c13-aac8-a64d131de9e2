{"it": "IT", "code": "Code", "format": "Format", "generator": "Generator", "json": "JSON", "calc": "Calc", "text": "Text", "tool": "Tool", "common": {"appName": "ToolMi", "slogan": "Next generation online tools", "input": "Input", "output": "Output", "copy": "Copy", "download": "Download", "clear": "Clear", "example": "Load Example", "search": "Search", "searchPlaceholder": "Enter keywords, {count} tools available", "about": "About", "feedback": "<PERSON><PERSON><PERSON>", "category": "Category", "latest": "Latest", "tools": "tools", "inputRequired": "Please enter content before proceeding", "copySuccess": "<PERSON><PERSON>d successfully", "copyFailed": "Co<PERSON> failed", "languageSwitch": "Language", "useTool": "Use Tool", "backToCategoryList": "Back to Category List"}, "nav": {"home": "Home", "encrypt": "Encode", "format": "Format", "generator": "Generator", "json": "JSON", "calc": "Calculator", "text": "Text", "table": "Table"}, "categories": {"encrypt": "Encoding", "format": "Formatting", "generator": "Generator", "json": "JSON Tools", "calc": "Calculator", "text": "Text Processing", "table": "Table Tools", "iframe": "Online Tools", "math": "<PERSON> Tools"}, "tools": {"base64": {"title": "Base64 Encode/Decode", "description": "Base64 encoding and decoding tool, supports text and file formats", "encode": "Base64 Encode", "decode": "Base64 Decode"}, "md5": {"title": "MD5 Encryption", "description": "MD5 hash encryption tool", "encrypt": "MD5 Encrypt", "seo_title": "MD5 Encryption Tool - Online Text Hash Calculator | ToolMi", "seo_description": "MD5 online hash encryption tool, quickly converts text to MD5 hash values. Supports Chinese characters, suitable for scenarios such as data integrity verification, cache key generation, data deduplication, etc. Provides detailed technical instructions and security suggestions. Free online use, no installation required.", "seo_keywords": "MD5 encryption,MD5 hash,hash calculation,data integrity,password hash,cache key,data deduplication,online encryption,text hash,MD5 tool"}, "sha1": {"title": "SHA1 Encryption", "description": "SHA1 hash encryption tool", "encrypt": "SHA1 Encrypt"}, "urlencode": {"title": "URL Encode/Decode", "description": "URL encoding and decoding tool", "encode": "URL Encode", "decode": "URL Decode"}, "jwtParse": {"title": "JWT Parser", "description": "JWT Token parsing tool", "decode": "Parse JWT"}, "json2yaml": {"title": "JSON to YAML", "description": "JSON and YAML format conversion tool", "toYaml": "To YAML", "toJson": "To JSON"}, "SQLFormatter": {"title": "SQL Formatter", "description": "SQL statement formatting and beautifying tool", "format": "Format"}, "qrGenerator": {"title": "QR Code Generator", "description": "Online QR code generation tool", "generate": "Generate QR"}, "uuid": {"title": "UUID Generator", "description": "UUID unique identifier generation tool", "generate": "Generate UUID"}, "numberToZh": {"title": "Number to Chinese", "description": "Convert Arabic numerals to Chinese characters", "convert": "Convert"}, "netscapeCookies": {"title": "Netscape Cookies", "description": "Netscape Cookies format conversion tool", "convert": "Convert"}, "yaml": {"title": "YAML Formatter", "description": "YAML format conversion and validation tool", "format": "Format", "validate": "Validate"}, "reverseString": {"title": "Reverse String", "description": "String reversal and text processing tool", "reverse": "Reverse"}, "jsonToPhpArrayCode": {"title": "JSON to PHP Array Code", "description": "Convert JSON to PHP array code, directly copy and paste into PHP code", "convert": "Convert"}}, "footer": {"copyright": "ToolMi Build with love."}}