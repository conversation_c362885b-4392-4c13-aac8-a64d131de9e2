{"common": {"appName": "ToolMi", "slogan": "Next generation online tools", "input": "Input", "output": "Output", "copy": "Copy", "download": "Download", "clear": "Clear", "example": "Load Example", "search": "Search", "searchPlaceholder": "Enter keywords, {count} tools available", "about": "About", "feedback": "<PERSON><PERSON><PERSON>", "category": "Category", "latest": "Latest", "tools": "tools", "inputRequired": "Please enter content before proceeding", "copySuccess": "<PERSON><PERSON>d successfully", "copyFailed": "Co<PERSON> failed", "languageSwitch": "Language"}, "nav": {"home": "Home", "encrypt": "Encode", "format": "Format", "generator": "Generator", "json": "JSON", "calc": "Calculator", "text": "Text", "table": "Table"}, "categories": {"encrypt": "Encoding", "format": "Formatting", "generator": "Generator", "json": "JSON Tools", "calc": "Calculator", "text": "Text Processing", "table": "Table Tools", "iframe": "Online Tools", "math": "<PERSON> Tools"}, "tools": {"base64": {"title": "Base64 Encode/Decode", "description": "Base64 encoding and decoding tool", "encode": "Base64 Encode", "decode": "Base64 Decode"}, "md5": {"title": "MD5 Encryption", "description": "MD5 hash encryption tool", "encrypt": "MD5 Encrypt"}, "sha1": {"title": "SHA1 Encryption", "description": "SHA1 hash encryption tool", "encrypt": "SHA1 Encrypt"}, "url": {"title": "URL Encode/Decode", "description": "URL encoding and decoding tool", "encode": "URL Encode", "decode": "URL Decode"}, "jwt": {"title": "JWT Parser", "description": "JWT Token parsing tool", "decode": "Parse JWT"}, "json2yaml": {"title": "JSON to YAML", "description": "JSON and YAML format conversion tool", "toYaml": "To YAML", "toJson": "To JSON"}, "sql": {"title": "SQL Formatter", "description": "SQL statement formatting and beautifying tool", "format": "Format"}, "qr": {"title": "QR Code Generator", "description": "Online QR code generation tool", "generate": "Generate QR"}, "uuid": {"title": "UUID Generator", "description": "UUID unique identifier generation tool", "generate": "Generate UUID"}}, "footer": {"copyright": "ToolMi Build with love."}}