{"it": "IT", "code": "エンコード", "format": "フォーマット", "generator": "ジェネレーター", "json": "JSON", "calc": "計算機", "text": "テキスト", "tool": "ツール", "common": {"appName": "ツールミ", "slogan": "次世代オンライン効率ツール", "input": "入力", "output": "出力", "copy": "コピー", "download": "ダウンロード", "clear": "クリア", "example": "サンプル読み込み", "search": "検索", "searchPlaceholder": "キーワードを入力してください。現在{count}個のツールがあります", "about": "このサイトについて", "feedback": "フィードバック", "category": "カテゴリ", "latest": "最新", "tools": "個のツール", "inputRequired": "内容を入力してから操作を続けてください", "copySuccess": "コピーしました", "copyFailed": "コピーに失敗しました", "languageSwitch": "言語切り替え", "useTool": "ツールを使用", "backToCategoryList": "カテゴリリストに戻る"}, "nav": {"home": "ホーム", "encrypt": "エンコード", "format": "フォーマット", "generator": "ジェネレーター", "json": "JSON", "calc": "計算機", "text": "テキスト", "table": "テーブル"}, "categories": {"encrypt": "エンコード", "format": "フォーマット", "generator": "ジェネレーター", "json": "JSONツール", "calc": "計算機", "text": "テキスト処理", "table": "テーブルツール", "iframe": "オンラインツール", "math": "数学ツール"}, "tools": {"base64": {"title": "Base64 エンコード・デコード", "description": "Base64 エンコード・デコードツール、テキストとファイルのBase64エンコードとデコードをサポート", "encode": "Base64エンコード", "decode": "Base64デコード", "seo_title": "Base64 エンコード・デコードツール - オンラインテキストバイナリ変換 | ツールミ", "seo_description": "Base64 オンラインエンコード・デコードツール、テキスト、URL、JSON など多様な形式の Base64 エンコードとデコードをサポート。日本語文字に完全対応、ファイルダウンロード機能を提供。データ転送、メール添付、API インターフェースなどのシーンに適用。無料でオンライン使用、インストール不要。", "seo_keywords": "Base64エンコード,Base64デコード,オンラインエンコード,テキスト変換,バイナリエンコード,URLエンコード,JSONエンコード,データ転送,メール添付,APIインターフェース,オンラインツール"}, "md5": {"title": "MD5 暗号化", "description": "MD5 ハッシュ暗号化ツール", "encrypt": "MD5暗号化", "seo_title": "MD5 暗号化ツール - オンラインテキストハッシュ計算 | ツールミ", "seo_description": "MD5 オンラインハッシュ暗号化ツール、テキストを MD5 ハッシュ値に高速変換。日本語文字をサポート、データ整合性検証、キャッシュキー生成、データ重複排除などのシーンに適用。詳細な技術説明とセキュリティ提案を提供。無料でオンライン使用、インストール不要。", "seo_keywords": "MD5暗号化,MD5ハッシュ,ハッシュ計算,データ整合性,パスワードハッシュ,キャッシュキー,データ重複排除,オンライン暗号化,テキストハッシュ,MD5ツール"}, "sha1": {"title": "SHA1 暗号化", "description": "SHA1 ハッシュ暗号化ツール、テキストをSHA1ハッシュ値に変換", "encrypt": "SHA1暗号化", "seo_title": "SHA1 ハッシュ暗号化ツール - オンライン SHA1 散列計算 | ツールミ", "seo_description": "SHA1 オンラインハッシュ暗号化ツール、テキストを SHA1 散列値に高速変換。日本語文字をサポート、データ整合性検証、バージョン管理などのシーンに適用。詳細なセキュリティ説明と代替案提案を提供。", "seo_keywords": "SHA1暗号化,SHA1ハッシュ,散列計算,データ整合性,バージョン管理,Git,ファイル検証,オンライン暗号化,SHA1ツール"}, "urlencode": {"title": "URL エンコード・デコード", "description": "URL エンコード・デコードツール、URL内の特殊文字を処理", "encode": "URLエンコード", "decode": "URLデコード", "seo_title": "URL エンコード・デコードツール - オンラインパーセントエンコード変換 | ツールミ", "seo_description": "URL エンコード・デコードオンラインツール、日本語文字、特殊記号のパーセントエンコード変換をサポート。Web 開発、API 呼び出し、フォーム送信などのシーンに適用。URL パラメータエンコード問題を高速処理、データの正確な転送を保証。", "seo_keywords": "URLエンコード,URLデコード,パーセントエンコード,encodeURIComponent,Web開発,API呼び出し,フォーム送信,特殊文字,日本語エンコード", "inputPlaceholder": "エンコード/デコードするURLまたはテキストを入力してください", "decodeError": "URLデコードに失敗しました。入力形式を確認してください"}, "jwtParse": {"title": "JWT 解析", "description": "JWT Token 解析ツール、JSON Web Tokenの解析と検証", "decode": "JWT解析", "seo_title": "JWT 解析ツール - オンライン JSON Web Token デコーダー | ツールミ", "seo_description": "JWT オンライン解析ツール、JSON Web Token を高速解析・検証。Header、Payload、Signature 分析をサポート、詳細な JWT 構造説明とセキュリティ提案を提供。開発デバッグと Token 検証に適用。", "seo_keywords": "JWT解析,JSO<PERSON> <PERSON> Token,<PERSON><PERSON>デコード,JWT検証,Header解析,Payload解析,JWTデバッグ,Token分析", "inputPlaceholder": "JWT Tokenを入力してください", "copyResult": "結果をコピー", "header": "Header", "payload": "Payload", "signature": "Signature", "parseError": "JWT解析に失敗しました。Tokenの形式を確認してください", "invalidFormat": "無効なJWT形式です"}, "json2yaml": {"title": "JSON→YAML変換", "description": "JSON と YAML 形式の相互変換ツール", "toYaml": "YAMLに変換", "toJson": "JSONに変換", "seo_title": "JSON→YAMLツール - オンラインJSON YAML形式変換器 | ツールミ", "seo_description": "JSON→YAMLオンライン変換ツール、JSONとYAML形式の双方向変換をサポート。日本語文字に完全対応、形式検証とエラー提示を提供。設定ファイル変換、APIドキュメント作成、データ形式変換などのシーンに適用。", "seo_keywords": "JSON→YAML,YAML→JSON,形式変換,設定ファイル,APIドキュメント,データ変換,オンライン変換,JSONツール,YAMLツール", "inputPlaceholder": "JSONまたはYAMLの内容を入力してください", "convertError": "変換に失敗しました。入力形式を確認してください"}, "sql": {"title": "SQL フォーマット", "description": "SQL 文のフォーマットと整形ツール", "format": "フォーマット", "minify": "圧縮", "seo_title": "SQL フォーマットツール - オンライン SQL 文整形圧縮 | ツールミ", "seo_description": "SQL フォーマットオンラインツール、SQL 文のフォーマット、整形、圧縮をサポート。構文ハイライト、エラー検出を提供、複数のデータベース方言をサポート。データベース開発、SQL デバッグ、コードレビューなどのシーンに適用。", "seo_keywords": "SQLフォーマット,SQL整形,SQL圧縮,データベース,SQL文,コードフォーマット,SQLツール,データベース開発", "inputLabel": "SQL 入力", "outputLabel": "フォーマット出力", "inputPlaceholder": "SQL文を入力してください", "formatError": "SQLフォーマットに失敗しました。構文を確認してください"}, "qr": {"title": "QRコード生成器", "description": "オンラインQRコード生成ツール、テキスト、URLなどの内容をサポート", "generate": "QRコード生成", "seo_title": "QRコード生成器 - オンライン QR Code 作成ツール | ツールミ", "seo_description": "QRコードオンライン生成器、テキスト、URL、連絡先、WiFi パスワードなどの内容でQRコードを生成。高解像度出力、ダウンロード保存をサポート。マーケティング推進、製品識別、情報共有などのシーンに適用。", "seo_keywords": "QRコード生成器,QRコード作成,オンラインQRコード,QRコードツール,マーケティング推進,製品識別,情報共有,モバイル決済,連絡先", "inputLabel": "内容入力", "inputPlaceholder": "QRコードを生成する内容を入力してください（テキスト、URLなど）", "download": "QRコードダウンロード", "resultTitle": "生成されたQRコード", "generateError": "QRコード生成に失敗しました"}, "uuid": {"title": "UUID 生成器", "description": "UUID 一意識別子生成ツール", "generate": "UUID生成", "seo_title": "UUID 生成器 - オンライン一意識別子生成ツール | ツールミ", "seo_description": "UUID オンライン生成器、一意識別子を高速生成。バッチ生成、ワンクリックコピーをサポート、データベース主キー、API 識別、ファイル命名などのシーンに適用。RFC 4122 標準に準拠、グローバル一意性を保証。", "seo_keywords": "UUID生成器,一意識別子,GUID生成,データベース主キー,API識別,ファイル命名,RFC4122,グローバル一意", "countLabel": "生成数", "copyAll": "すべてコピー", "clear": "クリア", "resultTitle": "生成されたUUID", "countSuffix": "個"}, "numberToZh": {"title": "数字→中国語変換", "description": "アラビア数字を中国語数字に変換、簡体字と繁体字をサポート", "convert": "変換", "seo_title": "数字→中国語ツール - オンラインアラビア数字中国語変換器 | ツールミ", "seo_description": "数字→中国語オンラインツール、アラビア数字を簡体中国語、繁体中国語、大文字金額に変換をサポート。財務報告書、契約書、請求書発行などのシーンに適用。整数と小数の変換をサポート。", "seo_keywords": "数字→中国語,アラビア数字変換,中国語数字,大文字金額,財務報告書,契約書,請求書発行", "inputLabel": "数字入力", "inputPlaceholder": "数字を入力してください。例：12345", "typeLabel": "変換タイプ", "simple": "簡体中国語", "traditional": "繁体中国語", "financial": "大文字金額", "convertError": "数字変換に失敗しました。入力形式を確認してください"}, "netscapeCookies": {"title": "Netscape Cookies 形式変換", "description": "Netscape Cookies形式とJSON形式の相互変換ツール", "convert": "変換", "seo_title": "Netscape Cookies 形式変換ツール - オンライン Cookies 形式変換器 | ツールミ", "seo_description": "Netscape Cookies 形式変換オンラインツール、Netscape Cookies 形式と JSON 形式の双方向変換をサポート。ブラウザデータ移行、クローラー開発、Web テストなどのシーンに適用。", "seo_keywords": "Netscape Cookies,Cookies形式変換,ブラウザデータ,クローラー開発,Webテスト,JSON変換", "inputFormatLabel": "入力形式", "netscapeFormat": "Netscape形式", "jsonFormat": "JSON形式", "inputLabel": "内容入力", "resultLabel": "変換結果", "clear": "クリア", "convertError": "形式変換に失敗しました。入力形式を確認してください"}, "yaml": {"title": "YAML フォーマット", "description": "YAML フォーマット、検証、整形ツール", "format": "フォーマット", "validate": "検証", "seo_title": "YAML フォーマットツール - オンライン YAML 検証整形器 | ツールミ", "seo_description": "YAML フォーマットオンラインツール、YAML 形式検証、整形、フォーマットをサポート。構文チェック、エラー提示を提供、設定ファイル作成、DevOps デプロイ、API ドキュメントなどのシーンに適用。", "seo_keywords": "YAMLフォーマット,YAML検証,YAM<PERSON>整形,設定ファイル,<PERSON><PERSON><PERSON>,APIドキュメント,構文チェック", "inputLabel": "YAML 入力", "outputLabel": "フォーマット出力", "inputPlaceholder": "YAML内容を入力してください", "clear": "クリア", "validStatus": "✓ YAML 形式が正しいです", "invalidStatus": "✗ YAML 形式にエラーがあります", "formatError": "YAMLフォーマットに失敗しました。構文を確認してください"}, "reverseString": {"title": "文字列反転", "description": "文字列反転とテキスト処理ツール", "reverse": "反転", "seo_title": "文字列反転ツール - オンラインテキスト反転プロセッサー | ツールミ", "seo_description": "文字列反転オンラインツール、文字、単語、行でテキストを反転をサポート。テキスト処理、パスワード生成、データ分析などのシーンに適用。複数の反転モードを提供、操作が簡単で高速。", "seo_keywords": "文字列反転,テキスト反転,テキスト処理,パスワード生成,データ分析,オンラインツール", "inputLabel": "テキスト入力", "inputPlaceholder": "反転するテキストを入力してください", "optionsLabel": "反転オプション", "byCharacters": "文字で反転", "byWords": "単語で反転", "byLines": "行で反転", "resultLabel": "反転結果", "clear": "クリア", "reverseError": "テキスト反転に失敗しました"}, "jsonToPhpArrayCode": {"title": "JSON → PHP 配列コード", "description": "JSON を PHP の配列コードに変換、直接コピーして PHP コードに貼り付け可能", "convert": "変換", "seo_title": "JSON → PHP 配列コード - オンライン変換ツール | ツールミ", "seo_description": "JSON → PHP 配列コードオンラインツール、ネスト構造、日本語文字、特殊文字エスケープをサポート。ワンクリックで JSON データを形式規範の PHP 配列コードに変換、設定ファイル、API Mock データ、データベースシードファイルなどのシーンに適用。", "seo_keywords": "JSON→PHP,PHP配列,JSON変換,PHPコード生成,オンラインツール,設定ファイル,API Mock,データ変換,PHP開発ツール,JSON解析", "inputLabel": "JSON 入力", "inputPlaceholder": "JSON データを入力してください", "outputLabel": "PHP 配列コード", "copyResult": "結果をコピー", "loadExample": "サンプル読み込み", "convertError": "JSON変換に失敗しました。形式を確認してください"}}, "footer": {"copyright": "ツールミ Build with love。"}, "about": {"title": "このサイトについて", "seo_title": "このサイトについて - ツールミ", "seo_description": "ツールミウェブサイトの詳細情報、技術スタックと機能特性について", "seo_keywords": "について,ツールミ,オンラインツール,Nuxt3,技術スタック", "aboutSite": {"title": "ツールミについて", "description1": "ツールミは高品質なオンラインツールの提供に特化したウェブサイトです。開発者、デザイナー、一般ユーザーに便利で効率的なオンラインツールサービスを提供することに専念しています。", "description2": "私たちの目標は「次世代オンライン効率ツール」になることです。現代的な技術スタックとユーザーフレンドリーなインターフェースデザインを通じて、誰もが様々な実用的なツールを簡単に使用できるようにします。"}, "categories": {"title": "ツールカテゴリ", "toolsCount": "個のツール", "viewTools": "ツールを見る"}, "contact": {"title": "お問い合わせ", "description": "ご提案、ご質問、協力のご意向がございましたら、以下の方法でお気軽にお問い合わせください：", "feedback": "フィードバック提案", "website": "ウェブサイト"}}}