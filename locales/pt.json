{"it": "TI", "code": "Código", "format": "Formato", "generator": "G<PERSON>dor", "json": "JSON", "calc": "Calculadora", "text": "Texto", "tool": "Ferramenta", "common": {"appName": "ToolMi", "slogan": "Ferramentas de eficiência online de nova geração", "input": "Entrada", "output": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "download": "Baixar", "clear": "Limpar", "example": "<PERSON>egar exemplo", "search": "<PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "Digite palavras-chave, atualmente {count} ferramentas", "about": "Sobre este site", "feedback": "<PERSON><PERSON><PERSON>", "category": "Categoria", "latest": "<PERSON><PERSON>e", "tools": "ferramentas", "inputRequired": "Por favor, insira conteúdo antes de continuar", "copySuccess": "Copiado com sucesso", "copyFailed": "Falha ao copiar", "languageSwitch": "<PERSON><PERSON> idioma", "useTool": "<PERSON><PERSON> ferramenta", "backToCategoryList": "Voltar à lista de categorias"}, "nav": {"home": "Início", "encrypt": "Criptografar", "format": "Formato", "generator": "G<PERSON>dor", "json": "JSON", "calc": "Calculadora", "text": "Texto", "table": "<PERSON><PERSON><PERSON>"}, "categories": {"encrypt": "Criptografia", "format": "Formato", "generator": "G<PERSON>dor", "json": "Ferramentas JSON", "calc": "Calculadora", "text": "Processamento de texto", "table": "Ferramentas de tabela", "iframe": "Ferramentas online", "math": "Ferramentas matemáticas"}, "tools": {"base64": {"title": "Codificação e decodificação Base64", "description": "Ferramenta de codificação e decodificação Base64, compatível com codificação e decodificação Base64 de texto e arquivos", "encode": "Codificar Base64", "decode": "Decodificar Base64", "seo_title": "Ferramenta de codificação e decodificação Base64 - Conversão de texto binário online | ToolMi", "seo_description": "Ferramenta online de codificação e decodificação Base64, compatível com codificação e decodificação Base64 de texto, URL, JSON e outros formatos diversos. Totalmente compatível com caracteres portugueses, fornece função de download de arquivos. Aplicável a transferência de dados, anexos de e-mail, interfaces API e outros cenários. Uso gratuito online, sem necessidade de instalação.", "seo_keywords": "codificação Base64,decodificação Base64,codificação online,conversão de texto,codificação binária,codificação URL,codificação JSON,transferência de dados,anexos e-mail,interface API,ferramentas online", "inputPlaceholder": "Digite o texto ou URL para codificar/decodificar", "decodeError": "Erro na decodificação Base64, verifique o formato de entrada"}, "md5": {"title": "Criptografia MD5", "description": "Ferramenta de criptografia hash MD5", "encrypt": "Criptografar MD5", "seo_title": "Ferramenta de criptografia MD5 - C<PERSON>l<PERSON>lo de hash de texto online | ToolMi", "seo_description": "Ferramenta online de criptografia hash MD5, converte rapidamente texto em valores hash MD5. Compatível com caracteres portugueses, aplicável à verificação de integridade de dados, geração de chaves de cache, desduplicação de dados e outros cenários. Fornece explicações técnicas detalhadas e sugestões de segurança. Uso gratuito online, sem necessidade de instalação.", "seo_keywords": "criptografia MD5,hash MD5,c<PERSON><PERSON><PERSON>lo de hash,integridade de dados,hash de senha,chave de cache,desduplicação de dados,criptografia online,hash de texto,ferramenta MD5"}, "sha1": {"title": "Criptografia SHA1", "description": "Ferramenta de criptografia hash SHA1, converte texto em valores hash SHA1", "encrypt": "Criptografar SHA1", "seo_title": "Ferramenta de criptografia hash SHA1 - Cálculo de hash SHA1 online | ToolMi", "seo_description": "Ferramenta online de criptografia hash SHA1, converte rapidamente texto em valores hash SHA1. Compatível com caracteres portugueses, aplicável à verificação de integridade de dados, controle de versão e outros cenários. Fornece explicações de segurança detalhadas e propostas de alternativas.", "seo_keywords": "criptografia SHA1,hash SHA1,c<PERSON><PERSON><PERSON>lo de hash,integridade de dados,controle de versão,Git,verificação de arquivos,criptografia online,ferramenta SHA1"}, "urlencode": {"title": "Codificação e decodificação URL", "description": "Ferramenta de codificação e decodificação URL, lida com caracteres especiais em URLs", "encode": "Codificar URL", "decode": "Decodificar URL", "seo_title": "Ferramenta de codificação e decodificação URL - Conversão de codificação percentual online | ToolMi", "seo_description": "Ferramenta online de codificação e decodificação URL, compatível com conversão de codificação percentual de caracteres portugueses e símbolos especiais. Aplicável ao desenvolvimento web, chamadas API, submissão de formulários e outros cenários. Lida rapidamente com problemas de codificação de parâmetros URL, garante transmissão precisa de dados.", "seo_keywords": "codificação URL,decodificação URL,codificação percentual,encodeURIComponent,desenvolvimento web,chamadas API,submissão de formulários,caracteres especiais,codificação portuguesa", "inputPlaceholder": "Digite a URL ou texto para codificar/decodificar", "decodeError": "Erro na decodificação URL. Verifique o formato de entrada"}, "jwtParse": {"title": "Análise JWT", "description": "Ferramenta de análise de JWT Token, análise e verificação de JSON Web Token", "decode": "Analisar JWT", "seo_title": "Ferramenta de análise JWT - Decodificador de JSON Web Token online | ToolMi", "seo_description": "Ferramenta online de análise JWT, analisa e verifica rapidamente JSON Web Token. Compatível com análise de Header, Payload, Signature, fornece explicações detalhadas da estrutura JWT e sugestões de segurança. Aplicável ao debug de desenvolvimento e verificação de Token.", "seo_keywords": "an<PERSON><PERSON><PERSON>,JSON Web Token,decodificação Token,verificação JWT,an<PERSON><PERSON><PERSON>,an<PERSON><PERSON><PERSON> Payload,debug JWT,anális<PERSON>", "inputPlaceholder": "Digite o JWT Token", "copyResult": "Copiar resultado", "header": "Header", "payload": "Payload", "signature": "Signature", "parseError": "Erro na análise JWT. Verifique o formato do Token", "invalidFormat": "Formato JWT inválido"}, "json2yaml": {"title": "Conversão JSON→YAML", "description": "Ferramenta de conversão mútua entre formatos JSON e YAML", "toYaml": "Converter para YAML", "toJson": "Converter para JSON", "seo_title": "Ferramenta JSON→YAML - Conversor de formato JSON YAML online | ToolMi", "seo_description": "Ferramenta online de conversão JSON→YAML, compatível com conversão bidirecional entre formatos JSON e YAML. Totalmente compatível com caracteres portugueses, fornece validação de formato e indicação de erros. Aplicável à conversão de arquivos de configuração, criação de documentação API, conversão de formato de dados e outros cenários.", "seo_keywords": "JSON→YAML,YAML→JSON,conversão de formato,arquivo de configuração,documentação API,conversão de dados,conversão online,ferramenta JSON,ferramenta YAML", "inputPlaceholder": "Digite o conteúdo JSON ou YAML", "convertError": "Erro na conversão. Verifique o formato de entrada"}, "sql": {"title": "Formato SQL", "description": "Ferramenta de formatação e embelezamento de declarações SQL", "format": "Formatar", "minify": "Comprimir", "seo_title": "Ferramenta de formato SQL - Embelezamento e compressão de declarações SQL online | ToolMi", "seo_description": "Ferramenta online de formato SQL, compatível com formatação, embelezamento e compressão de declarações SQL. Fornece coloração sintática, detecção de erros, compatível com múltiplos dialetos de banco de dados. Aplicável ao desenvolvimento de bancos de dados, debug SQL, revisão de código e outros cenários.", "seo_keywords": "formato SQL,embelezamento SQL,compressão SQL,banco de dados,declaração SQL,formato de código,ferramenta SQL,desenvolvimento de bancos de dados", "inputLabel": "Entrada SQL", "outputLabel": "Saída formatada", "inputPlaceholder": "Digite a declaração SQL", "formatError": "Erro na formatação SQL. Verifique a sintaxe"}, "qr": {"title": "Gerador de códigos QR", "description": "Ferramenta online de geração de códigos QR, compatível com texto, URLs e outros conteúdos", "generate": "<PERSON><PERSON><PERSON> c<PERSON>", "seo_title": "Gerador de códigos QR - Ferramenta de criação de QR Code online | ToolMi", "seo_description": "Gerador online de códigos QR, gera códigos QR com conteúdo como texto, URLs, informações de contato, senhas WiFi. Compatível com saída de alta resolução, download e salvamento. Aplicável à promoção de marketing, identificação de produtos, compartilhamento de informações e outros cenários.", "seo_keywords": "gerador de códigos QR,criação de códigos QR,código QR online,ferramenta de códigos QR,promoção de marketing,identificação de produtos,compartilhamento de informações,pagamento móvel,contato", "inputLabel": "Entrada de conteúdo", "inputPlaceholder": "Digite o conteúdo para gerar o código QR (texto, URL, etc.)", "download": "Baixar código QR", "resultTitle": "Código QR gerado", "generateError": "Erro na geração do código QR"}, "uuid": {"title": "Gerador UUID", "description": "Ferramenta de geração de identificadores únicos UUID", "generate": "<PERSON><PERSON><PERSON>", "seo_title": "Gerador UUID - Ferramenta de geração de identificadores únicos online | ToolMi", "seo_description": "Gerador online UUID, gera rapidamente identificadores únicos. Compatível com geração em lote, cópia com um clique, aplicável a chaves primárias de banco de dados, identificação API, nomenclatura de arquivos e outros cenários. Conforme ao padrão RFC 4122, garante unicidade global.", "seo_keywords": "gerador UUI<PERSON>,identificador único,geração GUID,chave primária de banco de dados,identificação API,nomenclatura de arquivos,RFC4122,unicidade global", "countLabel": "Quantidade a gerar", "copyAll": "Copiar tudo", "clear": "Limpar", "resultTitle": "UUIDs gerados", "countSuffix": "unidades"}, "numberToZh": {"title": "Conversão de números para chinês", "description": "Converte números arábicos em números chineses, compatível com chinês simplificado e tradicional", "convert": "Converter", "seo_title": "Ferramenta de números em chinês - Conversor de números arábicos para chinês online | ToolMi", "seo_description": "Ferramenta online de números em chinês, compatível com conversão de números arábicos para chinês simplificado, chinês tradicional e valores em maiúsculas. Aplicável a relatórios financeiros, contratos, emissão de faturas e outros cenários. Compatível com conversão de inteiros e decimais.", "seo_keywords": "números em chinês,conversão de números arábicos,números chineses,valores em maiúsculas,relatórios financeiros,contratos,emissão de faturas", "inputLabel": "Entrada de números", "inputPlaceholder": "Di<PERSON>e nú<PERSON>, por exemplo: 12345", "typeLabel": "Tipo de conversão", "simple": "<PERSON><PERSON><PERSON> simplificado", "traditional": "Chinês tradicional", "financial": "Valores em maiúsculas", "convertError": "Erro na conversão de números. Verifique o formato de entrada"}, "netscapeCookies": {"title": "Conversão de formato Netscape Cookies", "description": "Ferramenta de conversão mútua entre formato Netscape Cookies e formato JSON", "convert": "Converter", "seo_title": "Ferramenta de conversão de formato Netscape Cookies - Conversor de formato Cookies online | ToolMi", "seo_description": "Ferramenta online de conversão de formato Netscape Cookies, compatível com conversão bidirecional entre formato Netscape Cookies e formato JSON. Aplicável à migração de dados de navegador, desenvolvimento de crawlers, testes web e outros cenários.", "seo_keywords": "Netscape Cookies,conversão de formato Cookies,dados de navegador,desenvolvimento de crawlers,testes web,conversão JSON", "inputFormatLabel": "Formato de entrada", "netscapeFormat": "Formato Netscape", "jsonFormat": "Formato JSON", "inputLabel": "Entrada de conteúdo", "resultLabel": "Resultado da conversão", "clear": "Limpar", "convertError": "Erro na conversão de formato. Verifique o formato de entrada"}, "yaml": {"title": "Formato YAML", "description": "Ferramenta de formatação, validação e embelezamento YAML", "format": "Formatar", "validate": "Validar", "seo_title": "Ferramenta de formato YAML - Validador e embelezador YAML online | ToolMi", "seo_description": "Ferramenta online de formato YAML, compatível com validação, embelezamento e formatação YAML. Fornece verificação de sintaxe, indicação de erros, aplicável à criação de arquivos de configuração, implantação DevOps, documentação API e outros cenários.", "seo_keywords": "formato YAML,validação YAML,embelezamento YAML,arquivo de configuração,DevOps,documentação API,verificação de sintaxe", "inputLabel": "Entrada YAML", "outputLabel": "Saída formatada", "inputPlaceholder": "Digite o conteúdo YAML", "clear": "Limpar", "validStatus": "✓ O formato YAML está correto", "invalidStatus": "✗ Há erros no formato YAML", "formatError": "Erro na formatação YAML. Verifique a sintaxe"}, "reverseString": {"title": "Inversão de strings", "description": "Ferramenta de inversão de strings e processamento de texto", "reverse": "Inverter", "seo_title": "Ferramenta de inversão de strings - Processador de inversão de texto online | ToolMi", "seo_description": "Ferramenta online de inversão de strings, compatível com inversão de texto por caracteres, palavras e linhas. Aplicável ao processamento de texto, geração de senhas, análise de dados e outros cenários. Fornece múltiplos modos de inversão, operação simples e rápida.", "seo_keywords": "inversão de strings,inversão de texto,processamento de texto,geração de senhas,análise de dados,ferramentas online", "inputLabel": "Entrada de texto", "inputPlaceholder": "Digite o texto a ser invertido", "optionsLabel": "Opções de inversão", "byCharacters": "Inverter por caracteres", "byWords": "Inverter por palavras", "byLines": "Inverter por linhas", "resultLabel": "Resultado da inversão", "clear": "Limpar", "reverseError": "Erro na inversão do texto"}, "jsonToPhpArrayCode": {"title": "JSON → Código de array PHP", "description": "Converte JSON em código de array PHP, pode ser copiado diretamente e colado no código PHP", "convert": "Converter", "seo_title": "JSON → Código de array PHP - Ferramenta de conversão online | ToolMi", "seo_description": "Ferramenta online JSON → Código de array PHP, compatível com estruturas aninhadas, caracteres portugueses, escape de caracteres especiais. Converte com um clique dados JSON em código de array PHP no formato padrão, aplicável a arquivos de configuração, dados Mock API, arquivos seed de banco de dados e outros cenários.", "seo_keywords": "JSON→PHP,array PHP,conversão JSON,geração de código PHP,ferramentas online,arquivo de configuração,API Mock,conversão de dados,ferramentas de desenvolvimento PHP,análise JSON", "inputLabel": "Entrada JSON", "inputPlaceholder": "Digite os dados JSON", "outputLabel": "Código de array PHP", "copyResult": "Copiar resultado", "loadExample": "<PERSON>egar exemplo", "convertError": "Erro na conversão JSON. Verifique o formato"}}, "footer": {"copyright": "ToolMi Construído com amor."}, "about": {"title": "Sobre este site", "seo_title": "Sobre este site - ToolMi", "seo_description": "Informações detalhadas do site ToolMi, stack tecnológica e características funcionais", "seo_keywords": "sobre,ToolMi,ferramentas online,Nuxt3,stack tecnológica", "aboutSite": {"title": "Sobre o ToolMi", "description1": "ToolMi é um site especializado em fornecer ferramentas online de alta qualidade. Dedicamo-nos a fornecer serviços de ferramentas online práticos e eficientes para desenvolvedores, designers e usuários em geral.", "description2": "Nosso objetivo é nos tornar 'ferramentas de eficiência online de nova geração'. Através de uma stack tecnológica moderna e design de interface amigável, permitimos que todos usem facilmente várias ferramentas práticas."}, "categories": {"title": "Categorias de ferramentas", "toolsCount": "ferramentas", "viewTools": "Ver ferramentas"}, "contact": {"title": "Contato", "description": "Se você tem sugestões, perguntas ou intenções de cooperação, sinta-se à vontade para nos contatar pelos seguintes métodos:", "feedback": "Sugestões e feedback", "website": "Site"}}}