{"it": "IT", "code": "Code", "format": "Format", "generator": "Générateur", "json": "JSON", "calc": "Calculatrice", "text": "Texte", "tool": "Outil", "common": {"appName": "ToolMi", "slogan": "Outils d'efficacité en ligne de nouvelle génération", "input": "Entrée", "output": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "download": "Télécharger", "clear": "<PERSON><PERSON><PERSON><PERSON>", "example": "Charger l'exemple", "search": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Entrez des mots-clés, actuellement {count} outils", "about": "À propos de ce site", "feedback": "Commentaires", "category": "<PERSON><PERSON><PERSON><PERSON>", "latest": "<PERSON><PERSON>", "tools": "outils", "inputRequired": "Veuillez saisir du contenu avant de continuer", "copySuccess": "<PERSON><PERSON><PERSON> avec succès", "copyFailed": "Échec de la copie", "languageSwitch": "Changer de langue", "useTool": "Utiliser l'outil", "backToCategoryList": "Retour à la liste des catégories"}, "nav": {"home": "Accueil", "encrypt": "<PERSON><PERSON><PERSON>", "format": "Format", "generator": "Générateur", "json": "JSON", "calc": "Calculatrice", "text": "Texte", "table": "<PERSON><PERSON>"}, "categories": {"encrypt": "Chiffrement", "format": "Format", "generator": "Générateur", "json": "Outils JSON", "calc": "Calculatrice", "text": "Traitement de texte", "table": "<PERSON><PERSON> de tableau", "iframe": "Outils en ligne", "math": "Outils mathématiques"}, "tools": {"base64": {"title": "Encodage et décodage Base64", "description": "Outil d'encodage et de décodage Base64, compatible avec l'encodage et le décodage Base64 de texte et de fichiers", "encode": "Encoder Base64", "decode": "Décoder Base64", "seo_title": "Outil d'encodage et décodage Base64 - Conversion de texte binaire en ligne | ToolMi", "seo_description": "Outil en ligne d'encodage et de décodage Base64, compatible avec l'encodage et le décodage Base64 de texte, URL, JSON et autres formats divers. Entièrement compatible avec les caractères français, fournit une fonction de téléchargement de fichiers. Applicable au transfert de données, pièces jointes d'e-mail, interfaces API et autres scénarios. Utilisation gratuite en ligne, aucune installation requise.", "seo_keywords": "encodage Base64,décodage Base64,encodage en ligne,conversion de texte,encodage binaire,encodage URL,encodage JSON,transfert de données,pièces jointes e-mail,interface API,outils en ligne", "inputPlaceholder": "Entrez le texte ou l'URL à encoder/décoder", "decodeError": "Erreur de décodage Base64, vérifiez le format d'entrée"}, "md5": {"title": "Chiffrement MD5", "description": "<PERSON>il de chiffrement de hachage MD5", "encrypt": "Chiffrer MD5", "seo_title": "Outil de chiffrement MD5 - Calcul de hachage de texte en ligne | ToolMi", "seo_description": "Outil en ligne de chiffrement de hachage MD5, convertit rapidement le texte en valeurs de hachage MD5. Compatible avec les caractères français, applicable à la vérification d'intégrité des données, génération de clés de cache, déduplication de données et autres scénarios. Fournit des explications techniques détaillées et des suggestions de sécurité. Utilisation gratuite en ligne, aucune installation requise.", "seo_keywords": "chiffrement MD5,hachage MD5,calcul de hachage,intégrité des données,hachage de mot de passe,clé de cache,déduplication de données,chiffrement en ligne,hachage de texte,outil MD5"}, "sha1": {"title": "Chiffrement SHA1", "description": "Outil de chiffrement de hachage SHA1, convertit le texte en valeurs de hachage SHA1", "encrypt": "Chiffrer SHA1", "seo_title": "Outil de chiffrement de hachage SHA1 - Calcul de hachage SHA1 en ligne | ToolMi", "seo_description": "Outil en ligne de chiffrement de hachage SHA1, convertit rapidement le texte en valeurs de hachage SHA1. Compatible avec les caractères français, applicable à la vérification d'intégrité des données, contrôle de version et autres scénarios. Fournit des explications de sécurité détaillées et des propositions d'alternatives.", "seo_keywords": "chiffrement SHA1,hachage SHA1,calcul de hachage,intégrité des données,contrôle de version,Git,vérification de fichiers,chiffrement en ligne,outil SHA1"}, "urlencode": {"title": "Encodage et décodage URL", "description": "Outil d'encodage et de décodage URL, gère les caractères spéciaux dans les URL", "encode": "Encoder URL", "decode": "Décoder URL", "seo_title": "Outil d'encodage et décodage URL - Conversion d'encodage en pourcentage en ligne | ToolMi", "seo_description": "Outil en ligne d'encodage et de décodage URL, compatible avec la conversion d'encodage en pourcentage des caractères français et symboles spéciaux. Applicable au développement web, appels API, soumission de formulaires et autres scénarios. Gère rapidement les problèmes d'encodage des paramètres URL, garantit une transmission précise des données.", "seo_keywords": "encodage URL,décodage URL,encodage en pourcentage,encodeURIComponent,développement web,appels API,soumission de formulaires,caractères spéciaux,encodage français", "inputPlaceholder": "Entrez l'URL ou le texte à encoder/décoder", "decodeError": "Erreur de décodage URL. Vérifiez le format d'entrée"}, "jwtParse": {"title": "Analyse JWT", "description": "Outil d'analyse de JWT Token, analyse et vérification de JSON Web Token", "decode": "Analyser JWT", "seo_title": "Outil d'analyse JWT - Décodeur de JSON Web Token en ligne | ToolMi", "seo_description": "Outil en ligne d'analyse JWT, analyse et vérifie rapidement les JSON Web Token. Compatible avec l'analyse d'Header, Payload, Signature, fournit des explications détaillées de la structure JWT et des suggestions de sécurité. Applicable au débogage de développement et à la vérification de Token.", "seo_keywords": "analyse JWT,JSON Web Token,décodage Token,vérification JWT,analyse Header,analyse Payload,débogage JWT,analyse Token", "inputPlaceholder": "Entrez le JWT Token", "copyResult": "<PERSON><PERSON><PERSON> le résultat", "header": "Header", "payload": "Payload", "signature": "Signature", "parseError": "Erreur d'analyse JWT. Vérifiez le format du Token", "invalidFormat": "Format JWT invalide"}, "json2yaml": {"title": "Conversion JSON→YAML", "description": "Outil de conversion mutuelle entre les formats JSON et YAML", "toYaml": "Convertir en YAML", "toJson": "Convertir en JSON", "seo_title": "Outil JSON→YAML - Convertisseur de format JSON YAML en ligne | ToolMi", "seo_description": "Outil en ligne de conversion JSON→YAML, compatible avec la conversion bidirectionnelle entre les formats JSON et YAML. Entièrement compatible avec les caractères français, fournit une validation de format et une indication d'erreurs. Applicable à la conversion de fichiers de configuration, création de documentation API, conversion de format de données et autres scénarios.", "seo_keywords": "JSON→YAML,YAML→JSON,conversion de format,fichier de configuration,documentation API,conversion de données,conversion en ligne,outil JSON,outil YAML", "inputPlaceholder": "Entrez le contenu JSON ou YAML", "convertError": "Erreur de conversion. Vérifiez le format d'entrée"}, "sql": {"title": "Format SQL", "description": "Outil de formatage et d'embellissement des déclarations SQL", "format": "Formater", "minify": "Compresser", "seo_title": "Outil de format SQL - Embellissement et compression des déclarations SQL en ligne | ToolMi", "seo_description": "Outil en ligne de format SQL, compatible avec le formatage, l'embellissement et la compression des déclarations SQL. Fournit une coloration syntaxique, détection d'erreurs, compatible avec plusieurs dialectes de base de données. Applicable au développement de bases de données, débogage SQL, révision de code et autres scénarios.", "seo_keywords": "format SQL,embellissement SQL,compression SQL,base de données,déclaration SQL,format de code,outil SQL,développement de bases de données", "inputLabel": "Entrée SQL", "outputLabel": "Sortie formatée", "inputPlaceholder": "Entrez la déclaration SQL", "formatError": "Erreur de formatage SQL. Vérifiez la syntaxe"}, "qr": {"title": "Générateur de codes QR", "description": "Outil en ligne de génération de codes QR, compatible avec le texte, les URL et autres contenus", "generate": "Générer le code QR", "seo_title": "Générateur de codes QR - Outil de création de QR Code en ligne | ToolMi", "seo_description": "Générateur en ligne de codes QR, génère des codes QR avec du contenu comme du texte, des URL, des informations de contact, des mots de passe WiFi. Compatible avec la sortie haute résolution, téléchargement et sauvegarde. Applicable à la promotion marketing, identification de produits, partage d'informations et autres scénarios.", "seo_keywords": "générateur de codes QR,création de codes QR,code QR en ligne,outil de codes QR,promotion marketing,identification de produits,partage d'informations,paiement mobile,contact", "inputLabel": "Entrée de contenu", "inputPlaceholder": "Entrez le contenu pour générer le code QR (texte, URL, etc.)", "download": "Télécharger le code QR", "resultTitle": "Code QR généré", "generateError": "Erreur de génération du code QR"}, "uuid": {"title": "Générateur UUID", "description": "Outil de génération d'identifiants uniques UUID", "generate": "<PERSON><PERSON><PERSON>rer UUID", "seo_title": "Générateur UUID - Outil de génération d'identifiants uniques en ligne | ToolMi", "seo_description": "Générateur en ligne UUID, génère rapidement des identifiants uniques. Compatible avec la génération par lots, copie en un clic, applicable aux clés primaires de base de données, identification API, nomenclature de fichiers et autres scénarios. Conforme au standard RFC 4122, garantit l'unicité globale.", "seo_keywords": "générateur UUID,identifiant unique,génération GUID,clé primaire de base de données,identification API,nomenclature de fichiers,RFC4122,unicité globale", "countLabel": "Quantité à générer", "copyAll": "<PERSON><PERSON><PERSON> tout", "clear": "<PERSON><PERSON><PERSON><PERSON>", "resultTitle": "UUID générés", "countSuffix": "unités"}, "numberToZh": {"title": "Conversion de nombres en chinois", "description": "Convertit les nombres arabes en nombres chinois, compatible avec le chinois simplifié et traditionnel", "convert": "Convertir", "seo_title": "Outil de nombres en chinois - Convertisseur de nombres arabes en chinois en ligne | ToolMi", "seo_description": "Outil en ligne de nombres en chinois, compatible avec la conversion de nombres arabes en chinois simplifié, chinois traditionnel et montants en majuscules. Applicable aux rapports financiers, contrats, émission de factures et autres scénarios. Compatible avec la conversion d'entiers et de décimales.", "seo_keywords": "nombres en chinois,conversion de nombres arabes,nombres chinois,montants en majuscules,rapports financiers,contrats,émission de factures", "inputLabel": "Entrée de nombres", "inputPlaceholder": "Entrez des nombres, par exemple : 12345", "typeLabel": "Type de conversion", "simple": "<PERSON><PERSON> simplifié", "traditional": "Chinois traditionnel", "financial": "Montants en majuscules", "convertError": "Erreur de conversion des nombres. Vérifiez le format d'entrée"}, "netscapeCookies": {"title": "Conversion de format Netscape Cookies", "description": "Outil de conversion mutuelle entre le format Netscape Cookies et le format JSON", "convert": "Convertir", "seo_title": "Outil de conversion de format Netscape Cookies - Convertisseur de format Cookies en ligne | ToolMi", "seo_description": "Outil en ligne de conversion de format Netscape Cookies, compatible avec la conversion bidirectionnelle entre le format Netscape Cookies et le format JSON. Applicable à la migration de données de navigateur, développement de crawlers, tests web et autres scénarios.", "seo_keywords": "Netscape Cookies,conversion de format Cookies,données de navigateur,développement de crawlers,tests web,conversion JSON", "inputFormatLabel": "Format d'entrée", "netscapeFormat": "Format Netscape", "jsonFormat": "Format JSON", "inputLabel": "Entrée de contenu", "resultLabel": "Résultat de conversion", "clear": "<PERSON><PERSON><PERSON><PERSON>", "convertError": "Erreur de conversion de format. Vérifiez le format d'entrée"}, "yaml": {"title": "Format YAML", "description": "Outil de formatage, validation et embellissement YAML", "format": "Formater", "validate": "Valider", "seo_title": "Outil de format YAML - Validateur et embellisseur YAML en ligne | ToolMi", "seo_description": "Outil en ligne de format YAML, compatible avec la validation, l'embellissement et le formatage YAML. Fournit une vérification de syntaxe, indication d'erreurs, applicable à la création de fichiers de configuration, déploiement DevOps, documentation API et autres scénarios.", "seo_keywords": "format YAML,validation YAML,embellissement YAML,fichier de configuration,DevOps,documentation API,vérification de syntaxe", "inputLabel": "Entrée YAML", "outputLabel": "Sortie formatée", "inputPlaceholder": "Entrez le contenu YAML", "clear": "<PERSON><PERSON><PERSON><PERSON>", "validStatus": "✓ Le format YAML est correct", "invalidStatus": "✗ Il y a des erreurs dans le format YAML", "formatError": "Erreur de formatage YAML. Vérifiez la syntaxe"}, "reverseString": {"title": "Inversion de chaînes", "description": "Outil d'inversion de chaînes et de traitement de texte", "reverse": "Inverser", "seo_title": "Outil d'inversion de chaînes - Processeur d'inversion de texte en ligne | ToolMi", "seo_description": "Outil en ligne d'inversion de chaînes, compatible avec l'inversion de texte par caractères, mots et lignes. Applicable au traitement de texte, génération de mots de passe, analyse de données et autres scénarios. Fournit plusieurs modes d'inversion, opération simple et rapide.", "seo_keywords": "inversion de chaînes,inversion de texte,traitement de texte,génération de mots de passe,analyse de données,outils en ligne", "inputLabel": "Entrée de texte", "inputPlaceholder": "Entrez le texte à inverser", "optionsLabel": "Options d'inversion", "byCharacters": "Inverser par caractères", "byWords": "Inverser par mots", "byLines": "Inverser par lignes", "resultLabel": "Résultat d'inversion", "clear": "<PERSON><PERSON><PERSON><PERSON>", "reverseError": "Erreur d'inversion du texte"}, "jsonToPhpArrayCode": {"title": "JSON → Code de tableau PHP", "description": "Convertit JSON en code de tableau PHP, peut être copié directement et collé dans le code PHP", "convert": "Convertir", "seo_title": "JSON → Code de tableau PHP - Outil de conversion en ligne | ToolMi", "seo_description": "Outil en ligne JSON → Code de tableau PHP, compatible avec les structures imbriquées, caractères français, échappement de caractères spéciaux. Convertit en un clic les données JSON en code de tableau PHP au format standard, applicable aux fichiers de configuration, données Mock API, fichiers seed de base de données et autres scénarios.", "seo_keywords": "JSON→PHP,tableau PHP,conversion JSON,génération de code PHP,outils en ligne,fichier de configuration,API Mock,conversion de données,outils de développement PHP,analyse JSON", "inputLabel": "Entrée JSON", "inputPlaceholder": "Entrez les données JSON", "outputLabel": "Code de tableau PHP", "copyResult": "<PERSON><PERSON><PERSON> le résultat", "loadExample": "Charger l'exemple", "convertError": "Erreur de conversion JSON. Vérifiez le format"}}, "footer": {"copyright": "ToolMi Construit avec amour."}, "about": {"title": "À propos de ce site", "seo_title": "À propos de ce site - ToolMi", "seo_description": "Informations détaillées du site web ToolMi, pile technologique et caractéristiques fonctionnelles", "seo_keywords": "à propos,<PERSON><PERSON><PERSON><PERSON>,outils en ligne,Nuxt3,pile technologique", "aboutSite": {"title": "À propos de ToolMi", "description1": "ToolMi est un site web spécialisé dans la fourniture d'outils en ligne de haute qualité. Nous nous consacrons à fournir des services d'outils en ligne pratiques et efficaces pour les développeurs, designers et utilisateurs généraux.", "description2": "Notre objectif est de devenir des 'outils d'efficacité en ligne de nouvelle génération'. Grâce à une pile technologique moderne et une conception d'interface conviviale, nous permettons à chacun d'utiliser facilement divers outils pratiques."}, "categories": {"title": "Catégories d'outils", "toolsCount": "outils", "viewTools": "Voir les outils"}, "contact": {"title": "Contact", "description": "Si vous avez des suggestions, des questions ou des intentions de coopération, n'hésitez pas à nous contacter par les méthodes suivantes :", "feedback": "Suggestions et commentaires", "website": "Site web"}}}