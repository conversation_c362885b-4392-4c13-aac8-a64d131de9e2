{"it": "ИТ", "code": "<PERSON>од", "format": "Формат", "generator": "Гене<PERSON><PERSON><PERSON><PERSON>р", "json": "JSON", "calc": "Калькулятор", "text": "Текст", "tool": "Инструмент", "common": {"appName": "ToolMi", "slogan": "Онлайн-инструменты эффективности нового поколения", "input": "Ввод", "output": "Вывод", "copy": "Копировать", "download": "Скачать", "clear": "Очистить", "example": "Загрузить пример", "search": "Поиск", "searchPlaceholder": "Введите ключевые слова, сейчас {count} инструментов", "about": "О сайте", "feedback": "Обратная связь", "category": "Категория", "latest": "Последние", "tools": "инстру<PERSON><PERSON><PERSON>тов", "inputRequired": "Пожалуйста, введите содержимое перед продолжением", "copySuccess": "Успешно скопировано", "copyFailed": "Ошибка копирования", "languageSwitch": "Сменить язык", "useTool": "Использовать инструмент", "backToCategoryList": "Вернуться к списку категорий"}, "nav": {"home": "Главная", "encrypt": "Шифрование", "format": "Формат", "generator": "Гене<PERSON><PERSON><PERSON><PERSON>р", "json": "JSON", "calc": "Калькулятор", "text": "Текст", "table": "Таблица"}, "categories": {"encrypt": "Шифрование", "format": "Формат", "generator": "Гене<PERSON><PERSON><PERSON><PERSON>р", "json": "JSON инструменты", "calc": "Калькулятор", "text": "Обработка текста", "table": "Инструменты таблиц", "iframe": "Он<PERSON><PERSON><PERSON>н инструменты", "math": "Математические инструменты"}, "tools": {"base64": {"title": "Кодирование и декодирование Base64", "description": "Инструмент кодирования и декодирования Base64, совместимый с кодированием и декодированием Base64 текста и файлов", "encode": "Кодировать Base64", "decode": "Декодировать Base64", "seo_title": "Инструмент кодирования и декодирования Base64 - Онлайн конвертация двоичного текста | ToolMi", "seo_description": "Онлайн инструмент кодирования и декодирования Base64, совместимый с кодированием и декодированием Base64 текста, URL, JSON и других различных форматов. Полностью совместим с русскими символами, предоставляет функцию загрузки файлов. Применим к передаче данных, вложениям электронной почты, API интерфейсам и другим сценариям. Бесплатное онлайн использование, установка не требуется.", "seo_keywords": "кодирование Base64,декодирование Base64,он<PERSON><PERSON><PERSON><PERSON> кодирование,конвертация текста,двоичное кодирование,кодировани<PERSON> URL,кодирование JSON,передача данных,вложения email,API интерфейс,онлайн инструменты", "inputPlaceholder": "Введите текст или URL для кодирования/декодирования", "decodeError": "Ошибка декодирования Base64, проверьте формат ввода"}, "md5": {"title": "Шифрование MD5", "description": "Инструмент хеш-шифрования MD5", "encrypt": "Шифровать MD5", "seo_title": "Инструмент шифрования MD5 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> вычисление хеша текста | ToolMi", "seo_description": "Онлайн инструмент хеш-шифрования MD5, быстро конвертирует текст в значения хеша MD5. Совместим с русскими символами, применим к проверке целостности данных, генерации ключей кеша, дедупликации данных и другим сценариям. Предоставляет подробные технические объяснения и рекомендации по безопасности. Бесплатное онлайн использование, установка не требуется.", "seo_keywords": "шифрование MD5,хеш MD5,вычисление хеша,целостность данных,хеш пароля,ключ кеша,дедупликация данных,он<PERSON><PERSON><PERSON><PERSON> шифрование,хеш текста,инструмент MD5"}, "sha1": {"title": "Шифрование SHA1", "description": "Инструмент хеш-шифрования SHA1, конвертирует текст в значения хеша SHA1", "encrypt": "Шифровать SHA1", "seo_title": "Инструмент хеш-шифрования SHA1 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> вычисление хеша SHA1 | ToolMi", "seo_description": "Онлайн инструмент хеш-шифрования SHA1, быстро конвертирует текст в значения хеша SHA1. Совместим с русскими символами, применим к проверке целостности данных, контролю версий и другим сценариям. Предоставляет подробные объяснения безопасности и предложения альтернатив.", "seo_keywords": "шифрование SHA1,хеш SHA1,вычисление хеша,целостность данных,контроль версий,Git,проверка файлов,онлайн шифрование,инструмент SHA1"}, "urlencode": {"title": "Кодирование и декодирование URL", "description": "Инструмент кодирования и декодирования URL, обрабатывает специальные символы в URL", "encode": "Кодировать URL", "decode": "Декодировать URL", "seo_title": "Инструмент кодирования и декодирования URL - Онлайн конвертация процентного кодирования | ToolMi", "seo_description": "Онлайн инструмент кодирования и декодирования URL, совместимый с конвертацией процентного кодирования русских символов и специальных символов. Применим к веб-разработке, API вызовам, отправке форм и другим сценариям. Быстро обрабатывает проблемы кодирования URL параметров, гарантирует точную передачу данных.", "seo_keywords": "кодирование URL,декодирование URL,процентное кодирование,encodeURIComponent,веб-разработка,API вызовы,отправка форм,специальные символы,русское кодирование", "inputPlaceholder": "Введите URL или текст для кодирования/декодирования", "decodeError": "Ошибка декодирования URL. Проверьте формат ввода"}, "jwtParse": {"title": "Анализ JWT", "description": "Инструмент анализа JWT Token, анализ и проверка JSON Web Token", "decode": "Анализировать JWT", "seo_title": "Инструмент анализа JWT - Онлайн декодер JSON Web Token | ToolMi", "seo_description": "Онлайн инструмент анализа JWT, быстро анализирует и проверяет JSON Web Token. Совместим с анализом Header, Payload, Signature, предоставляет подробные объяснения структуры JWT и рекомендации по безопасности. Применим к отладке разработки и проверке Token.", "seo_keywords": "анал<PERSON>з JW<PERSON>,JSO<PERSON>,декодирова<PERSON><PERSON><PERSON>,проверка JWT,ана<PERSON><PERSON><PERSON> Header,анализ Payload,отладка JWT,анализ <PERSON>", "inputPlaceholder": "Введите JWT Token", "copyResult": "Копировать результат", "header": "Header", "payload": "Payload", "signature": "Signature", "parseError": "Ошибка анализа JWT. Проверьте формат Token", "invalidFormat": "Неверный формат JWT"}, "json2yaml": {"title": "Конвертация JSON→YAML", "description": "Инструмент взаимной конвертации между форматами JSON и YAML", "toYaml": "Конвертировать в YAML", "toJson": "Конвертировать в JSON", "seo_title": "Инструмент JSON→YAML - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> конвертер формата JSON YAML | ToolMi", "seo_description": "Онлайн инструмент конвертации JSON→YAML, совместимый с двунаправленной конвертацией между форматами JSON и YAML. Полностью совместим с русскими символами, предоставляет валидацию формата и указание ошибок. Применим к конвертации конфигурационных файлов, созданию API документации, конвертации формата данных и другим сценариям.", "seo_keywords": "JSON→YAML,YAML→JSON,конвертация формата,конфигурационный файл,API документация,конвертация данных,о<PERSON><PERSON><PERSON><PERSON><PERSON> конвертация,JSON инструмент,YAML инструмент", "inputPlaceholder": "Введите содержимое JSON или YAML", "convertError": "Ошибка конвертации. Проверьте формат ввода"}, "sql": {"title": "Формат SQL", "description": "Инструмент форматирования и украшения SQL операторов", "format": "Форматировать", "minify": "Сжать", "seo_title": "Инструмент формата SQL - Он<PERSON><PERSON>йн украшение и сжатие SQL операторов | ToolMi", "seo_description": "Онлайн инструмент формата SQL, совместимый с форматированием, украшением и сжатием SQL операторов. Предоставляет подсветку синтаксиса, обнаружение ошибок, совместим с множественными диалектами баз данных. Применим к разработке баз данных, отладке SQL, обзору кода и другим сценариям.", "seo_keywords": "формат <PERSON>,украш<PERSON><PERSON><PERSON><PERSON> SQL,сжа<PERSON><PERSON><PERSON> SQL,база данных,SQL оператор,формат кода,SQL инструмент,разработка баз данных", "inputLabel": "Ввод SQL", "outputLabel": "Форматированный вывод", "inputPlaceholder": "Введите SQL оператор", "formatError": "Ошибка форматирования SQL. Проверьте синтаксис"}, "qr": {"title": "Генератор QR кодов", "description": "Онлайн инструмент генерации QR кодов, совместимый с текстом, URL и другим содержимым", "generate": "Генерировать QR код", "seo_title": "Генератор QR кодов - Онлайн инструмент создания QR Code | ToolMi", "seo_description": "Онлайн генератор QR кодов, генерирует QR коды с содержимым как текст, URL, контактная информация, пароли WiFi. Совместим с высоким разрешением вывода, загрузкой и сохранением. Применим к маркетинговому продвижению, идентификации продуктов, обмену информацией и другим сценариям.", "seo_keywords": "генератор QR кодов,создание QR кодов,онлайн QR код,инструмент QR кодов,маркетинговое продвижение,идентификация продуктов,обмен информацией,мобильный платеж,контакт", "inputLabel": "Ввод содержимого", "inputPlaceholder": "Введите содержимое для генерации QR кода (текст, URL и т.д.)", "download": "Скачать QR код", "resultTitle": "Сгенерированный QR код", "generateError": "Ошибка генерации QR кода"}, "uuid": {"title": "Генер<PERSON><PERSON><PERSON><PERSON> UUID", "description": "Инструмент генерации уникальных идентификаторов UUID", "generate": "Генерировать UUID", "seo_title": "Генера<PERSON>о<PERSON> UUID - Онлайн инструмент генерации уникальных идентификаторов | ToolMi", "seo_description": "Онлайн генерато<PERSON> UUID, быстро генерирует уникальные идентификаторы. Совместим с пакетной генерацией, копированием одним кликом, применим к первичным ключам базы данных, API идентификации, именованию файлов и другим сценариям. Соответствует стандарту RFC 4122, гарантирует глобальную уникальность.", "seo_keywords": "генер<PERSON><PERSON><PERSON><PERSON>,уникальный идентификатор,генерация GUID,первичный ключ базы данных,API идентификация,именование файлов,RFC4122,глобальная уникальность", "countLabel": "Количество для генерации", "copyAll": "Копировать все", "clear": "Очистить", "resultTitle": "Сгенерированные UUID", "countSuffix": "единиц"}, "numberToZh": {"title": "Конвертация чисел в китайский", "description": "Конвертирует арабские цифры в китайские числа, совместимо с упрощенным и традиционным китайским", "convert": "Конвертировать", "seo_title": "Инструмент китайских чисел - Он<PERSON><PERSON>й<PERSON> конвертер арабских цифр в китайские | ToolMi", "seo_description": "Онлайн инструмент китайских чисел, совместимый с конвертацией арабских цифр в упрощенный китайский, традиционный китайский и суммы заглавными буквами. Применим к финансовым отчетам, контрактам, выставлению счетов и другим сценариям. Совместим с конвертацией целых чисел и десятичных дробей.", "seo_keywords": "китайские числа,конвертация арабских цифр,китайские цифры,суммы заглавными буквами,финансовые отчеты,контракты,выставление счетов", "inputLabel": "Ввод чисел", "inputPlaceholder": "Введите числа, например: 12345", "typeLabel": "Тип конвертации", "simple": "Упрощенный китайский", "traditional": "Традиционный китайский", "financial": "Суммы заглавными буквами", "convertError": "Ошибка конвертации чисел. Проверьте формат ввода"}, "netscapeCookies": {"title": "Конвертация формата Netscape Cookies", "description": "Инструмент взаимной конвертации между форматом Netscape Cookies и форматом JSON", "convert": "Конвертировать", "seo_title": "Инструмент конвертации формата Netscape Cookies - Онлайн конвертер формата Cookies | ToolMi", "seo_description": "Онлайн инструмент конвертации формата Netscape Cookies, совместимый с двунаправленной конвертацией между форматом Netscape Cookies и форматом JSON. Применим к миграции данных браузера, разработке краулеров, веб-тестированию и другим сценариям.", "seo_keywords": "Netscape Cookies,конвертация формата Cookies,данные браузера,разработка краулеров,веб-тестирование,JSON конвертация", "inputFormatLabel": "Формат ввода", "netscapeFormat": "Формат Netscape", "jsonFormat": "Формат JSON", "inputLabel": "Ввод содержимого", "resultLabel": "Результат конвертации", "clear": "Очистить", "convertError": "Ошибка конвертации формата. Проверьте формат ввода"}, "yaml": {"title": "Формат YAML", "description": "Инструмент форматирования, валидации и украшения YAML", "format": "Форматировать", "validate": "Валид<PERSON><PERSON><PERSON>а<PERSON>ь", "seo_title": "Инструмент формата YAML - Онл<PERSON>йн валидатор и украшатель YAML | ToolMi", "seo_description": "Онлайн инструмент формата YAML, совместимый с валидацией, украшением и форматированием YAML. Предоставляет проверку синтаксиса, указание ошибок, применим к созданию конфигурационных файлов, DevOps развертыванию, API документации и другим сценариям.", "seo_keywords": "формат YAML,валидация YAML,украшение YAML,конфигурационный файл,DevOps,API документация,проверка синтаксиса", "inputLabel": "Ввод YAML", "outputLabel": "Форматированный вывод", "inputPlaceholder": "Введите содержимое YAML", "clear": "Очистить", "validStatus": "✓ Формат YAML корректен", "invalidStatus": "✗ Есть ошибки в формате YAML", "formatError": "Ошибка форматирования YAML. Проверьте синтаксис"}, "reverseString": {"title": "Обращение строк", "description": "Инструмент обращения строк и обработки текста", "reverse": "Обратить", "seo_title": "Инструмент обращения строк - Онлайн процессор обращения текста | ToolMi", "seo_description": "Онлайн инструмент обращения строк, совместимый с обращением текста по символам, словам и строкам. Применим к обработке текста, генерации паролей, анализу данных и другим сценариям. Предоставляет множественные режимы обращения, простая и быстрая операция.", "seo_keywords": "обращение строк,обращение текста,обработка текста,генерация паролей,анализ данных,онлайн инструменты", "inputLabel": "Ввод текста", "inputPlaceholder": "Введите текст для обращения", "optionsLabel": "Опции обращения", "byCharacters": "Обратить по символам", "byWords": "Обратить по словам", "byLines": "Обратить по строкам", "resultLabel": "Результат обращения", "clear": "Очистить", "reverseError": "Ошибка обращения текста"}, "jsonToPhpArrayCode": {"title": "JSON → Код массива PHP", "description": "Конвертирует JSON в код массива PHP, может быть скопирован напрямую и вставлен в PHP код", "convert": "Конвертировать", "seo_title": "JSON → Код массива PHP - Онл<PERSON>йн инструмент конвертации | ToolMi", "seo_description": "Онлайн инструмент JSON → Код массива PHP, совместимый с вложенными структурами, русскими символами, экранированием специальных символов. Конвертирует одним кликом JSON данные в стандартный формат кода массива PHP, применим к конфигурационным файлам, Mock API данным, seed файлам базы данных и другим сценариям.", "seo_keywords": "JSON→PHP,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PHP,JSON конвертация,генерация PHP кода,онлайн инструменты,конфигурационный файл,<PERSON><PERSON>,конвертация данных,PHP инструменты разработки,JSON анализ", "inputLabel": "Ввод JSON", "inputPlaceholder": "Введите JSON данные", "outputLabel": "Код массива PHP", "copyResult": "Копировать результат", "loadExample": "Загрузить пример", "convertError": "Ошибка конвертации JSON. Проверьте формат"}}, "footer": {"copyright": "ToolMi Создано с любовью."}, "about": {"title": "О сайте", "seo_title": "О сайте - ToolMi", "seo_description": "Подробная информация о сайте ToolMi, технологический стек и функциональные характеристики", "seo_keywords": "о сайте,<PERSON><PERSON><PERSON><PERSON>,он<PERSON><PERSON><PERSON>н инструменты,Nuxt3,технологический стек", "aboutSite": {"title": "О ToolMi", "description1": "ToolMi - это сайт, специализирующийся на предоставлении высококачественных онлайн инструментов. Мы посвящаем себя предоставлению практичных и эффективных онлайн инструментальных сервисов для разработчиков, дизайнеров и обычных пользователей.", "description2": "Наша цель - стать 'онлайн инструментами эффективности нового поколения'. Через современный технологический стек и дружественный дизайн интерфейса мы позволяем каждому легко использовать различные практичные инструменты."}, "categories": {"title": "Категории инструментов", "toolsCount": "инстру<PERSON><PERSON><PERSON>тов", "viewTools": "Посмотреть инструменты"}, "contact": {"title": "Кон<PERSON><PERSON><PERSON>т", "description": "Если у вас есть предложения, вопросы или намерения сотрудничества, не стесняйтесь связаться с нами следующими способами:", "feedback": "Предложения и обратная связь", "website": "Сайт"}}}