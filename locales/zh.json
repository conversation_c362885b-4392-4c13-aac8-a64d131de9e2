{"welcome": {"title": "欢迎使用 Nuxt i18n 演示", "subtitle": "这是一个国际化演示项目", "description": "本项目展示了如何在 Nuxt 3 中实现多语言支持"}, "navigation": {"home": "首页", "about": "关于", "contact": "联系我们"}, "common": {"language": "语言", "switchLanguage": "切换语言", "hello": "你好", "goodbye": "再见", "yes": "是", "no": "否", "save": "保存", "cancel": "取消", "submit": "提交", "loading": "加载中...", "error": "错误", "success": "成功"}, "pages": {"home": {"title": "首页", "content": "这是首页内容。您可以在这里看到中文界面。"}, "about": {"title": "关于我们", "content": "这是关于页面。我们致力于提供优质的国际化解决方案。", "team": "团队", "mission": "使命", "vision": "愿景"}, "contact": {"title": "联系我们", "form": {"name": "姓名", "email": "邮箱", "message": "消息", "send": "发送"}, "info": {"address": "地址", "phone": "电话", "email": "邮箱"}}}, "demo": {"features": {"title": "功能特性", "routing": "路由国际化", "seo": "SEO 优化", "lazy": "懒加载", "detection": "语言检测"}, "examples": {"pluralization": "复数形式", "interpolation": "插值", "formatting": "格式化"}}}