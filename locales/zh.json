{"it": "IT", "code": "编码", "format": "格式化", "generator": "生成器", "json": "JSON", "calc": "计算器", "text": "文本", "tool": "工具", "common": {"appName": "工具迷", "slogan": "下一代在线效率工具", "input": "输入", "output": "输出", "copy": "复制", "download": "下载", "clear": "清空", "example": "载入示例", "search": "搜索", "searchPlaceholder": "输入关键字, 当前共有{count}个工具", "about": "关于本站", "feedback": "反馈", "category": "分类", "latest": "最新", "tools": "个工具", "inputRequired": "请输入内容后再继续相关操作", "copySuccess": "复制成功", "copyFailed": "复制失败", "languageSwitch": "语言切换", "useTool": "使用工具", "backToCategoryList": "返回分类列表"}, "nav": {"home": "首页", "encrypt": "编码", "format": "格式化", "generator": "生成器", "json": "JSON", "calc": "计算器", "text": "文本", "table": "表格"}, "categories": {"encrypt": "编码", "format": "格式化", "generator": "生成器", "json": "JSON工具", "calc": "计算器", "text": "文本处理", "table": "表格工具", "iframe": "在线工具", "math": "数学工具"}, "tools": {"base64": {"title": "Base64 编码解码", "description": "Base64 编码解码工具，支持文本和文件的Base64编码和解码", "encode": "Base64编码", "decode": "Base64解码", "seo_title": "Base64 编码解码工具 - 在线文本二进制转换 | 工具迷", "seo_description": "Base64 在线编码解码工具，支持文本、URL、JSON 等多种格式的 Base64 编码和解码。完美支持中文字符，提供文件下载功能。适用于数据传输、邮件附件、API 接口等场景。免费在线使用，无需安装。", "seo_keywords": "Base64编码,Base64解码,在线编码,文本转换,二进制编码,URL编码,JSON编码,数据传输,邮件附件,API接口,在线工具"}, "md5": {"title": "MD5 加密", "description": "MD5 哈希加密工具", "encrypt": "MD5加密", "seo_title": "MD5 加密工具 - 在线文本哈希计算 | 工具迷", "seo_description": "MD5 在线哈希加密工具，快速将文本转换为 MD5 哈希值。支持中文字符，适用于数据完整性验证、缓存键生成、数据去重等场景。提供详细的技术说明和安全建议。免费在线使用，无需安装。", "seo_keywords": "MD5加密,MD5哈希,哈希计算,数据完整性,密码哈希,缓存键,数据去重,在线加密,文本哈希,MD5工具"}, "sha1": {"title": "SHA1 加密", "description": "SHA1 哈希加密工具，将文本转换为SHA1哈希值", "encrypt": "SHA1加密", "seo_title": "SHA1 哈希加密工具 - 在线 SHA1 散列计算 | 工具迷", "seo_description": "SHA1 在线哈希加密工具，快速将文本转换为 SHA1 散列值。支持中文字符，适用于数据完整性验证、版本控制等场景。提供详细的安全说明和替代方案建议。", "seo_keywords": "SHA1加密,SHA1哈希,散列计算,数据完整性,版本控制,<PERSON><PERSON>,文件校验,在线加密,SHA1工具"}, "urlencode": {"title": "URL 编码解码", "description": "URL 编码解码工具，处理URL中的特殊字符", "encode": "URL编码", "decode": "URL解码", "seo_title": "URL 编码解码工具 - 在线百分号编码转换 | 工具迷", "seo_description": "URL 编码解码在线工具，支持中文字符、特殊符号的百分号编码转换。适用于 Web 开发、API 调用、表单提交等场景。快速处理 URL 参数编码问题，确保数据正确传输。", "seo_keywords": "URL编码,URL解码,百分号编码,encodeURIComponent,Web开发,API调用,表单提交,特殊字符,中文编码", "inputPlaceholder": "请输入要编码/解码的URL或文本", "decodeError": "URL解码失败，请检查输入格式"}, "jwtParse": {"title": "JWT 解析", "description": "JWT Token 解析工具，解析和验证JSON Web Token", "decode": "解析JWT", "seo_title": "JWT 解析工具 - 在线 JSON Web Token 解码器 | 工具迷", "seo_description": "JWT 在线解析工具，快速解析和验证 JSON Web Token。支持 Header、Payload、Signature 分析，提供详细的 JWT 结构说明和安全建议。适用于开发调试和 Token 验证。", "seo_keywords": "JWT解析,JSON <PERSON> Token,Token解码,JWT验证,Header解析,Payload解析,JWT调试,Token分析", "inputPlaceholder": "请输入JWT Token", "copyResult": "复制结果", "header": "Header", "payload": "Payload", "signature": "Signature", "parseError": "JWT解析失败，请检查Token格式", "invalidFormat": "无效的JWT格式"}, "json2yaml": {"title": "JSON转YAML", "description": "JSON 和 YAML 格式互转工具", "toYaml": "转为YAML", "toJson": "转为JSON", "seo_title": "JSON转YAML工具 - 在线JSON YAML格式转换器 | 工具迷", "seo_description": "JSON转YAML在线转换工具，支持JSON和YAML格式双向转换。完美支持中文字符，提供格式验证和错误提示。适用于配置文件转换、API文档编写、数据格式转换等场景。", "seo_keywords": "JSON转YAML,YAML转JSON,格式转换,配置文件,API文档,数据转换,在线转换,JSON工具,YAML工具", "inputPlaceholder": "请输入JSON或YAML内容", "convertError": "转换失败，请检查输入格式"}, "sql": {"title": "SQL 格式化", "description": "SQL 语句格式化和美化工具", "format": "格式化"}, "qr": {"title": "二维码生成器", "description": "在线二维码生成工具", "generate": "生成二维码"}, "uuid": {"title": "UUID 生成器", "description": "UUID 唯一标识符生成工具", "generate": "生成UUID"}, "numberToZh": {"title": "数字转中文", "description": "阿拉伯数字转换为中文数字格式", "convert": "数字转中文"}, "netscapeCookies": {"title": "Netscape Cookies", "description": "Netscape Cookies格式转换工具", "convert": "转换"}, "yaml": {"title": "YAML 格式化", "description": "YAML 格式化和验证工具", "format": "格式化", "validate": "验证"}, "reverseString": {"title": "字符串反转", "description": "字符串反转和文本处理工具", "reverse": "字符串反转"}, "jsonToPhpArrayCode": {"title": "JSON 转 PHP 数组代码", "description": "将 JSON 转换为 PHP 的数组代码，直接复制可以粘贴到 PHP 代码中", "convert": "转换"}}, "footer": {"copyright": "工具迷 Build with love。"}}