{"it": "IT", "code": "编码", "format": "格式化", "generator": "生成器", "json": "JSON", "calc": "计算器", "text": "文本", "tool": "工具", "common": {"appName": "工具迷", "slogan": "下一代在线效率工具", "input": "输入", "output": "输出", "copy": "复制", "download": "下载", "clear": "清空", "example": "载入示例", "search": "搜索", "searchPlaceholder": "输入关键字, 当前共有{count}个工具", "about": "关于本站", "feedback": "反馈", "category": "分类", "latest": "最新", "tools": "个工具", "inputRequired": "请输入内容后再继续相关操作", "copySuccess": "复制成功", "copyFailed": "复制失败", "languageSwitch": "语言切换", "useTool": "使用工具", "backToCategoryList": "返回分类列表"}, "nav": {"home": "首页", "encrypt": "编码", "format": "格式化", "generator": "生成器", "json": "JSON", "calc": "计算器", "text": "文本", "table": "表格"}, "categories": {"encrypt": "编码", "format": "格式化", "generator": "生成器", "json": "JSON工具", "calc": "计算器", "text": "文本处理", "table": "表格工具", "iframe": "在线工具", "math": "数学工具"}, "tools": {"base64": {"title": "Base64 编码解码", "description": "Base64 编码解码工具，支持文本和文件的Base64编码和解码", "encode": "Base64编码", "decode": "Base64解码"}, "md5": {"title": "MD5 加密", "description": "MD5 哈希加密工具", "encrypt": "MD5加密", "seo_title": "MD5 加密工具 - 在线文本哈希计算 | 工具迷", "seo_description": "MD5 在线哈希加密工具，快速将文本转换为 MD5 哈希值。支持中文字符，适用于数据完整性验证、缓存键生成、数据去重等场景。提供详细的技术说明和安全建议。免费在线使用，无需安装。", "seo_keywords": "MD5加密,MD5哈希,哈希计算,数据完整性,密码哈希,缓存键,数据去重,在线加密,文本哈希,MD5工具"}, "sha1": {"title": "SHA1 加密", "description": "SHA1 哈希加密工具", "encrypt": "SHA1加密"}, "urlencode": {"title": "URL 编码解码", "description": "URL 编码解码工具", "encode": "URL编码", "decode": "URL解码"}, "jwtParse": {"title": "JWT 解析", "description": "JWT Token 解析工具", "decode": "JWT解析"}, "json2yaml": {"title": "JSON转YAML", "description": "JSON 和 YAML 格式互转工具", "toYaml": "转为YAML", "toJson": "转为JSON"}, "sql": {"title": "SQL 格式化", "description": "SQL 语句格式化和美化工具", "format": "格式化"}, "qr": {"title": "二维码生成器", "description": "在线二维码生成工具", "generate": "生成二维码"}, "uuid": {"title": "UUID 生成器", "description": "UUID 唯一标识符生成工具", "generate": "生成UUID"}, "numberToZh": {"title": "数字转中文", "description": "阿拉伯数字转换为中文数字格式", "convert": "数字转中文"}, "netscapeCookies": {"title": "Netscape Cookies", "description": "Netscape Cookies格式转换工具", "convert": "转换"}, "yaml": {"title": "YAML 格式化", "description": "YAML 格式化和验证工具", "format": "格式化", "validate": "验证"}, "reverseString": {"title": "字符串反转", "description": "字符串反转和文本处理工具", "reverse": "字符串反转"}, "jsonToPhpArrayCode": {"title": "JSON 转 PHP 数组代码", "description": "将 JSON 转换为 PHP 的数组代码，直接复制可以粘贴到 PHP 代码中", "convert": "转换"}}, "footer": {"copyright": "工具迷 Build with love。"}}