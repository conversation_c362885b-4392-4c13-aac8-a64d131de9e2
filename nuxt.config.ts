// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  modules: [
    '@nuxtjs/i18n',
    '@pinia/nuxt'
  ],

  // CSS 配置
  css: [
    '~/assets/styles/globals.scss',
    'highlight.js/styles/github-dark.css'
  ],
  
  
  build: {
    transpile: ['@fortawesome/vue-fontawesome']
  },

  ssr: true,

  i18n: {
    locales: [
      {
        code: 'en',
        name: 'English',
        file: 'en.json'
      },
      {
        code: 'zh',
        name: '中文',
        file: 'zh.json'
      }
    ],
    defaultLocale: 'zh',
    langDir: '../locales',
    strategy: 'prefix',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  },

  nitro: {
    // 禁用预渲染，使用纯SSR
    prerender: {
      routes: []
    }
  },
})
