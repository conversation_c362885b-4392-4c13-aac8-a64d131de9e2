$widthLayoutMax: 1024px;
$mainColor: #00b96b;

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 64px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: saturate(180%) blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  .nav-link {
    height: 100%;
    max-width: $widthLayoutMax;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 32px;
  }

  .logo {
    color: #000;
    font-weight: bold;
    font-size: 24px;
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: $mainColor;
    }
  }

  a {
    color: #333;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      color: $mainColor;
      background: rgba(0, 185, 107, 0.1);
    }
  }

  .language-switcher {
    margin-left: auto;

    select {
      padding: 6px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      background: white;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;

      &:hover {
        border-color: $mainColor;
      }

      &:focus {
        outline: none;
        border-color: $mainColor;
        box-shadow: 0 0 0 2px rgba(0, 185, 107, 0.2);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    .nav-link {
      padding: 0 16px;
      gap: 16px;
    }

    .logo {
      font-size: 20px;
    }

    a {
      font-size: 14px;
      padding: 6px 12px;
    }

    .language-switcher select {
      padding: 4px 8px;
      font-size: 12px;
    }
  }
}

.content-wrapper {
  max-width: $widthLayoutMax;
  margin: 80px auto 0;
  width: 100%;
  min-height: calc(100vh - 80px);
  padding: 0 20px;

  .markdown-body {
    margin-top: 10px;
  }
}

// 响应式内容区域
@media (max-width: 768px) {
  .content-wrapper {
    padding: 0 16px;
  }
}

.index {
  h1 {
    text-align: center;
    font-size: 34px;
    margin-top: 20px;
  }

  .slogan {
    text-align: center;
    color: #7e7e7e;
  }

  .search-input {
    margin: 30px 0;
  }

  .category-list {
    .category-item {
      padding: 40px 0px;
      border-radius: 4px;
      text-align: center;
      box-shadow: 0 0 0 1px rgb(0 0 0 / 7%), 0 2px 4px rgb(0 0 0 / 5%),
        0 12px 24px rgb(0 0 0 / 5%);
      transition: opacity 0.3s ease;
      color: linear-gradient(180deg, #555, #000);
      font-size: 20px;

      &:hover {
        background: radial-gradient(
          circle at 811.59px 102.194px,
          rgb(192 255 1 / 33%),
          rgba(0, 0, 0, 0.0588235294)
        );
        cursor: pointer;
        color: $mainColor;
      }

      .name {
      }

      .desc {
        font-size: 14px;
        margin-top: 10px;
        color: #aaa;

        &:hover {
          color: $mainColor;
        }
      }
    }
  }
}

// 按钮样式系统
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  white-space: nowrap;
  user-select: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 185, 107, 0.2);
  }

  // 主要按钮
  &.btn-primary {
    background: $mainColor;
    color: white;
    border-color: $mainColor;

    &:hover {
      background: #009456;
      border-color: #009456;
    }

    &:active {
      background: #007a47;
      border-color: #007a47;
    }
  }

  // 次要按钮
  &.btn-secondary {
    background: #f0f0f0;
    color: #333;
    border-color: #ccc;

    &:hover {
      background: #e0e0e0;
      border-color: #999;
    }

    &:active {
      background: #d0d0d0;
      border-color: #888;
    }
  }

  // 信息按钮
  &.btn-info {
    background: #007bff;
    color: white;
    border-color: #007bff;

    &:hover {
      background: #0056b3;
      border-color: #0056b3;
    }

    &:active {
      background: #004085;
      border-color: #004085;
    }
  }

  // 成功按钮
  &.btn-success {
    background: #28a745;
    color: white;
    border-color: #28a745;

    &:hover {
      background: #218838;
      border-color: #218838;
    }

    &:active {
      background: #1e7e34;
      border-color: #1e7e34;
    }
  }

  // 警告按钮
  &.btn-warning {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;

    &:hover {
      background: #e0a800;
      border-color: #e0a800;
    }

    &:active {
      background: #d39e00;
      border-color: #d39e00;
    }
  }

  // 危险按钮
  &.btn-danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;

    &:hover {
      background: #c82333;
      border-color: #c82333;
    }

    &:active {
      background: #bd2130;
      border-color: #bd2130;
    }
  }

  // 轮廓按钮
  &.btn-outline {
    background: transparent;
    color: $mainColor;
    border-color: $mainColor;

    &:hover {
      background: $mainColor;
      color: white;
    }

    &:active {
      background: #009456;
      border-color: #009456;
    }
  }

  // 文本按钮
  &.btn-text {
    background: transparent;
    color: $mainColor;
    border-color: transparent;
    padding: 4px 8px;

    &:hover {
      background: rgba(0, 185, 107, 0.1);
    }

    &:active {
      background: rgba(0, 185, 107, 0.2);
    }
  }

  // 小尺寸按钮
  &.btn-sm {
    padding: 4px 12px;
    font-size: 12px;
  }

  // 大尺寸按钮
  &.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
  }

  // 块级按钮
  &.btn-block {
    width: 100%;
    display: flex;
  }
}

.btn-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin: 16px 0;

  .btn {
    margin: 0;
  }

  // 响应式按钮组
  @media (max-width: 768px) {
    gap: 6px;

    .btn {
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}

.detail {
  .related-category {
    margin-bottom: 10px;
  }
}

.iframe {
  width: 100%;
  min-height: 800px;
}

.simple-textarea {
  .form-item {
    margin-bottom: 16px;
  }
}