{"welcome": {"title": "Welcome to Nuxt i18n Demo", "subtitle": "This is an internationalization demo project", "description": "This project demonstrates how to implement multi-language support in Nuxt 3"}, "navigation": {"home": "Home", "about": "About", "contact": "Contact"}, "common": {"language": "Language", "switchLanguage": "Switch Language", "hello": "Hello", "goodbye": "Goodbye", "yes": "Yes", "no": "No", "save": "Save", "cancel": "Cancel", "submit": "Submit", "loading": "Loading...", "error": "Error", "success": "Success"}, "pages": {"home": {"title": "Home", "content": "This is the home page content. You can see the English interface here."}, "about": {"title": "About Us", "content": "This is the about page. We are committed to providing quality internationalization solutions.", "team": "Team", "mission": "Mission", "vision": "Vision"}, "contact": {"title": "Contact Us", "form": {"name": "Name", "email": "Email", "message": "Message", "send": "Send"}, "info": {"address": "Address", "phone": "Phone", "email": "Email"}}}, "demo": {"features": {"title": "Features", "routing": "Routing Internationalization", "seo": "SEO Optimization", "lazy": "Lazy Loading", "detection": "Language Detection"}, "examples": {"pluralization": "Pluralization", "interpolation": "Interpolation", "formatting": "Formatting"}}}