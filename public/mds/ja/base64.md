# Base64 エンコード・デコードツール

Base64は、バイナリデータをテキスト形式で表現するためのエンコード方式です。主にメール送信、Web開発、データ保存などの場面で、バイナリデータを安全にテキストとして転送・保存するために使用されます。

## ✨ 主な機能

- 🔄 **双方向変換**：テキストのBase64エンコードとデコードをサポート
- 🌐 **多言語対応**：日本語、中国語、英語などのUnicode文字をサポート
- 📁 **ファイル処理**：画像、文書などのファイルのBase64変換
- 📋 **ワンクリックコピー**：変換結果を直接コピーして使用可能
- ⚡ **リアルタイム変換**：入力と同時に結果を表示

## 📖 使用例

### テキストエンコード

**入力テキスト：**
```
こんにちは、ツールミ！
```

**Base64エンコード結果：**
```
44GT44KT44Gr44Gh44Gv44CB44OE44O844Or44Of77yB
```

### URLエンコード

**入力URL：**
```
https://www.toolmi.com/search?q=Base64ツール
```

**Base64エンコード結果：**
```
aHR0cHM6Ly93d3cudG9vbG1pLmNvbS9zZWFyY2g/cT1CYXNlNjTjg4Tjg7zjg6s=
```

### JSONデータエンコード

**入力JSON：**
```json
{
  "name": "田中太郎",
  "email": "<EMAIL>",
  "age": 30
}
```

**Base64エンコード結果：**
```
ewogICJuYW1lIjogIueUsOS4reWkquiOlyIsCiAgImVtYWlsIjogInRhbmFrYUBleGFtcGxlLmNvbSIsCiAgImFnZSI6IDMwCn0=
```

## 🎯 応用シーン

### 1. Web開発

Web開発でのBase64活用例：

```html
<!-- 画像のBase64埋め込み -->
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA6VP8EQAAAABJRU5ErkJggg==" alt="1x1透明画像">

<!-- CSSでのBase64フォント埋め込み -->
@font-face {
  font-family: 'CustomFont';
  src: url('data:font/woff2;base64,d09GMgABAAAAAA...') format('woff2');
}

<!-- JavaScriptでのBase64データ処理 -->
<script>
// Base64エンコード
const encoded = btoa('こんにちは');
console.log(encoded); // 44GT44KT44Gr44Gh44Gv

// Base64デコード
const decoded = atob(encoded);
console.log(decoded); // こんにちは
</script>
```

### 2. API開発

API通信でのBase64使用例：

```javascript
// ファイルアップロードAPI
const uploadFile = async (file) => {
  const reader = new FileReader();
  
  return new Promise((resolve, reject) => {
    reader.onload = async () => {
      const base64Data = reader.result.split(',')[1];
      
      try {
        const response = await fetch('/api/upload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            filename: file.name,
            data: base64Data,
            mimeType: file.type
          })
        });
        
        const result = await response.json();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

// 認証ヘッダーの生成
const createAuthHeader = (username, password) => {
  const credentials = `${username}:${password}`;
  const encoded = btoa(credentials);
  return `Basic ${encoded}`;
};

// 使用例
const authHeader = createAuthHeader('<EMAIL>', 'password123');
console.log(authHeader); // Basic ****************************************
```

### 3. データ保存

ローカルストレージでのBase64活用：

```javascript
// 設定データの保存
const saveUserSettings = (settings) => {
  const settingsJson = JSON.stringify(settings);
  const encodedSettings = btoa(settingsJson);
  localStorage.setItem('userSettings', encodedSettings);
};

// 設定データの読み込み
const loadUserSettings = () => {
  const encodedSettings = localStorage.getItem('userSettings');
  if (encodedSettings) {
    try {
      const settingsJson = atob(encodedSettings);
      return JSON.parse(settingsJson);
    } catch (error) {
      console.error('設定の読み込みに失敗しました:', error);
      return null;
    }
  }
  return null;
};

// 使用例
const userSettings = {
  theme: 'dark',
  language: 'ja',
  notifications: true
};

saveUserSettings(userSettings);
const loadedSettings = loadUserSettings();
console.log(loadedSettings);
```

### 4. メール送信

メール添付ファイルのBase64エンコード：

```javascript
// Node.js でのメール送信例
const nodemailer = require('nodemailer');
const fs = require('fs');

const sendEmailWithAttachment = async (to, subject, text, filePath) => {
  // ファイルをBase64エンコード
  const fileContent = fs.readFileSync(filePath);
  const base64Content = fileContent.toString('base64');
  
  const transporter = nodemailer.createTransporter({
    service: 'gmail',
    auth: {
      user: '<EMAIL>',
      pass: 'your-password'
    }
  });

  const mailOptions = {
    from: '<EMAIL>',
    to: to,
    subject: subject,
    text: text,
    attachments: [
      {
        filename: 'document.pdf',
        content: base64Content,
        encoding: 'base64'
      }
    ]
  };

  try {
    const result = await transporter.sendMail(mailOptions);
    console.log('メール送信成功:', result.messageId);
    return result;
  } catch (error) {
    console.error('メール送信失敗:', error);
    throw error;
  }
};
```

## 🔧 技術詳細

### Base64エンコード原理

Base64エンコードの仕組み：

**文字セット：**
- A-Z（26文字）
- a-z（26文字）  
- 0-9（10文字）
- +、/（2文字）
- =（パディング文字）

**エンコード手順：**
1. 入力データを8ビットずつ読み取り
2. 3バイト（24ビット）を4つの6ビットグループに分割
3. 各6ビットを対応するBase64文字に変換
4. 必要に応じてパディング文字「=」を追加

### 文字エンコーディング

日本語文字の処理：

```javascript
// UTF-8エンコーディングでのBase64変換
function encodeUTF8ToBase64(str) {
  // 文字列をUTF-8バイト配列に変換
  const utf8Bytes = new TextEncoder().encode(str);
  
  // バイト配列を文字列に変換
  let binary = '';
  utf8Bytes.forEach(byte => {
    binary += String.fromCharCode(byte);
  });
  
  // Base64エンコード
  return btoa(binary);
}

function decodeBase64ToUTF8(base64) {
  // Base64デコード
  const binary = atob(base64);
  
  // 文字列をバイト配列に変換
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  
  // UTF-8デコード
  return new TextDecoder().decode(bytes);
}

// 使用例
const original = "こんにちは、世界！";
const encoded = encodeUTF8ToBase64(original);
const decoded = decodeBase64ToUTF8(encoded);

console.log('元の文字列:', original);
console.log('エンコード結果:', encoded);
console.log('デコード結果:', decoded);
```

## 💡 使用のコツ

- **文字エンコーディング**：日本語文字を扱う際はUTF-8エンコーディングに注意
- **データサイズ**：Base64エンコード後のサイズは元データの約1.33倍になる
- **パフォーマンス**：大きなファイルの処理時はメモリ使用量に注意
- **セキュリティ**：Base64は暗号化ではなく、データの難読化程度の効果

## ⚠️ 注意事項

- **セキュリティ**：Base64は暗号化ではないため、機密データの保護には不適切
- **サイズ増加**：エンコード後のデータサイズが約33%増加する
- **文字制限**：一部のシステムでは長いBase64文字列に制限がある場合がある
- **改行処理**：システムによってはBase64文字列の改行処理が異なる

## 🚀 使い方

1. **テキスト入力**：エンコード・デコードしたいテキストを入力ボックスに入力
2. **操作選択**：「エンコード」または「デコード」ボタンをクリック
3. **結果確認**：変換結果が出力エリアに表示される
4. **コピー使用**：「コピー」ボタンをクリックして結果をクリップボードにコピー
5. **ファイル処理**：ファイルをドラッグ&ドロップしてBase64変換

> **ヒント**：このツールはクライアント側でローカル処理を行い、データをサーバーにアップロードしないため、プライバシーとセキュリティが保証されます。
