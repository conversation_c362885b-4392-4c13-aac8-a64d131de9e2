# QRコード生成器

QRコード（Quick Response Code）は、1994年に日本のデンソーウェーブ社によって発明された二次元バーコードです。テキスト、URL、連絡先情報などの様々な情報を素早くQRコード画像に変換できるツールです。

## ✨ 主な機能

- 📱 **多コンテンツ対応**：テキスト、URL、連絡先、WiFiパスワードなどをサポート
- 🎨 **高画質出力**：高品質なQRコード画像を生成
- 📏 **サイズ調整**：複数のサイズ仕様をサポート
- 🎯 **エラー訂正レベル**：異なるエラー訂正レベル設定をサポート
- 💾 **ワンクリックダウンロード**：生成されたQRコードを直接ダウンロード保存
- 🔧 **リアルタイムプレビュー**：入力内容に応じて即座にQRコードプレビューを生成

## 📖 使用例

### URLのQRコード

**入力内容：**
```
https://www.toolmi.com
```

**生成効果：**
- スキャン後、ツールミウェブサイトに直接ジャンプ
- ウェブサイト推進、リンク共有に適用

### テキストQRコード

**入力内容：**
```
ツールミオンラインツールへようこそ！
ここには豊富な開発ツールと実用機能があります。
```

**生成効果：**
- スキャン後、完全なテキスト内容を表示
- 情報伝達、説明表示に適用

### 連絡先QRコード

**入力内容：**
```
BEGIN:VCARD
VERSION:3.0
FN:田中太郎
ORG:ツールミテクノロジー
TEL:+81-90-1234-5678
EMAIL:<EMAIL>
URL:https://www.toolmi.com
END:VCARD
```

**生成効果：**
- スキャン後、連絡先に直接追加可能
- 氏名、会社、電話、メールなどの情報を含む

## 🎯 応用シーン

### 1. ウェブサイト推進

ウェブサイトやアプリの推進QRコードを生成：

```html
<!-- 推進ポスターのQRコード -->
<div class="promotion">
  <h3>QRコードをスキャンしてツールミにアクセス</h3>
  <img src="qr-code.png" alt="ツールミQRコード">
  <p>より多くの実用ツールを発見</p>
</div>

<!-- 応用シーン -->
- 宣伝ポスター
- 名刺デザイン
- 商品パッケージ
- 広告投稿
```

### 2. モバイル決済

決済QRコードの生成：

```javascript
// PayPay決済リンク例
const paypayUrl = 'paypay://payment?merchantId=12345&amount=1000'

// LINE Pay決済リンク例
const linePayUrl = 'line://pay/payment?transactionId=abc123'

// 決済QRコード生成
generateQRCode(paypayUrl, {
  size: 200,
  errorCorrectionLevel: 'M'
})
```

### 3. WiFiパスワード共有

WiFi接続QRコードの生成：

```
WiFi QRコード形式：
WIFI:T:WPA;S:ネットワーク名;P:パスワード;H:false;;

例：
WIFI:T:WPA;S:ToolMi_5G;P:12345678;H:false;;

パラメータ説明：
- T: 暗号化タイプ (WPA/WEP/nopass)
- S: ネットワーク名 (SSID)
- P: パスワード
- H: ネットワークを隠すかどうか (true/false)
```

### 4. イベントチェックイン

イベントチェックインQRコードの生成：

```json
{
  "type": "event_checkin",
  "event_id": "tech_meetup_2024",
  "event_name": "技術交流会",
  "location": "東京国際会議センター",
  "date": "2024-06-15",
  "checkin_url": "https://event.toolmi.com/checkin/tech_meetup_2024"
}
```

### 5. 商品トレーサビリティ

商品トレーサビリティQRコードの生成：

```javascript
// 商品情報
const productInfo = {
  id: 'TM2024001',
  name: 'スマートツールボックス',
  batch: 'B20240615',
  production_date: '2024-06-15',
  manufacturer: 'ツールミテクノロジー',
  quality_check: 'PASS',
  trace_url: 'https://trace.toolmi.com/product/TM2024001'
}

// トレーサビリティQRコード生成
const traceData = JSON.stringify(productInfo)
generateQRCode(traceData)
```

## 🔧 技術詳解

### QRコード構造

QRコードの基本構造：

**機能パターン：**
- 位置検出パターン：三つの角の大きな四角形
- 位置検出パターン分離符：白い枠線
- 位置合わせパターン：小さな黒点、方向確定用

**データ領域：**
- 形式情報：エラー訂正レベルとマスク情報
- バージョン情報：QRコードのバージョン番号
- データとエラー訂正コードワード：実際に保存されるデータ

**容量仕様：**
- Version 1: 21×21モジュール、最大25文字
- Version 40: 177×177モジュール、最大4296文字
- 数字、文字、漢字、バイナリデータをサポート

### エラー訂正レベル

QRコードは4つのエラー訂正レベルをサポート：

| レベル | 訂正率 | 適用シーン |
|--------|--------|------------|
| L | ~7% | クリーンな環境、高品質印刷 |
| M | ~15% | 一般的な環境、標準印刷 |
| Q | ~25% | 悪い環境、汚損の可能性 |
| H | ~30% | 極めて悪い環境、深刻な汚損 |

**選択推奨：**
- URL共有：LまたはMレベルを使用
- 屋外広告：QまたはHレベルを使用
- 商品ラベル：MまたはQレベルを使用

### エンコードモード

QRコードは複数のエンコードモードをサポート：

**数字モード：**
- 数字0-9のみ保存可能
- 保存効率が最も高い
- 純粋な数字内容に適用

**英数字モード：**
- 数字、大文字、一部記号を保存
- 効率が高い
- URL、コードなどに適用

**バイトモード：**
- 任意の文字を保存可能
- 日本語、特殊記号をサポート
- 汎用性が最も強い

**漢字モード：**
- 日本語文字専用最適化
- 日本語環境でより高い効率

## 💡 使用のコツ

- **内容最適化**：できるだけ短い内容を使用し、スキャン成功率を向上
- **サイズ選択**：使用距離に応じて適切なサイズを選択
- **エラー訂正レベル**：使用環境に応じて適切なエラー訂正レベルを選択
- **テスト検証**：生成後、複数のデバイスでスキャン効果をテスト

## ⚠️ 注意事項

- **内容長さ**：内容が長すぎるとQRコードが複雑になり、スキャンに影響
- **印刷品質**：印刷が鮮明であることを確保し、ぼやけや変形を避ける
- **色コントラスト**：十分な色コントラストを保持、白黒配色を推奨
- **周囲の余白**：QRコード周囲に十分な空白領域を残す

## 🚀 使い方

1. **内容入力**：入力ボックスにQRコードを生成したい内容を入力
2. **設定調整**：適切なサイズとエラー訂正レベルを選択
3. **プレビュー生成**：生成ボタンをクリックしてQRコードプレビューを確認
4. **ダウンロード保存**：ダウンロードボタンをクリックしてQRコード画像を保存
5. **スキャンテスト**：スマートフォンでスキャンして生成効果をテスト

> **ヒント**：このツールはクライアント側でローカルにQRコードを生成し、データをサーバーにアップロードしないため、プライバシーとセキュリティが保証されます。
