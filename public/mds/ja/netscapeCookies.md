# Netscape Cookies 形式変換ツール

Netscape Cookies形式とJSON形式の相互変換ツールです。ブラウザデータ移行、Webクローラー開発、Webテストなどのシーンに適用され、異なるシステム間でのCookieデータの互換性を提供します。

## ✨ 主な機能

- 🔄 **双方向変換**：Netscape形式とJSON形式の相互変換をサポート
- 📊 **データ検証**：Cookie形式の正確性を自動チェック
- 🌐 **ブラウザ互換**：主要ブラウザのCookie形式をサポート
- 📋 **ワンクリックコピー**：変換結果を直接コピーして使用可能
- 🔧 **エラー検出**：無効なCookie形式を自動検出

## 📖 使用例

### Netscape → JSON 変換

**入力Netscape形式：**
```
# Netscape HTTP Cookie File
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123def456
.example.com	TRUE	/	FALSE	1640995200	user_pref	theme=dark
example.com	FALSE	/login	TRUE	1640995200	csrf_token	xyz789uvw012
```

**変換後JSON：**
```json
[
  {
    "domain": ".example.com",
    "hostOnly": false,
    "path": "/",
    "secure": false,
    "expirationDate": 1640995200,
    "name": "session_id",
    "value": "abc123def456"
  },
  {
    "domain": ".example.com",
    "hostOnly": false,
    "path": "/",
    "secure": false,
    "expirationDate": 1640995200,
    "name": "user_pref",
    "value": "theme=dark"
  },
  {
    "domain": "example.com",
    "hostOnly": true,
    "path": "/login",
    "secure": true,
    "expirationDate": 1640995200,
    "name": "csrf_token",
    "value": "xyz789uvw012"
  }
]
```

### JSON → Netscape 変換

**入力JSON：**
```json
[
  {
    "domain": ".toolmi.com",
    "hostOnly": false,
    "path": "/",
    "secure": true,
    "expirationDate": 1735689600,
    "name": "auth_token",
    "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
  },
  {
    "domain": "toolmi.com",
    "hostOnly": true,
    "path": "/dashboard",
    "secure": false,
    "expirationDate": 1735689600,
    "name": "dashboard_settings",
    "value": "layout=grid&theme=light"
  }
]
```

**変換後Netscape形式：**
```
# Netscape HTTP Cookie File
.toolmi.com	TRUE	/	TRUE	1735689600	auth_token	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9
toolmi.com	FALSE	/dashboard	FALSE	1735689600	dashboard_settings	layout=grid&theme=light
```

## 🎯 応用シーン

### 1. ブラウザデータ移行

異なるブラウザ間でのCookieデータ移行：

```javascript
// ブラウザCookie移行ツール
class BrowserCookieMigrator {
  constructor() {
    this.supportedFormats = ['netscape', 'json', 'chrome', 'firefox']
  }

  // Netscape形式からJSONに変換
  netscapeToJson(netscapeContent) {
    const lines = netscapeContent.split('\n')
    const cookies = []

    for (const line of lines) {
      // コメント行と空行をスキップ
      if (line.startsWith('#') || line.trim() === '') continue

      const parts = line.split('\t')
      if (parts.length >= 7) {
        const cookie = {
          domain: parts[0],
          hostOnly: parts[1] === 'FALSE',
          path: parts[2],
          secure: parts[3] === 'TRUE',
          expirationDate: parseInt(parts[4]),
          name: parts[5],
          value: parts[6]
        }
        cookies.push(cookie)
      }
    }

    return cookies
  }

  // JSONからNetscape形式に変換
  jsonToNetscape(jsonCookies) {
    let result = '# Netscape HTTP Cookie File\n'
    
    for (const cookie of jsonCookies) {
      const line = [
        cookie.domain,
        cookie.hostOnly ? 'FALSE' : 'TRUE',
        cookie.path,
        cookie.secure ? 'TRUE' : 'FALSE',
        cookie.expirationDate || Math.floor(Date.now() / 1000) + 86400,
        cookie.name,
        cookie.value
      ].join('\t')
      
      result += line + '\n'
    }

    return result
  }

  // ブラウザ固有形式の処理
  processBrowserExport(data, browserType) {
    switch (browserType) {
      case 'chrome':
        return this.processChromeExport(data)
      case 'firefox':
        return this.processFirefoxExport(data)
      case 'safari':
        return this.processSafariExport(data)
      default:
        throw new Error(`サポートされていないブラウザタイプ: ${browserType}`)
    }
  }

  processChromeExport(chromeData) {
    // Chrome固有のCookie処理
    return chromeData.map(cookie => ({
      domain: cookie.domain,
      hostOnly: !cookie.domain.startsWith('.'),
      path: cookie.path,
      secure: cookie.secure,
      expirationDate: cookie.expirationDate,
      name: cookie.name,
      value: cookie.value,
      httpOnly: cookie.httpOnly || false,
      sameSite: cookie.sameSite || 'unspecified'
    }))
  }

  // Cookie有効期限の管理
  manageCookieExpiration(cookies, action = 'extend') {
    const now = Math.floor(Date.now() / 1000)
    const oneYear = 365 * 24 * 60 * 60

    return cookies.map(cookie => {
      switch (action) {
        case 'extend':
          cookie.expirationDate = now + oneYear
          break
        case 'session':
          delete cookie.expirationDate
          break
        case 'expire':
          cookie.expirationDate = now - 1
          break
      }
      return cookie
    })
  }

  // Cookieフィルタリング
  filterCookies(cookies, criteria) {
    return cookies.filter(cookie => {
      if (criteria.domain && !cookie.domain.includes(criteria.domain)) {
        return false
      }
      if (criteria.secure !== undefined && cookie.secure !== criteria.secure) {
        return false
      }
      if (criteria.name && !cookie.name.includes(criteria.name)) {
        return false
      }
      return true
    })
  }
}

// 使用例
const migrator = new BrowserCookieMigrator()

// Netscape形式のCookieファイルを読み込み
const netscapeCookies = `# Netscape HTTP Cookie File
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123
.example.com	TRUE	/	TRUE	1640995200	auth_token	xyz789`

// JSONに変換
const jsonCookies = migrator.netscapeToJson(netscapeCookies)
console.log('JSON形式:', JSON.stringify(jsonCookies, null, 2))

// 有効期限を延長
const extendedCookies = migrator.manageCookieExpiration(jsonCookies, 'extend')

// 再びNetscape形式に変換
const newNetscapeCookies = migrator.jsonToNetscape(extendedCookies)
console.log('新しいNetscape形式:', newNetscapeCookies)
```

### 2. Webクローラー開発

Webスクレイピングでのセッション管理：

```javascript
// Webクローラー用Cookie管理
class CrawlerCookieManager {
  constructor() {
    this.cookieJar = new Map()
    this.sessionCookies = new Set()
  }

  // Cookieファイルの読み込み
  loadCookiesFromFile(filePath, format = 'netscape') {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      
      if (format === 'netscape') {
        return this.parseNetscapeCookies(content)
      } else if (format === 'json') {
        return JSON.parse(content)
      }
    } catch (error) {
      console.error('Cookieファイル読み込みエラー:', error)
      return []
    }
  }

  // Netscape形式の解析
  parseNetscapeCookies(content) {
    const lines = content.split('\n')
    const cookies = []

    for (const line of lines) {
      if (line.startsWith('#') || line.trim() === '') continue

      const parts = line.split('\t')
      if (parts.length >= 7) {
        const cookie = {
          domain: parts[0],
          includeSubdomains: parts[1] === 'TRUE',
          path: parts[2],
          secure: parts[3] === 'TRUE',
          expires: parseInt(parts[4]),
          name: parts[5],
          value: parts[6]
        }
        cookies.push(cookie)
      }
    }

    return cookies
  }

  // リクエスト用Cookieヘッダーの生成
  generateCookieHeader(url, cookies) {
    const urlObj = new URL(url)
    const applicableCookies = cookies.filter(cookie => {
      // ドメインマッチング
      if (cookie.domain.startsWith('.')) {
        if (!urlObj.hostname.endsWith(cookie.domain.substring(1))) {
          return false
        }
      } else {
        if (urlObj.hostname !== cookie.domain) {
          return false
        }
      }

      // パスマッチング
      if (!urlObj.pathname.startsWith(cookie.path)) {
        return false
      }

      // セキュア属性チェック
      if (cookie.secure && urlObj.protocol !== 'https:') {
        return false
      }

      // 有効期限チェック
      if (cookie.expires && cookie.expires < Math.floor(Date.now() / 1000)) {
        return false
      }

      return true
    })

    return applicableCookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ')
  }

  // レスポンスからCookieを抽出
  extractCookiesFromResponse(response, url) {
    const setCookieHeaders = response.headers['set-cookie'] || []
    const urlObj = new URL(url)
    const newCookies = []

    for (const header of setCookieHeaders) {
      const cookie = this.parseCookieHeader(header, urlObj.hostname)
      if (cookie) {
        newCookies.push(cookie)
      }
    }

    return newCookies
  }

  parseCookieHeader(header, defaultDomain) {
    const parts = header.split(';').map(part => part.trim())
    const [nameValue] = parts
    const [name, value] = nameValue.split('=', 2)

    const cookie = {
      name: name.trim(),
      value: value ? value.trim() : '',
      domain: defaultDomain,
      path: '/',
      secure: false,
      httpOnly: false,
      expires: null
    }

    // 属性の解析
    for (let i = 1; i < parts.length; i++) {
      const [attrName, attrValue] = parts[i].split('=', 2)
      
      switch (attrName.toLowerCase()) {
        case 'domain':
          cookie.domain = attrValue
          break
        case 'path':
          cookie.path = attrValue
          break
        case 'expires':
          cookie.expires = Math.floor(new Date(attrValue).getTime() / 1000)
          break
        case 'max-age':
          cookie.expires = Math.floor(Date.now() / 1000) + parseInt(attrValue)
          break
        case 'secure':
          cookie.secure = true
          break
        case 'httponly':
          cookie.httpOnly = true
          break
      }
    }

    return cookie
  }

  // Cookieの保存
  saveCookiesToFile(cookies, filePath, format = 'netscape') {
    try {
      let content = ''
      
      if (format === 'netscape') {
        content = this.cookiesToNetscape(cookies)
      } else if (format === 'json') {
        content = JSON.stringify(cookies, null, 2)
      }

      fs.writeFileSync(filePath, content, 'utf8')
      console.log(`Cookieを${filePath}に保存しました`)
    } catch (error) {
      console.error('Cookie保存エラー:', error)
    }
  }

  cookiesToNetscape(cookies) {
    let content = '# Netscape HTTP Cookie File\n'
    
    for (const cookie of cookies) {
      const line = [
        cookie.domain,
        cookie.includeSubdomains ? 'TRUE' : 'FALSE',
        cookie.path,
        cookie.secure ? 'TRUE' : 'FALSE',
        cookie.expires || 0,
        cookie.name,
        cookie.value
      ].join('\t')
      
      content += line + '\n'
    }

    return content
  }
}

// 使用例
const cookieManager = new CrawlerCookieManager()

// Cookieファイルの読み込み
const cookies = cookieManager.loadCookiesFromFile('./cookies.txt', 'netscape')

// リクエスト用Cookieヘッダーの生成
const cookieHeader = cookieManager.generateCookieHeader('https://example.com/api/data', cookies)
console.log('Cookie Header:', cookieHeader)

// HTTPリクエストでの使用
const requestOptions = {
  method: 'GET',
  headers: {
    'Cookie': cookieHeader,
    'User-Agent': 'Mozilla/5.0 (compatible; WebCrawler/1.0)'
  }
}
```

### 3. Webテスト自動化

テスト環境でのCookie管理：

```javascript
// テスト用Cookie管理システム
class TestCookieManager {
  constructor() {
    this.testSessions = new Map()
    this.cookieTemplates = new Map()
  }

  // テストセッションの作成
  createTestSession(sessionId, initialCookies = []) {
    this.testSessions.set(sessionId, {
      cookies: [...initialCookies],
      createdAt: new Date(),
      lastUsed: new Date()
    })
  }

  // Cookieテンプレートの定義
  defineCookieTemplate(templateName, template) {
    this.cookieTemplates.set(templateName, template)
  }

  // テンプレートからCookieを生成
  generateCookiesFromTemplate(templateName, variables = {}) {
    const template = this.cookieTemplates.get(templateName)
    if (!template) {
      throw new Error(`テンプレート ${templateName} が見つかりません`)
    }

    return template.map(cookieTemplate => {
      const cookie = { ...cookieTemplate }
      
      // 変数の置換
      for (const [key, value] of Object.entries(variables)) {
        if (cookie.value && typeof cookie.value === 'string') {
          cookie.value = cookie.value.replace(`{{${key}}}`, value)
        }
        if (cookie.domain && typeof cookie.domain === 'string') {
          cookie.domain = cookie.domain.replace(`{{${key}}}`, value)
        }
      }

      return cookie
    })
  }

  // テストシナリオの実行
  async runTestScenario(scenarioName, steps) {
    const sessionId = `test_${Date.now()}`
    this.createTestSession(sessionId)

    console.log(`テストシナリオ "${scenarioName}" を開始します`)

    for (const [index, step] of steps.entries()) {
      console.log(`ステップ ${index + 1}: ${step.description}`)
      
      try {
        await this.executeTestStep(sessionId, step)
        console.log(`✓ ステップ ${index + 1} 完了`)
      } catch (error) {
        console.error(`✗ ステップ ${index + 1} 失敗:`, error.message)
        throw error
      }
    }

    console.log(`テストシナリオ "${scenarioName}" が正常に完了しました`)
    return this.testSessions.get(sessionId)
  }

  async executeTestStep(sessionId, step) {
    const session = this.testSessions.get(sessionId)
    if (!session) {
      throw new Error(`セッション ${sessionId} が見つかりません`)
    }

    switch (step.action) {
      case 'setCookie':
        session.cookies.push(step.cookie)
        break
      case 'loadTemplate':
        const templateCookies = this.generateCookiesFromTemplate(step.template, step.variables)
        session.cookies.push(...templateCookies)
        break
      case 'makeRequest':
        await this.makeTestRequest(sessionId, step.url, step.options)
        break
      case 'verifyCookie':
        this.verifyCookieExists(sessionId, step.cookieName, step.expectedValue)
        break
      case 'exportCookies':
        this.exportSessionCookies(sessionId, step.format, step.filePath)
        break
    }

    session.lastUsed = new Date()
  }

  verifyCookieExists(sessionId, cookieName, expectedValue = null) {
    const session = this.testSessions.get(sessionId)
    const cookie = session.cookies.find(c => c.name === cookieName)

    if (!cookie) {
      throw new Error(`Cookie "${cookieName}" が見つかりません`)
    }

    if (expectedValue !== null && cookie.value !== expectedValue) {
      throw new Error(`Cookie "${cookieName}" の値が期待値と異なります。期待値: ${expectedValue}, 実際の値: ${cookie.value}`)
    }

    return true
  }

  exportSessionCookies(sessionId, format, filePath) {
    const session = this.testSessions.get(sessionId)
    const migrator = new BrowserCookieMigrator()

    let content = ''
    if (format === 'netscape') {
      content = migrator.jsonToNetscape(session.cookies)
    } else if (format === 'json') {
      content = JSON.stringify(session.cookies, null, 2)
    }

    fs.writeFileSync(filePath, content, 'utf8')
    console.log(`セッションCookieを ${filePath} にエクスポートしました`)
  }
}

// 使用例
const testManager = new TestCookieManager()

// Cookieテンプレートの定義
testManager.defineCookieTemplate('loginSession', [
  {
    name: 'session_id',
    value: '{{sessionId}}',
    domain: '{{domain}}',
    path: '/',
    secure: true,
    expires: Math.floor(Date.now() / 1000) + 3600
  },
  {
    name: 'user_id',
    value: '{{userId}}',
    domain: '{{domain}}',
    path: '/',
    secure: false,
    expires: Math.floor(Date.now() / 1000) + 86400
  }
])

// テストシナリオの実行
const testSteps = [
  {
    action: 'loadTemplate',
    description: 'ログインセッションテンプレートを読み込み',
    template: 'loginSession',
    variables: {
      sessionId: 'test_session_123',
      userId: 'user_456',
      domain: 'example.com'
    }
  },
  {
    action: 'verifyCookie',
    description: 'セッションCookieの存在確認',
    cookieName: 'session_id',
    expectedValue: 'test_session_123'
  },
  {
    action: 'exportCookies',
    description: 'Cookieをファイルにエクスポート',
    format: 'netscape',
    filePath: './test_cookies.txt'
  }
]

testManager.runTestScenario('ログインテスト', testSteps)
  .then(session => {
    console.log('テスト完了。セッション情報:', session)
  })
  .catch(error => {
    console.error('テスト失敗:', error)
  })
```

## 🔧 技術詳解

### Netscape Cookie形式

Netscape Cookie形式の構造：

**フィールド構成：**
1. **domain**: Cookieが有効なドメイン
2. **flag**: サブドメインを含むかどうか（TRUE/FALSE）
3. **path**: Cookieが有効なパス
4. **secure**: HTTPS接続でのみ送信するか（TRUE/FALSE）
5. **expiration**: 有効期限（Unixタイムスタンプ）
6. **name**: Cookie名
7. **value**: Cookie値

**形式例：**
```
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123def456
```

### JSON Cookie形式

JSON形式でのCookie表現：

```json
{
  "domain": ".example.com",
  "hostOnly": false,
  "path": "/",
  "secure": false,
  "expirationDate": 1640995200,
  "name": "session_id",
  "value": "abc123def456",
  "httpOnly": false,
  "sameSite": "unspecified"
}
```

## 💡 使用のコツ

- **形式確認**：変換前にCookie形式が正しいことを確認
- **ドメイン注意**：ドメインの先頭ドット（.）の意味を理解
- **有効期限**：Unixタイムスタンプの正確性を確認
- **セキュリティ**：機密Cookieの取り扱いに注意

## ⚠️ 注意事項

- **プライバシー**：Cookie情報には個人情報が含まれる可能性がある
- **セキュリティ**：認証Cookieの漏洩に注意
- **有効期限**：期限切れCookieの処理を適切に行う
- **ドメイン制限**：Cookieのドメイン制限を理解して使用

## 🚀 使い方

1. **形式選択**：入力形式（NetscapeまたはJSON）を選択
2. **データ入力**：変換したいCookieデータを入力ボックスに貼り付け
3. **変換実行**：「変換」ボタンをクリックして変換を実行
4. **結果確認**：変換結果が出力エリアに表示される
5. **コピー使用**：「コピー」ボタンで結果をクリップボードにコピー

> **ヒント**：このツールはクライアント側でローカル処理を行い、Cookieデータをサーバーに送信しないため、機密性の高いセッション情報も安全に変換できます。
