# SHA1 ハッシュ暗号化ツール

SHA1（Secure Hash Algorithm 1）は、任意の長さの入力データから160ビット（40文字の16進数）のハッシュ値を生成する暗号学的ハッシュ関数です。データの整合性検証、デジタル署名、バージョン管理システムなどで使用されていますが、現在はセキュリティ上の理由から非推奨とされています。

## ✨ 主な機能

- 🔐 **SHA1ハッシュ生成**：テキストを160ビットのSHA1ハッシュ値に変換
- 🌐 **多言語対応**：日本語、中国語、英語などのUnicode文字をサポート
- 📊 **一意性**：同じ入力に対して常に同じハッシュ値を生成
- 📋 **ワンクリックコピー**：生成されたハッシュ値を直接コピー可能
- ⚡ **高速処理**：入力と同時にハッシュ値を表示

## 📖 使用例

### テキストハッシュ化

**入力テキスト：**
```
こんにちは、世界！
```

**SHA1ハッシュ値：**
```
a94a8fe5ccb19ba61c4c0873d391e987982fbbd3
```

### ファイル整合性チェック

**入力ファイル内容：**
```
重要なドキュメント
バージョン: 1.0
作成日: 2024-01-15
```

**SHA1ハッシュ値：**
```
2fd4e1c67a2d28fced849ee1bb76e7391b93eb12
```

## 🎯 応用シーン

### 1. バージョン管理システム

Gitでのコミット識別：

```bash
# Gitでのコミットハッシュ（SHA1ベース）
git log --oneline
# a94a8fe 最新の機能追加
# 2fd4e1c バグ修正
# 7b3c9d1 初期コミット

# ファイルのSHA1ハッシュ確認
git hash-object README.md
# 2fd4e1c67a2d28fced849ee1bb76e7391b93eb12
```

### 2. データ整合性検証

ファイルの整合性チェックシステム：

```javascript
// ファイル整合性チェッククラス
class FileIntegrityChecker {
  constructor() {
    this.checksums = new Map();
  }

  // ファイルのSHA1チェックサム計算
  async calculateSHA1(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = async (event) => {
        try {
          const arrayBuffer = event.target.result;
          const hashBuffer = await crypto.subtle.digest('SHA-1', arrayBuffer);
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
          resolve(hashHex);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  }

  // チェックサムの記録
  async recordChecksum(fileId, file) {
    const checksum = await this.calculateSHA1(file);
    this.checksums.set(fileId, {
      checksum: checksum,
      fileName: file.name,
      fileSize: file.size,
      recordedAt: new Date().toISOString()
    });
    return checksum;
  }

  // 整合性の検証
  async verifyIntegrity(fileId, currentFile) {
    const record = this.checksums.get(fileId);
    if (!record) {
      return { valid: false, reason: 'チェックサムが記録されていません' };
    }

    const currentChecksum = await this.calculateSHA1(currentFile);
    const isValid = record.checksum === currentChecksum;

    return {
      valid: isValid,
      originalChecksum: record.checksum,
      currentChecksum: currentChecksum,
      fileName: record.fileName,
      reason: isValid ? '整合性OK' : 'ファイルが変更されています'
    };
  }
}

// 使用例
const integrityChecker = new FileIntegrityChecker();

// ファイル選択時の処理
document.getElementById('fileInput').addEventListener('change', async (event) => {
  const file = event.target.files[0];
  if (file) {
    try {
      const checksum = await integrityChecker.recordChecksum('file001', file);
      console.log(`ファイル ${file.name} のチェックサム:`, checksum);
    } catch (error) {
      console.error('チェックサム計算エラー:', error);
    }
  }
});
```

## 🔧 技術詳細

### SHA1アルゴリズム

SHA1の基本的な特徴：

**ハッシュ長：** 160ビット（40文字の16進数）
**ブロックサイズ：** 512ビット
**処理ラウンド：** 80ラウンド
**セキュリティ：** 現在は非推奨

### セキュリティ上の問題

SHA1の制限と代替案：

```javascript
// SHA1の問題点
const sha1Issues = {
  collision: '2017年にGoogleが衝突攻撃を実証',
  deprecation: '多くのブラウザとシステムで非推奨',
  weakness: '暗号学的強度が不十分',
  recommendation: 'SHA-256以上の使用を推奨'
};

// 推奨される代替案
const alternatives = {
  'SHA-256': '現在の標準、256ビットハッシュ',
  'SHA-3': '最新のKeccak基盤ハッシュ',
  'BLAKE2': '高速で安全なハッシュ関数',
  'BLAKE3': '最新の高性能ハッシュ関数'
};
```

## 💡 使用のコツ

- **用途限定**：レガシーシステムとの互換性が必要な場合のみ使用
- **代替検討**：新規開発ではSHA-256以上を使用
- **整合性チェック**：重要でないデータの整合性チェックに限定
- **移行計画**：SHA1からより安全なハッシュ関数への移行を計画

## ⚠️ 注意事項

- **セキュリティリスク**：SHA1は暗号学的に安全ではない
- **衝突攻撃**：異なる入力から同じハッシュ値が生成される可能性
- **非推奨**：新規開発での使用は推奨されない
- **移行必要**：既存システムでは早急にSHA-256以上への移行を検討

## 🚀 使い方

1. **テキスト入力**：ハッシュ化したいテキストを入力
2. **自動生成**：入力と同時にSHA1ハッシュ値が生成
3. **結果確認**：40文字の16進数ハッシュ値を確認
4. **コピー使用**：ハッシュ値をクリップボードにコピー

> **重要**：SHA1は現在セキュリティ上の理由から非推奨です。新規開発ではSHA-256以上の使用を強く推奨します。
