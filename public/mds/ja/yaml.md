# YAML フォーマットツール

YAML（YAML Ain't Markup Language）は、人間が読みやすいデータシリアライゼーション標準で、設定ファイル、データ交換、ドキュメント作成によく使用されます。このツールは、YAMLフォーマットの検証、美化、圧縮機能を提供し、開発者がYAMLファイルを処理・管理するのに役立ちます。

## ✨ 主な機能

- 🎯 **フォーマット検証**：YAML構文エラーをチェックし、詳細なヒントを提供
- 🎨 **フォーマット美化**：YAMLコンテンツを自動的にインデントとフォーマット
- 🗜️ **コンテンツ圧縮**：余分なスペースとコメントを削除し、ファイルサイズを圧縮
- 📋 **ワンクリックコピー**：フォーマット結果を直接コピーして使用可能
- 🔧 **エラー位置特定**：構文エラーの位置を正確に特定

## 📖 使用例

### フォーマット例

**元のYAML：**
```yaml
name:ツールミ
version:1.0.0
features:
- フォーマット
- 検証
- 圧縮
config:
  debug:true
  port:3000
```

**フォーマット後：**
```yaml
name: ツールミ
version: 1.0.0
features:
  - フォーマット
  - 検証
  - 圧縮
config:
  debug: true
  port: 3000
```

### 複雑構造例

**入力YAML：**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
  pools:
    - name: read_pool
      size: 10
    - name: write_pool
      size: 5
```

**検証・美化後：**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
  pools:
    - name: read_pool
      size: 10
    - name: write_pool
      size: 5
```

## 🎯 応用シーン

### 1. 設定ファイル管理

アプリケーション設定ファイルの整理と検証：

```yaml
# アプリケーション設定ファイル (config.yml)
app:
  name: ツールミ
  version: 1.0.0
  environment: production
  
server:
  host: 0.0.0.0
  port: 3000
  ssl:
    enabled: true
    cert_path: /etc/ssl/certs/app.crt
    key_path: /etc/ssl/private/app.key
    
database:
  type: postgresql
  host: localhost
  port: 5432
  name: toolmi_db
  username: ${DB_USER}
  password: ${DB_PASSWORD}
  pool:
    min_connections: 2
    max_connections: 10
    
redis:
  host: localhost
  port: 6379
  password: ${REDIS_PASSWORD}
  db: 0
  
logging:
  level: info
  format: json
  outputs:
    - type: file
      path: /var/log/app.log
    - type: console
```

### 2. Docker Composeファイル

Dockerコンテナオーケストレーション設定の管理：

```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=database
    depends_on:
      - database
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - app-network
      
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: toolmi_db
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
      
  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 3. CI/CD設定

GitHub Actionsワークフロー設定：

```yaml
# .github/workflows/deploy.yml
name: 本番環境へのデプロイ

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: コードチェックアウト
        uses: actions/checkout@v3
        
      - name: Node.jsセットアップ
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 依存関係インストール
        run: npm ci
        
      - name: テスト実行
        run: npm test
        
      - name: リンティング実行
        run: npm run lint
        
  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - name: コードチェックアウト
        uses: actions/checkout@v3
        
      - name: Dockerイメージビルド
        run: |
          docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest .
          
      - name: レジストリにプッシュ
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
```

### 4. Kubernetes設定

Kubernetesデプロイ設定ファイル：

```yaml
# deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  labels:
    app: toolmi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: toolmi
  template:
    metadata:
      labels:
        app: toolmi
    spec:
      containers:
        - name: app
          image: toolmi/app:latest
          ports:
            - containerPort: 3000
          env:
            - name: NODE_ENV
              value: "production"
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: db-host
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
spec:
  selector:
    app: toolmi
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer
```

## 🔧 技術詳解

### YAML構文規則

YAMLの基本構文規則：

**インデント規則：**
- スペースを使用してインデント、タブは使用不可
- 同じレベルの要素は左揃えが必要
- 子要素は親要素よりも多くインデントが必要

**データ型：**
- 文字列：引用符なしでも可、特殊文字には引用符が必要
- 数字：整数と浮動小数点数
- ブール値：true/false、yes/no、on/off
- null値：null、~、または空

**コレクション型：**
- 配列：- を使用してリスト項目を表示
- オブジェクト：key: value を使用してキー値ペアを表示
- ネスト：任意レベルのネスト構造をサポート

### よくあるエラー

YAMLフォーマットツールが検出できるよくあるエラー：

**インデントエラー：**
```yaml
# 間違った例
config:
  debug: true
 port: 3000  # インデントが一致しない

# 正しい例
config:
  debug: true
  port: 3000
```

**引用符の問題：**
```yaml
# 間違った例
message: It's a test  # 単一引用符がエスケープされていない

# 正しい例
message: "It's a test"
# または
message: 'It''s a test'
```

**リスト形式：**
```yaml
# 間違った例
features:
- format
 - validate  # インデントエラー

# 正しい例
features:
  - format
  - validate
```

## 💡 使用のコツ

- **一貫したインデント**：常に同じ数のスペースでインデント（2スペース推奨）
- **引用符の使用**：特殊文字を含む文字列は引用符で囲む
- **コメント規範**：# を使用してコメントを追加、コメント前にスペースを追加
- **複数行文字列**：| または > を使用して複数行テキストを処理

## ⚠️ 注意事項

- **タブ禁止**：YAMLはタブを許可せず、スペースのみ使用可能
- **インデント敏感**：インデントエラーは解析失敗を引き起こす
- **特殊文字**：コロン、引用符などの特殊文字は正しく処理する必要
- **エンコード形式**：ファイルがUTF-8エンコードを使用することを確保

## 🚀 使い方

1. **YAML入力**：入力ボックスに処理したいYAMLコンテンツを貼り付け
2. **操作選択**：「フォーマット」、「検証」、または「圧縮」ボタンをクリック
3. **結果確認**：出力ボックスで処理後のYAMLを確認
4. **コピー使用**：「コピー」ボタンをクリックして結果をクリップボードにコピー
5. **エラー修正**：エラーヒントに基づいて構文問題を修正

> **ヒント**：このツールはクライアント側でローカル処理を行い、YAMLコンテンツをサーバーにアップロードしないため、データセキュリティが保証されます。
