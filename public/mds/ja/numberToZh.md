# 数字→中国語変換ツール

アラビア数字を中国語数字に変換するツールです。簡体字中国語、繁体字中国語、大文字金額表記をサポートし、財務報告書、契約書、請求書発行などのシーンに適用されます。整数と小数の変換をサポートします。

## ✨ 主な機能

- 🔢 **複数変換タイプ**：簡体字、繁体字、大文字金額をサポート
- 💰 **金額変換**：財務用の大文字金額表記に対応
- 📊 **小数対応**：整数と小数の両方を正確に変換
- 📋 **ワンクリックコピー**：変換結果を直接コピーして使用可能
- ⚡ **リアルタイム変換**：入力と同時に変換結果を表示

## 📖 使用例

### 基本数字変換

**入力数字：**
```
12345
```

**簡体字中国語：**
```
一万二千三百四十五
```

**繁体字中国語：**
```
一萬二千三百四十五
```

**大文字金額：**
```
壹万贰仟叁佰肆拾伍元整
```

### 小数変換

**入力数字：**
```
1234.56
```

**簡体字中国語：**
```
一千二百三十四点五六
```

**大文字金額：**
```
壹仟贰佰叁拾肆元伍角陆分
```

### 大きな数字

**入力数字：**
```
9876543210
```

**簡体字中国語：**
```
九十八亿七千六百五十四万三千二百一十
```

**繁体字中国語：**
```
九十八億七千六百五十四萬三千二百一十
```

## 🎯 応用シーン

### 1. 財務報告書

財務文書での金額表記：

```javascript
// 財務金額変換システム
class FinancialAmountConverter {
  constructor() {
    this.digitMap = {
      0: '零', 1: '壹', 2: '贰', 3: '叁', 4: '肆',
      5: '伍', 6: '陆', 7: '柒', 8: '捌', 9: '玖'
    }
    this.unitMap = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿']
  }

  // 金額を大文字中国語に変換
  convertToChineseAmount(amount) {
    if (amount === 0) return '零元整'
    
    const [integerPart, decimalPart] = amount.toString().split('.')
    let result = this.convertInteger(parseInt(integerPart)) + '元'
    
    if (decimalPart) {
      result += this.convertDecimal(decimalPart)
    } else {
      result += '整'
    }
    
    return result
  }

  convertInteger(num) {
    if (num === 0) return '零'
    
    const str = num.toString()
    const len = str.length
    let result = ''
    let zeroFlag = false
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(str[i])
      const unit = this.unitMap[len - i - 1]
      
      if (digit === 0) {
        zeroFlag = true
      } else {
        if (zeroFlag && result !== '') {
          result += '零'
        }
        result += this.digitMap[digit] + unit
        zeroFlag = false
      }
    }
    
    return result
  }

  convertDecimal(decimal) {
    const jiao = parseInt(decimal[0] || 0)
    const fen = parseInt(decimal[1] || 0)
    let result = ''
    
    if (jiao > 0) {
      result += this.digitMap[jiao] + '角'
    }
    if (fen > 0) {
      result += this.digitMap[fen] + '分'
    }
    
    return result || '整'
  }

  // 請求書用フォーマット
  formatForInvoice(amount, currency = '人民币') {
    const chineseAmount = this.convertToChineseAmount(amount)
    return {
      original: `¥${amount.toFixed(2)}`,
      chinese: chineseAmount,
      currency: currency,
      formatted: `${currency}${chineseAmount}`
    }
  }
}

// 使用例
const converter = new FinancialAmountConverter()

// 請求書金額の変換
const invoiceAmount = 12345.67
const formatted = converter.formatForInvoice(invoiceAmount)

console.log('請求書情報:')
console.log('金額:', formatted.original)
console.log('大文字:', formatted.chinese)
console.log('完全形式:', formatted.formatted)

// 出力:
// 請求書情報:
// 金額: ¥12345.67
// 大文字: 壹万贰仟叁佰肆拾伍元陆角柒分
// 完全形式: 人民币壹万贰仟叁佰肆拾伍元陆角柒分
```

### 2. 契約書作成

法的文書での数字表記：

```javascript
// 契約書数字変換システム
class ContractNumberConverter {
  constructor() {
    this.simpleDigits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    this.traditionalDigits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    this.units = ['', '十', '百', '千', '万', '十', '百', '千', '亿']
  }

  // 契約期間の変換
  convertContractPeriod(years, months = 0, days = 0) {
    let result = ''
    
    if (years > 0) {
      result += this.convertToSimpleChinese(years) + '年'
    }
    if (months > 0) {
      result += this.convertToSimpleChinese(months) + '个月'
    }
    if (days > 0) {
      result += this.convertToSimpleChinese(days) + '日'
    }
    
    return result || '零日'
  }

  // 簡体字中国語変換
  convertToSimpleChinese(num) {
    if (num === 0) return '零'
    if (num < 10) return this.simpleDigits[num]
    
    const str = num.toString()
    const len = str.length
    let result = ''
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(str[i])
      const unitIndex = len - i - 1
      
      if (digit !== 0) {
        result += this.simpleDigits[digit]
        if (unitIndex > 0) {
          result += this.units[unitIndex]
        }
      } else if (result !== '' && i < len - 1) {
        const nextDigit = parseInt(str[i + 1])
        if (nextDigit !== 0) {
          result += '零'
        }
      }
    }
    
    return result
  }

  // 契約条項の生成
  generateContractClause(amount, period, paymentTerms) {
    const chineseAmount = this.convertToTraditionalChinese(amount)
    const chinesePeriod = this.convertContractPeriod(period.years, period.months, period.days)
    
    return {
      amount: `合同金额为人民币${chineseAmount}元整（¥${amount.toFixed(2)}）`,
      period: `合同期限为${chinesePeriod}`,
      payment: `付款方式：${paymentTerms}`,
      fullClause: `本合同金额为人民币${chineseAmount}元整（¥${amount.toFixed(2)}），合同期限为${chinesePeriod}，付款方式：${paymentTerms}。`
    }
  }

  convertToTraditionalChinese(num) {
    // 繁体字変換の実装（簡略化）
    return this.convertToSimpleChinese(num).replace(/一/g, '壹').replace(/二/g, '贰')
  }
}

// 使用例
const contractConverter = new ContractNumberConverter()

// 契約情報
const contractInfo = {
  amount: 500000,
  period: { years: 2, months: 6, days: 0 },
  paymentTerms: '分期付款，每月支付'
}

const clause = contractConverter.generateContractClause(
  contractInfo.amount,
  contractInfo.period,
  contractInfo.paymentTerms
)

console.log('契約条項:')
console.log(clause.fullClause)
// 出力: 本合同金额为人民币五十万元整（¥500000.00），合同期限为二年六个月，付款方式：分期付款，每月支付。
```

### 3. 会計システム

会計ソフトウェアでの数字処理：

```javascript
// 会計数字変換システム
class AccountingNumberConverter {
  constructor() {
    this.config = {
      currency: '人民币',
      precision: 2,
      useTraditional: true
    }
  }

  // 会計帳簿エントリの作成
  createJournalEntry(debit, credit, description) {
    return {
      date: new Date().toISOString().split('T')[0],
      description: description,
      debit: {
        amount: debit,
        chinese: this.convertToAccountingFormat(debit)
      },
      credit: {
        amount: credit,
        chinese: this.convertToAccountingFormat(credit)
      },
      balance: debit - credit
    }
  }

  // 会計形式への変換
  convertToAccountingFormat(amount) {
    const absAmount = Math.abs(amount)
    const chineseAmount = this.convertToChinese(absAmount)
    const sign = amount >= 0 ? '' : '负'
    
    return `${sign}${this.config.currency}${chineseAmount}元`
  }

  // 月次報告書の生成
  generateMonthlyReport(transactions) {
    const totalDebit = transactions.reduce((sum, t) => sum + t.debit, 0)
    const totalCredit = transactions.reduce((sum, t) => sum + t.credit, 0)
    const netIncome = totalCredit - totalDebit
    
    return {
      period: '本月',
      totalDebit: {
        amount: totalDebit,
        chinese: this.convertToAccountingFormat(totalDebit)
      },
      totalCredit: {
        amount: totalCredit,
        chinese: this.convertToAccountingFormat(totalCredit)
      },
      netIncome: {
        amount: netIncome,
        chinese: this.convertToAccountingFormat(netIncome)
      },
      summary: `本月总收入${this.convertToAccountingFormat(totalCredit)}，总支出${this.convertToAccountingFormat(totalDebit)}，净收入${this.convertToAccountingFormat(netIncome)}。`
    }
  }

  convertToChinese(num) {
    // 中国語変換の実装（簡略化）
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    const units = ['', '拾', '佰', '仟', '万']
    
    if (num === 0) return '零'
    
    let result = ''
    const str = num.toString()
    const len = str.length
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(str[i])
      const unitIndex = len - i - 1
      
      if (digit !== 0) {
        result += digits[digit]
        if (unitIndex > 0 && unitIndex < units.length) {
          result += units[unitIndex]
        }
      }
    }
    
    return result
  }
}

// 使用例
const accounting = new AccountingNumberConverter()

// 取引記録
const transactions = [
  { debit: 0, credit: 50000, description: '売上収入' },
  { debit: 15000, credit: 0, description: '事務用品購入' },
  { debit: 8000, credit: 0, description: '交通費' }
]

// 月次報告書生成
const monthlyReport = accounting.generateMonthlyReport(transactions)
console.log('月次報告書:')
console.log(monthlyReport.summary)
```

## 🔧 技術詳解

### 中国語数字システム

中国語数字の基本構造：

**基本数字：**
- 簡体字：零、一、二、三、四、五、六、七、八、九
- 繁体字：零、壹、贰、叁、肆、伍、陆、柒、捌、玖

**単位：**
- 基本単位：十、百、千、万、亿
- 金額単位：元、角、分

**特殊規則：**
- ゼロの処理：連続するゼロは一つの「零」で表現
- 単位の省略：特定の条件下で単位を省略
- 大文字使用：財務文書では改ざん防止のため大文字を使用

### 変換アルゴリズム

```javascript
// 完全な変換アルゴリズム
class ChineseNumberConverter {
  constructor() {
    this.digits = {
      simple: ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'],
      traditional: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    }
    this.units = ['', '十', '百', '千', '万', '十', '百', '千', '亿']
  }

  convert(number, type = 'simple') {
    if (number === 0) return this.digits[type][0]
    
    const isNegative = number < 0
    const absNumber = Math.abs(number)
    const [integerPart, decimalPart] = absNumber.toString().split('.')
    
    let result = this.convertInteger(parseInt(integerPart), type)
    
    if (decimalPart) {
      result += '点' + this.convertDecimal(decimalPart, type)
    }
    
    return isNegative ? '负' + result : result
  }

  convertInteger(num, type) {
    const str = num.toString()
    const len = str.length
    let result = ''
    let hasZero = false
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(str[i])
      const unitIndex = len - i - 1
      
      if (digit === 0) {
        hasZero = true
      } else {
        if (hasZero && result !== '') {
          result += this.digits[type][0] // 零
        }
        result += this.digits[type][digit]
        if (unitIndex > 0) {
          result += this.units[unitIndex]
        }
        hasZero = false
      }
    }
    
    return result
  }

  convertDecimal(decimal, type) {
    return decimal.split('').map(d => this.digits[type][parseInt(d)]).join('')
  }
}
```

## 💡 使用のコツ

- **用途選択**：財務文書には大文字、一般文書には簡体字を使用
- **精度確認**：小数点以下の桁数に注意
- **文脈考慮**：使用する文書の種類に応じて適切な形式を選択
- **検証重要**：重要な文書では変換結果を必ず確認

## ⚠️ 注意事項

- **精度限界**：非常に大きな数字や複雑な小数は正確性に注意
- **地域差異**：中国本土と台湾・香港では表記が異なる場合がある
- **法的要件**：財務文書では法的要件に従った表記を使用
- **文字エンコーディング**：中国語文字の正しい表示を確保

## 🚀 使い方

1. **数字入力**：変換したい数字を入力ボックスに入力
2. **タイプ選択**：簡体字、繁体字、または大文字金額を選択
3. **結果確認**：変換結果がリアルタイムで表示される
4. **コピー使用**：「コピー」ボタンで結果をクリップボードにコピー
5. **用途活用**：契約書、請求書、財務報告書などで活用

> **ヒント**：このツールはクライアント側でローカル処理を行い、入力データをサーバーに送信しないため、機密性の高い財務情報も安全に変換できます。
