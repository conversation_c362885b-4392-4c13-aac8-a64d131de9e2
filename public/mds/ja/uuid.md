# UUID 生成器

UUID（Universally Unique Identifier、汎用一意識別子）は、分散システムで情報を一意に識別するための標準化された識別情報です。UUIDは128ビットの数字で構成され、通常32個の16進数で表現され、ハイフンで5つのグループに分割されます。

## ✨ 主な機能

- 🔢 **複数バージョン対応**：UUID v1、v4などの異なるバージョンをサポート
- ⚡ **バッチ生成**：一度に複数のUUIDを生成をサポート
- 📋 **複数形式**：標準形式、ハイフンなし形式などをサポート
- 💾 **ワンクリックコピー**：生成されたUUIDを直接コピーして使用可能
- 🔧 **リアルタイム生成**：クリックで新しいUUIDを即座に生成

## 📖 使用例

### 標準UUID v4

**生成例：**
```
f47ac10b-58cc-4372-a567-0e02b2c3d479
```

**特徴：**
- ランダム生成、時間情報なし
- 衝突確率が極めて低い
- 最も一般的に使用されるUUIDバージョン

### UUID v1（時間ベース）

**生成例：**
```
6ba7b810-9dad-11d1-80b4-00c04fd430c8
```

**特徴：**
- タイムスタンプ情報を含む
- MACアドレス情報を含む
- 生成時間を推測可能

### ハイフンなし形式

**生成例：**
```
f47ac10b58cc4372a5670e02b2c3d479
```

**特徴：**
- 32個の連続した16進文字
- 特定のデータベースやシステムに適用

## 🎯 応用シーン

### 1. データベース主キー

データベースでUUIDを主キーとして使用：

```sql
-- UUID主キーを使用するテーブルの作成
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- データの挿入（UUIDを自動生成）
INSERT INTO users (username, email) 
VALUES ('tanaka', '<EMAIL>');

-- データの検索
SELECT * FROM users WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
```

### 2. 分散システム

分散システムで一意識別子を生成：

```javascript
// マイクロサービスでのリクエスト追跡
class RequestTracker {
  constructor() {
    this.requestId = generateUUID()
    this.timestamp = new Date().toISOString()
  }

  log(message) {
    console.log(`[${this.requestId}] ${this.timestamp}: ${message}`)
  }
}

// 使用例
const tracker = new RequestTracker()
tracker.log('ユーザーリクエスト処理開始')
tracker.log('ユーザーサービス呼び出し')
tracker.log('注文サービス呼び出し')
tracker.log('リクエスト処理完了')

// 出力例：
// [f47ac10b-58cc-4372-a567-0e02b2c3d479] 2024-06-15T10:30:00.000Z: ユーザーリクエスト処理開始
```

### 3. ファイル命名

アップロードファイルの一意名を生成：

```javascript
// ファイルアップロード処理
function handleFileUpload(file) {
  const fileExtension = file.name.split('.').pop()
  const uniqueFileName = `${generateUUID()}.${fileExtension}`
  
  // ファイル保存
  const filePath = `/uploads/${uniqueFileName}`
  saveFile(file, filePath)
  
  return {
    originalName: file.name,
    fileName: uniqueFileName,
    filePath: filePath,
    uploadTime: new Date().toISOString()
  }
}

// 使用例
const uploadResult = handleFileUpload(userFile)
console.log(uploadResult)
// {
//   originalName: "document.pdf",
//   fileName: "f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   filePath: "/uploads/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   uploadTime: "2024-06-15T10:30:00.000Z"
// }
```

### 4. セッション管理

ユーザーセッションの一意識別子を生成：

```javascript
// セッション管理システム
class SessionManager {
  constructor() {
    this.sessions = new Map()
  }

  createSession(userId) {
    const sessionId = generateUUID()
    const session = {
      id: sessionId,
      userId: userId,
      createdAt: new Date(),
      lastAccess: new Date(),
      data: {}
    }
    
    this.sessions.set(sessionId, session)
    return sessionId
  }

  getSession(sessionId) {
    const session = this.sessions.get(sessionId)
    if (session) {
      session.lastAccess = new Date()
    }
    return session
  }

  destroySession(sessionId) {
    return this.sessions.delete(sessionId)
  }
}

// 使用例
const sessionManager = new SessionManager()
const sessionId = sessionManager.createSession('user123')
console.log('セッションID:', sessionId)
// セッションID: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

### 5. APIキー生成

APIアクセス用の一意キーを生成：

```javascript
// APIキー管理
class ApiKeyManager {
  constructor() {
    this.apiKeys = new Map()
  }

  generateApiKey(userId, permissions = []) {
    const apiKey = generateUUID()
    const keyInfo = {
      key: apiKey,
      userId: userId,
      permissions: permissions,
      createdAt: new Date(),
      lastUsed: null,
      isActive: true
    }
    
    this.apiKeys.set(apiKey, keyInfo)
    return apiKey
  }

  validateApiKey(apiKey) {
    const keyInfo = this.apiKeys.get(apiKey)
    if (keyInfo && keyInfo.isActive) {
      keyInfo.lastUsed = new Date()
      return keyInfo
    }
    return null
  }

  revokeApiKey(apiKey) {
    const keyInfo = this.apiKeys.get(apiKey)
    if (keyInfo) {
      keyInfo.isActive = false
      return true
    }
    return false
  }
}

// 使用例
const apiManager = new ApiKeyManager()
const apiKey = apiManager.generateApiKey('user123', ['read', 'write'])
console.log('APIキー:', apiKey)
// APIキー: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

## 🔧 技術詳解

### UUIDバージョン

異なるバージョンのUUIDには異なる生成方式があります：

**UUID v1（時間ベース）：**
- タイムスタンプ（60ビット）を含む
- クロックシーケンス（14ビット）を含む
- ノード識別子（48ビット、通常はMACアドレス）を含む
- 生成時間と場所を推測可能

**UUID v4（ランダム）：**
- 122ビットのランダム数
- 6ビットのバージョンとバリアント識別子
- 衝突確率は約1/2^122
- 最も一般的に使用されるバージョン

**UUID v5（名前ベース）：**
- SHA-1ハッシュアルゴリズムを使用
- 名前空間と名前に基づいて生成
- 同じ入力は常に同じUUIDを生成

### 形式構造

標準UUID形式：`xxxxxxxx-xxxx-Mxxx-Nxxx-xxxxxxxxxxxx`

- **M**：バージョン番号（1、4、5など）
- **N**：バリアント識別子（通常は8、9、A、B）
- **x**：16進数字（0-9、A-F）

### 衝突確率

UUID v4の衝突確率は極めて低い：

- 合計2^122種類の可能なUUID
- 10^18個のUUIDを生成した場合の衝突確率は約50%
- 実際の応用では一意と見なすことができる

## 💡 使用のコツ

- **バージョン選択**：一般的な場合はUUID v4を使用、時間情報が必要な場合はv1を使用
- **ストレージ最適化**：データベースでBINARY(16)を使用してスペースを節約
- **インデックス性能**：UUIDを主キーとして使用する際はデータベースインデックス性能への影響に注意
- **形式統一**：同一システム内でUUID形式の一貫性を保持

## ⚠️ 注意事項

- **性能影響**：UUIDを主キーとして使用するとデータベース性能に影響する可能性
- **ソート問題**：UUIDは生成時間でソートできない（v1を使用しない限り）
- **ストレージスペース**：UUIDは整数IDよりも多くのストレージスペースを占有（36文字または16バイト）
- **可読性**：UUIDは自動増分IDほど直感的で読みやすくない

## 🚀 使い方

1. **バージョン選択**：要件に応じてUUIDバージョンを選択
2. **数量設定**：生成するUUIDの数量を選択
3. **形式選択**：標準形式またはハイフンなし形式を選択
4. **UUID生成**：生成ボタンをクリックしてUUIDを作成
5. **コピー使用**：コピーボタンをクリックしてUUIDをクリップボードにコピー

> **ヒント**：このツールはクライアント側でローカルにUUIDを生成し、データをサーバーにアップロードしないため、プライバシーとセキュリティが保証されます。
