# 文字列反転ツール

文字列反転は基本的なテキスト処理操作で、文字列内の文字順序を完全に逆転させます。このツールは複数の反転モードをサポートし、全体反転、行別反転、単語別反転などを含み、テキスト処理、データ変換、プログラミング練習などのシーンに適用されます。

## ✨ 主な機能

- 🔄 **複数反転モード**：全体反転、行別反転、単語別反転をサポート
- 🌐 **Unicode対応**：日本語、絵文字などのUnicode文字を正しく処理
- ⚡ **リアルタイム処理**：入力テキストと同時に反転結果を表示
- 📋 **ワンクリックコピー**：反転結果を直接コピーして使用可能
- 🔧 **バッチ処理**：複数行テキストのバッチ反転をサポート

## 📖 使用例

### 全体反転

**入力テキスト：**
```
Hello World!
```

**反転結果：**
```
!dlroW olleH
```

### 日本語テキスト反転

**入力テキスト：**
```
ツールミへようこそ
```

**反転結果：**
```
そこうよへミルーツ
```

### 行別反転

**入力テキスト：**
```
第一行のテキスト
第二行のテキスト
第三行のテキスト
```

**反転結果：**
```
トスキテの行一第
トスキテの行二第
トスキテの行三第
```

### 単語別反転

**入力テキスト：**
```
Hello World Welcome
```

**反転結果：**
```
Welcome World Hello
```

## 🎯 応用シーン

### 1. データ処理

データ処理と変換での文字列反転の使用：

```javascript
// データマスキング処理
function maskSensitiveData(data) {
  // 機密データを反転して簡単な難読化として使用
  const reversed = data.split('').reverse().join('')
  return btoa(reversed) // さらにBase64エンコード
}

// 使用例
const sensitiveInfo = "ユーザー機密情報"
const masked = maskSensitiveData(sensitiveInfo)
console.log('マスキング後:', masked)

// 復号化関数
function unmaskSensitiveData(maskedData) {
  const decoded = atob(maskedData)
  return decoded.split('').reverse().join('')
}

const original = unmaskSensitiveData(masked)
console.log('元のデータ:', original)
```

### 2. テキストゲーム

文字ゲームとパズルの作成：

```javascript
// 回文検出器
function isPalindrome(str) {
  const cleaned = str.toLowerCase().replace(/[^a-z0-9\u3040-\u309f\u30a0-\u30ff\u4e00-\u9faf]/g, '')
  const reversed = cleaned.split('').reverse().join('')
  return cleaned === reversed
}

// テスト例
console.log(isPalindrome("しんぶんし")) // true
console.log(isPalindrome("A man a plan a canal Panama")) // true
console.log(isPalindrome("race a car")) // false

// 文字パズル生成器
function generateWordPuzzle(sentence) {
  const words = sentence.split(' ')
  const puzzles = words.map(word => {
    return {
      original: word,
      reversed: word.split('').reverse().join(''),
      hint: `${word.length} 文字`
    }
  })
  return puzzles
}

// 使用例
const puzzles = generateWordPuzzle("ツールミ オンライン ツール")
console.log(puzzles)
// [
//   { original: "ツールミ", reversed: "ミルーツ", hint: "4 文字" },
//   { original: "オンライン", reversed: "ンイラノ", hint: "5 文字" },
//   { original: "ツール", reversed: "ルーツ", hint: "3 文字" }
// ]
```

### 3. プログラミング練習

アルゴリズム学習とプログラミング練習用：

```javascript
// 文字列反転の複数実装方法

// 方法1：組み込みメソッドを使用
function reverseString1(str) {
  return str.split('').reverse().join('')
}

// 方法2：ループを使用
function reverseString2(str) {
  let result = ''
  for (let i = str.length - 1; i >= 0; i--) {
    result += str[i]
  }
  return result
}

// 方法3：再帰を使用
function reverseString3(str) {
  if (str === '') return ''
  return reverseString3(str.substr(1)) + str.charAt(0)
}

// 方法4：双方向ポインタを使用
function reverseString4(str) {
  const arr = str.split('')
  let left = 0
  let right = arr.length - 1
  
  while (left < right) {
    [arr[left], arr[right]] = [arr[right], arr[left]]
    left++
    right--
  }
  
  return arr.join('')
}

// 性能テスト
function performanceTest(str) {
  const methods = [reverseString1, reverseString2, reverseString3, reverseString4]
  const methodNames = ['組み込みメソッド', 'ループ', '再帰', '双方向ポインタ']
  
  methods.forEach((method, index) => {
    const start = performance.now()
    const result = method(str)
    const end = performance.now()
    console.log(`${methodNames[index]}: ${end - start}ms`)
  })
}

// テスト
const testString = "これは性能テスト用の長い文字列です".repeat(1000)
performanceTest(testString)
```

### 4. テキストアート

テキストアートとエフェクトの作成：

```javascript
// テキストアニメーションエフェクト
class TextAnimation {
  constructor(element, text) {
    this.element = element
    this.originalText = text
    this.currentText = text
  }

  // 文字ごとの反転アニメーション
  async reverseAnimation(speed = 100) {
    const chars = this.originalText.split('')
    
    for (let i = 0; i < chars.length; i++) {
      // 最初のi文字を反転
      const reversed = chars.slice(0, i + 1).reverse()
      const remaining = chars.slice(i + 1)
      this.currentText = [...reversed, ...remaining].join('')
      
      this.element.textContent = this.currentText
      await this.delay(speed)
    }
  }

  // 波状反転エフェクト
  async waveReverse(speed = 50) {
    const chars = this.originalText.split('')
    const length = chars.length
    
    for (let wave = 0; wave < length; wave++) {
      const newChars = [...chars]
      
      // 波状エフェクトを作成
      for (let i = 0; i < length; i++) {
        const distance = Math.abs(i - wave)
        if (distance <= 2) {
          // 波の範囲内の文字を反転
          const start = Math.max(0, wave - 2)
          const end = Math.min(length, wave + 3)
          const section = chars.slice(start, end).reverse()
          newChars.splice(start, section.length, ...section)
        }
      }
      
      this.element.textContent = newChars.join('')
      await this.delay(speed)
    }
    
    // 最終的に完全反転
    this.element.textContent = chars.reverse().join('')
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 使用例
const textElement = document.getElementById('animated-text')
const animation = new TextAnimation(textElement, 'ツールミオンラインプラットフォーム')

// アニメーション開始
animation.reverseAnimation(200)
```

### 5. データ検証

データ検証と整合性チェック用：

```javascript
// 簡単なデータ整合性チェッカー
class DataIntegrityChecker {
  // チェックサムを生成
  static generateChecksum(data) {
    const reversed = data.split('').reverse().join('')
    const combined = data + reversed
    
    // 簡単なハッシュアルゴリズム
    let hash = 0
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 32ビット整数に変換
    }
    
    return Math.abs(hash).toString(36)
  }

  // データ整合性を検証
  static verifyIntegrity(data, checksum) {
    const calculatedChecksum = this.generateChecksum(data)
    return calculatedChecksum === checksum
  }
}

// 使用例
const originalData = "重要なビジネスデータ"
const checksum = DataIntegrityChecker.generateChecksum(originalData)
console.log('チェックサム:', checksum)

// データ検証
const isValid = DataIntegrityChecker.verifyIntegrity(originalData, checksum)
console.log('データ整合性:', isValid ? '正常' : '異常')

// データ改ざんをシミュレート
const tamperedData = "重要なビジネスデータが変更された"
const isTamperedValid = DataIntegrityChecker.verifyIntegrity(tamperedData, checksum)
console.log('改ざんデータ検証:', isTamperedValid ? '正常' : '異常')
```

## 🔧 技術詳解

### Unicode文字処理

Unicode文字の反転を正しく処理：

**基本文字反転：**
- ASCII文字：直接バイト反転
- 日本語文字：Unicodeコードポイントで反転
- 絵文字：複合文字の特別処理が必要

**複雑文字処理：**
```javascript
// Unicode文字の反転を正しく処理
function reverseUnicode(str) {
  // Array.fromを使用してUnicode文字を正しく分割
  return Array.from(str).reverse().join('')
}

// 異なるタイプの文字をテスト
console.log(reverseUnicode("Hello 世界 🌍"))
// 出力: 🌍 界世 olleH
```

### 性能最適化

異なる反転方法の性能比較：

**方法性能ランキング：**
1. 組み込みメソッド：`split('').reverse().join('')`
2. 双方向ポインタアルゴリズム：大きな文字列に適用
3. ループ構築：メモリ効率が高い
4. 再帰方法：簡潔だが性能が劣る

## 💡 使用のコツ

- **Unicode処理**：`Array.from()` を使用して複合文字を正しく処理
- **性能考慮**：大きなテキストには双方向ポインタアルゴリズムを推奨
- **メモリ最適化**：過度な一時文字列の作成を避ける
- **特殊文字**：改行文字と特殊記号の処理に注意

## ⚠️ 注意事項

- **文字エンコーディング**：UTF-8エンコードの文字を正しく処理することを確保
- **複合文字**：絵文字などの複合文字は特別処理が必要
- **性能影響**：超長テキストの反転は性能に影響する可能性
- **メモリ使用**：大きなテキスト処理時はメモリ占有に注意

## 🚀 使い方

1. **テキスト入力**：入力ボックスに反転したいテキストを入力
2. **モード選択**：全体反転、行別反転、または単語別反転を選択
3. **結果確認**：反転結果がリアルタイムで出力エリアに表示
4. **コピー使用**：「コピー」ボタンをクリックして結果をクリップボードにコピー
5. **バッチ処理**：複数行テキストのバッチ反転操作をサポート

> **ヒント**：このツールはクライアント側でローカル処理を行い、テキストコンテンツをサーバーにアップロードしないため、プライバシーとセキュリティが保証されます。
