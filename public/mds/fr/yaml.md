# Outil de Format YAML

YAML (YAML Ain't Markup Language) est un standard de sérialisation de données lisible par l'homme, couramment utilisé dans les fichiers de configuration, l'échange de données et la création de documents. Cet outil fournit des fonctions de validation, d'embellissement et de compression de format YAML pour aider les développeurs à traiter et gérer les fichiers YAML.

## ✨ Caractéristiques Principales

- 🎯 **Validation de Format** : Vérifie les erreurs de syntaxe YAML et fournit des suggestions détaillées
- 🎨 **Embellissement de Format** : Formate automatiquement le contenu YAML avec indentation et format
- 🗜️ **Compression de Contenu** : Supprime les espaces et commentaires inutiles pour compresser la taille du fichier
- 📋 **Copie en Un Clic** : Les résultats de format peuvent être copiés directement pour utilisation
- 🔧 **Localisation d'Erreurs** : Localise avec précision la position des erreurs de syntaxe

## 📖 Exemples d'Utilisation

### Exemple de Format

**YAML Original :**
```yaml
nom:ToolMi
version:1.0.0
caracteristiques:
- Format
- Validation
- Compression
config:
  debug:true
  port:3000
```

**Après le Format :**
```yaml
nom: ToolMi
version: 1.0.0
caracteristiques:
  - Format
  - Validation
  - Compression
config:
  debug: true
  port: 3000
```

### Exemple de Structure Complexe

**YAML d'Entrée :**
```yaml
baseDeDonnees:
  hote: localhost
  port: 5432
  identifiants:
    nomUtilisateur: admin
    motDePasse: secret
  pools:
    - nom: pool_lecture
      taille: 10
    - nom: pool_ecriture
      taille: 5
```

**Après Validation et Embellissement :**
```yaml
baseDeDonnees:
  hote: localhost
  port: 5432
  identifiants:
    nomUtilisateur: admin
    motDePasse: secret
  pools:
    - nom: pool_lecture
      taille: 10
    - nom: pool_ecriture
      taille: 5
```

## 🎯 Scénarios d'Application

### 1. Gestion de Fichiers de Configuration

Organiser et valider les fichiers de configuration d'applications :

```yaml
# Fichier de configuration d'application (config.yml)
app:
  nom: ToolMi
  version: 1.0.0
  environnement: production
  
serveur:
  hote: 0.0.0.0
  port: 3000
  ssl:
    active: true
    cheminCert: /etc/ssl/certs/app.crt
    cheminCle: /etc/ssl/private/app.key
    
baseDeDonnees:
  type: postgresql
  hote: localhost
  port: 5432
  nom: toolmi_db
  nomUtilisateur: ${DB_USER}
  motDePasse: ${DB_PASSWORD}
  pool:
    connexionsMin: 2
    connexionsMax: 10
    
redis:
  hote: localhost
  port: 6379
  motDePasse: ${REDIS_PASSWORD}
  db: 0
  
journalisation:
  niveau: info
  format: json
  sorties:
    - type: fichier
      chemin: /var/log/app.log
    - type: console
```

### 2. Fichiers Docker Compose

Gestion de configuration d'orchestration de conteneurs Docker :

```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=basededonnees
    depends_on:
      - basededonnees
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - reseau-app
      
  basededonnees:
    image: postgres:13
    environment:
      POSTGRES_DB: toolmi_db
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - donnees_postgres:/var/lib/postgresql/data
    networks:
      - reseau-app
      
  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - donnees_redis:/data
    networks:
      - reseau-app

volumes:
  donnees_postgres:
  donnees_redis:

networks:
  reseau-app:
    driver: bridge
```

### 3. Configuration CI/CD

Configuration de flux de travail GitHub Actions :

```yaml
# .github/workflows/deploy.yml
nom: Déploiement en Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  VERSION_NODE: '18'
  REGISTRE: ghcr.io
  NOM_IMAGE: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout du code
        uses: actions/checkout@v3
        
      - name: Configurer Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.VERSION_NODE }}
          cache: 'npm'
          
      - name: Installer les dépendances
        run: npm ci
        
      - name: Exécuter les tests
        run: npm test
        
      - name: Exécuter le linting
        run: npm run lint
        
  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout du code
        uses: actions/checkout@v3
        
      - name: Construire l'image Docker
        run: |
          docker build -t ${{ env.REGISTRE }}/${{ env.NOM_IMAGE }}:latest .
          
      - name: Push vers le registre
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ${{ env.REGISTRE }} -u ${{ github.actor }} --password-stdin
          docker push ${{ env.REGISTRE }}/${{ env.NOM_IMAGE }}:latest
```

### 4. Configuration Kubernetes

Fichiers de configuration de déploiement Kubernetes :

```yaml
# deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  labels:
    app: toolmi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: toolmi
  template:
    metadata:
      labels:
        app: toolmi
    spec:
      containers:
        - name: app
          image: toolmi/app:latest
          ports:
            - containerPort: 3000
          env:
            - name: NODE_ENV
              value: "production"
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: db-host
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
spec:
  selector:
    app: toolmi
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer
```

## 🔧 Détails Techniques

### Règles de Syntaxe YAML

Règles de base de syntaxe YAML :

**Règles d'Indentation :**
- Utiliser des espaces pour l'indentation, les tabulations ne sont pas autorisées
- Les éléments du même niveau nécessitent un alignement à gauche
- Les éléments enfants nécessitent plus d'indentation que les éléments parents

**Types de Données :**
- Chaînes : Peuvent ne pas avoir de guillemets, les caractères spéciaux nécessitent des guillemets
- Nombres : Entiers et nombres à virgule flottante
- Booléens : true/false, yes/no, on/off
- Valeurs null : null, ~, ou vide

**Types de Collection :**
- Tableaux : Utiliser - pour afficher les éléments de liste
- Objets : Utiliser key: value pour afficher les paires clé-valeur
- Imbrication : Compatible avec structure imbriquée de n'importe quel niveau

### Erreurs Communes

Erreurs communes que l'outil de format YAML peut détecter :

**Erreurs d'Indentation :**
```yaml
# Exemple incorrect
config:
  debug: true
 port: 3000  # Indentation ne correspond pas

# Exemple correct
config:
  debug: true
  port: 3000
```

**Problèmes de Guillemets :**
```yaml
# Exemple incorrect
message: C'est un test  # Guillemet simple non échappé

# Exemple correct
message: "C'est un test"
# ou
message: 'C''est un test'
```

**Format de Liste :**
```yaml
# Exemple incorrect
caracteristiques:
- format
 - valider  # Erreur d'indentation

# Exemple correct
caracteristiques:
  - format
  - valider
```

## 💡 Conseils d'Utilisation

- **Indentation Cohérente** : Toujours utiliser le même nombre d'espaces pour l'indentation (2 espaces recommandés)
- **Usage de Guillemets** : Les chaînes contenant des caractères spéciaux doivent être entre guillemets
- **Normes de Commentaires** : Utiliser # pour ajouter des commentaires, ajouter un espace avant le commentaire
- **Chaînes Multilignes** : Utiliser | ou > pour gérer le texte multiligne

## ⚠️ Notes Importantes

- **Interdiction de Tabulations** : YAML n'autorise pas les tabulations, seuls les espaces peuvent être utilisés
- **Sensible à l'Indentation** : Les erreurs d'indentation causeront un échec d'analyse
- **Caractères Spéciaux** : Les caractères spéciaux comme les deux-points, guillemets nécessitent une gestion correcte
- **Format d'Encodage** : S'assurer que le fichier utilise l'encodage UTF-8

## 🚀 Comment Utiliser

1. **Saisie YAML** : Collez le contenu YAML à traiter dans la zone de saisie
2. **Sélection d'Opération** : Cliquez sur les boutons "Formater", "Valider" ou "Compresser"
3. **Vérification des Résultats** : Vérifiez le YAML traité dans la zone de sortie
4. **Utilisation de Copie** : Cliquez sur le bouton "Copier" pour copier les résultats dans le presse-papiers
5. **Correction d'Erreurs** : Corrigez les problèmes de syntaxe basés sur les suggestions d'erreur

> **Conseil** : Cet outil traite localement côté client et ne télécharge pas le contenu YAML vers le serveur, garantissant la sécurité des données.
