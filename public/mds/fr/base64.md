# Outil d'Encodage et de Décodage Base64

Base64 est une méthode d'encodage utilisée pour représenter des données binaires sous forme de texte. Il est principalement utilisé dans l'envoi d'e-mails, le développement web, le stockage de données et d'autres scénarios pour transférer et stocker en toute sécurité des données binaires sous forme de texte.

## ✨ Caractéristiques Principales

- 🔄 **Conversion Bidirectionnelle** : Compatible avec l'encodage et le décodage Base64 de texte
- 🌐 **Support Multilingue** : Compatible avec les caractères Unicode comme le français, le chinois, l'anglais
- 📁 **Traitement de Fichiers** : Conversion Base64 de fichiers comme les images et documents
- 📋 **Copie en Un Clic** : Les résultats de conversion peuvent être copiés directement pour utilisation
- ⚡ **Conversion en Temps Réel** : Affiche les résultats en même temps que la saisie

## 📖 Exemples d'Utilisation

### Encodage de Texte

**Texte d'Entrée :**
```
Bonjour, ToolMi !
```

**Résultat d'Encodage Base64 :**
```
Qm9uam91ciwgVG9vbE1pICE=
```

### Encodage d'URL

**URL d'Entrée :**
```
https://www.toolmi.com/rechercher?q=outil Base64
```

**Résultat d'Encodage Base64 :**
```
aHR0cHM6Ly93d3cudG9vbG1pLmNvbS9yZWNoZXJjaGVyP3E9b3V0aWwgQmFzZTY0
```

### Encodage de Données JSON

**JSON d'Entrée :**
```json
{
  "nom": "Pierre Dupont",
  "email": "<EMAIL>",
  "age": 30
}
```

**Résultat d'Encodage Base64 :**
```
****************************************************************************************************
```

## 🎯 Scénarios d'Application

### 1. Développement Web

Exemples d'utilisation de Base64 dans le développement web :

```html
<!-- Intégration d'image Base64 -->
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA6VP8EQAAAABJRU5ErkJggg==" alt="Image transparente 1x1">

<!-- Intégration de police Base64 en CSS -->
@font-face {
  font-family: 'PolicePersonnalisee';
  src: url('data:font/woff2;base64,d09GMgABAAAAAA...') format('woff2');
}

<!-- Traitement de données Base64 en JavaScript -->
<script>
// Encodage Base64
const encode = btoa('Bonjour !');
console.log(encode); // Qm9uam91ciAh

// Décodage Base64
const decode = atob(encode);
console.log(decode); // Bonjour !
</script>
```

### 2. Développement d'API

Exemple d'utilisation de Base64 dans la communication API :

```javascript
// API de téléchargement de fichiers
const uploaderFichier = async (fichier) => {
  const lecteur = new FileReader();
  
  return new Promise((resoudre, rejeter) => {
    lecteur.onload = async () => {
      const donneesBase64 = lecteur.result.split(',')[1];
      
      try {
        const reponse = await fetch('/api/upload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            nomFichier: fichier.name,
            donnees: donneesBase64,
            typeMime: fichier.type
          })
        });
        
        const resultat = await reponse.json();
        resoudre(resultat);
      } catch (erreur) {
        rejeter(erreur);
      }
    };
    
    lecteur.onerror = rejeter;
    lecteur.readAsDataURL(fichier);
  });
};

// Génération d'en-tête d'authentification
const creerEnTeteAuth = (utilisateur, motDePasse) => {
  const identifiants = `${utilisateur}:${motDePasse}`;
  const encode = btoa(identifiants);
  return `Basic ${encode}`;
};

// Exemple d'utilisation
const enTeteAuth = creerEnTeteAuth('<EMAIL>', 'motdepasse123');
console.log(enTeteAuth); // Basic ****************************************************
```

### 3. Stockage de Données

Utilisation de Base64 dans le stockage local :

```javascript
// Sauvegarder les données de configuration
const sauvegarderConfigUtilisateur = (configuration) => {
  const configurationJson = JSON.stringify(configuration);
  const configurationEncodee = btoa(configurationJson);
  localStorage.setItem('configurationUtilisateur', configurationEncodee);
};

// Charger les données de configuration
const chargerConfigUtilisateur = () => {
  const configurationEncodee = localStorage.getItem('configurationUtilisateur');
  if (configurationEncodee) {
    try {
      const configurationJson = atob(configurationEncodee);
      return JSON.parse(configurationJson);
    } catch (erreur) {
      console.error('Erreur lors du chargement de la configuration:', erreur);
      return null;
    }
  }
  return null;
};

// Exemple d'utilisation
const configurationUtilisateur = {
  theme: 'sombre',
  langue: 'fr',
  notifications: true
};

sauvegarderConfigUtilisateur(configurationUtilisateur);
const configurationChargee = chargerConfigUtilisateur();
console.log(configurationChargee);
```

### 4. Envoi d'E-mail

Encodage Base64 des pièces jointes d'e-mail :

```javascript
// Exemple d'envoi d'e-mail en Node.js
const nodemailer = require('nodemailer');
const fs = require('fs');

const envoyerEmailAvecPieceJointe = async (destinataire, sujet, texte, cheminFichier) => {
  // Encoder le fichier en Base64
  const contenuFichier = fs.readFileSync(cheminFichier);
  const contenuBase64 = contenuFichier.toString('base64');
  
  const transporteur = nodemailer.createTransporter({
    service: 'gmail',
    auth: {
      user: '<EMAIL>',
      pass: 'votre-mot-de-passe'
    }
  });

  const optionsEmail = {
    from: '<EMAIL>',
    to: destinataire,
    subject: sujet,
    text: texte,
    attachments: [
      {
        filename: 'document.pdf',
        content: contenuBase64,
        encoding: 'base64'
      }
    ]
  };

  try {
    const resultat = await transporteur.sendMail(optionsEmail);
    console.log('E-mail envoyé avec succès:', resultat.messageId);
    return resultat;
  } catch (erreur) {
    console.error('Erreur lors de l\'envoi de l\'e-mail:', erreur);
    throw erreur;
  }
};
```

## 🔧 Détails Techniques

### Principe d'Encodage Base64

Fonctionnement de l'encodage Base64 :

**Jeu de Caractères :**
- A-Z (26 caractères)
- a-z (26 caractères)  
- 0-9 (10 caractères)
- +, / (2 caractères)
- = (caractère de remplissage)

**Étapes d'Encodage :**
1. Lire les données d'entrée par groupes de 8 bits
2. Diviser 3 octets (24 bits) en 4 groupes de 6 bits
3. Convertir chaque groupe de 6 bits au caractère Base64 correspondant
4. Ajouter des caractères de remplissage "=" si nécessaire

### Encodage de Caractères

Traitement des caractères français :

```javascript
// Conversion Base64 avec encodage UTF-8
function encoderUTF8VersBase64(str) {
  // Convertir la chaîne en tableau d'octets UTF-8
  const octetsUtf8 = new TextEncoder().encode(str);
  
  // Convertir le tableau d'octets en chaîne
  let binaire = '';
  octetsUtf8.forEach(octet => {
    binaire += String.fromCharCode(octet);
  });
  
  // Encodage Base64
  return btoa(binaire);
}

function decoderBase64VersUtf8(base64) {
  // Décodage Base64
  const binaire = atob(base64);
  
  // Convertir la chaîne en tableau d'octets
  const octets = new Uint8Array(binaire.length);
  for (let i = 0; i < binaire.length; i++) {
    octets[i] = binaire.charCodeAt(i);
  }
  
  // Décodage UTF-8
  return new TextDecoder().decode(octets);
}

// Exemple d'utilisation
const original = "Bonjour, le monde !";
const encode = encoderUTF8VersBase64(original);
const decode = decoderBase64VersUtf8(encode);

console.log('Chaîne originale:', original);
console.log('Résultat encodé:', encode);
console.log('Résultat décodé:', decode);
```

## 💡 Conseils d'Utilisation

- **Encodage de Caractères** : Faire attention à l'encodage UTF-8 lors du traitement des caractères français
- **Taille des Données** : La taille après encodage Base64 est environ 1,33 fois celle des données originales
- **Performance** : Faire attention à l'utilisation de la mémoire lors du traitement de gros fichiers
- **Sécurité** : Base64 n'est pas un chiffrement, il n'a qu'un effet d'obfuscation des données

## ⚠️ Notes Importantes

- **Sécurité** : Base64 n'est pas un chiffrement, donc ne convient pas pour protéger des données confidentielles
- **Augmentation de Taille** : La taille des données après encodage augmente d'environ 33%
- **Limitations de Caractères** : Certains systèmes peuvent avoir des limitations sur les chaînes Base64 longues
- **Traitement des Sauts de Ligne** : Différents systèmes peuvent gérer les sauts de ligne dans les chaînes Base64 différemment

## 🚀 Comment Utiliser

1. **Saisie de Texte** : Entrez le texte à encoder/décoder dans la zone de saisie
2. **Sélection d'Opération** : Cliquez sur le bouton "Encoder" ou "Décoder"
3. **Vérification des Résultats** : Les résultats de conversion s'affichent dans la zone de sortie
4. **Utilisation de Copie** : Cliquez sur le bouton "Copier" pour copier les résultats dans le presse-papiers
5. **Traitement de Fichiers** : Glissez-déposez des fichiers pour la conversion Base64

> **Conseil** : Cet outil traite localement côté client et ne télécharge pas de données vers le serveur, garantissant la confidentialité et la sécurité.
