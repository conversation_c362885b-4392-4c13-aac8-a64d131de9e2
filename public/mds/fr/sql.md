# Outil de Format SQL

SQL (Structured Query Language) est le langage standard pour la gestion et la manipulation de bases de données relationnelles. Cet outil fournit le formatage, l'embellissement et la compression des déclarations SQL pour améliorer la lisibilité et la maintenabilité du code.

## ✨ Caractéristiques Principales

- 🎨 **Format Automatique** : Formate automatiquement les déclarations SQL dans un style lisible
- 🗜️ **Compression de Code** : Supprime les espaces et sauts de ligne inutiles pour réduire la taille du fichier
- ✅ **Validation de Syntaxe** : Détecte les erreurs de syntaxe SQL de base
- 📋 **Copie en Un Clic** : Les déclarations SQL formatées peuvent être copiées directement
- 🔧 **Personnalisable** : Ajuster le style d'indentation et la configuration des sauts de ligne

## 📖 Exemples d'Utilisation

### Format de Déclaration SELECT de Base

**Avant le format :**
```sql
select u.id,u.nom,u.email,p.titre from utilisateurs u join articles p on u.id=p.id_utilisateur where u.actif=1 and p.publie=1 order by p.cree_le desc limit 10;
```

**Après le format :**
```sql
SELECT 
    u.id,
    u.nom,
    u.email,
    p.titre
FROM utilisateurs u
JOIN articles p ON u.id = p.id_utilisateur
WHERE u.actif = 1
    AND p.publie = 1
ORDER BY p.cree_le DESC
LIMIT 10;
```

### Format de Requête Complexe

**Avant le format :**
```sql
with ventes_mensuelles as(select date_trunc('month',date_commande)as mois,sum(montant_total)as ventes_totales from commandes where date_commande>=current_date-interval'12 months'group by date_trunc('month',date_commande)),ventes_moyennes as(select avg(ventes_totales)as ventes_mensuelles_moyennes from ventes_mensuelles)select m.mois,m.ventes_totales,a.ventes_mensuelles_moyennes,case when m.ventes_totales>a.ventes_mensuelles_moyennes then'Au-dessus de la moyenne'else'En-dessous de la moyenne'end as performance from ventes_mensuelles m cross join ventes_moyennes a order by m.mois;
```

**Après le format :**
```sql
WITH ventes_mensuelles AS (
    SELECT 
        DATE_TRUNC('month', date_commande) AS mois,
        SUM(montant_total) AS ventes_totales
    FROM commandes
    WHERE date_commande >= CURRENT_DATE - INTERVAL '12 months'
    GROUP BY DATE_TRUNC('month', date_commande)
),
ventes_moyennes AS (
    SELECT AVG(ventes_totales) AS ventes_mensuelles_moyennes
    FROM ventes_mensuelles
)
SELECT 
    m.mois,
    m.ventes_totales,
    a.ventes_mensuelles_moyennes,
    CASE 
        WHEN m.ventes_totales > a.ventes_mensuelles_moyennes THEN 'Au-dessus de la moyenne'
        ELSE 'En-dessous de la moyenne'
    END AS performance
FROM ventes_mensuelles m
CROSS JOIN ventes_moyennes a
ORDER BY m.mois;
```

## 🎯 Scénarios d'Application

### 1. Développement de Bases de Données

Standardisation SQL pour les équipes de développement :

```sql
-- Exemple de format standard d'équipe

-- Obtenir informations d'utilisateurs et nombre d'articles
SELECT 
    u.id AS id_utilisateur,
    u.nom_utilisateur,
    u.email,
    u.cree_le AS date_inscription,
    COUNT(p.id) AS nombre_articles,
    MAX(p.cree_le) AS dernier_article
FROM utilisateurs u
LEFT JOIN articles p ON u.id = p.id_utilisateur
WHERE u.statut = 'actif'
    AND u.cree_le >= '2024-01-01'
GROUP BY 
    u.id,
    u.nom_utilisateur,
    u.email,
    u.cree_le
HAVING COUNT(p.id) > 0
ORDER BY 
    nombre_articles DESC,
    u.nom_utilisateur ASC
LIMIT 50;

-- Requête d'analyse des ventes
WITH ventes_quotidiennes AS (
    SELECT 
        DATE(date_commande) AS date_vente,
        SUM(montant_total) AS total_quotidien,
        COUNT(*) AS nombre_commandes,
        AVG(montant_total) AS valeur_commande_moyenne
    FROM commandes
    WHERE date_commande >= CURRENT_DATE - INTERVAL '30 days'
        AND statut = 'termine'
    GROUP BY DATE(date_commande)
),
ventes_avec_tendance AS (
    SELECT 
        *,
        LAG(total_quotidien) OVER (ORDER BY date_vente) AS total_jour_precedent,
        AVG(total_quotidien) OVER (
            ORDER BY date_vente 
            ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
        ) AS moyenne_mobile_7_jours
    FROM ventes_quotidiennes
)
SELECT 
    date_vente,
    total_quotidien,
    nombre_commandes,
    valeur_commande_moyenne,
    ROUND(
        ((total_quotidien - total_jour_precedent) / total_jour_precedent * 100), 2
    ) AS croissance_jour_precedent,
    ROUND(moyenne_mobile_7_jours, 2) AS moyenne_mobile_7_jours
FROM ventes_avec_tendance
ORDER BY date_vente DESC;
```

### 2. Optimisation de Performance

Amélioration de lisibilité des requêtes et analyse de performance :

```sql
-- Requête avant optimisation
SELECT * FROM commandes o, clients c, produits p, elements_commande oi 
WHERE o.id_client = c.id AND oi.id_commande = o.id AND oi.id_produit = p.id 
AND o.date_commande > '2024-01-01' AND c.pays = 'France' AND p.categorie = 'Electronique';

-- Requête après optimisation (formatée)
SELECT 
    o.id AS id_commande,
    o.date_commande,
    o.montant_total,
    c.nom AS nom_client,
    c.email AS email_client,
    p.nom AS nom_produit,
    p.prix AS prix_produit,
    oi.quantite,
    oi.prix_unitaire
FROM commandes o
INNER JOIN clients c ON o.id_client = c.id
INNER JOIN elements_commande oi ON oi.id_commande = o.id
INNER JOIN produits p ON oi.id_produit = p.id
WHERE o.date_commande > '2024-01-01'
    AND c.pays = 'France'
    AND p.categorie = 'Electronique'
ORDER BY o.date_commande DESC;

-- Requête pour analyse d'optimisation d'index
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    c.nom,
    COUNT(o.id) AS nombre_commandes,
    SUM(o.montant_total) AS total_depense
FROM clients c
LEFT JOIN commandes o ON c.id = o.id_client
    AND o.date_commande >= CURRENT_DATE - INTERVAL '1 year'
WHERE c.statut = 'actif'
GROUP BY c.id, c.nom
HAVING SUM(o.montant_total) > 1000
ORDER BY total_depense DESC;
```

### 3. Migration de Données

Organisation SQL pendant la migration de bases de données :

```sql
-- Exemple de script de migration de données

-- 1. Créer nouvelle structure de table
CREATE TABLE utilisateurs_nouveau (
    id BIGSERIAL PRIMARY KEY,
    nom_utilisateur VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    hash_mot_de_passe VARCHAR(255) NOT NULL,
    prenom VARCHAR(100),
    nom VARCHAR(100),
    telephone VARCHAR(20),
    date_naissance DATE,
    statut VARCHAR(20) DEFAULT 'actif',
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    mis_a_jour_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT utilisateurs_statut_check 
        CHECK (statut IN ('actif', 'inactif', 'suspendu')),
    CONSTRAINT utilisateurs_format_email_check 
        CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- 2. Migration de données
INSERT INTO utilisateurs_nouveau (
    nom_utilisateur,
    email,
    hash_mot_de_passe,
    prenom,
    nom,
    telephone,
    statut,
    cree_le
)
SELECT 
    LOWER(TRIM(ancien_nom_utilisateur)) AS nom_utilisateur,
    LOWER(TRIM(ancien_email)) AS email,
    ancien_mot_de_passe AS hash_mot_de_passe,
    INITCAP(TRIM(ancien_prenom)) AS prenom,
    INITCAP(TRIM(ancien_nom)) AS nom,
    REGEXP_REPLACE(ancien_telephone, '[^0-9+\-()]', '', 'g') AS telephone,
    CASE 
        WHEN ancien_actif = 1 THEN 'actif'
        WHEN ancien_actif = 0 THEN 'inactif'
        ELSE 'suspendu'
    END AS statut,
    ancienne_date_creation AS cree_le
FROM utilisateurs_ancien
WHERE ancien_email IS NOT NULL
    AND ancien_email != ''
    AND ancien_nom_utilisateur IS NOT NULL
    AND ancien_nom_utilisateur != '';

-- 3. Vérification d'intégrité des données
SELECT 
    'utilisateurs_ancien' AS nom_table,
    COUNT(*) AS nombre_enregistrements
FROM utilisateurs_ancien
WHERE ancien_email IS NOT NULL AND ancien_email != ''

UNION ALL

SELECT 
    'utilisateurs_nouveau' AS nom_table,
    COUNT(*) AS nombre_enregistrements
FROM utilisateurs_nouveau;

-- 4. Vérification de données dupliquées
SELECT 
    email,
    COUNT(*) AS nombre_doublons
FROM utilisateurs_nouveau
GROUP BY email
HAVING COUNT(*) > 1
ORDER BY nombre_doublons DESC;
```

### 4. Génération de Rapports

SQL organisé pour les rapports d'entreprise :

```sql
-- Rapport de ventes mensuelles
WITH metriques_mensuelles AS (
    SELECT 
        DATE_TRUNC('month', date_commande) AS mois,
        COUNT(DISTINCT id_client) AS clients_uniques,
        COUNT(*) AS total_commandes,
        SUM(montant_total) AS chiffre_affaires_total,
        AVG(montant_total) AS valeur_commande_moyenne,
        MIN(montant_total) AS valeur_commande_min,
        MAX(montant_total) AS valeur_commande_max
    FROM commandes
    WHERE date_commande >= CURRENT_DATE - INTERVAL '12 months'
        AND statut = 'termine'
    GROUP BY DATE_TRUNC('month', date_commande)
),
croissance_mensuelle AS (
    SELECT 
        *,
        LAG(chiffre_affaires_total) OVER (ORDER BY mois) AS ca_mois_precedent,
        LAG(clients_uniques) OVER (ORDER BY mois) AS clients_mois_precedent
    FROM metriques_mensuelles
)
SELECT 
    TO_CHAR(mois, 'YYYY-MM') AS mois_annee,
    clients_uniques,
    total_commandes,
    ROUND(chiffre_affaires_total, 2) AS chiffre_affaires_total,
    ROUND(valeur_commande_moyenne, 2) AS valeur_commande_moyenne,
    ROUND(
        CASE 
            WHEN ca_mois_precedent > 0 THEN
                ((chiffre_affaires_total - ca_mois_precedent) / ca_mois_precedent * 100)
            ELSE 0
        END, 2
    ) AS croissance_ca_pourcentage,
    ROUND(
        CASE 
            WHEN clients_mois_precedent > 0 THEN
                ((clients_uniques - clients_mois_precedent) / clients_mois_precedent * 100)
            ELSE 0
        END, 2
    ) AS croissance_clients_pourcentage
FROM croissance_mensuelle
ORDER BY mois DESC;

-- Analyse par catégorie de produits
SELECT 
    p.categorie,
    COUNT(DISTINCT p.id) AS nombre_produits,
    COUNT(oi.id) AS total_ventes,
    SUM(oi.quantite) AS quantite_totale_vendue,
    SUM(oi.quantite * oi.prix_unitaire) AS chiffre_affaires_total,
    ROUND(AVG(oi.prix_unitaire), 2) AS prix_unitaire_moyen,
    ROUND(
        SUM(oi.quantite * oi.prix_unitaire) / 
        SUM(SUM(oi.quantite * oi.prix_unitaire)) OVER () * 100, 2
    ) AS pourcentage_chiffre_affaires
FROM produits p
INNER JOIN elements_commande oi ON p.id = oi.id_produit
INNER JOIN commandes o ON oi.id_commande = o.id
WHERE o.date_commande >= CURRENT_DATE - INTERVAL '3 months'
    AND o.statut = 'termine'
GROUP BY p.categorie
ORDER BY chiffre_affaires_total DESC;
```

## 🔧 Détails Techniques

### Règles de Format

Règles de base de format SQL :

**Mots-clés :**
- SELECT, FROM, WHERE, etc. en majuscules
- Chaque clause principale commence sur une nouvelle ligne
- Sous-requêtes avec indentation appropriée

**Indentation :**
- Utiliser 2 ou 4 espaces de manière cohérente
- Requêtes imbriquées avec indentation supplémentaire
- Liste de colonnes alignée verticalement

**Sauts de ligne :**
- Requêtes longues divisées en unités logiques
- Conditions JOIN placées de manière lisible
- Déclarations CASE structurées pour l'affichage

### Support de Dialectes de Base de Données

```sql
-- Caractéristiques spécifiques à PostgreSQL
SELECT 
    id,
    nom,
    cree_le::DATE AS date_creation,
    EXTRACT(YEAR FROM cree_le) AS annee_creation
FROM utilisateurs
WHERE cree_le >= CURRENT_DATE - INTERVAL '1 month';

-- Caractéristiques spécifiques à MySQL
SELECT 
    id,
    nom,
    DATE(cree_le) AS date_creation,
    YEAR(cree_le) AS annee_creation
FROM utilisateurs
WHERE cree_le >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH);

-- Caractéristiques spécifiques à SQL Server
SELECT 
    id,
    nom,
    CAST(cree_le AS DATE) AS date_creation,
    YEAR(cree_le) AS annee_creation
FROM utilisateurs
WHERE cree_le >= DATEADD(MONTH, -1, GETDATE());
```

## 💡 Conseils d'Utilisation

- **Cohérence** : Unifier les règles de format au sein de l'équipe
- **Lisibilité** : Ajouter des commentaires appropriés pour les requêtes complexes
- **Performance** : Vérifier la performance des requêtes après le format
- **Contrôle de Version** : Gérer le SQL formaté dans les systèmes de contrôle de version

## ⚠️ Notes Importantes

- **Erreurs de Syntaxe** : Vérifier que la syntaxe SQL soit correcte avant le format
- **Dialectes de Base de Données** : Faire attention aux dialectes de la base de données en usage
- **Performance** : Vérifier que le format n'affecte pas la performance des requêtes
- **Sauvegarde** : Créer une sauvegarde des requêtes importantes avant le format

## 🚀 Comment Utiliser

1. **Saisie SQL** : Collez la déclaration SQL à formater dans la zone de saisie
2. **Sélection d'Option** : Sélectionnez "Formater" ou "Compresser"
3. **Exécuter** : Cliquez sur le bouton pour exécuter le traitement
4. **Vérifier les Résultats** : Le SQL formaté s'affiche dans la zone de sortie
5. **Utilisation de Copie** : Utilisez le bouton "Copier" pour copier les résultats dans le presse-papiers

> **Conseil** : Cet outil traite localement côté client et n'envoie pas de requêtes SQL au serveur, donc les requêtes confidentielles peuvent aussi être formatées en toute sécurité.
