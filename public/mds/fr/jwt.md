# Outil d'Analyse JWT

JWT (JSON Web Token) est un format de jeton compact et sécurisé pour URL utilisé pour transférer des informations de manière sécurisée entre les parties. Il est principalement utilisé dans l'authentification web, l'authentification d'API, l'échange d'informations et d'autres scénarios, étant l'une des technologies importantes du développement web moderne.

## ✨ Caractéristiques Principales

- 🔍 **Analyse Complète** : Analyse détaillée d'Header, Payload et Signature
- ✅ **Validation de Format** : Vérification automatique de l'exactitude de la structure JWT
- 📊 **Visualisation** : Informations structurées affichées au format JSON
- 🔧 **Support de Débogage** : Vérification et débogage de jetons pendant le développement
- 📋 **Copie en Un Clic** : Les résultats d'analyse peuvent être copiés directement

## 📖 Exemples d'Utilisation

### JWT Standard

**JWT d'Entrée :**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlBpZXJyZSBEdXBvbnQiLCJpYXQiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

**Résultat d'Analyse :**

**Header :**
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

**Payload :**
```json
{
  "sub": "1234567890",
  "name": "Pierre Dupont",
  "iat": 1516239022
}
```

**Signature :**
```
SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

### Payload Complexe

**JWT d'Entrée (Claims Complexes) :**
```
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.signature_here
```

**Résultat d'Analyse :**

**Header :**
```json
{
  "alg": "RS256",
  "typ": "JWT",
  "kid": "12345"
}
```

**Payload :**
```json
{
  "iss": "https://auth.toolmi.com",
  "sub": "user_12345",
  "aud": ["api.toolmi.com", "admin.toolmi.com"],
  "exp": 1687536000,
  "iat": 1687449600,
  "nbf": 1687449600,
  "jti": "abcdef-123456",
  "name": "Marie Durand",
  "email": "<EMAIL>",
  "roles": ["user", "admin"],
  "permissions": ["read", "write", "delete"]
}
```

## 🎯 Scénarios d'Application

### 1. Système d'Authentification Web

Authentification d'utilisateurs utilisant JWT :

```javascript
// Processus de connexion
const seConnecter = async (email, motDePasse) => {
  try {
    const reponse = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, motDePasse })
    });

    if (reponse.ok) {
      const donnees = await reponse.json();
      const jeton = donnees.token;
      
      // Sauvegarder JWT dans le stockage local
      localStorage.setItem('authToken', jeton);
      
      // Analyser le contenu du jeton
      const payload = analyserJWT(jeton);
      console.log('Informations utilisateur:', payload);
      
      return { succes: true, utilisateur: payload };
    } else {
      throw new Error('Erreur de connexion');
    }
  } catch (erreur) {
    console.error('Erreur de connexion:', erreur);
    return { succes: false, erreur: erreur.message };
  }
};

// Fonction d'analyse JWT
const analyserJWT = (jeton) => {
  try {
    const base64Url = jeton.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    return JSON.parse(jsonPayload);
  } catch (erreur) {
    console.error('Erreur d\'analyse JWT:', erreur);
    return null;
  }
};

// Vérifier l'état d'authentification
const verifierEtatAuth = () => {
  const jeton = localStorage.getItem('authToken');
  if (!jeton) {
    return { authentifie: false };
  }

  const payload = analyserJWT(jeton);
  if (!payload) {
    return { authentifie: false };
  }

  // Vérifier la date d'expiration du jeton
  const tempsActuel = Math.floor(Date.now() / 1000);
  if (payload.exp && payload.exp < tempsActuel) {
    localStorage.removeItem('authToken');
    return { authentifie: false, raison: 'expire' };
  }

  return { 
    authentifie: true, 
    utilisateur: payload,
    expireDans: payload.exp 
  };
};
```

### 2. Authentification d'API

Utilisation de JWT dans les requêtes d'API :

```javascript
// Classe client API
class ClientAPI {
  constructor(urlBase) {
    this.urlBase = urlBase;
    this.jeton = localStorage.getItem('authToken');
  }

  // Requête avec en-tête d'authentification
  async requete(endpoint, options = {}) {
    const url = `${this.urlBase}${endpoint}`;
    const enTetes = {
      'Content-Type': 'application/json',
      ...options.enTetes
    };

    // Ajouter le jeton JWT à l'en-tête Authorization
    if (this.jeton) {
      enTetes.Authorization = `Bearer ${this.jeton}`;
    }

    try {
      const reponse = await fetch(url, {
        ...options,
        headers: enTetes
      });

      // Si erreur 401, le jeton est invalide
      if (reponse.status === 401) {
        this.gererNonAutorise();
        throw new Error('Authentification requise');
      }

      if (!reponse.ok) {
        throw new Error(`Erreur HTTP! statut: ${reponse.status}`);
      }

      return await reponse.json();
    } catch (erreur) {
      console.error('Erreur de requête API:', erreur);
      throw erreur;
    }
  }

  // Gestion d'erreur d'authentification
  gererNonAutorise() {
    localStorage.removeItem('authToken');
    this.jeton = null;
    // Rediriger vers la page de connexion
    window.location.href = '/login';
  }

  // Mettre à jour le jeton
  mettreAJourJeton(nouveauJeton) {
    this.jeton = nouveauJeton;
    localStorage.setItem('authToken', nouveauJeton);
  }

  // Obtenir le profil utilisateur
  async obtenirProfilUtilisateur() {
    return await this.requete('/api/user/profile');
  }

  // Obtenir des données
  async obtenirDonnees(endpoint) {
    return await this.requete(endpoint);
  }

  // Mettre à jour des données
  async mettreAJourDonnees(endpoint, donnees) {
    return await this.requete(endpoint, {
      method: 'PUT',
      body: JSON.stringify(donnees)
    });
  }
}

// Exemple d'utilisation
const clientApi = new ClientAPI('https://api.toolmi.com');

// Obtenir le profil utilisateur
clientApi.obtenirProfilUtilisateur()
  .then(profil => {
    console.log('Profil utilisateur:', profil);
  })
  .catch(erreur => {
    console.error('Erreur lors de l\'obtention du profil:', erreur);
  });
```

## 🔧 Détails Techniques

### Structure JWT

JWT est composé de trois parties :

**1. Header (En-tête)**
```json
{
  "alg": "HS256",    // Algorithme de signature
  "typ": "JWT"       // Type de jeton
}
```

**2. Payload (Charge utile)**
```json
{
  "iss": "issuer",           // Émetteur
  "sub": "subject",          // Sujet
  "aud": "audience",         // Audience
  "exp": 1234567890,         // Date d'expiration
  "iat": 1234567890,         // Temps d'émission
  "nbf": 1234567890,         // Pas valide avant
  "jti": "jwt-id"            // ID JWT
}
```

**3. Signature**
```
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret
)
```

### Claims Standard

Claims standard utilisés dans JWT :

- **iss (Issuer)** : Émetteur du jeton
- **sub (Subject)** : Sujet du jeton (généralement ID utilisateur)
- **aud (Audience)** : Audience du jeton
- **exp (Expiration Time)** : Date d'expiration (timestamp Unix)
- **iat (Issued At)** : Temps d'émission
- **nbf (Not Before)** : Temps de début de validité
- **jti (JWT ID)** : Identifiant unique du JWT

## 💡 Conseils d'Utilisation

- **Vérification d'Expiration** : Vérifier la date d'expiration du jeton avec le claim exp
- **Sécurité** : Ne pas inclure d'informations confidentielles dans le payload
- **Vérification de Signature** : Toujours vérifier la signature en environnement de production
- **Taille du Jeton** : Faire attention à ce que le payload ne soit pas trop volumineux

## ⚠️ Notes Importantes

- **Informations Confidentielles** : Le payload de JWT n'est pas chiffré, donc ne doit pas inclure d'informations confidentielles
- **Vérification de Signature** : Cet outil analyse seulement, ne vérifie pas les signatures
- **Date d'Expiration** : Ne pas utiliser de jetons expirés
- **Lieu de Stockage** : Faire attention au lieu de stockage des jetons dans le navigateur (prévention XSS)

## 🚀 Comment Utiliser

1. **Saisie JWT** : Collez le jeton JWT à analyser dans la zone de saisie
2. **Analyse Automatique** : Header, Payload et Signature sont analysés automatiquement lors de la saisie
3. **Vérification des Résultats** : Vérifiez les informations détaillées de chaque partie au format JSON
4. **Utilisation de Copie** : Utilisez le bouton "Copier" pour copier les résultats d'analyse dans le presse-papiers
5. **Utilisation en Débogage** : Utilisez pour la vérification et le débogage de jetons pendant le développement

> **Conseil** : Cet outil traite localement côté client et n'envoie pas de jetons JWT au serveur, garantissant la sécurité.
