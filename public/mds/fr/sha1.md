# Outil de Chiffrement de Hachage SHA1

SHA1 (Secure Hash Algorithm 1) est une fonction de hachage cryptographique qui génère une valeur de hachage de 160 bits (40 caractères hexadécimaux) à partir de données d'entrée de n'importe quelle longueur. Il a été utilisé dans la vérification d'intégrité des données, les signatures numériques et les systèmes de contrôle de version, mais est actuellement considéré comme obsolète pour des raisons de sécurité.

## ✨ Caractéristiques Principales

- 🔐 **Génération de Hachage SHA1** : Convertit le texte en valeurs de hachage SHA1 de 160 bits
- 🌐 **Support Multilingue** : Compatible avec les caractères Unicode comme le français, le chinois, l'anglais
- 📊 **Unicité** : Génère toujours la même valeur de hachage pour la même entrée
- 📋 **Copie en Un Clic** : Les valeurs de hachage générées peuvent être copiées directement
- ⚡ **Traitement Rapide** : Affiche les valeurs de hachage en même temps que la saisie

## 📖 Exemples d'Utilisation

### Hachage de Texte

**Texte d'Entrée :**
```
Bonjour, le monde !
```

**Valeur de Hachage SHA1 :**
```
a94a8fe5ccb19ba61c4c0873d391e987982fbbd3
```

### Vérification d'Intégrité de Fichiers

**Contenu de Fichier d'Entrée :**
```
Document important
Version : 1.0
Date de création : 2024-01-15
```

**Valeur de Hachage SHA1 :**
```
2fd4e1c67a2d28fced849ee1bb76e7391b93eb12
```

## 🎯 Scénarios d'Application

### 1. Systèmes de Contrôle de Version

Identification de commits dans Git :

```bash
# Hachage de commits dans Git (basé sur SHA1)
git log --oneline
# a94a8fe Ajouter nouvelle fonctionnalité
# 2fd4e1c Correction de bugs
# 7b3c9d1 Commit initial

# Vérifier le hachage SHA1 de fichier
git hash-object README.md
# 2fd4e1c67a2d28fced849ee1bb76e7391b93eb12
```

### 2. Vérification d'Intégrité des Données

Système de vérification d'intégrité de fichiers :

```javascript
// Classe de vérification d'intégrité de fichiers
class VerificateurIntegriteFichiers {
  constructor() {
    this.checksums = new Map();
  }

  // Calculer checksum SHA1 de fichier
  async calculerSHA1(fichier) {
    return new Promise((resoudre, rejeter) => {
      const lecteur = new FileReader();
      
      lecteur.onload = async (evenement) => {
        try {
          const arrayBuffer = evenement.target.result;
          const hashBuffer = await crypto.subtle.digest('SHA-1', arrayBuffer);
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
          resoudre(hashHex);
        } catch (erreur) {
          rejeter(erreur);
        }
      };
      
      lecteur.onerror = rejeter;
      lecteur.readAsArrayBuffer(fichier);
    });
  }

  // Enregistrer checksum
  async enregistrerChecksum(idFichier, fichier) {
    const checksum = await this.calculerSHA1(fichier);
    this.checksums.set(idFichier, {
      checksum: checksum,
      nomFichier: fichier.name,
      tailleFichier: fichier.size,
      enregistreLe: new Date().toISOString()
    });
    return checksum;
  }

  // Vérifier l'intégrité
  async verifierIntegrite(idFichier, fichierActuel) {
    const enregistrement = this.checksums.get(idFichier);
    if (!enregistrement) {
      return { valide: false, raison: 'Checksum non enregistré' };
    }

    const checksumActuel = await this.calculerSHA1(fichierActuel);
    const estValide = enregistrement.checksum === checksumActuel;

    return {
      valide: estValide,
      checksumOriginal: enregistrement.checksum,
      checksumActuel: checksumActuel,
      nomFichier: enregistrement.nomFichier,
      raison: estValide ? 'Intégrité OK' : 'Le fichier a été modifié'
    };
  }
}

// Exemple d'utilisation
const verificateurIntegrite = new VerificateurIntegriteFichiers();

// Traitement lors de la sélection de fichier
document.getElementById('entreeFichier').addEventListener('change', async (evenement) => {
  const fichier = evenement.target.files[0];
  if (fichier) {
    try {
      const checksum = await verificateurIntegrite.enregistrerChecksum('fichier001', fichier);
      console.log(`Checksum du fichier ${fichier.name}:`, checksum);
    } catch (erreur) {
      console.error('Erreur lors du calcul du checksum:', erreur);
    }
  }
});
```

## 🔧 Détails Techniques

### Algorithme SHA1

Caractéristiques de base de SHA1 :

**Longueur de Hachage :** 160 bits (40 caractères hexadécimaux)
**Taille de Bloc :** 512 bits
**Rondes de Traitement :** 80 rondes
**Sécurité :** Actuellement non recommandé

### Problèmes de Sécurité

Limitations de SHA1 et alternatives :

```javascript
// Problèmes de SHA1
const problemesSha1 = {
  collision: 'Google a démontré des attaques de collision en 2017',
  obsolescence: 'Obsolète dans de nombreux navigateurs et systèmes',
  faiblesse: 'Force cryptographique insuffisante',
  recommandation: 'Il est recommandé d\'utiliser SHA-256 ou supérieur'
};

// Alternatives recommandées
const alternatives = {
  'SHA-256': 'Standard actuel, hachage de 256 bits',
  'SHA-3': 'Hachage le plus récent basé sur Keccak',
  'BLAKE2': 'Fonction de hachage rapide et sûre',
  'BLAKE3': 'Fonction de hachage haute performance la plus récente'
};
```

## 💡 Conseils d'Utilisation

- **Limitation d'Usage** : Utiliser seulement quand la compatibilité avec les systèmes legacy est requise
- **Considérer les Alternatives** : Utiliser SHA-256 ou supérieur pour les nouveaux développements
- **Vérification d'Intégrité** : Limiter à la vérification d'intégrité de données non critiques
- **Plan de Migration** : Planifier la migration de SHA1 vers des fonctions de hachage plus sûres

## ⚠️ Notes Importantes

- **Risque de Sécurité** : SHA1 n'est pas cryptographiquement sûr
- **Attaques de Collision** : Il existe la possibilité que différentes entrées génèrent la même valeur de hachage
- **Obsolète** : Son utilisation n'est pas recommandée dans les nouveaux développements
- **Migration Nécessaire** : Les systèmes existants doivent considérer migrer vers SHA-256 ou supérieur de manière urgente

## 🚀 Comment Utiliser

1. **Saisie de Texte** : Entrez le texte à hacher
2. **Génération Automatique** : La valeur de hachage SHA1 est générée lors de la saisie
3. **Vérification des Résultats** : Vérifiez la valeur de hachage hexadécimale de 40 caractères
4. **Utilisation de Copie** : Copiez la valeur de hachage dans le presse-papiers

> **Important** : SHA1 est actuellement obsolète pour des raisons de sécurité. Il est fortement recommandé d'utiliser SHA-256 ou supérieur pour les nouveaux développements.
