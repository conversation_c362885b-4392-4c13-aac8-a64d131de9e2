# Outil d'Encodage et de Décodage URL

L'encodage URL (encodage en pourcentage) est une méthode d'encodage utilisée pour transférer en toute sécurité des caractères qui ont une signification spéciale dans les URL ou des caractères en dehors du jeu de caractères ASCII. Il est largement utilisé dans le développement web, la communication API, la soumission de formulaires et d'autres scénarios.

## ✨ Caractéristiques Principales

- 🔄 **Conversion Bidirectionnelle** : Compatible avec l'encodage et le décodage URL
- 🌐 **Support Multilingue** : Compatible avec les caractères Unicode comme le français, le chinois, les emojis
- ⚡ **Conversion en Temps Réel** : Affiche les résultats de conversion en même temps que la saisie
- 📋 **Copie en Un Clic** : Les résultats de conversion peuvent être copiés directement
- 🔧 **Détection d'Erreurs** : Détecte automatiquement les formats d'encodage invalides

## 📖 Exemples d'Utilisation

### Encodage URL en Français

**URL d'Entrée :**
```
https://www.example.com/recherche?q=ToolMi
```

**Résultat d'Encodage :**
```
https://www.example.com/recherche?q=ToolMi
```

### Encodage de Paramètres de Requête

**Paramètres d'Entrée :**
```
nom=Pierre Dupont&email=<EMAIL>&message=Bonjour !
```

**Résultat d'Encodage :**
```
nom=Pierre%20Dupont&email=pierre%40example.com&message=Bonjour%20%21
```

### Encodage de Caractères Spéciaux

**Texte d'Entrée :**
```
Hello World! #caractères spéciaux @symboles &esperluette
```

**Résultat d'Encodage :**
```
Hello%20World%21%20%23caract%C3%A8res%20sp%C3%A9ciaux%20%40symboles%20%26esperluette
```

## 🎯 Scénarios d'Application

### 1. Développement Web

Soumission de formulaires et construction d'URL :

```javascript
// Encodage URL de données de formulaire
const encoderDonneesFormulaire = (donneesFormulaire) => {
  const parametres = new URLSearchParams();
  
  for (const [cle, valeur] of Object.entries(donneesFormulaire)) {
    parametres.append(cle, valeur);
  }
  
  return parametres.toString();
};

// Exemple d'utilisation
const donneesFormulaire = {
  nom: 'Pierre Dupont',
  email: '<EMAIL>',
  message: 'Contenu de la demande.',
  categorie: 'Support'
};

const donneesEncodees = encoderDonneesFormulaire(donneesFormulaire);
console.log('Données de formulaire encodées:', donneesEncodees);
// nom=Pierre%20Dupont&email=pierre%40example.com...

// Construction dynamique d'URL
const construireURLRecherche = (urlBase, parametresRecherche) => {
  const url = new URL(urlBase);
  
  for (const [cle, valeur] of Object.entries(parametresRecherche)) {
    url.searchParams.set(cle, valeur);
  }
  
  return url.toString();
};

// Construction d'URL de recherche
const urlRecherche = construireURLRecherche('https://www.example.com/rechercher', {
  q: 'Outils JavaScript',
  categorie: 'Programmation',
  ordre: 'plus récent'
});

console.log('URL de recherche:', urlRecherche);
// https://www.example.com/rechercher?q=Outils%20JavaScript&categorie=Programmation...
```

### 2. Communication API

Gestion de paramètres dans les API RESTful :

```javascript
// Classe client API
class ClientAPI {
  constructor(urlBase) {
    this.urlBase = urlBase;
  }

  // Gestion de paramètres de requête dans les requêtes GET
  async get(endpoint, parametres = {}) {
    const url = new URL(`${this.urlBase}${endpoint}`);
    
    // Encoder les paramètres de manière sûre
    for (const [cle, valeur] of Object.entries(parametres)) {
      if (valeur !== null && valeur !== undefined) {
        url.searchParams.set(cle, valeur);
      }
    }

    try {
      const reponse = await fetch(url.toString());
      
      if (!reponse.ok) {
        throw new Error(`Erreur HTTP! statut: ${reponse.status}`);
      }
      
      return await reponse.json();
    } catch (erreur) {
      console.error('Erreur de requête API:', erreur);
      throw erreur;
    }
  }

  // Envoi de données de formulaire dans les requêtes POST
  async post(endpoint, donnees) {
    const url = `${this.urlBase}${endpoint}`;
    
    // Encoder comme données de formulaire
    const donneesFormulaire = new URLSearchParams();
    for (const [cle, valeur] of Object.entries(donnees)) {
      donneesFormulaire.append(cle, valeur);
    }

    try {
      const reponse = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: donneesFormulaire.toString()
      });

      if (!reponse.ok) {
        throw new Error(`Erreur HTTP! statut: ${reponse.status}`);
      }

      return await reponse.json();
    } catch (erreur) {
      console.error('Erreur POST API:', erreur);
      throw erreur;
    }
  }

  // Téléchargement de fichiers
  async uploaderFichier(endpoint, fichier, donneesSupplementaires = {}) {
    const url = `${this.urlBase}${endpoint}`;
    const donneesFormulaire = new FormData();
    
    // Ajouter le fichier
    donneesFormulaire.append('fichier', fichier);
    
    // Ajouter des données supplémentaires (encodées automatiquement)
    for (const [cle, valeur] of Object.entries(donneesSupplementaires)) {
      donneesFormulaire.append(cle, valeur);
    }

    try {
      const reponse = await fetch(url, {
        method: 'POST',
        body: donneesFormulaire // Content-Type défini automatiquement
      });

      if (!reponse.ok) {
        throw new Error(`Erreur HTTP! statut: ${reponse.status}`);
      }

      return await reponse.json();
    } catch (erreur) {
      console.error('Erreur de téléchargement de fichier:', erreur);
      throw erreur;
    }
  }
}

// Exemple d'utilisation
const clientApi = new ClientAPI('https://api.example.com');

// Appel API de recherche
clientApi.get('/rechercher', {
  q: 'Bibliothèques JavaScript',
  categorie: 'Programmation',
  page: 1,
  limite: 20
}).then(resultats => {
  console.log('Résultats de recherche:', resultats);
}).catch(erreur => {
  console.error('Erreur de recherche:', erreur);
});

// Inscription d'utilisateur
clientApi.post('/utilisateurs', {
  nom: 'Marie Durand',
  email: '<EMAIL>',
  bio: 'Développeuse frontend.'
}).then(utilisateur => {
  console.log('Utilisateur créé avec succès:', utilisateur);
}).catch(erreur => {
  console.error('Erreur lors de la création d\'utilisateur:', erreur);
});
```

### 3. Gestion de l'Historique du Navigateur

Gestion d'état URL dans les SPA :

```javascript
// Gestionnaire d'état URL
class GestionnaireEtatURL {
  constructor() {
    this.etatActuel = this.analyserURLActuelle();
    
    // Support pour les boutons précédent/suivant du navigateur
    window.addEventListener('popstate', (evenement) => {
      this.etatActuel = evenement.state || this.analyserURLActuelle();
      this.auChangementEtat(this.etatActuel);
    });
  }

  // Analyser l'état depuis l'URL actuelle
  analyserURLActuelle() {
    const url = new URL(window.location.href);
    const etat = {};
    
    // Obtenir les paramètres de requête comme état
    for (const [cle, valeur] of url.searchParams.entries()) {
      etat[cle] = decodeURIComponent(valeur);
    }
    
    return etat;
  }

  // Refléter l'état dans l'URL
  mettreAJourURL(nouvelEtat, titre = '') {
    const url = new URL(window.location.href);
    
    // Nettoyer les paramètres de requête existants
    url.search = '';
    
    // Définir le nouvel état comme paramètres de requête
    for (const [cle, valeur] of Object.entries(nouvelEtat)) {
      if (valeur !== null && valeur !== undefined && valeur !== '') {
        url.searchParams.set(cle, valeur);
      }
    }

    // Mettre à jour l'historique du navigateur
    window.history.pushState(nouvelEtat, titre, url.toString());
    this.etatActuel = nouvelEtat;
    this.auChangementEtat(nouvelEtat);
  }

  // Remplacer l'état (ne pas ajouter à l'historique)
  remplacerURL(nouvelEtat, titre = '') {
    const url = new URL(window.location.href);
    url.search = '';
    
    for (const [cle, valeur] of Object.entries(nouvelEtat)) {
      if (valeur !== null && valeur !== undefined && valeur !== '') {
        url.searchParams.set(cle, valeur);
      }
    }

    window.history.replaceState(nouvelEtat, titre, url.toString());
    this.etatActuel = nouvelEtat;
  }

  // Callback au changement d'état
  auChangementEtat(etat) {
    console.log('État URL changé:', etat);
    // Mettre à jour l'état de l'application
    this.mettreAJourEtatApplication(etat);
  }

  // Mettre à jour l'état de l'application
  mettreAJourEtatApplication(etat) {
    // Mettre à jour le formulaire de recherche
    if (etat.q) {
      const entreeRecherche = document.getElementById('entreeRecherche');
      if (entreeRecherche) {
        entreeRecherche.value = etat.q;
      }
    }

    // Mettre à jour les filtres
    if (etat.categorie) {
      const selectCategorie = document.getElementById('selectCategorie');
      if (selectCategorie) {
        selectCategorie.value = etat.categorie;
      }
    }

    // Mettre à jour la pagination
    if (etat.page) {
      this.mettreAJourPagination(parseInt(etat.page));
    }
  }

  // Mettre à jour l'état de recherche
  mettreAJourEtatRecherche(requete, categorie = '', page = 1) {
    const nouvelEtat = {
      q: requete,
      categorie: categorie,
      page: page > 1 ? page : undefined
    };

    this.mettreAJourURL(nouvelEtat, `Recherche: ${requete}`);
  }

  // Obtenir l'état actuel
  obtenirEtatActuel() {
    return { ...this.etatActuel };
  }

  // Obtenir un paramètre spécifique
  obtenirParametre(cle, valeurParDefaut = '') {
    return this.etatActuel[cle] || valeurParDefaut;
  }
}

// Exemple d'utilisation
const gestionnaireEtatURL = new GestionnaireEtatURL();

// Traitement de formulaire de recherche
document.getElementById('formulaireRecherche').addEventListener('submit', (evenement) => {
  evenement.preventDefault();
  
  const donneesFormulaire = new FormData(evenement.target);
  const requete = donneesFormulaire.get('q');
  const categorie = donneesFormulaire.get('categorie');
  
  // Mettre à jour l'état URL
  gestionnaireEtatURL.mettreAJourEtatRecherche(requete, categorie, 1);
  
  // Exécuter la recherche
  effectuerRecherche(requete, categorie, 1);
});

// Traitement de pagination
const gererChangementPage = (page) => {
  const etatActuel = gestionnaireEtatURL.obtenirEtatActuel();
  gestionnaireEtatURL.mettreAJourEtatRecherche(
    etatActuel.q || '',
    etatActuel.categorie || '',
    page
  );
  
  effectuerRecherche(etatActuel.q, etatActuel.categorie, page);
};

// Fonction d'exécution de recherche
const effectuerRecherche = async (requete, categorie, page) => {
  try {
    const resultats = await clientApi.get('/rechercher', {
      q: requete,
      categorie: categorie,
      page: page,
      limite: 20
    });
    
    afficherResultatsRecherche(resultats);
  } catch (erreur) {
    console.error('Erreur de recherche:', erreur);
  }
};
```

## 🔧 Détails Techniques

### Règles d'Encodage

Règles de base d'encodage URL :

**Caractères Réservés :**
- `:` → `%3A`
- `/` → `%2F`
- `?` → `%3F`
- `#` → `%23`
- `[` → `%5B`
- `]` → `%5D`
- `@` → `%40`

**Caractères Non Réservés :**
- `A-Z`, `a-z`, `0-9` → Inchangés
- `-`, `.`, `_`, `~` → Inchangés

**Autres Caractères :**
- Convertir en séquence d'octets UTF-8, puis encoder chaque octet au format `%XX`

### Implémentation en JavaScript

```javascript
// Fonctions standard d'encodage/décodage
const encoderURL = (str) => {
  return encodeURIComponent(str);
};

const decoderURL = (str) => {
  try {
    return decodeURIComponent(str);
  } catch (erreur) {
    console.error('Erreur de décodage:', erreur);
    return str; // Retourner la chaîne originale si le décodage échoue
  }
};

// Fonction d'encodage personnalisée (contrôle plus fin)
const encoderURLPersonnalise = (str, encoderEspaceCommeplus = false) => {
  let encode = encodeURIComponent(str);
  
  if (encoderEspaceCommeplus) {
    encode = encode.replace(/%20/g, '+');
  }
  
  return encode;
};

// Fonction de décodage sûre
const decoderURLSecurise = (str) => {
  try {
    // Convertir + en espace (pour les données de formulaire)
    const normalise = str.replace(/\+/g, ' ');
    return decodeURIComponent(normalise);
  } catch (erreur) {
    console.error('Erreur de décodage:', erreur);
    return str;
  }
};
```

## 💡 Conseils d'Utilisation

- **Sélection de Fonction Appropriée** : Distinguer entre `encodeURIComponent()` et `encodeURI()`
- **Gestion d'Erreurs** : Ne pas oublier la gestion d'exceptions lors du décodage
- **Encodage de Caractères** : Supposer l'encodage UTF-8
- **Tests** : Tester exhaustivement avec des données contenant des caractères spéciaux

## ⚠️ Notes Importantes

- **Double Encodage** : Éviter d'encoder à nouveau des chaînes déjà encodées
- **Erreurs de Décodage** : Des exceptions peuvent survenir lors du décodage de chaînes d'encodage invalides
- **Limitations de Caractères** : Certains systèmes peuvent avoir des limitations sur la longueur d'URL
- **Sécurité** : Effectuer une validation appropriée lors de l'inclusion d'entrée utilisateur dans les URL

## 🚀 Comment Utiliser

1. **Saisie de Texte** : Entrez le texte ou l'URL à encoder/décoder dans la zone de saisie
2. **Sélection d'Opération** : Cliquez sur le bouton "Encoder" ou "Décoder"
3. **Vérification des Résultats** : Les résultats de conversion s'affichent dans la zone de sortie
4. **Utilisation de Copie** : Utilisez le bouton "Copier" pour copier les résultats dans le presse-papiers
5. **Vérification d'Erreurs** : Des messages d'erreur s'affichent pour les formats invalides

> **Conseil** : Cet outil traite localement côté client et n'envoie pas de données d'entrée au serveur, garantissant la protection de la confidentialité.
