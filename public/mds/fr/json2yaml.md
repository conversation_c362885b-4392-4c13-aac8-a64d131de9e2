# Outil de Conversion JSON ⇄ YAML

JSON (JavaScript Object Notation) et YAML (YAML Ain't Markup Language) sont tous deux des formats largement utilisés pour l'échange de données et les fichiers de configuration. Cet outil permet d'effectuer des conversions mutuelles entre JSON et YAML de manière simple.

## ✨ Caractéristiques Principales

- 🔄 **Conversion Bidirectionnelle** : Compatible avec la conversion dans les deux sens JSON vers YAML et YAML vers JSON
- ✅ **Validation de Format** : Vérification de syntaxe des données d'entrée et affichage d'erreurs
- 🎨 **Sortie Belle** : Format dans un style lisible
- 📋 **Copie en Un Clic** : Les résultats de conversion peuvent être copiés directement
- 🌐 **Support Multilingue** : Entièrement compatible avec les caractères Unicode comme le français

## 📖 Exemples d'Utilisation

### Conversion JSON → YAML

**JSON d'Entrée :**
```json
{
  "nom": "ToolMi",
  "version": "1.0.0",
  "description": "Collection d'outils en ligne utiles",
  "caracteristiques": [
    "Conversion JSON",
    "Conversion YAML",
    "Validation de données"
  ],
  "configuration": {
    "debug": true,
    "port": 3000,
    "baseDeDonnees": {
      "hote": "localhost",
      "port": 5432
    }
  }
}
```

**YAML Converti :**
```yaml
nom: ToolMi
version: 1.0.0
description: Collection d'outils en ligne utiles
caracteristiques:
  - Conversion JSON
  - Conversion YAML
  - Validation de données
configuration:
  debug: true
  port: 3000
  baseDeDonnees:
    hote: localhost
    port: 5432
```

### Conversion YAML → JSON

**YAML d'Entrée :**
```yaml
# Configuration d'application
app:
  nom: Application Exemple
  version: 2.1.0
  
serveur:
  hote: 0.0.0.0
  port: 8080
  ssl:
    active: true
    cheminCertificat: /etc/ssl/cert.pem
    
baseDeDonnees:
  type: postgresql
  hote: localhost
  port: 5432
  identifiants:
    utilisateur: admin
    motDePasse: secret
```

**JSON Converti :**
```json
{
  "app": {
    "nom": "Application Exemple",
    "version": "2.1.0"
  },
  "serveur": {
    "hote": "0.0.0.0",
    "port": 8080,
    "ssl": {
      "active": true,
      "cheminCertificat": "/etc/ssl/cert.pem"
    }
  },
  "baseDeDonnees": {
    "type": "postgresql",
    "hote": "localhost",
    "port": 5432,
    "identifiants": {
      "utilisateur": "admin",
      "motDePasse": "secret"
    }
  }
}
```

## 🎯 Scénarios d'Application

### 1. Gestion de Fichiers de Configuration

Conversion de format de configuration d'applications :

```javascript
// Utilitaire de conversion de fichiers de configuration
class ConvertisseurConfiguration {
  constructor() {
    this.analyseurYaml = require('js-yaml');
  }

  // Convertir configuration JSON en YAML
  jsonVersYaml(configurationJson) {
    try {
      const objetConfiguration = typeof configurationJson === 'string' 
        ? JSON.parse(configurationJson) 
        : configurationJson;
      
      return this.analyseurYaml.dump(objetConfiguration, {
        indent: 2,
        lineWidth: 120,
        noRefs: true
      });
    } catch (erreur) {
      throw new Error(`Erreur de conversion JSON→YAML: ${erreur.message}`);
    }
  }

  // Convertir configuration YAML en JSON
  yamlVersJson(configurationYaml, joli = true) {
    try {
      const objetConfiguration = this.analyseurYaml.load(configurationYaml);
      
      return joli 
        ? JSON.stringify(objetConfiguration, null, 2)
        : JSON.stringify(objetConfiguration);
    } catch (erreur) {
      throw new Error(`Erreur de conversion YAML→JSON: ${erreur.message}`);
    }
  }

  // Valider la configuration
  validerConfiguration(configuration, format) {
    try {
      if (format === 'json') {
        JSON.parse(configuration);
        return { valide: true, message: 'Format JSON correct' };
      } else if (format === 'yaml') {
        this.analyseurYaml.load(configuration);
        return { valide: true, message: 'Format YAML correct' };
      }
    } catch (erreur) {
      return { 
        valide: false, 
        message: `Erreur de format ${format.toUpperCase()}: ${erreur.message}` 
      };
    }
  }

  // Migrer la configuration
  migrerConfiguration(configurationAncienne, formatAncien, formatNouveau) {
    try {
      // Valider le format original
      const validation = this.validerConfiguration(configurationAncienne, formatAncien);
      if (!validation.valide) {
        throw new Error(validation.message);
      }

      // Exécuter la conversion
      if (formatAncien === 'json' && formatNouveau === 'yaml') {
        return this.jsonVersYaml(configurationAncienne);
      } else if (formatAncien === 'yaml' && formatNouveau === 'json') {
        return this.yamlVersJson(configurationAncienne);
      } else {
        throw new Error('Format de conversion non supporté');
      }
    } catch (erreur) {
      throw new Error(`Erreur de migration de configuration: ${erreur.message}`);
    }
  }
}

// Exemple d'utilisation
const convertisseur = new ConvertisseurConfiguration();

// Migrer configuration JSON existante vers YAML
const configurationJson = `{
  "baseDeDonnees": {
    "hote": "localhost",
    "port": 5432,
    "nom": "monapp"
  },
  "redis": {
    "hote": "localhost",
    "port": 6379
  }
}`;

try {
  const configurationYaml = convertisseur.migrerConfiguration(configurationJson, 'json', 'yaml');
  console.log('Fichier de configuration YAML:');
  console.log(configurationYaml);
} catch (erreur) {
  console.error('Erreur de conversion:', erreur.message);
}
```

### 2. Conversion de Documentation API

Conversion de format de spécifications OpenAPI :

```javascript
// Convertisseur de spécifications OpenAPI
class ConvertisseurOpenAPI {
  constructor() {
    this.yaml = require('js-yaml');
  }

  // Convertir spécification OpenAPI JSON en YAML
  convertirVersYaml(openApiJson) {
    try {
      const specification = typeof openApiJson === 'string' 
        ? JSON.parse(openApiJson) 
        : openApiJson;

      // Validation de base de spécification OpenAPI
      if (!specification.openapi && !specification.swagger) {
        throw new Error('Ce n\'est pas une spécification OpenAPI valide');
      }

      return this.yaml.dump(specification, {
        indent: 2,
        lineWidth: 120,
        noRefs: true,
        sortKeys: false
      });
    } catch (erreur) {
      throw new Error(`Erreur de conversion OpenAPI: ${erreur.message}`);
    }
  }

  // Convertir spécification OpenAPI YAML en JSON
  convertirVersJson(openApiYaml, minifier = false) {
    try {
      const specification = this.yaml.load(openApiYaml);

      // Validation de base de spécification OpenAPI
      if (!specification.openapi && !specification.swagger) {
        throw new Error('Ce n\'est pas une spécification OpenAPI valide');
      }

      return minifier 
        ? JSON.stringify(specification)
        : JSON.stringify(specification, null, 2);
    } catch (erreur) {
      throw new Error(`Erreur de conversion OpenAPI: ${erreur.message}`);
    }
  }

  // Extraire informations de spécification
  extraireInfoApi(specification) {
    try {
      const specificationApi = typeof specification === 'string' 
        ? this.yaml.load(specification) 
        : specification;

      return {
        version: specificationApi.openapi || specificationApi.swagger,
        titre: specificationApi.info?.title || 'Titre non défini',
        description: specificationApi.info?.description || 'Aucune description',
        version: specificationApi.info?.version || '1.0.0',
        serveurs: specificationApi.servers || [],
        compteRoutes: Object.keys(specificationApi.paths || {}).length,
        compteComposants: Object.keys(specificationApi.components?.schemas || {}).length
      };
    } catch (erreur) {
      throw new Error(`Erreur d'analyse de spécification: ${erreur.message}`);
    }
  }
}

// Exemple d'utilisation
const convertisseurApi = new ConvertisseurOpenAPI();

// Spécification OpenAPI d'exemple (JSON)
const openApiJson = {
  "openapi": "3.0.0",
  "info": {
    "title": "API ToolMi",
    "description": "Spécification API d'outils utiles",
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "https://api.toolmi.com/v1",
      "description": "Environnement de production"
    }
  ],
  "paths": {
    "/outils": {
      "get": {
        "summary": "Obtenir la liste des outils",
        "responses": {
          "200": {
            "description": "Succès",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/Outil"
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "Outil": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string"
          },
          "nom": {
            "type": "string"
          },
          "description": {
            "type": "string"
          }
        }
      }
    }
  }
};

// Conversion JSON → YAML
try {
  const specificationYaml = convertisseurApi.convertirVersYaml(openApiJson);
  console.log('Spécification OpenAPI au format YAML:');
  console.log(specificationYaml);

  // Extraire informations de spécification
  const infoApi = convertisseurApi.extraireInfoApi(openApiJson);
  console.log('Informations API:', infoApi);
} catch (erreur) {
  console.error('Erreur de conversion:', erreur.message);
}
```

## 🔧 Détails Techniques

### Comparaison de Caractéristiques JSON et YAML

| Caractéristique | JSON | YAML |
|------------------|------|------|
| Lisibilité | Moyenne | Élevée |
| Taille de fichier | Petite | Grande |
| Commentaires | Non | Oui |
| Chaînes multilignes | Non | Oui |
| Types de données | Limités | Riches |
| Vitesse d'analyse | Rapide | Moyenne |

### Considérations de Conversion

```javascript
// Considérations et meilleures pratiques de conversion
const meilleuresPratiquesConversion = {
  // Gestion des commentaires
  commentaires: {
    probleme: 'JSON ne supporte pas les commentaires, ils sont perdus lors de la conversion YAML→JSON',
    solution: 'Enregistrer les informations importantes dans des champs description au lieu de commentaires'
  },

  // Différences de types de données
  typesDonnees: {
    probleme: 'YAML exprime les dates, null, booléens de manière plus flexible',
    solution: 'Vérifier les types de données après conversion et ajuster si nécessaire'
  },

  // Chaînes multilignes
  chaineMultiligne: {
    probleme: 'Les chaînes multilignes YAML (|, >) sont converties en chaînes simples en JSON',
    solution: 'Utiliser des caractères de nouvelle ligne (\\n) pour représenter'
  },

  // Références et ancres
  references: {
    probleme: 'Les ancres (&) et références (*) de YAML sont développées en JSON',
    solution: 'Reconnaître que des données dupliquées peuvent survenir'
  }
};
```

## 💡 Conseils d'Utilisation

- **Sélection de Format** : YAML est adapté aux fichiers de configuration, JSON pour la communication API
- **Validation Importante** : Toujours vérifier l'intégrité des données avant et après conversion
- **Usage de Commentaires** : Profiter des commentaires en YAML pour améliorer la lisibilité
- **Sauvegarde** : Créer une sauvegarde des fichiers de configuration importants avant conversion

## ⚠️ Notes Importantes

- **Perte de Commentaires** : Les commentaires sont perdus lors de la conversion YAML→JSON
- **Changement de Types de Données** : Les types de données peuvent changer pendant la conversion
- **Erreurs de Syntaxe** : Une entrée avec un format invalide résultera en erreur
- **Encodage de Caractères** : Il est recommandé d'utiliser l'encodage UTF-8

## 🚀 Comment Utiliser

1. **Saisie de Données** : Collez les données JSON ou YAML à convertir dans la zone de saisie
2. **Sélection de Direction de Conversion** : Sélectionnez "JSON→YAML" ou "YAML→JSON"
3. **Exécuter la Conversion** : Cliquez sur le bouton "Convertir" pour exécuter la conversion
4. **Vérifier les Résultats** : Les résultats de conversion s'affichent dans la zone de sortie
5. **Utilisation de Copie** : Utilisez le bouton "Copier" pour copier les résultats dans le presse-papiers

> **Conseil** : Cet outil traite localement côté client et n'envoie pas de données au serveur, donc les informations confidentielles peuvent aussi être converties en toute sécurité.
