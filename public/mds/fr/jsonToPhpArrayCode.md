# Outil JSON → Code de Tableau PHP

Outil pour convertir les données JSON en code de tableau PHP, compatible avec les structures imbriquées, les caractères français, l'échappement de caractères spéciaux. Convertit en un clic les données JSON en code de tableau PHP au format standard, applicable aux fichiers de configuration, données Mock API, fichiers seed de base de données et autres scénarios.

## ✨ Caractéristiques Principales

- 🔄 **Conversion Complète** : Convertit les données JSON en code de tableau PHP exécutable
- 🌐 **Support Multilingue** : Gère correctement le français, le chinois, les caractères spéciaux
- 🎨 **Format Beau** : Génère du code avec indentation lisible et structure
- 📋 **Copie en Un Clic** : Le code PHP généré peut être copié directement pour utilisation
- 🔧 **Traitement d'Échappement** : Échappement automatique de caractères spéciaux et traitement de sécurité

## 📖 Exemples d'Utilisation

### Conversion JSON de Base

**JSON d'Entrée :**
```json
{
  "nom": "ToolMi",
  "version": "1.0.0",
  "caracteristiques": [
    "Conversion JSON",
    "Génération de tableau PHP",
    "Génération automatique de code"
  ],
  "configuration": {
    "debug": true,
    "port": 3000
  }
}
```

**Code PHP Généré :**
```php
<?php
$data = [
    'nom' => 'ToolMi',
    'version' => '1.0.0',
    'caracteristiques' => [
        'Conversion JSON',
        'Génération de tableau PHP',
        'Génération automatique de code'
    ],
    'configuration' => [
        'debug' => true,
        'port' => 3000
    ]
];
?>
```

### Structure Imbriquée Complexe

**JSON d'Entrée :**
```json
{
  "utilisateurs": [
    {
      "id": 1,
      "nom": "Pierre Dupont",
      "email": "<EMAIL>",
      "profil": {
        "age": 30,
        "ville": "Paris",
        "loisirs": ["lecture", "cinéma", "programmation"]
      }
    },
    {
      "id": 2,
      "nom": "Marie Durand",
      "email": "<EMAIL>",
      "profil": {
        "age": 25,
        "ville": "Lyon",
        "loisirs": ["voyages", "cuisine"]
      }
    }
  ],
  "meta": {
    "total": 2,
    "page": 1,
    "par_page": 10
  }
}
```

**Code PHP Généré :**
```php
<?php
$data = [
    'utilisateurs' => [
        [
            'id' => 1,
            'nom' => 'Pierre Dupont',
            'email' => '<EMAIL>',
            'profil' => [
                'age' => 30,
                'ville' => 'Paris',
                'loisirs' => [
                    'lecture',
                    'cinéma',
                    'programmation'
                ]
            ]
        ],
        [
            'id' => 2,
            'nom' => 'Marie Durand',
            'email' => '<EMAIL>',
            'profil' => [
                'age' => 25,
                'ville' => 'Lyon',
                'loisirs' => [
                    'voyages',
                    'cuisine'
                ]
            ]
        ]
    ],
    'meta' => [
        'total' => 2,
        'page' => 1,
        'par_page' => 10
    ]
];
?>
```

## 🎯 Scénarios d'Application

### 1. Génération de Fichiers de Configuration

Créer des fichiers de configuration d'applications :

```php
<?php
// config/app.php
return [
    'nom' => 'Application ToolMi',
    'environnement' => 'production',
    'debug' => false,
    'url' => 'https://www.toolmi.com',
    
    'base_donnees' => [
        'par_defaut' => 'mysql',
        'connexions' => [
            'mysql' => [
                'driver' => 'mysql',
                'host' => 'localhost',
                'port' => 3306,
                'database' => 'toolmi_db',
                'username' => 'root',
                'password' => 'secret',
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci'
            ]
        ]
    ],
    
    'cache' => [
        'par_defaut' => 'redis',
        'stores' => [
            'redis' => [
                'driver' => 'redis',
                'host' => 'localhost',
                'port' => 6379,
                'database' => 0
            ]
        ]
    ],
    
    'mail' => [
        'driver' => 'smtp',
        'host' => 'smtp.gmail.com',
        'port' => 587,
        'encryption' => 'tls',
        'username' => '<EMAIL>',
        'password' => 'mot_de_passe_mail'
    ],
    
    'services' => [
        'stripe' => [
            'key' => 'pk_test_...',
            'secret' => 'sk_test_...'
        ],
        'aws' => [
            'key' => 'AKIAIOSFODNN7EXAMPLE',
            'secret' => 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
            'region' => 'eu-west-1'
        ]
    ]
];
```

### 2. Données Mock API

Générer des données mock pour les tests :

```php
<?php
// tests/fixtures/api_responses.php
return [
    'liste_utilisateurs' => [
        'donnees' => [
            [
                'id' => 1,
                'nom_utilisateur' => 'pierre_dupont',
                'email' => '<EMAIL>',
                'prenom' => 'Pierre',
                'nom' => 'Dupont',
                'avatar' => 'https://example.com/avatars/1.jpg',
                'cree_le' => '2024-01-15T10:30:00Z',
                'mis_a_jour_le' => '2024-06-15T14:20:00Z',
                'statut' => 'actif',
                'roles' => ['utilisateur', 'editeur']
            ],
            [
                'id' => 2,
                'nom_utilisateur' => 'marie_durand',
                'email' => '<EMAIL>',
                'prenom' => 'Marie',
                'nom' => 'Durand',
                'avatar' => 'https://example.com/avatars/2.jpg',
                'cree_le' => '2024-02-20T09:15:00Z',
                'mis_a_jour_le' => '2024-06-14T16:45:00Z',
                'statut' => 'actif',
                'roles' => ['utilisateur', 'admin']
            ]
        ],
        'meta' => [
            'page_actuelle' => 1,
            'par_page' => 20,
            'total' => 2,
            'derniere_page' => 1
        ]
    ],
    
    'catalogue_produits' => [
        'categories' => [
            [
                'id' => 1,
                'nom' => 'Électronique',
                'slug' => 'electronique',
                'description' => 'Appareils électroniques et gadgets les plus récents',
                'produits' => [
                    [
                        'id' => 101,
                        'nom' => 'Smartphone Pro',
                        'prix' => 899.99,
                        'devise' => 'EUR',
                        'en_stock' => true,
                        'specifications' => [
                            'taille_ecran' => '6.1 pouces',
                            'stockage' => '128GB',
                            'appareil_photo' => '12MP',
                            'batterie' => '3000mAh'
                        ]
                    ],
                    [
                        'id' => 102,
                        'nom' => 'Écouteurs Sans Fil',
                        'prix' => 159.99,
                        'devise' => 'EUR',
                        'en_stock' => true,
                        'specifications' => [
                            'duree_batterie' => '24 heures',
                            'reduction_bruit' => true,
                            'resistance_eau' => 'IPX4'
                        ]
                    ]
                ]
            ]
        ]
    ]
];
```

### 3. Fichiers Seed de Base de Données

Générer des données initiales de base de données :

```php
<?php
// database/seeders/UsersTableSeeder.php
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UsersTableSeeder extends Seeder
{
    public function run()
    {
        $utilisateurs = [
            [
                'nom' => 'Administrateur',
                'email' => '<EMAIL>',
                'email_verifie_le' => now(),
                'mot_de_passe' => Hash::make('motdepasse'),
                'role' => 'admin',
                'profil' => [
                    'prenom' => 'Admin',
                    'nom' => 'istrateur',
                    'telephone' => '+33-6-12-34-56-78',
                    'departement' => 'Administration Système',
                    'biographie' => 'Responsable de la gestion générale du système.'
                ],
                'configuration' => [
                    'theme' => 'sombre',
                    'langue' => 'fr',
                    'fuseau_horaire' => 'Europe/Paris',
                    'notifications' => [
                        'email' => true,
                        'push' => true,
                        'sms' => false
                    ]
                ],
                'cree_le' => now(),
                'mis_a_jour_le' => now()
            ],
            [
                'nom' => 'Pierre Dupont',
                'email' => '<EMAIL>',
                'email_verifie_le' => now(),
                'mot_de_passe' => Hash::make('motdepasse'),
                'role' => 'utilisateur',
                'profil' => [
                    'prenom' => 'Pierre',
                    'nom' => 'Dupont',
                    'telephone' => '+33-6-23-45-67-89',
                    'departement' => 'Développement',
                    'biographie' => 'Responsable du développement frontend.'
                ],
                'configuration' => [
                    'theme' => 'clair',
                    'langue' => 'fr',
                    'fuseau_horaire' => 'Europe/Paris',
                    'notifications' => [
                        'email' => true,
                        'push' => false,
                        'sms' => false
                    ]
                ],
                'cree_le' => now(),
                'mis_a_jour_le' => now()
            ]
        ];

        foreach ($utilisateurs as $donneesUtilisateur) {
            DB::table('utilisateurs')->insert([
                'nom' => $donneesUtilisateur['nom'],
                'email' => $donneesUtilisateur['email'],
                'email_verifie_le' => $donneesUtilisateur['email_verifie_le'],
                'mot_de_passe' => $donneesUtilisateur['mot_de_passe'],
                'role' => $donneesUtilisateur['role'],
                'profil' => json_encode($donneesUtilisateur['profil']),
                'configuration' => json_encode($donneesUtilisateur['configuration']),
                'cree_le' => $donneesUtilisateur['cree_le'],
                'mis_a_jour_le' => $donneesUtilisateur['mis_a_jour_le']
            ]);
        }
    }
}
```

### 4. Modèles de Réponse API

Modèles de réponse pour le développement API :

```php
<?php
// app/Http/Resources/ApiResponseTemplates.php
class ModelesReponseApi
{
    public static function succes($donnees = null, $message = 'Succès')
    {
        return [
            'statut' => 'succes',
            'message' => $message,
            'donnees' => $donnees,
            'horodatage' => now()->toISOString(),
            'id_requete' => request()->header('X-Request-ID', uniqid())
        ];
    }

    public static function erreur($message = 'Une erreur s\'est produite', $code = 500, $erreurs = null)
    {
        return [
            'statut' => 'erreur',
            'message' => $message,
            'code_erreur' => $code,
            'erreurs' => $erreurs,
            'horodatage' => now()->toISOString(),
            'id_requete' => request()->header('X-Request-ID', uniqid())
        ];
    }

    public static function pagination($donnees, $total, $page, $parPage)
    {
        return [
            'statut' => 'succes',
            'donnees' => $donnees,
            'pagination' => [
                'page_actuelle' => $page,
                'par_page' => $parPage,
                'total' => $total,
                'derniere_page' => ceil($total / $parPage),
                'de' => ($page - 1) * $parPage + 1,
                'a' => min($page * $parPage, $total)
            ],
            'horodatage' => now()->toISOString()
        ];
    }

    public static function profilUtilisateur($utilisateur)
    {
        return [
            'id' => $utilisateur->id,
            'nom_utilisateur' => $utilisateur->nom_utilisateur,
            'email' => $utilisateur->email,
            'profil' => [
                'prenom' => $utilisateur->prenom,
                'nom' => $utilisateur->nom,
                'nom_complet' => $utilisateur->prenom . ' ' . $utilisateur->nom,
                'avatar' => $utilisateur->url_avatar,
                'biographie' => $utilisateur->biographie,
                'localisation' => $utilisateur->localisation,
                'site_web' => $utilisateur->site_web
            ],
            'statistiques' => [
                'nombre_posts' => $utilisateur->posts()->count(),
                'nombre_abonnes' => $utilisateur->abonnes()->count(),
                'nombre_abonnements' => $utilisateur->abonnements()->count()
            ],
            'configuration' => [
                'theme' => $utilisateur->configuration['theme'] ?? 'clair',
                'langue' => $utilisateur->configuration['langue'] ?? 'fr',
                'confidentialite' => [
                    'profil_public' => $utilisateur->configuration['confidentialite']['profil_public'] ?? true,
                    'email_public' => $utilisateur->configuration['confidentialite']['email_public'] ?? false
                ]
            ],
            'horodatages' => [
                'cree_le' => $utilisateur->cree_le->toISOString(),
                'mis_a_jour_le' => $utilisateur->mis_a_jour_le->toISOString(),
                'dernier_acces_le' => $utilisateur->dernier_acces_le?->toISOString()
            ]
        ];
    }
}
```

## 🔧 Détails Techniques

### Mappage de Types de Données

Mappage de types de données de JSON vers PHP :

| Type JSON | Type PHP | Exemple de Conversion |
|-----------|----------|-----------------------|
| string | string | `"texte"` → `'texte'` |
| number | int/float | `123` → `123`, `12.3` → `12.3` |
| boolean | boolean | `true` → `true`, `false` → `false` |
| null | null | `null` → `null` |
| array | array | `[1,2,3]` → `[1, 2, 3]` |
| object | array | `{"cle":"valeur"}` → `['cle' => 'valeur']` |

### Échappement de Caractères Spéciaux

Traitement de caractères spéciaux dans le code PHP :

```php
// Exemples d'échappement de chaînes
$exemples = [
    'apostrophe' => 'C\'est un test',           // Échappement d'apostrophe
    'guillemets' => "Il a dit \"Bonjour\"",     // Échappement de guillemets
    'antislash' => 'Chemin: C:\\Users\\<USER>\nLigne 2",     // Caractère de nouvelle ligne
    'tabulation' => "Colonne1\tColonne2",       // Caractère de tabulation
    'unicode' => 'Bonjour le monde',            // Caractères Unicode (tels quels)
    'emoji' => '😀🎉🚀',                        // Emojis (tels quels)
];
```

### Options de Génération de Code

Options de personnalisation pour le code PHP généré :

```php
// Syntaxe de tableau courte (PHP 5.4+)
$donnees = [
    'cle' => 'valeur'
];

// Syntaxe de tableau traditionnelle
$donnees = array(
    'cle' => 'valeur'
);

// Style d'indentation
$donnees = [
    'niveau1' => [
        'niveau2' => [
            'niveau3' => 'valeur'
        ]
    ]
];
```

## 💡 Conseils d'Utilisation

- **Vérification de Syntaxe** : Vérifier que la syntaxe du code PHP généré soit correcte
- **Validation d'Échappement** : Vérifier que les caractères spéciaux soient échappés appropriément
- **Performance** : Faire attention à l'utilisation de mémoire pour les grands ensembles de données
- **Sécurité** : Implémenter un assainissement approprié lors de l'inclusion de données d'entrée utilisateur

## ⚠️ Notes Importantes

- **Format JSON** : Vérifier que le JSON d'entrée ait un format valide
- **Encodage de Caractères** : Vérifier que les caractères français soient traités correctement
- **Limites de Mémoire** : Faire attention aux limites de mémoire lors du traitement de fichiers JSON très volumineux
- **Sécurité** : Vérifier que le code généré ne contienne pas d'informations confidentielles

## 🚀 Comment Utiliser

1. **Saisie JSON** : Collez les données JSON à convertir dans la zone de saisie
2. **Exécuter la Conversion** : Cliquez sur le bouton "Convertir" pour générer le code de tableau PHP
3. **Vérifier les Résultats** : Le code PHP généré s'affiche dans la zone de sortie
4. **Utilisation de Copie** : Utilisez le bouton "Copier" pour copier le code PHP dans le presse-papiers
5. **Utilisation du Code** : Collez dans les fichiers PHP pour utiliser comme fichiers de configuration ou données

> **Conseil** : Cet outil traite localement côté client et n'envoie pas de données JSON au serveur, donc les informations confidentielles de configuration peuvent aussi être converties en toute sécurité.
