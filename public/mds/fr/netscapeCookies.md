# Outil de Conversion de Format Netscape Cookies

Outil de conversion mutuelle entre le format Netscape Cookies et le format JSON, applicable à la migration de données de navigateur, développement de crawlers web, tests web et autres scénarios, fournissant une compatibilité de données de cookies entre différents systèmes.

## ✨ Caractéristiques Principales

- 🔄 **Conversion Bidirectionnelle** : Compatible avec la conversion mutuelle entre format Netscape et format JSON
- 📊 **Validation de Données** : Vérification automatique de l'exactitude du format de cookies
- 🌐 **Compatibilité de Navigateurs** : Compatible avec les formats de cookies des navigateurs principaux
- 📋 **Copie en Un Clic** : Les résultats de conversion peuvent être copiés directement pour utilisation
- 🔧 **Détection d'Erreurs** : Détection automatique de formats de cookies invalides

## 📖 Exemples d'Utilisation

### Conversion Netscape → JSON

**Format Netscape d'Entrée :**
```
# Netscape HTTP Cookie File
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123def456
.example.com	TRUE	/	FALSE	1640995200	user_pref	theme=dark
example.com	FALSE	/login	TRUE	1640995200	csrf_token	xyz789uvw012
```

**JSON Converti :**
```json
[
  {
    "domain": ".example.com",
    "hostOnly": false,
    "path": "/",
    "secure": false,
    "expirationDate": 1640995200,
    "name": "session_id",
    "value": "abc123def456"
  },
  {
    "domain": ".example.com",
    "hostOnly": false,
    "path": "/",
    "secure": false,
    "expirationDate": 1640995200,
    "name": "user_pref",
    "value": "theme=dark"
  },
  {
    "domain": "example.com",
    "hostOnly": true,
    "path": "/login",
    "secure": true,
    "expirationDate": 1640995200,
    "name": "csrf_token",
    "value": "xyz789uvw012"
  }
]
```

### Conversion JSON → Netscape

**JSON d'Entrée :**
```json
[
  {
    "domain": ".toolmi.com",
    "hostOnly": false,
    "path": "/",
    "secure": true,
    "expirationDate": 1735689600,
    "name": "auth_token",
    "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
  },
  {
    "domain": "toolmi.com",
    "hostOnly": true,
    "path": "/dashboard",
    "secure": false,
    "expirationDate": 1735689600,
    "name": "dashboard_settings",
    "value": "layout=grid&theme=light"
  }
]
```

**Format Netscape Converti :**
```
# Netscape HTTP Cookie File
.toolmi.com	TRUE	/	TRUE	1735689600	auth_token	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9
toolmi.com	FALSE	/dashboard	FALSE	1735689600	dashboard_settings	layout=grid&theme=light
```

## 🎯 Scénarios d'Application

### 1. Migration de Données de Navigateur

Migration de données de cookies entre différents navigateurs :

```javascript
// Outil de migration de cookies de navigateur
class MigrateurCookiesNavigateur {
  constructor() {
    this.formatsSupportes = ['netscape', 'json', 'chrome', 'firefox']
  }

  // Convertir format Netscape en JSON
  netscapeVersJson(contenuNetscape) {
    const lignes = contenuNetscape.split('\n')
    const cookies = []

    for (const ligne of lignes) {
      // Ignorer les lignes de commentaires et lignes vides
      if (ligne.startsWith('#') || ligne.trim() === '') continue

      const parties = ligne.split('\t')
      if (parties.length >= 7) {
        const cookie = {
          domain: parties[0],
          hostOnly: parties[1] === 'FALSE',
          path: parties[2],
          secure: parties[3] === 'TRUE',
          expirationDate: parseInt(parties[4]),
          name: parties[5],
          value: parties[6]
        }
        cookies.push(cookie)
      }
    }

    return cookies
  }

  // Convertir JSON en format Netscape
  jsonVersNetscape(cookiesJson) {
    let resultat = '# Netscape HTTP Cookie File\n'
    
    for (const cookie of cookiesJson) {
      const ligne = [
        cookie.domain,
        cookie.hostOnly ? 'FALSE' : 'TRUE',
        cookie.path,
        cookie.secure ? 'TRUE' : 'FALSE',
        cookie.expirationDate || Math.floor(Date.now() / 1000) + 86400,
        cookie.name,
        cookie.value
      ].join('\t')
      
      resultat += ligne + '\n'
    }

    return resultat
  }

  // Traiter format spécifique de navigateur
  traiterExportNavigateur(donnees, typeNavigateur) {
    switch (typeNavigateur) {
      case 'chrome':
        return this.traiterExportChrome(donnees)
      case 'firefox':
        return this.traiterExportFirefox(donnees)
      case 'safari':
        return this.traiterExportSafari(donnees)
      default:
        throw new Error(`Type de navigateur non supporté : ${typeNavigateur}`)
    }
  }

  traiterExportChrome(donneesChrome) {
    // Traitement spécifique de cookies Chrome
    return donneesChrome.map(cookie => ({
      domain: cookie.domain,
      hostOnly: !cookie.domain.startsWith('.'),
      path: cookie.path,
      secure: cookie.secure,
      expirationDate: cookie.expirationDate,
      name: cookie.name,
      value: cookie.value,
      httpOnly: cookie.httpOnly || false,
      sameSite: cookie.sameSite || 'unspecified'
    }))
  }

  // Gestion d'expiration de cookies
  gererExpirationCookies(cookies, action = 'extend') {
    const maintenant = Math.floor(Date.now() / 1000)
    const uneAnnee = 365 * 24 * 60 * 60

    return cookies.map(cookie => {
      switch (action) {
        case 'extend':
          cookie.expirationDate = maintenant + uneAnnee
          break
        case 'session':
          delete cookie.expirationDate
          break
        case 'expire':
          cookie.expirationDate = maintenant - 1
          break
      }
      return cookie
    })
  }

  // Filtrage de cookies
  filtrerCookies(cookies, criteres) {
    return cookies.filter(cookie => {
      if (criteres.domain && !cookie.domain.includes(criteres.domain)) {
        return false
      }
      if (criteres.secure !== undefined && cookie.secure !== criteres.secure) {
        return false
      }
      if (criteres.name && !cookie.name.includes(criteres.name)) {
        return false
      }
      return true
    })
  }
}

// Exemple d'utilisation
const migrateur = new MigrateurCookiesNavigateur()

// Charger fichier de cookies format Netscape
const cookiesNetscape = `# Netscape HTTP Cookie File
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123
.example.com	TRUE	/	TRUE	1640995200	auth_token	xyz789`

// Convertir en JSON
const cookiesJson = migrateur.netscapeVersJson(cookiesNetscape)
console.log('Format JSON:', JSON.stringify(cookiesJson, null, 2))

// Étendre la date d'expiration
const cookiesEtendus = migrateur.gererExpirationCookies(cookiesJson, 'extend')

// Convertir de nouveau en format Netscape
const nouveauxCookiesNetscape = migrateur.jsonVersNetscape(cookiesEtendus)
console.log('Nouveau format Netscape:', nouveauxCookiesNetscape)
```

### 2. Développement de Crawler Web

Gestion de cookies pour web scraping :

```javascript
// Gestionnaire de cookies pour crawler
class GestionnaireCookiesCrawler {
  constructor() {
    this.jarCookies = new Map()
    this.cookiesSession = new Set()
  }

  // Charger cookies depuis fichier
  chargerCookiesDepuisFichier(cheminFichier, format = 'netscape') {
    try {
      const contenu = fs.readFileSync(cheminFichier, 'utf8')
      
      if (format === 'netscape') {
        return this.analyserCookiesNetscape(contenu)
      } else if (format === 'json') {
        return JSON.parse(contenu)
      }
    } catch (erreur) {
      console.error('Erreur lors du chargement du fichier de cookies:', erreur)
      return []
    }
  }

  // Analyser format Netscape
  analyserCookiesNetscape(contenu) {
    const lignes = contenu.split('\n')
    const cookies = []

    for (const ligne of lignes) {
      if (ligne.startsWith('#') || ligne.trim() === '') continue

      const parties = ligne.split('\t')
      if (parties.length >= 7) {
        const cookie = {
          domain: parties[0],
          inclureSousdomaines: parties[1] === 'TRUE',
          path: parties[2],
          secure: parties[3] === 'TRUE',
          expires: parseInt(parties[4]),
          name: parties[5],
          value: parties[6]
        }
        cookies.push(cookie)
      }
    }

    return cookies
  }

  // Générer en-tête Cookie pour requêtes
  genererEnTeteCookie(url, cookies) {
    const objetUrl = new URL(url)
    const cookiesApplicables = cookies.filter(cookie => {
      // Correspondance de domaine
      if (cookie.domain.startsWith('.')) {
        if (!objetUrl.hostname.endsWith(cookie.domain.substring(1))) {
          return false
        }
      } else {
        if (objetUrl.hostname !== cookie.domain) {
          return false
        }
      }

      // Correspondance de chemin
      if (!objetUrl.pathname.startsWith(cookie.path)) {
        return false
      }

      // Vérification d'attribut secure
      if (cookie.secure && objetUrl.protocol !== 'https:') {
        return false
      }

      // Vérification de date d'expiration
      if (cookie.expires && cookie.expires < Math.floor(Date.now() / 1000)) {
        return false
      }

      return true
    })

    return cookiesApplicables.map(cookie => `${cookie.name}=${cookie.value}`).join('; ')
  }

  // Extraire cookies de réponse
  extraireCookiesDeReponse(reponse, url) {
    const enTetesSetCookie = reponse.headers['set-cookie'] || []
    const objetUrl = new URL(url)
    const nouveauxCookies = []

    for (const enTete of enTetesSetCookie) {
      const cookie = this.analyserEnTeteCookie(enTete, objetUrl.hostname)
      if (cookie) {
        nouveauxCookies.push(cookie)
      }
    }

    return nouveauxCookies
  }

  analyserEnTeteCookie(enTete, domaineParDefaut) {
    const parties = enTete.split(';').map(partie => partie.trim())
    const [nomValeur] = parties
    const [nom, valeur] = nomValeur.split('=', 2)

    const cookie = {
      name: nom.trim(),
      value: valeur ? valeur.trim() : '',
      domain: domaineParDefaut,
      path: '/',
      secure: false,
      httpOnly: false,
      expires: null
    }

    // Analyser attributs
    for (let i = 1; i < parties.length; i++) {
      const [nomAttribut, valeurAttribut] = parties[i].split('=', 2)
      
      switch (nomAttribut.toLowerCase()) {
        case 'domain':
          cookie.domain = valeurAttribut
          break
        case 'path':
          cookie.path = valeurAttribut
          break
        case 'expires':
          cookie.expires = Math.floor(new Date(valeurAttribut).getTime() / 1000)
          break
        case 'max-age':
          cookie.expires = Math.floor(Date.now() / 1000) + parseInt(valeurAttribut)
          break
        case 'secure':
          cookie.secure = true
          break
        case 'httponly':
          cookie.httpOnly = true
          break
      }
    }

    return cookie
  }

  // Sauvegarder cookies dans fichier
  sauvegarderCookiesDansFichier(cookies, cheminFichier, format = 'netscape') {
    try {
      let contenu = ''
      
      if (format === 'netscape') {
        contenu = this.cookiesVersNetscape(cookies)
      } else if (format === 'json') {
        contenu = JSON.stringify(cookies, null, 2)
      }

      fs.writeFileSync(cheminFichier, contenu, 'utf8')
      console.log(`Cookies sauvegardés dans ${cheminFichier}`)
    } catch (erreur) {
      console.error('Erreur lors de la sauvegarde des cookies:', erreur)
    }
  }

  cookiesVersNetscape(cookies) {
    let contenu = '# Netscape HTTP Cookie File\n'
    
    for (const cookie of cookies) {
      const ligne = [
        cookie.domain,
        cookie.inclureSousdomaines ? 'TRUE' : 'FALSE',
        cookie.path,
        cookie.secure ? 'TRUE' : 'FALSE',
        cookie.expires || 0,
        cookie.name,
        cookie.value
      ].join('\t')
      
      contenu += ligne + '\n'
    }

    return contenu
  }
}

// Exemple d'utilisation
const gestionnaireCookies = new GestionnaireCookiesCrawler()

// Charger fichier de cookies
const cookies = gestionnaireCookies.chargerCookiesDepuisFichier('./cookies.txt', 'netscape')

// Générer en-tête Cookie pour requête
const enTeteCookie = gestionnaireCookies.genererEnTeteCookie('https://example.com/api/data', cookies)
console.log('En-tête Cookie:', enTeteCookie)

// Utiliser dans requête HTTP
const optionsRequete = {
  method: 'GET',
  headers: {
    'Cookie': enTeteCookie,
    'User-Agent': 'Mozilla/5.0 (compatible; WebCrawler/1.0)'
  }
}
```

## 🔧 Détails Techniques

### Format Netscape Cookie

Structure du format Netscape Cookie :

**Composition de Champs :**
1. **domain** : Domaine où le cookie est valide
2. **flag** : Si inclut les sous-domaines (TRUE/FALSE)
3. **path** : Chemin où le cookie est valide
4. **secure** : Si envoyé seulement en connexions HTTPS (TRUE/FALSE)
5. **expiration** : Date d'expiration (timestamp Unix)
6. **name** : Nom du cookie
7. **value** : Valeur du cookie

**Exemple de format :**
```
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123def456
```

### Format JSON Cookie

Représentation de cookies au format JSON :

```json
{
  "domain": ".example.com",
  "hostOnly": false,
  "path": "/",
  "secure": false,
  "expirationDate": 1640995200,
  "name": "session_id",
  "value": "abc123def456",
  "httpOnly": false,
  "sameSite": "unspecified"
}
```

## 💡 Conseils d'Utilisation

- **Vérification de Format** : Vérifier que le format de cookies soit correct avant conversion
- **Attention au Domaine** : Comprendre la signification du point initial (.) dans le domaine
- **Date d'Expiration** : Vérifier l'exactitude du timestamp Unix
- **Sécurité** : Faire attention à la gestion des cookies confidentiels

## ⚠️ Notes Importantes

- **Confidentialité** : Les informations de cookies peuvent contenir des données personnelles
- **Sécurité** : Faire attention à la fuite de cookies d'authentification
- **Date d'Expiration** : Gérer appropriément les cookies expirés
- **Restrictions de Domaine** : Comprendre et utiliser les restrictions de domaine des cookies

## 🚀 Comment Utiliser

1. **Sélection de Format** : Sélectionnez le format d'entrée (Netscape ou JSON)
2. **Saisie de Données** : Collez les données de cookies à convertir dans la zone de saisie
3. **Exécuter la Conversion** : Cliquez sur le bouton "Convertir" pour exécuter la conversion
4. **Vérifier les Résultats** : Les résultats de conversion s'affichent dans la zone de sortie
5. **Utilisation de Copie** : Utilisez le bouton "Copier" pour copier les résultats dans le presse-papiers

> **Conseil** : Cet outil traite localement côté client et n'envoie pas de données de cookies au serveur, donc les informations confidentielles de session peuvent aussi être converties en toute sécurité.
