# Générateur de Codes QR

Le code QR (Quick Response Code) est un code-barres bidimensionnel inventé par Denso Wave du Japon en 1994. Cet outil peut rapidement convertir du texte, des URL, des informations de contact et d'autres contenus divers en images de codes QR.

## ✨ Caractéristiques Principales

- 📱 **Support Multi-contenu** : Compatible avec le texte, URL, contacts, mots de passe WiFi, etc.
- 🎨 **Sortie Haute Qualité** : Génère des images de codes QR de haute qualité
- 📏 **Tailles Ajustables** : Compatible avec plusieurs spécifications de taille
- 🎯 **Niveaux de Correction d'Erreurs** : Compatible avec différentes configurations de niveaux de correction d'erreurs
- 💾 **Téléchargement en Un Clic** : Télécharge et sauvegarde directement les codes QR générés
- 🔧 **Aperçu en Temps Réel** : Génère un aperçu de code QR instantanément selon le contenu d'entrée

## 📖 Exemples d'Utilisation

### Code QR d'URL

**Contenu d'Entrée :**
```
https://www.toolmi.com
```

**Effet Gén<PERSON> :**
- Après le scan, redirige directement vers le site web de ToolMi
- Applicable à la promotion de sites web, partage de liens

### Code QR de Texte

**Contenu d'Entrée :**
```
Bienvenue aux outils en ligne de ToolMi !
Ici se trouvent des outils de développement riches et des fonctions pratiques.
```

**Effet Généré :**
- Après le scan, affiche le contenu complet du texte
- Applicable à la transmission d'informations, affichage d'instructions

### Code QR de Contact

**Contenu d'Entrée :**
```
BEGIN:VCARD
VERSION:3.0
FN:Pierre Dupont
ORG:Technologie ToolMi
TEL:+33-6-12-34-56-78
EMAIL:<EMAIL>
URL:https://www.toolmi.com
END:VCARD
```

**Effet Généré :**
- Après le scan, peut être ajouté directement aux contacts
- Inclut des informations comme nom, entreprise, téléphone, e-mail

## 🎯 Scénarios d'Application

### 1. Promotion de Sites Web

Générer des codes QR de promotion pour sites web et applications :

```html
<!-- Code QR sur affiche promotionnelle -->
<div class="promotion">
  <h3>Scannez le code QR pour accéder à ToolMi</h3>
  <img src="qr-code.png" alt="Code QR de ToolMi">
  <p>Découvrez plus d'outils pratiques</p>
</div>

<!-- Scénarios d'application -->
- Affiches promotionnelles
- Design de cartes de visite
- Emballage de produits
- Publications publicitaires
```

### 2. Paiements Mobiles

Générer des codes QR de paiement :

```javascript
// Exemple de lien de paiement PayPal
const paypalUrl = 'https://paypal.me/utilisateur/50EUR'

// Exemple de lien de paiement mobile français
const lydiUrl = 'lydia://payment?amount=50&recipient=utilisateur'

// Générer code QR de paiement
generateQRCode(paypalUrl, {
  size: 200,
  errorCorrectionLevel: 'M'
})
```

### 3. Partager Mot de Passe WiFi

Générer code QR de connexion WiFi :

```
Format de code QR WiFi :
WIFI:T:WPA;S:nom_reseau;P:mot_de_passe;H:false;;

Exemple :
WIFI:T:WPA;S:ToolMi_5G;P:12345678;H:false;;

Explication des paramètres :
- T: Type de chiffrement (WPA/WEP/nopass)
- S: Nom de réseau (SSID)
- P: Mot de passe
- H: Si masquer le réseau (true/false)
```

### 4. Check-in d'Événements

Générer code QR de check-in d'événements :

```json
{
  "type": "event_checkin",
  "event_id": "tech_meetup_2024",
  "event_name": "Rencontre Technologique",
  "location": "Centre de Conventions de Paris",
  "date": "2024-06-15",
  "checkin_url": "https://event.toolmi.com/checkin/tech_meetup_2024"
}
```

### 5. Traçabilité de Produits

Générer code QR de traçabilité de produits :

```javascript
// Informations du produit
const productInfo = {
  id: 'TM2024001',
  name: 'Boîte à Outils Intelligente',
  batch: 'B20240615',
  production_date: '2024-06-15',
  manufacturer: 'Technologie ToolMi',
  quality_check: 'PASS',
  trace_url: 'https://trace.toolmi.com/product/TM2024001'
}

// Générer code QR de traçabilité
const traceData = JSON.stringify(productInfo)
generateQRCode(traceData)
```

## 🔧 Détails Techniques

### Structure du Code QR

Structure de base du code QR :

**Motifs Fonctionnels :**
- Motifs de détection de position : Grands carrés dans trois coins
- Séparateurs de motifs de détection de position : Cadres blancs
- Motifs d'alignement : Petits points noirs pour déterminer la direction

**Zone de Données :**
- Informations de format : Niveau de correction d'erreurs et informations de masque
- Informations de version : Numéro de version du code QR
- Mots de code de données et correction d'erreurs : Données réellement stockées

**Spécifications de Capacité :**
- Version 1 : 21×21 modules, maximum 25 caractères
- Version 40 : 177×177 modules, maximum 4296 caractères
- Compatible avec données numériques, alphabétiques, kanji et binaires

### Niveaux de Correction d'Erreurs

Le code QR supporte 4 niveaux de correction d'erreurs :

| Niveau | Taux de Correction | Scénario d'Application |
|--------|-------------------|------------------------|
| L | ~7% | Environnement propre, impression haute qualité |
| M | ~15% | Environnement général, impression standard |
| Q | ~25% | Environnement difficile, contamination possible |
| H | ~30% | Environnement très difficile, contamination sévère |

**Recommandations de Sélection :**
- Partage d'URL : Utiliser niveau L ou M
- Publicité extérieure : Utiliser niveau Q ou H
- Étiquettes de produits : Utiliser niveau M ou Q

### Modes d'Encodage

Le code QR supporte plusieurs modes d'encodage :

**Mode Numérique :**
- Ne peut stocker que les chiffres 0-9
- Efficacité de stockage la plus élevée
- Applicable au contenu purement numérique

**Mode Alphanumérique :**
- Peut stocker chiffres, lettres majuscules et certains symboles
- Haute efficacité
- Applicable aux URL, codes, etc.

**Mode Octet :**
- Peut stocker n'importe quel caractère
- Compatible avec le français, symboles spéciaux
- Polyvalence la plus forte

**Mode Kanji :**
- Optimisation spécifique pour les caractères japonais
- Plus grande efficacité en environnement japonais

## 💡 Conseils d'Utilisation

- **Optimisation du Contenu** : Utiliser un contenu le plus court possible pour améliorer le taux de réussite du scan
- **Sélection de Taille** : Choisir une taille appropriée selon la distance d'utilisation
- **Niveau de Correction d'Erreurs** : Choisir un niveau de correction d'erreurs approprié selon l'environnement d'utilisation
- **Vérification de Tests** : Après génération, tester l'effet de scan avec plusieurs appareils

## ⚠️ Notes Importantes

- **Longueur du Contenu** : Un contenu trop long rendra le code QR complexe, affectant le scan
- **Qualité d'Impression** : S'assurer que l'impression soit claire, éviter le flou et la déformation
- **Contraste de Couleur** : Maintenir un contraste de couleur suffisant, combinaison noir et blanc recommandée
- **Marge Environnante** : Laisser suffisamment d'espace blanc autour du code QR

## 🚀 Comment Utiliser

1. **Saisie de Contenu** : Entrez le contenu pour générer le code QR dans la zone de saisie
2. **Ajustement de Configuration** : Sélectionnez la taille appropriée et le niveau de correction d'erreurs
3. **Générer Aperçu** : Cliquez sur le bouton générer pour voir l'aperçu du code QR
4. **Télécharger et Sauvegarder** : Cliquez sur le bouton télécharger pour sauvegarder l'image du code QR
5. **Test de Scan** : Utilisez un smartphone pour scanner et tester l'effet généré

> **Conseil** : Cet outil génère des codes QR localement côté client et ne télécharge pas de données vers le serveur, garantissant la confidentialité et la sécurité.
