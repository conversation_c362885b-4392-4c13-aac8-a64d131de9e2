# Générateur UUID

UUID (Universally Unique Identifier, Identifiant Unique Universel) est une information d'identification standardisée utilisée pour identifier de manière unique des informations dans les systèmes distribués. Un UUID est composé de 128 bits, généralement représenté par 32 chiffres hexadécimaux, divisés en 5 groupes séparés par des tirets.

## ✨ Caractéristiques Principales

- 🔢 **Support Multiples Versions** : Compatible avec différentes versions comme UUID v1, v4, etc.
- ⚡ **Génération par Lots** : Compatible avec la génération de multiples UUID à la fois
- 📋 **Multiples Formats** : Compatible avec format standard, format sans tirets, etc.
- 💾 **Copie en Un Clic** : Les UUID générés peuvent être copiés directement pour utilisation
- 🔧 **Génération en Temps Réel** : Cliquez pour générer de nouveaux UUID instantanément

## 📖 Exemples d'Utilisation

### UUID v4 Standard

**Exemple Généré :**
```
f47ac10b-58cc-4372-a567-0e02b2c3d479
```

**Caractéristiques :**
- Génération aléatoire, sans informations de temps
- Probabilité de collision extrêmement faible
- Version UUID la plus couramment utilisée

### UUID v1 (Basé sur le Temps)

**Exemple Généré :**
```
6ba7b810-9dad-11d1-80b4-00c04fd430c8
```

**Caractéristiques :**
- Contient des informations d'horodatage
- Contient des informations d'adresse MAC
- Le temps de génération peut être inféré

### Format Sans Tirets

**Exemple Généré :**
```
f47ac10b58cc4372a5670e02b2c3d479
```

**Caractéristiques :**
- 32 caractères hexadécimaux continus
- Applicable à certaines bases de données et systèmes

## 🎯 Scénarios d'Application

### 1. Clé Primaire de Base de Données

Utiliser UUID comme clé primaire en base de données :

```sql
-- Créer table utilisant UUID comme clé primaire
CREATE TABLE utilisateurs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nom_utilisateur VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    cree_le TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insérer données (UUID généré automatiquement)
INSERT INTO utilisateurs (nom_utilisateur, email) 
VALUES ('pierre', '<EMAIL>');

-- Consulter données
SELECT * FROM utilisateurs WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
```

### 2. Systèmes Distribués

Générer des identifiants uniques dans les systèmes distribués :

```javascript
// Traceur de requêtes dans les microservices
class TraceurRequete {
  constructor() {
    this.idRequete = genererUUID()
    this.horodatage = new Date().toISOString()
  }

  log(message) {
    console.log(`[${this.idRequete}] ${this.horodatage}: ${message}`)
  }
}

// Exemple d'utilisation
const traceur = new TraceurRequete()
traceur.log('Début du traitement de la requête utilisateur')
traceur.log('Appel au service utilisateur')
traceur.log('Appel au service commandes')
traceur.log('Traitement de la requête terminé')

// Sortie d'exemple :
// [f47ac10b-58cc-4372-a567-0e02b2c3d479] 2024-06-15T10:30:00.000Z: Début du traitement de la requête utilisateur
```

### 3. Nomenclature de Fichiers

Générer des noms uniques pour les fichiers téléchargés :

```javascript
// Traitement de téléchargement de fichiers
function gererTelechargementFichier(fichier) {
  const extensionFichier = fichier.name.split('.').pop()
  const nomFichierUnique = `${genererUUID()}.${extensionFichier}`
  
  // Sauvegarder fichier
  const cheminFichier = `/uploads/${nomFichierUnique}`
  sauvegarderFichier(fichier, cheminFichier)
  
  return {
    nomOriginal: fichier.name,
    nomFichier: nomFichierUnique,
    cheminFichier: cheminFichier,
    tempsTelechargement: new Date().toISOString()
  }
}

// Exemple d'utilisation
const resultatTelechargement = gererTelechargementFichier(fichierUtilisateur)
console.log(resultatTelechargement)
// {
//   nomOriginal: "document.pdf",
//   nomFichier: "f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   cheminFichier: "/uploads/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   tempsTelechargement: "2024-06-15T10:30:00.000Z"
// }
```

### 4. Gestion de Sessions

Générer des identifiants uniques pour les sessions utilisateur :

```javascript
// Système de gestion de sessions
class GestionnaireSessions {
  constructor() {
    this.sessions = new Map()
  }

  creerSession(idUtilisateur) {
    const idSession = genererUUID()
    const session = {
      id: idSession,
      idUtilisateur: idUtilisateur,
      creeeLe: new Date(),
      dernierAcces: new Date(),
      donnees: {}
    }
    
    this.sessions.set(idSession, session)
    return idSession
  }

  obtenirSession(idSession) {
    const session = this.sessions.get(idSession)
    if (session) {
      session.dernierAcces = new Date()
    }
    return session
  }

  detruireSession(idSession) {
    return this.sessions.delete(idSession)
  }
}

// Exemple d'utilisation
const gestionnaireSessions = new GestionnaireSessions()
const idSession = gestionnaireSessions.creerSession('user123')
console.log('ID de Session:', idSession)
// ID de Session: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

### 5. Génération de Clés API

Générer des clés uniques pour l'accès API :

```javascript
// Gestion de clés API
class GestionnaireClesAPI {
  constructor() {
    this.clesApi = new Map()
  }

  genererCleAPI(idUtilisateur, permissions = []) {
    const cleApi = genererUUID()
    const infoCle = {
      cle: cleApi,
      idUtilisateur: idUtilisateur,
      permissions: permissions,
      creeeLe: new Date(),
      derniereUtilisation: null,
      estActive: true
    }
    
    this.clesApi.set(cleApi, infoCle)
    return cleApi
  }

  validerCleAPI(cleApi) {
    const infoCle = this.clesApi.get(cleApi)
    if (infoCle && infoCle.estActive) {
      infoCle.derniereUtilisation = new Date()
      return infoCle
    }
    return null
  }

  revoquerCleAPI(cleApi) {
    const infoCle = this.clesApi.get(cleApi)
    if (infoCle) {
      infoCle.estActive = false
      return true
    }
    return false
  }
}

// Exemple d'utilisation
const gestionnaireApi = new GestionnaireClesAPI()
const cleApi = gestionnaireApi.genererCleAPI('user123', ['read', 'write'])
console.log('Clé API:', cleApi)
// Clé API: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

## 🔧 Détails Techniques

### Versions UUID

Différentes versions UUID ont différentes méthodes de génération :

**UUID v1 (Basé sur le Temps) :**
- Contient horodatage (60 bits)
- Contient séquence d'horloge (14 bits)
- Contient identifiant de nœud (48 bits, généralement adresse MAC)
- Le temps et lieu de génération peuvent être inférés

**UUID v4 (Aléatoire) :**
- 122 bits de nombres aléatoires
- 6 bits d'identifiants de version et variante
- Probabilité de collision d'environ 1/2^122
- Version la plus couramment utilisée

**UUID v5 (Basé sur le Nom) :**
- Utilise l'algorithme de hachage SHA-1
- Généré basé sur l'espace de noms et le nom
- La même entrée génère toujours le même UUID

### Structure de Format

Format UUID standard : `xxxxxxxx-xxxx-Mxxx-Nxxx-xxxxxxxxxxxx`

- **M** : Numéro de version (1, 4, 5, etc.)
- **N** : Identifiant de variante (généralement 8, 9, A, B)
- **x** : Chiffre hexadécimal (0-9, A-F)

### Probabilité de Collision

La probabilité de collision d'UUID v4 est extrêmement faible :

- Total de 2^122 types d'UUID possibles
- Probabilité de collision d'environ 50% lors de la génération de 10^18 UUID
- Peut être considéré comme unique dans les applications pratiques

## 💡 Conseils d'Utilisation

- **Sélection de Version** : Généralement utiliser UUID v4, utiliser v1 quand des informations de temps sont nécessaires
- **Optimisation de Stockage** : Utiliser BINARY(16) en base de données pour économiser l'espace
- **Performance d'Index** : Faire attention à l'impact sur la performance d'index de base de données lors de l'utilisation d'UUID comme clé primaire
- **Unification de Format** : Maintenir la cohérence de format UUID au sein du même système

## ⚠️ Notes Importantes

- **Impact sur Performance** : Utiliser UUID comme clé primaire peut affecter la performance de la base de données
- **Problème de Tri** : UUID ne peut pas être trié par temps de génération (sauf si utilise v1)
- **Espace de Stockage** : UUID occupe plus d'espace de stockage que les ID entiers (36 caractères ou 16 octets)
- **Lisibilité** : UUID n'est pas aussi intuitif et lisible que les ID auto-incrémentaux

## 🚀 Comment Utiliser

1. **Sélection de Version** : Choisissez la version UUID selon les exigences
2. **Configuration de Quantité** : Sélectionnez le nombre d'UUID à générer
3. **Sélection de Format** : Choisissez format standard ou format sans tirets
4. **Générer UUID** : Cliquez sur le bouton générer pour créer des UUID
5. **Utilisation de Copie** : Cliquez sur le bouton copier pour copier les UUID dans le presse-papiers

> **Conseil** : Cet outil génère des UUID localement côté client et ne télécharge pas de données vers le serveur, garantissant la confidentialité et la sécurité.
