# Outil de Conversion de Nombres en Chinois

Outil pour convertir les nombres arabes en nombres chinois, compatible avec le chinois simplifié, le chinois traditionnel et la notation de montants en majuscules, applicable à des scénarios comme les rapports financiers, les contrats, l'émission de factures. Compatible avec la conversion d'entiers et de décimales.

## ✨ Caractéristiques Principales

- 🔢 **Multiples Types de Conversion** : Compatible avec le chinois simplifié, le chinois traditionnel, les montants en majuscules
- 💰 **Conversion de Montants** : Compatible avec la notation de montants en majuscules pour usage financier
- 📊 **Support de Décimales** : Conversion précise tant d'entiers que de décimales
- 📋 **Copie en Un Clic** : Les résultats de conversion peuvent être copiés directement pour utilisation
- ⚡ **Conversion en Temps Réel** : Affiche les résultats de conversion simultanément avec la saisie

## 📖 Exemples d'Utilisation

### Conversion de Nombres de Base

**Nombre d'Entrée :**
```
12345
```

**Chinois Simplifié :**
```
一万二千三百四十五
```

**Chinois Traditionnel :**
```
一萬二千三百四十五
```

**Montants en Majuscules :**
```
壹万贰仟叁佰肆拾伍元整
```

### Conversion de Décimales

**Nombre d'Entrée :**
```
1234.56
```

**Chinois Simplifié :**
```
一千二百三十四点五六
```

**Montants en Majuscules :**
```
壹仟贰佰叁拾肆元伍角陆分
```

### Nombres Grands

**Nombre d'Entrée :**
```
9876543210
```

**Chinois Simplifié :**
```
九十八亿七千六百五十四万三千二百一十
```

**Chinois Traditionnel :**
```
九十八億七千六百五十四萬三千二百一十
```

## 🎯 Scénarios d'Application

### 1. Rapports Financiers

Notation de montants dans les documents financiers :

```javascript
// Système de conversion de montants financiers
class ConvertisseurMontantFinancier {
  constructor() {
    this.carteChiffres = {
      0: '零', 1: '壹', 2: '贰', 3: '叁', 4: '肆',
      5: '伍', 6: '陆', 7: '柒', 8: '捌', 9: '玖'
    }
    this.carteUnites = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿']
  }

  // Convertir montant en chinois majuscules
  convertirEnMontantChinois(montant) {
    if (montant === 0) return '零元整'
    
    const [partieEntiere, partieDecimale] = montant.toString().split('.')
    let resultat = this.convertirEntier(parseInt(partieEntiere)) + '元'
    
    if (partieDecimale) {
      resultat += this.convertirDecimale(partieDecimale)
    } else {
      resultat += '整'
    }
    
    return resultat
  }

  convertirEntier(num) {
    if (num === 0) return '零'
    
    const str = num.toString()
    const len = str.length
    let resultat = ''
    let drapeauZero = false
    
    for (let i = 0; i < len; i++) {
      const chiffre = parseInt(str[i])
      const unite = this.carteUnites[len - i - 1]
      
      if (chiffre === 0) {
        drapeauZero = true
      } else {
        if (drapeauZero && resultat !== '') {
          resultat += '零'
        }
        resultat += this.carteChiffres[chiffre] + unite
        drapeauZero = false
      }
    }
    
    return resultat
  }

  convertirDecimale(decimale) {
    const jiao = parseInt(decimale[0] || 0)
    const fen = parseInt(decimale[1] || 0)
    let resultat = ''
    
    if (jiao > 0) {
      resultat += this.carteChiffres[jiao] + '角'
    }
    if (fen > 0) {
      resultat += this.carteChiffres[fen] + '分'
    }
    
    return resultat || '整'
  }

  // Format pour factures
  formaterPourFacture(montant, devise = 'yuan chinois') {
    const montantChinois = this.convertirEnMontantChinois(montant)
    return {
      original: `€${montant.toFixed(2)}`,
      chinois: montantChinois,
      devise: devise,
      formate: `${devise}${montantChinois}`
    }
  }
}

// Exemple d'utilisation
const convertisseur = new ConvertisseurMontantFinancier()

// Conversion de montant de facture
const montantFacture = 12345.67
const formate = convertisseur.formaterPourFacture(montantFacture)

console.log('Informations de facture:')
console.log('Montant:', formate.original)
console.log('Majuscules:', formate.chinois)
console.log('Format complet:', formate.formate)

// Sortie :
// Informations de facture:
// Montant: €12345.67
// Majuscules: 壹万贰仟叁佰肆拾伍元陆角柒分
// Format complet: yuan chinois壹万贰仟叁佰肆拾伍元陆角柒分
```

### 2. Création de Contrats

Notation de nombres dans les documents légaux :

```javascript
// Système de conversion de nombres pour contrats
class ConvertisseurNombresContrat {
  constructor() {
    this.chiffresSimples = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    this.chiffresTraditionnels = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    this.unites = ['', '十', '百', '千', '万', '十', '百', '千', '亿']
  }

  // Conversion de période de contrat
  convertirPeriodeContrat(annees, mois = 0, jours = 0) {
    let resultat = ''
    
    if (annees > 0) {
      resultat += this.convertirEnChinoisSimple(annees) + '年'
    }
    if (mois > 0) {
      resultat += this.convertirEnChinoisSimple(mois) + '个月'
    }
    if (jours > 0) {
      resultat += this.convertirEnChinoisSimple(jours) + '日'
    }
    
    return resultat || '零日'
  }

  // Conversion en chinois simplifié
  convertirEnChinoisSimple(num) {
    if (num === 0) return '零'
    if (num < 10) return this.chiffresSimples[num]
    
    const str = num.toString()
    const len = str.length
    let resultat = ''
    
    for (let i = 0; i < len; i++) {
      const chiffre = parseInt(str[i])
      const indexUnite = len - i - 1
      
      if (chiffre !== 0) {
        resultat += this.chiffresSimples[chiffre]
        if (indexUnite > 0) {
          resultat += this.unites[indexUnite]
        }
      } else if (resultat !== '' && i < len - 1) {
        const chiffreSuivant = parseInt(str[i + 1])
        if (chiffreSuivant !== 0) {
          resultat += '零'
        }
      }
    }
    
    return resultat
  }

  // Générer clause de contrat
  genererClauseContrat(montant, periode, conditionsPaiement) {
    const montantChinois = this.convertirEnChinoisTraditionnel(montant)
    const periodeChinoise = this.convertirPeriodeContrat(periode.annees, periode.mois, periode.jours)
    
    return {
      montant: `Le montant du contrat est de ${montantChinois} euros (€${montant.toFixed(2)})`,
      periode: `La période du contrat est de ${periodeChinoise}`,
      paiement: `Mode de paiement : ${conditionsPaiement}`,
      clauseComplete: `Le montant de ce contrat est de ${montantChinois} euros (€${montant.toFixed(2)}), la période du contrat est de ${periodeChinoise}, mode de paiement : ${conditionsPaiement}.`
    }
  }

  convertirEnChinoisTraditionnel(num) {
    // Implémentation de conversion en traditionnel (simplifiée)
    return this.convertirEnChinoisSimple(num).replace(/一/g, '壹').replace(/二/g, '贰')
  }
}

// Exemple d'utilisation
const convertisseurContrat = new ConvertisseurNombresContrat()

// Informations du contrat
const infoContrat = {
  montant: 500000,
  periode: { annees: 2, mois: 6, jours: 0 },
  conditionsPaiement: 'Paiement échelonné, paiement mensuel'
}

const clause = convertisseurContrat.genererClauseContrat(
  infoContrat.montant,
  infoContrat.periode,
  infoContrat.conditionsPaiement
)

console.log('Clause du contrat:')
console.log(clause.clauseComplete)
// Sortie : Le montant de ce contrat est de cinquante万 euros (€500000.00), la période du contrat est de deux ans six mois, mode de paiement : Paiement échelonné, paiement mensuel.
```

### 3. Système Comptable

Traitement de nombres dans les logiciels comptables :

```javascript
// Système de conversion de nombres comptables
class ConvertisseurNombresComptable {
  constructor() {
    this.config = {
      devise: 'yuan chinois',
      precision: 2,
      utiliserTraditionnel: true
    }
  }

  // Créer écriture de journal comptable
  creerEcritureJournal(debit, credit, description) {
    return {
      date: new Date().toISOString().split('T')[0],
      description: description,
      debit: {
        montant: debit,
        chinois: this.convertirEnFormatComptable(debit)
      },
      credit: {
        montant: credit,
        chinois: this.convertirEnFormatComptable(credit)
      },
      solde: debit - credit
    }
  }

  // Conversion en format comptable
  convertirEnFormatComptable(montant) {
    const montantAbs = Math.abs(montant)
    const montantChinois = this.convertirEnChinois(montantAbs)
    const signe = montant >= 0 ? '' : '负'
    
    return `${signe}${this.config.devise}${montantChinois}元`
  }

  // Générer rapport mensuel
  genererRapportMensuel(transactions) {
    const totalDebit = transactions.reduce((somme, t) => somme + t.debit, 0)
    const totalCredit = transactions.reduce((somme, t) => somme + t.credit, 0)
    const revenuNet = totalCredit - totalDebit
    
    return {
      periode: 'Ce mois',
      totalDebit: {
        montant: totalDebit,
        chinois: this.convertirEnFormatComptable(totalDebit)
      },
      totalCredit: {
        montant: totalCredit,
        chinois: this.convertirEnFormatComptable(totalCredit)
      },
      revenuNet: {
        montant: revenuNet,
        chinois: this.convertirEnFormatComptable(revenuNet)
      },
      resume: `Revenus totaux de ce mois ${this.convertirEnFormatComptable(totalCredit)}, dépenses totales ${this.convertirEnFormatComptable(totalDebit)}, revenus nets ${this.convertirEnFormatComptable(revenuNet)}.`
    }
  }

  convertirEnChinois(num) {
    // Implémentation de conversion en chinois (simplifiée)
    const chiffres = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    const unites = ['', '拾', '佰', '仟', '万']
    
    if (num === 0) return '零'
    
    let resultat = ''
    const str = num.toString()
    const len = str.length
    
    for (let i = 0; i < len; i++) {
      const chiffre = parseInt(str[i])
      const indexUnite = len - i - 1
      
      if (chiffre !== 0) {
        resultat += chiffres[chiffre]
        if (indexUnite > 0 && indexUnite < unites.length) {
          resultat += unites[indexUnite]
        }
      }
    }
    
    return resultat
  }
}

// Exemple d'utilisation
const comptabilite = new ConvertisseurNombresComptable()

// Enregistrements de transactions
const transactions = [
  { debit: 0, credit: 50000, description: 'Revenus de ventes' },
  { debit: 15000, credit: 0, description: 'Achat de fournitures de bureau' },
  { debit: 8000, credit: 0, description: 'Frais de transport' }
]

// Générer rapport mensuel
const rapportMensuel = comptabilite.genererRapportMensuel(transactions)
console.log('Rapport mensuel:')
console.log(rapportMensuel.resume)
```

## 🔧 Détails Techniques

### Système de Nombres Chinois

Structure de base des nombres chinois :

**Nombres de Base :**
- Simplifié : 零、一、二、三、四、五、六、七、八、九
- Traditionnel : 零、壹、贰、叁、肆、伍、陆、柒、捌、玖

**Unités :**
- Unités de base : 十、百、千、万、亿
- Unités de montant : 元、角、分

**Règles Spéciales :**
- Gestion des zéros : Les zéros consécutifs se représentent par un seul "零"
- Omission d'unités : Omettre les unités sous certaines conditions
- Usage de majuscules : Utiliser des majuscules dans les documents financiers pour prévenir les altérations

### Algorithme de Conversion

```javascript
// Algorithme complet de conversion
class ConvertisseurNombresChinois {
  constructor() {
    this.chiffres = {
      simple: ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'],
      traditionnel: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    }
    this.unites = ['', '十', '百', '千', '万', '十', '百', '千', '亿']
  }

  convertir(nombre, type = 'simple') {
    if (nombre === 0) return this.chiffres[type][0]
    
    const estNegatif = nombre < 0
    const nombreAbs = Math.abs(nombre)
    const [partieEntiere, partieDecimale] = nombreAbs.toString().split('.')
    
    let resultat = this.convertirEntier(parseInt(partieEntiere), type)
    
    if (partieDecimale) {
      resultat += '点' + this.convertirDecimale(partieDecimale, type)
    }
    
    return estNegatif ? '负' + resultat : resultat
  }

  convertirEntier(num, type) {
    const str = num.toString()
    const len = str.length
    let resultat = ''
    let aZero = false
    
    for (let i = 0; i < len; i++) {
      const chiffre = parseInt(str[i])
      const indexUnite = len - i - 1
      
      if (chiffre === 0) {
        aZero = true
      } else {
        if (aZero && resultat !== '') {
          resultat += this.chiffres[type][0] // 零
        }
        resultat += this.chiffres[type][chiffre]
        if (indexUnite > 0) {
          resultat += this.unites[indexUnite]
        }
        aZero = false
      }
    }
    
    return resultat
  }

  convertirDecimale(decimale, type) {
    return decimale.split('').map(d => this.chiffres[type][parseInt(d)]).join('')
  }
}
```

## 💡 Conseils d'Utilisation

- **Sélection d'Usage** : Utiliser des majuscules pour les documents financiers, simplifié pour les documents généraux
- **Vérification de Précision** : Faire attention au nombre de décimales
- **Considération de Contexte** : Choisir le format approprié selon le type de document utilisé
- **Vérification Importante** : Toujours vérifier les résultats de conversion pour les documents importants

## ⚠️ Notes Importantes

- **Limites de Précision** : Faire attention à la précision pour les nombres très grands ou les décimales complexes
- **Différences Régionales** : La Chine continentale et Taiwan/Hong Kong peuvent avoir des notations différentes
- **Exigences Légales** : Utiliser une notation conforme aux exigences légales dans les documents financiers
- **Encodage de Caractères** : S'assurer de l'affichage correct des caractères chinois

## 🚀 Comment Utiliser

1. **Saisie de Nombres** : Entrez le nombre à convertir dans la zone de saisie
2. **Sélection de Type** : Choisissez simplifié, traditionnel ou montants en majuscules
3. **Vérification des Résultats** : Les résultats de conversion s'affichent en temps réel
4. **Utilisation de Copie** : Utilisez le bouton "Copier" pour copier les résultats dans le presse-papiers
5. **Usage Appliqué** : Utilisez dans les contrats, factures, rapports financiers, etc.

> **Conseil** : Cet outil traite localement côté client et n'envoie pas de données d'entrée au serveur, donc les informations financières confidentielles peuvent aussi être converties en toute sécurité.
