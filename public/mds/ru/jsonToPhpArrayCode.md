# Конвертер JSON в PHP Array Code

Этот инструмент конвертирует JSON данные в валидный PHP array код. Необходим для PHP разработчиков, которым нужно мигрировать JSON данные в нативные PHP array структуры, облегчая интеграцию и манипуляцию данными в PHP приложениях.

## ✨ Основные Характеристики

- 🔄 **Прямая Конвертация** : JSON в валидный PHP array код
- 🎨 **Красивое Форматирование** : Хорошо отформатированный и отступленный PHP код
- 📋 **Копирование Одним Кликом** : Сгенерированный код может быть скопирован напрямую
- ✅ **Валидация JSON** : Проверяет валидность JSON перед конвертацией
- 🌐 **Поддержка Unicode** : Совместим со специальными символами и акцентами

## 📖 Примеры Использования

### Конвертация Простого Объекта

**Входной JSON:**
```json
{
  "имя": "Иван Петров",
  "возраст": 30,
  "email": "<EMAIL>",
  "активен": true
}
```

**Сгенерированный PHP Код:**
```php
<?php
$array = [
    'имя' => 'Иван Петров',
    'возраст' => 30,
    'email' => '<EMAIL>',
    'активен' => true
];
?>
```

### Конвертация Сложного Массива

**Входной JSON:**
```json
{
  "пользователи": [
    {
      "id": 1,
      "имя": "Мария Иванова",
      "профиль": {
        "био": "PHP Разработчик",
        "навыки": ["PHP", "MySQL", "Laravel"]
      }
    },
    {
      "id": 2,
      "имя": "Петр Сидоров",
      "профиль": {
        "био": "UX/UI Дизайнер",
        "навыки": ["Figma", "Photoshop", "CSS"]
      }
    }
  ],
  "конфигурация": {
    "тема": "темная",
    "язык": "ru-RU"
  }
}
```

**Сгенерированный PHP Код:**
```php
<?php
$array = [
    'пользователи' => [
        [
            'id' => 1,
            'имя' => 'Мария Иванова',
            'профиль' => [
                'био' => 'PHP Разработчик',
                'навыки' => [
                    'PHP',
                    'MySQL',
                    'Laravel'
                ]
            ]
        ],
        [
            'id' => 2,
            'имя' => 'Петр Сидоров',
            'профиль' => [
                'био' => 'UX/UI Дизайнер',
                'навыки' => [
                    'Figma',
                    'Photoshop',
                    'CSS'
                ]
            ]
        ]
    ],
    'конфигурация' => [
        'тема' => 'темная',
        'язык' => 'ru-RU'
    ]
];
?>
```

## 🎯 Сценарии Применения

### 1. Миграция API Данных

```php
<?php
// Данные, полученные от внешнего API
$apiResponse = '{
    "товары": [
        {
            "id": 101,
            "название": "Смартфон XYZ",
            "цена": 25999.99,
            "категория": "электроника",
            "характеристики": {
                "экран": "6.1 дюйма",
                "память": "128ГБ",
                "камера": "48МП"
            },
            "доступен": true
        },
        {
            "id": 102,
            "название": "Ноутбук ABC",
            "цена": 89999.99,
            "категория": "компьютеры",
            "характеристики": {
                "процессор": "Intel i7",
                "память": "16ГБ RAM",
                "хранилище": "512ГБ SSD"
            },
            "доступен": false
        }
    ],
    "метаданные": {
        "всего": 2,
        "страница": 1,
        "временная_метка": "2024-01-15T10:30:00Z"
    }
}';

// Конвертировать в PHP массив (результат инструмента)
$товары = [
    'товары' => [
        [
            'id' => 101,
            'название' => 'Смартфон XYZ',
            'цена' => 25999.99,
            'категория' => 'электроника',
            'характеристики' => [
                'экран' => '6.1 дюйма',
                'память' => '128ГБ',
                'камера' => '48МП'
            ],
            'доступен' => true
        ],
        [
            'id' => 102,
            'название' => 'Ноутбук ABC',
            'цена' => 89999.99,
            'категория' => 'компьютеры',
            'характеристики' => [
                'процессор' => 'Intel i7',
                'память' => '16ГБ RAM',
                'хранилище' => '512ГБ SSD'
            ],
            'доступен' => false
        ]
    ],
    'метаданные' => [
        'всего' => 2,
        'страница' => 1,
        'временная_метка' => '2024-01-15T10:30:00Z'
    ]
];

// Использовать данные в PHP
foreach ($товары['товары'] as $товар) {
    echo "Товар: " . $товар['название'] . "\n";
    echo "Цена: ₽" . number_format($товар['цена'], 2, ',', ' ') . "\n";
    echo "Доступен: " . ($товар['доступен'] ? 'Да' : 'Нет') . "\n\n";
}
?>
```

### 2. Конфигурация Приложения

```php
<?php
// Файл конфигурации, сгенерированный из JSON
$config = [
    'приложение' => [
        'название' => 'Система ToolMi',
        'версия' => '2.1.0',
        'среда' => 'продакшн',
        'отладка' => false
    ],
    'база_данных' => [
        'драйвер' => 'mysql',
        'хост' => 'localhost',
        'порт' => 3306,
        'название' => 'toolmi_db',
        'пользователь' => 'root',
        'пароль' => '',
        'кодировка' => 'utf8mb4',
        'опции' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    ],
    'кеш' => [
        'драйвер' => 'redis',
        'хост' => '127.0.0.1',
        'порт' => 6379,
        'база' => 0,
        'ttl' => 3600
    ],
    'email' => [
        'драйвер' => 'smtp',
        'хост' => 'smtp.gmail.com',
        'порт' => 587,
        'пользователь' => '<EMAIL>',
        'пароль' => 'секретный_пароль',
        'шифрование' => 'tls',
        'отправитель' => [
            'имя' => 'Система ToolMi',
            'email' => '<EMAIL>'
        ]
    ],
    'функции' => [
        'аутентификация' => [
            'включена' => true,
            'провайдер' => 'jwt',
            'истечение' => 86400,
            'алгоритм' => 'HS256'
        ],
        'ограничение_запросов' => [
            'включено' => true,
            'лимит' => 1000,
            'окно' => 3600
        ],
        'cors' => [
            'включен' => true,
            'источники' => [
                'https://toolmi.com',
                'https://www.toolmi.com'
            ],
            'методы' => ['GET', 'POST', 'PUT', 'DELETE'],
            'заголовки' => ['Content-Type', 'Authorization']
        ]
    ]
];

// Использовать конфигурацию
class МенеджерКонфигурации 
{
    private $config;
    
    public function __construct($config) 
    {
        $this->config = $config;
    }
    
    public function получить($ключ, $поУмолчанию = null) 
    {
        $ключи = explode('.', $ключ);
        $значение = $this->config;
        
        foreach ($ключи as $k) {
            if (!isset($значение[$k])) {
                return $поУмолчанию;
            }
            $значение = $значение[$k];
        }
        
        return $значение;
    }
    
    public function получитьКонфигурациюБД() 
    {
        return $this->получить('база_данных');
    }
    
    public function отладкаВключена() 
    {
        return $this->получить('приложение.отладка', false);
    }
}

$менеджерКонфигурации = new МенеджерКонфигурации($config);
echo "Название приложения: " . $менеджерКонфигурации->получить('приложение.название') . "\n";
echo "Отладка включена: " . ($менеджерКонфигурации->отладкаВключена() ? 'Да' : 'Нет') . "\n";
?>
```

### 3. Тестовые Данные и Фикстуры

```php
<?php
// Тестовые данные для разработки
$тестовыеДанные = [
    'пользователи' => [
        [
            'id' => 1,
            'имя' => 'Админ Системы',
            'email' => '<EMAIL>',
            'пароль' => password_hash('admin123', PASSWORD_DEFAULT),
            'тип' => 'администратор',
            'активен' => true,
            'создан' => '2024-01-01 00:00:00',
            'разрешения' => [
                'пользователи.создать',
                'пользователи.редактировать',
                'пользователи.удалить',
                'система.настроить'
            ]
        ],
        [
            'id' => 2,
            'имя' => 'Иван Разработчик',
            'email' => '<EMAIL>',
            'пароль' => password_hash('dev123', PASSWORD_DEFAULT),
            'тип' => 'разработчик',
            'активен' => true,
            'создан' => '2024-01-02 08:30:00',
            'разрешения' => [
                'инструменты.использовать',
                'отчеты.просматривать'
            ]
        ],
        [
            'id' => 3,
            'имя' => 'Мария Дизайнер',
            'email' => '<EMAIL>',
            'пароль' => password_hash('design123', PASSWORD_DEFAULT),
            'тип' => 'дизайнер',
            'активен' => true,
            'создан' => '2024-01-03 14:15:00',
            'разрешения' => [
                'инструменты.использовать',
                'темы.редактировать'
            ]
        ]
    ],
    'инструменты' => [
        [
            'id' => 'base64',
            'название' => 'Кодирование Base64',
            'категория' => 'кодирование',
            'активен' => true,
            'месячное_использование' => 15420,
            'рейтинг' => 4.8
        ],
        [
            'id' => 'uuid',
            'название' => 'Генератор UUID',
            'категория' => 'генерация',
            'активен' => true,
            'месячное_использование' => 12350,
            'рейтинг' => 4.7
        ],
        [
            'id' => 'json-format',
            'название' => 'Форматировщик JSON',
            'категория' => 'форматирование',
            'активен' => true,
            'месячное_использование' => 9870,
            'рейтинг' => 4.9
        ]
    ],
    'настройки_системы' => [
        'обслуживание' => false,
        'регистрация_включена' => true,
        'тема_по_умолчанию' => 'светлая',
        'язык_по_умолчанию' => 'ru-RU',
        'часовой_пояс' => 'Europe/Moscow',
        'лимиты' => [
            'максимальная_загрузка' => '10MB',
            'запросов_в_минуту' => 60,
            'длительность_сессии' => 7200
        ]
    ]
];

// Класс для заполнения базы данных тестовыми данными
class ЗаполнительБД 
{
    private $pdo;
    
    public function __construct($pdo) 
    {
        $this->pdo = $pdo;
    }
    
    public function заполнить($данные) 
    {
        $this->pdo->beginTransaction();
        
        try {
            // Заполнить пользователей
            foreach ($данные['пользователи'] as $пользователь) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO пользователи (id, имя, email, пароль, тип, активен, создан) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $пользователь['id'],
                    $пользователь['имя'],
                    $пользователь['email'],
                    $пользователь['пароль'],
                    $пользователь['тип'],
                    $пользователь['активен'],
                    $пользователь['создан']
                ]);
                
                // Заполнить разрешения
                foreach ($пользователь['разрешения'] as $разрешение) {
                    $stmt = $this->pdo->prepare("
                        INSERT INTO пользователь_разрешения (пользователь_id, разрешение) 
                        VALUES (?, ?)
                    ");
                    $stmt->execute([$пользователь['id'], $разрешение]);
                }
            }
            
            $this->pdo->commit();
            echo "Тестовые данные успешно вставлены!\n";
            
        } catch (Exception $e) {
            $this->pdo->rollback();
            echo "Ошибка при вставке данных: " . $e->getMessage() . "\n";
        }
    }
}
?>
```

## 🔧 Технические Детали

### Поддерживаемые Типы Данных

**Конвертации JSON → PHP:**
- **String**: `"текст"` → `'текст'`
- **Number**: `123` → `123`
- **Boolean**: `true/false` → `true/false`
- **Null**: `null` → `null`
- **Array**: `[1,2,3]` → `[1, 2, 3]`
- **Object**: `{"a":"b"}` → `['a' => 'b']`

### Форматирование Кода

**Характеристики Вывода:**
- Отступы в 4 пробела
- Одинарные кавычки для строк
- Короткий синтаксис массива `[]`
- Опциональные завершающие запятые
- Сохранение комментариев когда возможно

### Обработка Специальных Символов

**Автоматическое Экранирование:**
- Одинарные кавычки: `'` → `\'`
- Обратные слеши: `\` → `\\`
- Unicode символы сохраняются
- Переносы строк сохраняются

## 💡 Советы по Использованию

- **Валидация** : Всегда валидируйте JSON перед конвертацией
- **Тестирование** : Тестируйте сгенерированный PHP код перед использованием в продакшене
- **Форматирование** : Используйте сгенерированный код как основу и настраивайте по необходимости
- **Производительность** : Для больших массивов рассмотрите использование `var_export()` в PHP

## ⚠️ Важные Замечания

- **Синтаксис PHP** : Сгенерированный код использует синтаксис PHP 5.4+
- **Кодировка** : Убедитесь, что PHP файл использует UTF-8
- **Валидация** : Всегда валидируйте данные перед использованием в продакшене
- **Безопасность** : Не включайте чувствительные данные в статические массивы

## 🚀 Как Использовать

1. **Ввод JSON** : Вставьте валидный JSON в область ввода
2. **Автоматическая Валидация** : JSON валидируется автоматически
3. **Конвертация** : Нажмите "Конвертировать" для генерации PHP кода
4. **Проверка** : Просмотрите сгенерированный PHP код
5. **Использование Копирования** : Нажмите "Копировать" для использования кода

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и безопасность ваших данных.
