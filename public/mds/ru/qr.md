# Генератор QR Кодов

QR код (Quick Response Code) - это двумерный штрих-код, изобретенный компанией Denso Wave из Японии в 1994 году. Этот инструмент может быстро конвертировать текст, URL, контактную информацию и другое разнообразное содержимое в изображения QR кодов.

## ✨ Основные Характеристики

- 📱 **Поддержка Мульти-контента** : Совместим с текстом, URL, контактами, паролями WiFi и т.д.
- 🎨 **Высококачественный Вывод** : Генерирует высококачественные изображения QR кодов
- 📏 **Регулируемые Размеры** : Совместим с множественными спецификациями размеров
- 🎯 **Уровни Коррекции Ошибок** : Совместим с различными настройками уровней коррекции ошибок
- 💾 **Загрузка Одним Кликом** : Загружает и сохраняет сгенерированные QR коды напрямую
- 🔧 **Предварительный Просмотр в Реальном Времени** : Генерирует предварительный просмотр QR кода мгновенно согласно входному содержимому

## 📖 Примеры Использования

### QR Код URL

**Входное Содержимое:**
```
https://www.toolmi.com
```

**Сгенерированный Эффект:**
- После сканирования перенаправляет напрямую на сайт ToolMi
- Применим к продвижению сайтов, обмену ссылками

### QR Код Текста

**Входное Содержимое:**
```
Добро пожаловать в онлайн инструменты ToolMi!
Здесь находятся богатые инструменты разработки и практичные функции.
```

**Сгенерированный Эффект:**
- После сканирования отображает полное содержимое текста
- Применим к передаче информации, отображению инструкций

### QR Код Контакта

**Входное Содержимое:**
```
BEGIN:VCARD
VERSION:3.0
FN:Иван Петров
ORG:Технология ToolMi
TEL:******-123-45-67
EMAIL:<EMAIL>
URL:https://www.toolmi.com
END:VCARD
```

**Сгенерированный Эффект:**
- После сканирования может быть добавлен напрямую в контакты
- Включает информацию как имя, компания, телефон, email

## 🎯 Сценарии Применения

### 1. Продвижение Сайтов

Генерация промо QR кодов для сайтов и приложений:

```html
<!-- QR код на промо плакате -->
<div class="промо">
  <h3>Отсканируйте QR код для доступа к ToolMi</h3>
  <img src="qr-code.png" alt="QR код ToolMi">
  <p>Откройте больше практичных инструментов</p>
</div>

<!-- Сценарии применения -->
- Промо плакаты
- Дизайн визитных карточек
- Упаковка продуктов
- Рекламные публикации
```

### 2. Мобильные Платежи

Генерация QR кодов платежей:

```javascript
// Пример ссылки платежа PayPal
const paypalUrl = 'https://paypal.me/пользователь/50RUB'

// Пример ссылки российского мобильного платежа
const sbpUrl = 'sbp://pay?amount=50&recipient=пользователь@банк.ru'

// Генерировать QR код платежа
генерироватьQRКод(paypalUrl, {
  размер: 200,
  уровеньКоррекцииОшибок: 'M'
})
```

### 3. Поделиться Паролем WiFi

Генерация QR кода подключения WiFi:

```
Формат QR кода WiFi:
WIFI:T:WPA;S:имя_сети;P:пароль;H:false;;

Пример:
WIFI:T:WPA;S:ToolMi_5G;P:12345678;H:false;;

Объяснение параметров:
- T: Тип шифрования (WPA/WEP/nopass)
- S: Имя сети (SSID)
- P: Пароль
- H: Скрывать ли сеть (true/false)
```

### 4. Регистрация на События

Генерация QR кода регистрации на события:

```json
{
  "тип": "регистрация_события",
  "id_события": "tech_meetup_2024",
  "название_события": "Технологическая Встреча",
  "место": "Конференц-центр Москвы",
  "дата": "2024-06-15",
  "url_регистрации": "https://событие.toolmi.com/регистрация/tech_meetup_2024"
}
```

### 5. Отслеживаемость Продуктов

Генерация QR кода отслеживаемости продуктов:

```javascript
// Информация о продукте
const инфоПродукта = {
  id: 'TM2024001',
  название: 'Умный Набор Инструментов',
  партия: 'П20240615',
  дата_производства: '2024-06-15',
  производитель: 'Технология ToolMi',
  контроль_качества: 'ПРОЙДЕН',
  url_отслеживания: 'https://отслеживание.toolmi.com/продукт/TM2024001'
}

// Генерировать QR код отслеживаемости
const данныеОтслеживания = JSON.stringify(инфоПродукта)
генерироватьQRКод(данныеОтслеживания)
```

## 🔧 Технические Детали

### Структура QR Кода

Базовая структура QR кода:

**Функциональные Паттерны:**
- Паттерны обнаружения позиции: Большие квадраты в трех углах
- Разделители паттернов обнаружения позиции: Белые рамки
- Паттерны выравнивания: Маленькие черные точки для определения направления

**Область Данных:**
- Информация формата: Уровень коррекции ошибок и информация маски
- Информация версии: Номер версии QR кода
- Кодовые слова данных и коррекции ошибок: Фактически хранимые данные

**Спецификации Емкости:**
- Версия 1: 21×21 модулей, максимум 25 символов
- Версия 40: 177×177 модулей, максимум 4296 символов
- Совместим с числовыми, алфавитными, кандзи и двоичными данными

### Уровни Коррекции Ошибок

QR код поддерживает 4 уровня коррекции ошибок:

| Уровень | Коэффициент Коррекции | Сценарий Применения |
|---------|----------------------|---------------------|
| L | ~7% | Чистая среда, высококачественная печать |
| M | ~15% | Общая среда, стандартная печать |
| Q | ~25% | Сложная среда, возможное загрязнение |
| H | ~30% | Очень сложная среда, серьезное загрязнение |

**Рекомендации по Выбору:**
- Обмен URL: Использовать уровень L или M
- Наружная реклама: Использовать уровень Q или H
- Этикетки продуктов: Использовать уровень M или Q

## 💡 Советы по Использованию

- **Оптимизация Содержимого** : Использовать максимально короткое содержимое для улучшения успешности сканирования
- **Выбор Размера** : Выбирать подходящий размер согласно расстоянию использования
- **Уровень Коррекции Ошибок** : Выбирать подходящий уровень коррекции ошибок согласно среде использования
- **Проверка Тестов** : После генерации тестировать эффект сканирования с множественными устройствами

## ⚠️ Важные Замечания

- **Длина Содержимого** : Слишком длинное содержимое сделает QR код сложным, влияя на сканирование
- **Качество Печати** : Обеспечить четкую печать, избегать размытости и деформации
- **Цветовой Контраст** : Поддерживать достаточный цветовой контраст, рекомендуется черно-белая комбинация
- **Окружающая Граница** : Оставлять достаточно белого пространства вокруг QR кода

## 🚀 Как Использовать

1. **Ввод Содержимого** : Введите содержимое для генерации QR кода в область ввода
2. **Настройка Конфигурации** : Выберите подходящий размер и уровень коррекции ошибок
3. **Генерировать Предварительный Просмотр** : Нажмите кнопку генерации для просмотра QR кода
4. **Загрузить и Сохранить** : Нажмите кнопку загрузки для сохранения изображения QR кода
5. **Тест Сканирования** : Используйте смартфон для сканирования и тестирования сгенерированного эффекта

> **Совет** : Этот инструмент генерирует QR коды локально на стороне клиента и не загружает данные на сервер, гарантируя конфиденциальность и безопасность.
