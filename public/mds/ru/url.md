# Инструмент Кодирования и Декодирования URL

Кодирование URL (также известное как процентное кодирование) - это механизм для кодирования информации в URL, когда данные содержат специальные символы или не-ASCII символы. Этот инструмент предоставляет двунаправленную конвертацию между обычным текстом и форматом кодированного URL, что необходимо для веб-разработки и API коммуникации.

## ✨ Основные Характеристики

- 🔄 **Двунаправленная Конвертация** : Совместим с кодированием и декодированием URL
- 🌐 **Поддержка Unicode** : Правильно обрабатывает русские символы и специальные символы
- 📊 **Множественные Форматы** : Совместим с кодированием компонентов URL и полного URL
- 📋 **Копирование Одним Кликом** : Результаты могут быть скопированы напрямую для использования
- ⚡ **Обработка в Реальном Времени** : Мгновенная конвертация согласно вводу

## 📖 Примеры Использования

### Кодирование Русского Текста

**Ввод:**
```
Привет, как дела? Всё хорошо!
```

**Кодированный URL:**
```
%D0%9F%D1%80%D0%B8%D0%B2%D0%B5%D1%82%2C%20%D0%BA%D0%B0%D0%BA%20%D0%B4%D0%B5%D0%BB%D0%B0%3F%20%D0%92%D1%81%D1%91%20%D1%85%D0%BE%D1%80%D0%BE%D1%88%D0%BE%21
```

### Кодирование URL с Параметрами

**Ввод:**
```
https://www.toolmi.com/поиск?q=полезный инструмент&категория=разработка
```

**Кодированный URL:**
```
https%3A//www.toolmi.com/%D0%BF%D0%BE%D0%B8%D1%81%D0%BA%3Fq%3D%D0%BF%D0%BE%D0%BB%D0%B5%D0%B7%D0%BD%D1%8B%D0%B9%20%D0%B8%D0%BD%D1%81%D1%82%D1%80%D1%83%D0%BC%D0%B5%D0%BD%D1%82%26%D0%BA%D0%B0%D1%82%D0%B5%D0%B3%D0%BE%D1%80%D0%B8%D1%8F%3D%D1%80%D0%B0%D0%B7%D1%80%D0%B0%D0%B1%D0%BE%D1%82%D0%BA%D0%B0
```

### Кодирование JSON Данных

**Ввод:**
```
{"имя": "Иван Петров", "email": "<EMAIL>"}
```

**Кодированный URL:**
```
%7B%22%D0%B8%D0%BC%D1%8F%22%3A%20%22%D0%98%D0%B2%D0%B0%D0%BD%20%D0%9F%D0%B5%D1%82%D1%80%D0%BE%D0%B2%22%2C%20%22email%22%3A%20%22ivan%40example.com%22%7D
```

## 🎯 Сценарии Применения

### 1. Разработка API

```javascript
// API клиент с автоматическим кодированием параметров
class КлиентAPI {
  constructor(базовыйUrl) {
    this.базовыйUrl = базовыйUrl;
  }

  // Построить URL с кодированными параметрами
  построитьURL(endpoint, параметры = {}) {
    const url = new URL(endpoint, this.базовыйUrl);
    
    // Добавить кодированные параметры запроса
    Object.entries(параметры).forEach(([ключ, значение]) => {
      if (значение !== null && значение !== undefined) {
        url.searchParams.append(ключ, значение);
      }
    });

    return url.toString();
  }

  // Сделать GET запрос с параметрами
  async get(endpoint, параметры = {}) {
    const url = this.построитьURL(endpoint, параметры);
    
    try {
      const ответ = await fetch(url);
      if (!ответ.ok) {
        throw new Error(`HTTP ошибка: ${ответ.status}`);
      }
      return await ответ.json();
    } catch (ошибка) {
      console.error('Ошибка GET запроса:', ошибка);
      throw ошибка;
    }
  }

  // Сделать POST запрос с кодированными данными
  async post(endpoint, данные) {
    const url = this.построитьURL(endpoint);
    
    try {
      const ответ = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(данные)
      });

      if (!ответ.ok) {
        throw new Error(`HTTP ошибка: ${ответ.status}`);
      }
      return await ответ.json();
    } catch (ошибка) {
      console.error('Ошибка POST запроса:', ошибка);
      throw ошибка;
    }
  }

  // Кодировать данные формы
  кодироватьФорму(данные) {
    const параметры = new URLSearchParams();
    
    Object.entries(данные).forEach(([ключ, значение]) => {
      if (значение !== null && значение !== undefined) {
        параметры.append(ключ, значение);
      }
    });

    return параметры.toString();
  }
}

// Пример использования
const api = new КлиентAPI('https://api.toolmi.com');

// Поиск пользователей с фильтрами
const параметрыПоиска = {
  имя: 'Иван Петров',
  email: '<EMAIL>',
  город: 'Москва',
  активен: true
};

api.get('/пользователи', параметрыПоиска)
  .then(пользователи => {
    console.log('Найденные пользователи:', пользователи);
  })
  .catch(ошибка => {
    console.error('Ошибка поиска:', ошибка);
  });

// Создать нового пользователя
const новыйПользователь = {
  имя: 'Мария Иванова',
  email: '<EMAIL>',
  био: 'Разработчик, увлеченный технологиями!'
};

api.post('/пользователи', новыйПользователь)
  .then(результат => {
    console.log('Пользователь создан:', результат);
  });
```

### 2. Манипуляция URL

```javascript
// Утилита для продвинутой манипуляции URL
class МанипуляторURL {
  constructor(url) {
    this.url = new URL(url);
  }

  // Добавить или обновить параметр
  установитьПараметр(ключ, значение) {
    this.url.searchParams.set(ключ, значение);
    return this;
  }

  // Удалить параметр
  удалитьПараметр(ключ) {
    this.url.searchParams.delete(ключ);
    return this;
  }

  // Получить декодированный параметр
  получитьПараметр(ключ) {
    return this.url.searchParams.get(ключ);
  }

  // Получить все параметры
  получитьВсеПараметры() {
    const параметры = {};
    for (const [ключ, значение] of this.url.searchParams.entries()) {
      параметры[ключ] = значение;
    }
    return параметры;
  }

  // Очистить все параметры
  очиститьПараметры() {
    this.url.search = '';
    return this;
  }

  // Изменить домен
  изменитьДомен(новыйДомен) {
    this.url.hostname = новыйДомен;
    return this;
  }

  // Изменить протокол
  изменитьПротокол(новыйПротокол) {
    this.url.protocol = новыйПротокол;
    return this;
  }

  // Добавить фрагмент хеша
  установитьХеш(хеш) {
    this.url.hash = хеш;
    return this;
  }

  // Получить финальный URL
  toString() {
    return this.url.toString();
  }

  // Анализировать компоненты URL
  анализировать() {
    return {
      протокол: this.url.protocol,
      домен: this.url.hostname,
      порт: this.url.port,
      путь: this.url.pathname,
      параметры: this.получитьВсеПараметры(),
      хеш: this.url.hash,
      полныйUrl: this.url.toString()
    };
  }

  // Валидировать URL
  static валидировать(url) {
    try {
      new URL(url);
      return { действителен: true };
    } catch (ошибка) {
      return { 
        действителен: false, 
        ошибка: ошибка.message 
      };
    }
  }

  // Сравнить URL (игнорируя порядок параметров)
  static сравнить(url1, url2) {
    try {
      const u1 = new URL(url1);
      const u2 = new URL(url2);

      // Сравнить основные компоненты
      if (u1.protocol !== u2.protocol ||
          u1.hostname !== u2.hostname ||
          u1.port !== u2.port ||
          u1.pathname !== u2.pathname ||
          u1.hash !== u2.hash) {
        return false;
      }

      // Сравнить параметры (игнорируя порядок)
      const params1 = new Set(u1.searchParams.toString().split('&').sort());
      const params2 = new Set(u2.searchParams.toString().split('&').sort());

      if (params1.size !== params2.size) {
        return false;
      }

      for (const param of params1) {
        if (!params2.has(param)) {
          return false;
        }
      }

      return true;
    } catch (ошибка) {
      return false;
    }
  }
}

// Примеры использования
const манипулятор = new МанипуляторURL('https://www.toolmi.com/поиск');

// Построить URL поиска
манипулятор
  .установитьПараметр('q', 'инструмент разработки')
  .установитьПараметр('категория', 'программирование')
  .установитьПараметр('сортировка', 'релевантность')
  .установитьХеш('результаты');

console.log('Построенный URL:', манипулятор.toString());

// Анализировать URL
const анализ = манипулятор.анализировать();
console.log('Анализ URL:', анализ);

// Валидировать URL
console.log('Действительный URL:', МанипуляторURL.валидировать('https://www.toolmi.com'));
console.log('Недействительный URL:', МанипуляторURL.валидировать('не-url'));

// Сравнить URL
const url1 = 'https://example.com?a=1&b=2';
const url2 = 'https://example.com?b=2&a=1';
console.log('URL одинаковые:', МанипуляторURL.сравнить(url1, url2)); // true
```

### 3. Система Маршрутизации

```javascript
// Система маршрутизации с кодированием URL
class СистемаМаршрутизации {
  constructor() {
    this.маршруты = new Map();
    this.промежуточноеПО = [];
    this.глобальныеПараметры = new Map();
  }

  // Зарегистрировать маршрут
  зарегистрироватьМаршрут(шаблон, обработчик, опции = {}) {
    const маршрут = {
      шаблон: this.компилироватьШаблон(шаблон),
      обработчик,
      опции
    };
    
    this.маршруты.set(шаблон, маршрут);
    return this;
  }

  // Компилировать шаблон маршрута в regex
  компилироватьШаблон(шаблон) {
    const параметры = [];
    
    // Конвертировать шаблон как '/пользователь/:id/профиль' в regex
    const regex = шаблон.replace(/:([^/]+)/g, (совпадение, имяПарам) => {
      параметры.push(имяПарам);
      return '([^/]+)';
    });

    return {
      regex: new RegExp(`^${regex}$`),
      параметры
    };
  }

  // Навигировать к URL
  async навигировать(url, данные = {}) {
    try {
      const urlObj = new URL(url, window.location.origin);
      const путь = urlObj.pathname;
      
      // Найти соответствующий маршрут
      const соответствующийМаршрут = this.найтиМаршрут(путь);
      
      if (!соответствующийМаршрут) {
        throw new Error(`Маршрут не найден: ${путь}`);
      }

      // Извлечь параметры из URL
      const параметры = this.извлечьПараметры(путь, соответствующийМаршрут.маршрут);
      
      // Извлечь параметры запроса
      const параметрыЗапроса = {};
      for (const [ключ, значение] of urlObj.searchParams.entries()) {
        параметрыЗапроса[ключ] = значение;
      }

      // Создать контекст маршрута
      const контекст = {
        url: urlObj.toString(),
        путь,
        параметры,
        запрос: параметрыЗапроса,
        данные,
        хеш: urlObj.hash
      };

      // Выполнить промежуточное ПО
      for (const mw of this.промежуточноеПО) {
        await mw(контекст);
      }

      // Выполнить обработчик маршрута
      await соответствующийМаршрут.маршрут.обработчик(контекст);

      // Обновить историю браузера
      if (соответствующийМаршрут.маршрут.опции.обновлятьИсторию !== false) {
        history.pushState(данные, '', url);
      }

      return контекст;
    } catch (ошибка) {
      console.error('Ошибка навигации:', ошибка);
      throw ошибка;
    }
  }

  // Найти соответствующий маршрут
  найтиМаршрут(путь) {
    for (const [шаблон, маршрут] of this.маршруты.entries()) {
      if (маршрут.шаблон.regex.test(путь)) {
        return { шаблон, маршрут };
      }
    }
    return null;
  }

  // Извлечь параметры из URL
  извлечьПараметры(путь, маршрут) {
    const совпадение = путь.match(маршрут.шаблон.regex);
    const параметры = {};

    if (совпадение) {
      маршрут.шаблон.параметры.forEach((имяПарам, индекс) => {
        параметры[имяПарам] = decodeURIComponent(совпадение[индекс + 1]);
      });
    }

    return параметры;
  }

  // Добавить промежуточное ПО
  использовать(промежуточноеПО) {
    this.промежуточноеПО.push(промежуточноеПО);
    return this;
  }

  // Генерировать URL для маршрута
  генерироватьURL(шаблон, параметры = {}, запрос = {}) {
    let url = шаблон;

    // Заменить параметры в URL
    Object.entries(параметры).forEach(([ключ, значение]) => {
      url = url.replace(`:${ключ}`, encodeURIComponent(значение));
    });

    // Добавить параметры запроса
    const urlObj = new URL(url, window.location.origin);
    Object.entries(запрос).forEach(([ключ, значение]) => {
      urlObj.searchParams.set(ключ, значение);
    });

    return urlObj.toString();
  }
}

// Пример использования
const маршрутизатор = new СистемаМаршрутизации();

// Промежуточное ПО аутентификации
маршрутизатор.использовать(async (контекст) => {
  console.log(`Навигация к: ${контекст.путь}`);
  
  // Проверить аутентификацию при необходимости
  if (контекст.путь.startsWith('/админ')) {
    const токен = localStorage.getItem('authToken');
    if (!токен) {
      throw new Error('Доступ запрещен: требуется аутентификация');
    }
  }
});

// Зарегистрировать маршруты
маршрутизатор
  .зарегистрироватьМаршрут('/', async (ctx) => {
    console.log('Главная страница');
  })
  .зарегистрироватьМаршрут('/пользователь/:id', async (ctx) => {
    console.log(`Профиль пользователя: ${ctx.параметры.id}`);
    console.log('Параметры запроса:', ctx.запрос);
  })
  .зарегистрироватьМаршрут('/поиск', async (ctx) => {
    console.log(`Поиск по: ${ctx.запрос.q}`);
  });

// Навигировать
маршрутизатор.навигировать('/пользователь/123?вкладка=профиль&редактировать=true')
  .then(контекст => {
    console.log('Навигация завершена:', контекст);
  })
  .catch(ошибка => {
    console.error('Ошибка навигации:', ошибка);
  });

// Генерировать URL
const urlПрофиля = маршрутизатор.генерироватьURL('/пользователь/:id', 
  { id: 'иван-петров' }, 
  { вкладка: 'настройки' }
);
console.log('Сгенерированный URL:', urlПрофиля);
```

## 🔧 Технические Детали

### Процентное Кодирование

Кодирование URL использует формат `%XX` где XX - шестнадцатеричное значение:

**Общие Специальные Символы:**
- Пробел: `%20`
- `!`: `%21`
- `"`: `%22`
- `#`: `%23`
- `$`: `%24`
- `%`: `%25`
- `&`: `%26`
- `'`: `%27`
- `(`: `%28`
- `)`: `%29`
- `+`: `%2B`

### Русские Символы

**Кириллические Символы (UTF-8):**
- `а`: `%D0%B0`
- `б`: `%D0%B1`
- `в`: `%D0%B2`
- `г`: `%D0%B3`
- `д`: `%D0%B4`
- `е`: `%D0%B5`
- `ё`: `%D1%91`
- `ж`: `%D0%B6`
- `з`: `%D0%B7`
- `и`: `%D0%B8`
- `й`: `%D0%B9`
- `к`: `%D0%BA`

## 💡 Советы по Использованию

- **Параметры Запроса** : Всегда кодировать значения параметров запроса
- **Компоненты Пути** : Кодировать сегменты пути, содержащие специальные символы
- **Данные Формы** : Использовать `application/x-www-form-urlencoded` для данных формы
- **RESTful API** : Кодировать ID и параметры в URL API

## ⚠️ Важные Замечания

- **Двойное Кодирование** : Избегать кодирования уже кодированных URL
- **Подходящий Контекст** : Использовать `encodeURIComponent()` для параметров и `encodeURI()` для полных URL
- **Декодирование** : Всегда декодировать данные, полученные из URL
- **Валидация** : Валидировать декодированные URL для предотвращения атак

## 🚀 Как Использовать

1. **Ввод Данных** : Введите текст или URL для кодирования/декодирования
2. **Выбор Операции** : Выберите "Кодировать" или "Декодировать"
3. **Проверка Результатов** : Результат отображается мгновенно
4. **Использование Копирования** : Нажмите "Копировать" для копирования результата
5. **Применение** : Используйте в веб-разработке, API и манипуляции URL

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и безопасность.
