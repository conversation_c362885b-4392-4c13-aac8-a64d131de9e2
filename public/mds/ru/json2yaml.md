# Инструмент Конвертации JSON ⇄ YAML

Этот инструмент предоставляет двунаправленную конвертацию между форматами JSON и YAML, двумя широко используемыми форматами данных в современной разработке. JSON идеален для API и веб-коммуникации, в то время как YAML предпочтителен для конфигурационных файлов благодаря своей читаемости для человека.

## ✨ Основные Характеристики

- 🔄 **Двунаправленная Конвертация** : Безупречная конвертация между JSON и YAML
- 🌐 **Поддержка Unicode** : Совместим с русскими символами и другими специальными символами
- ✅ **Валидация Формата** : Автоматическое обнаружение синтаксических ошибок
- 📋 **Копирование Одним Кликом** : Результаты могут быть скопированы напрямую
- 🎨 **Красивое Форматирование** : Хорошо отформатированный и читаемый вывод

## 📖 Примеры Использования

### Конвертация JSON → YAML

**Входной JSON:**
```json
{
  "приложение": {
    "название": "ToolMi",
    "версия": "1.0.0",
    "описание": "Онлайн инструменты нового поколения"
  },
  "сервер": {
    "хост": "localhost",
    "порт": 3000,
    "ssl": false
  },
  "функции": [
    "кодирование-base64",
    "генерация-uuid",
    "форматирование-json"
  ]
}
```

**Конвертированный YAML:**
```yaml
приложение:
  название: ToolMi
  версия: 1.0.0
  описание: Онлайн инструменты нового поколения
сервер:
  хост: localhost
  порт: 3000
  ssl: false
функции:
  - кодирование-base64
  - генерация-uuid
  - форматирование-json
```

### Конвертация YAML → JSON

**Входной YAML:**
```yaml
база_данных:
  тип: postgresql
  хост: db.toolmi.com
  порт: 5432
  учетные_данные:
    пользователь: admin
    пароль: безопасный_пароль
конфигурация:
  отладка: true
  уровень_логов: info
  включенные_функции:
    - аутентификация
    - кеширование
    - мониторинг
```

**Конвертированный JSON:**
```json
{
  "база_данных": {
    "тип": "postgresql",
    "хост": "db.toolmi.com",
    "порт": 5432,
    "учетные_данные": {
      "пользователь": "admin",
      "пароль": "безопасный_пароль"
    }
  },
  "конфигурация": {
    "отладка": true,
    "уровень_логов": "info",
    "включенные_функции": [
      "аутентификация",
      "кеширование",
      "мониторинг"
    ]
  }
}
```

## 🎯 Сценарии Применения

### 1. Конфигурационные Файлы

```yaml
# config/приложение.yml
приложение:
  название: "Система ToolMi"
  среда: продакшн
  отладка: false
  
сервер:
  хост: "0.0.0.0"
  порт: 8080
  воркеры: 4
  таймаут: 30

база_данных:
  по_умолчанию: postgresql
  соединения:
    postgresql:
      драйвер: postgresql
      хост: localhost
      порт: 5432
      база: toolmi_db
      пользователь: postgres
      пароль: ${DB_PASSWORD}
      пул:
        мин: 2
        макс: 10

кеш:
  драйвер: redis
  хост: localhost
  порт: 6379
  база: 0
  ttl: 3600

логирование:
  уровень: info
  формат: json
  выводы:
    - консоль
    - файл
  файл:
    путь: /var/log/toolmi.log
    ротация: ежедневная
    хранение: 30

функции:
  аутентификация:
    включено: true
    провайдер: jwt
    истечение: 86400
  
  ограничение_запросов:
    включено: true
    лимит: 1000
    окно: 3600
  
  cors:
    включено: true
    источники:
      - "https://toolmi.com"
      - "https://www.toolmi.com"
    методы:
      - GET
      - POST
      - PUT
      - DELETE
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: toolmi
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: безопасный_пароль
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    driver: bridge
```

### 3. CI/CD Pipeline

```yaml
# .github/workflows/deploy.yml
name: Развертывание ToolMi

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: toolmi/app

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout кода
        uses: actions/checkout@v3
      
      - name: Настройка Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Установка зависимостей
        run: npm ci
      
      - name: Запуск тестов
        run: npm test
      
      - name: Запуск линтинга
        run: npm run lint
      
      - name: Проверка покрытия
        run: npm run coverage

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout кода
        uses: actions/checkout@v3
      
      - name: Вход в Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Извлечение метаданных
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
      
      - name: Сборка и push образа
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: Развертывание в продакшн
        run: |
          echo "Развертывание в продакшн..."
          # Команды развертывания здесь
```

### 4. Kubernetes Manifests

```yaml
# k8s/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  namespace: toolmi
  labels:
    app: toolmi
    component: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: toolmi
      component: backend
  template:
    metadata:
      labels:
        app: toolmi
        component: backend
    spec:
      containers:
        - name: app
          image: toolmi/app:latest
          ports:
            - containerPort: 3000
              name: http
          env:
            - name: NODE_ENV
              value: "production"
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: toolmi-secrets
                  key: db-host
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: toolmi-secrets
                  key: db-password
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
  namespace: toolmi
spec:
  selector:
    app: toolmi
    component: backend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: toolmi-ingress
  namespace: toolmi
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
    - hosts:
        - toolmi.com
        - www.toolmi.com
      secretName: toolmi-tls
  rules:
    - host: toolmi.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: toolmi-service
                port:
                  number: 80
    - host: www.toolmi.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: toolmi-service
                port:
                  number: 80
```

## 🔧 Технические Детали

### Различия в Синтаксисе

**JSON:**
- Использует фигурные скобки `{}` для объектов
- Использует квадратные скобки `[]` для массивов
- Требует двойные кавычки для строк
- Не позволяет комментарии
- Более многословный, но широко поддерживаемый

**YAML:**
- Использует отступы для структуры
- Поддерживает комментарии с `#`
- Строки могут быть без кавычек
- Более читаемый для людей
- Чувствителен к отступам

### Поддерживаемые Типы Данных

**Базовые Типы:**
- **String**: `"текст"` или `текст`
- **Number**: `123`, `45.67`
- **Boolean**: `true`, `false`
- **Null**: `null` или `~`
- **Array**: `[элемент1, элемент2]` или список с `-`
- **Object**: `{ключ: значение}` или структура с отступами

### Специальные Случаи

**Многострочные строки в YAML:**
```yaml
описание: |
  Это строка
  которая охватывает несколько
  строк сохраняя переносы

резюме: >
  Это строка
  которая будет свернута
  в одну строку
```

**Якоря и Ссылки:**
```yaml
по_умолчанию: &по_умолчанию
  таймаут: 30
  повторы: 3

сервисы:
  api:
    <<: *по_умолчанию
    порт: 3000
  
  воркер:
    <<: *по_умолчанию
    порт: 3001
```

## 💡 Советы по Использованию

- **Отступы YAML** : Используйте пробелы, не табы, для отступов
- **Валидация** : Всегда валидируйте синтаксис после конвертации
- **Комментарии** : Используйте YAML для конфигураций, которые нуждаются в документации
- **Производительность** : JSON быстрее для парсинга в приложениях

## ⚠️ Важные Замечания

- **Чувствительность к Отступам** : YAML очень чувствителен к правильным отступам
- **Типы Данных** : Некоторые типы YAML могут не иметь прямого эквивалента в JSON
- **Комментарии** : YAML комментарии теряются при конвертации в JSON
- **Экранирование Символов** : Специальные символы могут нуждаться в экранировании

## 🚀 Как Использовать

1. **Ввод Данных** : Вставьте содержимое JSON или YAML в область ввода
2. **Выбор Конвертации** : Выберите направление конвертации (JSON→YAML или YAML→JSON)
3. **Проверка Результатов** : Конвертированный результат появляется в области вывода
4. **Валидация** : Синтаксические ошибки автоматически подсвечиваются
5. **Использование Копирования** : Нажмите "Копировать" для копирования результата

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и безопасность ваших конфигурационных файлов.
