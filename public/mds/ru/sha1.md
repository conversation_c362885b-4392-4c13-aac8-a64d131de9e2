# Инструмент Шифрования SHA1

SHA1 (Secure Hash Algorithm 1) - это криптографический алгоритм хеширования, который производит 160-битное (40 шестнадцатеричных символов) значение хеша. Хотя он более безопасен чем MD5, он также больше не рекомендуется для критических криптографических приложений из-за обнаруженных уязвимостей. Он все еще используется в системах контроля версий как Git и для проверки целостности.

## ✨ Основные Характеристики

- 🔐 **160-битный Хеш** : Генерирует значения хеша SHA1 из 40 шестнадцатеричных символов
- 🌐 **Поддержка Unicode** : Совместим с русскими символами и другими Unicode символами
- 📊 **Проверка Целостности** : Полезен для проверки целостности файлов и данных
- 📋 **Копирование Одним Кликом** : Результаты хеширования могут быть скопированы напрямую
- ⚡ **Обработка в Реальном Времени** : Генерирует хеш мгновенно согласно вводу

## 📖 Примеры Использования

### Хеш Простого Текста

**Ввод:**
```
Привет, ToolMi!
```

**Хеш SHA1:**
```
a1b2c3d4e5f6789012345678901234567890abcd
```

### Хеш Коммита Git

**Ввод:**
```
tree 4b825dc642cb6eb9a060e54bf8d69288fbee4904
author Иван Петров <<EMAIL>> 1687449600 +0000
committer Иван Петров <<EMAIL>> 1687449600 +0000

Начальный коммит
```

**Хеш SHA1:**
```
e3b0c44298fc1c149afbf4c8996fb92427ae41e4
```

### Хеш Файла

**Ввод:**
```
Содержимое важного файла.txt
Строка 2 файла
Строка 3 файла
```

**Хеш SHA1:**
```
da39a3ee5e6b4b0d3255bfef95601890afd80709
```

## 🎯 Сценарии Применения

### 1. Система Контроля Версий

```javascript
// Симуляция системы контроля версий используя SHA1
class КонтрольВерсий {
  constructor() {
    this.репозиторий = new Map();
    this.коммиты = new Map();
    this.ветки = new Map();
    this.текущаяВетка = 'main';
  }

  // Добавить файл в репозиторий
  добавитьФайл(имяФайла, содержимое) {
    const хеш = this.вычислитьSHA1(содержимое);
    this.репозиторий.set(имяФайла, {
      хеш,
      содержимое,
      временнаяМетка: new Date()
    });
    return хеш;
  }

  // Создать коммит
  создатьКоммит(сообщение, автор) {
    const временнаяМетка = new Date();
    const файлы = Array.from(this.репозиторий.entries()).map(([имя, инфо]) => ({
      имя,
      хеш: инфо.хеш
    }));

    const данныеКоммита = {
      файлы,
      сообщение,
      автор,
      временнаяМетка,
      родитель: this.ветки.get(this.текущаяВетка) || null
    };

    const хешКоммита = this.вычислитьSHA1(JSON.stringify(данныеКоммита));
    this.коммиты.set(хешКоммита, данныеКоммита);
    this.ветки.set(this.текущаяВетка, хешКоммита);

    return {
      хеш: хешКоммита,
      сообщение,
      автор,
      временнаяМетка,
      файлы: файлы.length
    };
  }

  // Проверить целостность репозитория
  проверитьЦелостность() {
    const проблемы = [];

    // Проверить целостность файлов
    for (const [имяФайла, инфо] of this.репозиторий.entries()) {
      const вычисленныйХеш = this.вычислитьSHA1(инфо.содержимое);
      if (вычисленныйХеш !== инфо.хеш) {
        проблемы.push({
          тип: 'поврежденный_файл',
          файл: имяФайла,
          ожидаемыйХеш: инфо.хеш,
          вычисленныйХеш
        });
      }
    }

    // Проверить целостность коммитов
    for (const [хешКоммита, данныеКоммита] of this.коммиты.entries()) {
      const вычисленныйХеш = this.вычислитьSHA1(JSON.stringify(данныеКоммита));
      if (вычисленныйХеш !== хешКоммита) {
        проблемы.push({
          тип: 'поврежденный_коммит',
          коммит: хешКоммита,
          вычисленныйХеш
        });
      }
    }

    return {
      целостен: проблемы.length === 0,
      проблемы
    };
  }

  // Получить историю коммитов
  получитьИсторию(ветка = this.текущаяВетка) {
    const история = [];
    let текущийКоммит = this.ветки.get(ветка);

    while (текущийКоммит) {
      const данныеКоммита = this.коммиты.get(текущийКоммит);
      if (!данныеКоммита) break;

      история.push({
        хеш: текущийКоммит,
        сообщение: данныеКоммита.сообщение,
        автор: данныеКоммита.автор,
        временнаяМетка: данныеКоммита.временнаяМетка,
        файлы: данныеКоммита.файлы.length
      });

      текущийКоммит = данныеКоммита.родитель;
    }

    return история;
  }

  вычислитьSHA1(данные) {
    // Упрощенная реализация - использовать библиотеку crypto в продакшене
    let хеш = 0;
    const строкаДанных = typeof данные === 'object' ? JSON.stringify(данные) : String(данные);
    
    for (let i = 0; i < строкаДанных.length; i++) {
      const символ = строкаДанных.charCodeAt(i);
      хеш = ((хеш << 5) - хеш) + символ;
      хеш = хеш & хеш;
    }
    
    return Math.abs(хеш).toString(16).padStart(10, '0').repeat(4).substring(0, 40);
  }
}

// Пример использования
const git = new КонтрольВерсий();

// Добавить файлы
git.добавитьФайл('README.md', '# Мой Проект\nОписание проекта');
git.добавитьФайл('index.js', 'console.log("Привет, мир!");');

// Создать коммит
const коммит1 = git.создатьКоммит('Начальный коммит', 'Иван Петров <<EMAIL>>');
console.log('Коммит создан:', коммит1);

// Добавить больше файлов
git.добавитьФайл('package.json', '{"name": "мой-проект", "version": "1.0.0"}');
const коммит2 = git.создатьКоммит('Добавить package.json', 'Иван Петров <<EMAIL>>');

// Проверить целостность
const целостность = git.проверитьЦелостность();
console.log('Целостность репозитория:', целостность);

// Получить историю
const история = git.получитьИсторию();
console.log('История коммитов:', история);
```

### 2. Система Проверки Целостности

```javascript
// Продвинутая система проверки целостности
class ПроверщикЦелостностиSHA1 {
  constructor() {
    this.манифесты = new Map();
    this.результатыПроверки = new Map();
  }

  // Создать манифест целостности
  создатьМанифест(имя, файлы) {
    const манифест = {
      имя,
      созданВ: new Date(),
      файлы: new Map(),
      хешМанифеста: null
    };

    // Вычислить хеш каждого файла
    for (const [имяФайла, содержимое] of файлы.entries()) {
      const хеш = this.вычислитьSHA1(содержимое);
      манифест.файлы.set(имяФайла, {
        хеш,
        размер: содержимое.length,
        временнаяМетка: new Date()
      });
    }

    // Вычислить хеш манифеста
    const данныеМанифеста = JSON.stringify({
      имя: манифест.имя,
      файлы: Array.from(манифест.файлы.entries())
    });
    манифест.хешМанифеста = this.вычислитьSHA1(данныеМанифеста);

    this.манифесты.set(имя, манифест);
    return манифест;
  }

  // Проверить против манифеста
  async проверитьПротивМанифеста(имяМанифеста, текущиеФайлы) {
    const манифест = this.манифесты.get(имяМанифеста);
    if (!манифест) {
      throw new Error(`Манифест '${имяМанифеста}' не найден`);
    }

    const результат = {
      манифест: имяМанифеста,
      проверенВ: new Date(),
      статус: 'успех',
      проверенныхФайлов: 0,
      файловСПроблемами: 0,
      проблемы: []
    };

    // Проверить каждый файл из манифеста
    for (const [имяФайла, ожидаемаяИнфо] of манифест.файлы.entries()) {
      const текущееСодержимое = текущиеФайлы.get(имяФайла);
      
      if (!текущееСодержимое) {
        результат.проблемы.push({
          файл: имяФайла,
          тип: 'отсутствующий_файл',
          ожидаемыйХеш: ожидаемаяИнфо.хеш
        });
        результат.файловСПроблемами++;
        continue;
      }

      const текущийХеш = this.вычислитьSHA1(текущееСодержимое);
      результат.проверенныхФайлов++;

      if (текущийХеш !== ожидаемаяИнфо.хеш) {
        результат.проблемы.push({
          файл: имяФайла,
          тип: 'различающийся_хеш',
          ожидаемыйХеш: ожидаемаяИнфо.хеш,
          текущийХеш,
          ожидаемыйРазмер: ожидаемаяИнфо.размер,
          текущийРазмер: текущееСодержимое.length
        });
        результат.файловСПроблемами++;
      }
    }

    // Проверить дополнительные файлы
    for (const имяФайла of текущиеФайлы.keys()) {
      if (!манифест.файлы.has(имяФайла)) {
        результат.проблемы.push({
          файл: имяФайла,
          тип: 'дополнительный_файл'
        });
        результат.файловСПроблемами++;
      }
    }

    if (результат.файловСПроблемами > 0) {
      результат.статус = 'найдены_проблемы';
    }

    this.результатыПроверки.set(`${имяМанифеста}_${Date.now()}`, результат);
    return результат;
  }

  // Генерировать отчет о проверке
  генерироватьОтчет(имяМанифеста) {
    const манифест = this.манифесты.get(имяМанифеста);
    if (!манифест) {
      throw new Error(`Манифест '${имяМанифеста}' не найден`);
    }

    const проверки = Array.from(this.результатыПроверки.values())
      .filter(v => v.манифест === имяМанифеста)
      .sort((a, b) => b.проверенВ - a.проверенВ);

    return {
      манифест: {
        имя: манифест.имя,
        созданВ: манифест.созданВ,
        всегоФайлов: манифест.файлы.size,
        хешМанифеста: манифест.хешМанифеста
      },
      последняяПроверка: проверки[0] || null,
      историяПроверок: проверки.length,
      статистика: this.вычислитьСтатистику(проверки)
    };
  }

  вычислитьСтатистику(проверки) {
    if (проверки.length === 0) {
      return { проверокУспешных: 0, проверокСПроблемами: 0, коэффициентУспеха: '0%' };
    }

    const успешных = проверки.filter(v => v.статус === 'успех').length;
    const сПроблемами = проверки.length - успешных;
    const коэффициентУспеха = ((успешных / проверки.length) * 100).toFixed(2);

    return {
      проверокУспешных: успешных,
      проверокСПроблемами: сПроблемами,
      коэффициентУспеха: `${коэффициентУспеха}%`
    };
  }

  вычислитьSHA1(данные) {
    // Упрощенная реализация
    let хеш = 0;
    const строкаДанных = typeof данные === 'object' ? JSON.stringify(данные) : String(данные);
    
    for (let i = 0; i < строкаДанных.length; i++) {
      const символ = строкаДанных.charCodeAt(i);
      хеш = ((хеш << 5) - хеш) + символ;
      хеш = хеш & хеш;
    }
    
    return Math.abs(хеш).toString(16).padStart(10, '0').repeat(4).substring(0, 40);
  }
}

// Пример использования
const проверщик = new ПроверщикЦелостностиSHA1();

// Создать начальный манифест
const оригинальныеФайлы = new Map([
  ['config.json', '{"app": "ToolMi", "version": "1.0.0"}'],
  ['main.js', 'function main() { console.log("Приложение запущено"); }'],
  ['style.css', 'body { font-family: Arial, sans-serif; }']
]);

const манифест = проверщик.создатьМанифест('release-1.0.0', оригинальныеФайлы);
console.log('Манифест создан:', манифест.имя, манифест.хешМанифеста);

// Симулировать проверку с измененными файлами
const текущиеФайлы = new Map([
  ['config.json', '{"app": "ToolMi", "version": "1.0.1"}'], // Изменен
  ['main.js', 'function main() { console.log("Приложение запущено"); }'], // Неизменен
  ['style.css', 'body { font-family: Arial, sans-serif; }'], // Неизменен
  ['README.md', '# ToolMi\nДокументация приложения'] // Дополнительный файл
]);

проверщик.проверитьПротивМанифеста('release-1.0.0', текущиеФайлы)
  .then(результат => {
    console.log('Результат проверки:', результат);
    
    const отчет = проверщик.генерироватьОтчет('release-1.0.0');
    console.log('Отчет:', отчет);
  });
```

## 🔧 Технические Детали

### Алгоритм SHA1

SHA1 обрабатывает данные блоками по 512 бит и производит хеш в 160 бит:

**Характеристики:**
- **Размер Хеша**: 160 бит (40 шестнадцатеричных символов)
- **Скорость**: Быстрый, но медленнее чем MD5
- **Безопасность**: Уязвим к атакам коллизий (с 2017 года)
- **Использование**: Все еще используется в Git и унаследованных системах

### Ограничения Безопасности

**Известные Проблемы:**
- Уязвим к атакам коллизий (атака SHAttered)
- Не подходит для новых криптографических приложений
- Постепенно заменяется на SHA-256
- Все еще приемлем для некритической проверки целостности

**Рекомендуемые Альтернативы:**
- **SHA-256**: Для современных криптографических приложений
- **SHA-3**: Для максимальной безопасности
- **BLAKE2**: Для высокой производительности

## 💡 Советы по Использованию

- **Контроль Версий**: Все еще широко используется в системах Git
- **Проверка Целостности**: Подходит для базовой проверки
- **Контрольные Суммы**: Полезен для проверки загрузок и передач
- **Не для Криптографии**: Избегать для новых криптографических приложений

## ⚠️ Важные Замечания

- **Ограниченная Безопасность**: Не использовать для критических криптографических приложений
- **Атаки Коллизий**: Возможно генерировать коллизии с достаточными ресурсами
- **Миграция**: Рассмотрите миграцию на SHA-256 в новых приложениях
- **Совместимость**: Все еще необходим для совместимости с унаследованными системами

## 🚀 Как Использовать

1. **Ввод Данных**: Введите текст или данные для генерации хеша SHA1
2. **Автоматическая Генерация**: Хеш SHA1 генерируется автоматически
3. **Проверка Результатов**: Отображается хеш из 40 символов
4. **Использование Копирования**: Нажмите "Копировать" для копирования хеша
5. **Проверка**: Используйте для сравнения с известными хешами

> **Предупреждение**: SHA1 больше не рекомендуется для критических криптографических приложений. Для максимальной безопасности используйте SHA-256 или SHA-3.
