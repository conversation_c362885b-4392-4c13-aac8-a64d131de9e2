# Генератор UUID

UUID (Universally Unique Identifier, Универсально Уникальный Идентификатор) - это стандартизированная идентификационная информация, используемая для уникальной идентификации информации в распределенных системах. UUID состоит из 128 бит, обычно представленный 32 шестнадцатеричными цифрами, разделенными на 5 групп дефисами.

## ✨ Основные Характеристики

- 🔢 **Поддержка Множественных Версий** : Совместим с различными версиями как UUID v1, v4 и т.д.
- ⚡ **Пакетная Генерация** : Совместим с генерацией множественных UUID одновременно
- 📋 **Множественные Форматы** : Совместим со стандартным форматом, форматом без дефисов и т.д.
- 💾 **Копирование Одним Кликом** : Сгенерированные UUID могут быть скопированы напрямую для использования
- 🔧 **Генерация в Реальном Времени** : Кликните для мгновенной генерации новых UUID

## 📖 Примеры Использования

### Стандартный UUID v4

**Сгенерированный Пример:**
```
f47ac10b-58cc-4372-a567-0e02b2c3d479
```

**Характеристики:**
- Случайная генерация, без информации времени
- Крайне низкая вероятность коллизии
- Наиболее часто используемая версия UUID

### UUID v1 (Основанный на Времени)

**Сгенерированный Пример:**
```
6ba7b810-9dad-11d1-80b4-00c04fd430c8
```

**Характеристики:**
- Содержит информацию временной метки
- Содержит информацию MAC адреса
- Время генерации может быть выведено

### Формат Без Дефисов

**Сгенерированный Пример:**
```
f47ac10b58cc4372a5670e02b2c3d479
```

**Характеристики:**
- 32 непрерывных шестнадцатеричных символа
- Применим к определенным базам данных и системам

## 🎯 Сценарии Применения

### 1. Первичный Ключ Базы Данных

Использование UUID как первичного ключа в базе данных:

```sql
-- Создать таблицу используя UUID как первичный ключ
CREATE TABLE пользователи (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    имя_пользователя VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    создан_в TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Вставить данные (UUID генерируется автоматически)
INSERT INTO пользователи (имя_пользователя, email) 
VALUES ('иван', '<EMAIL>');

-- Запросить данные
SELECT * FROM пользователи WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
```

### 2. Распределенные Системы

Генерация уникальных идентификаторов в распределенных системах:

```javascript
// Трекер запросов в микросервисах
class ТрекерЗапросов {
  constructor() {
    this.idЗапроса = генерироватьUUID()
    this.временнаяМетка = new Date().toISOString()
  }

  log(сообщение) {
    console.log(`[${this.idЗапроса}] ${this.временнаяМетка}: ${сообщение}`)
  }
}

// Пример использования
const трекер = new ТрекерЗапросов()
трекер.log('Начало обработки пользовательского запроса')
трекер.log('Вызов сервиса пользователей')
трекер.log('Вызов сервиса заказов')
трекер.log('Обработка запроса завершена')

// Пример вывода:
// [f47ac10b-58cc-4372-a567-0e02b2c3d479] 2024-06-15T10:30:00.000Z: Начало обработки пользовательского запроса
```

### 3. Именование Файлов

Генерация уникальных имен для загружаемых файлов:

```javascript
// Обработка загрузки файлов
function обработатьЗагрузкуФайла(файл) {
  const расширениеФайла = файл.name.split('.').pop()
  const уникальноеИмяФайла = `${генерироватьUUID()}.${расширениеФайла}`
  
  // Сохранить файл
  const путьФайла = `/uploads/${уникальноеИмяФайла}`
  сохранитьФайл(файл, путьФайла)
  
  return {
    оригинальноеИмя: файл.name,
    имяФайла: уникальноеИмяФайла,
    путьФайла: путьФайла,
    времяЗагрузки: new Date().toISOString()
  }
}

// Пример использования
const результатЗагрузки = обработатьЗагрузкуФайла(файлПользователя)
console.log(результатЗагрузки)
// {
//   оригинальноеИмя: "документ.pdf",
//   имяФайла: "f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   путьФайла: "/uploads/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   времяЗагрузки: "2024-06-15T10:30:00.000Z"
// }
```

### 4. Управление Сессиями

Генерация уникальных идентификаторов для пользовательских сессий:

```javascript
// Система управления сессиями
class МенеджерСессий {
  constructor() {
    this.сессии = new Map()
  }

  создатьСессию(idПользователя) {
    const idСессии = генерироватьUUID()
    const сессия = {
      id: idСессии,
      idПользователя: idПользователя,
      созданаВ: new Date(),
      последнийДоступ: new Date(),
      данные: {}
    }
    
    this.сессии.set(idСессии, сессия)
    return idСессии
  }

  получитьСессию(idСессии) {
    const сессия = this.сессии.get(idСессии)
    if (сессия) {
      сессия.последнийДоступ = new Date()
    }
    return сессия
  }

  уничтожитьСессию(idСессии) {
    return this.сессии.delete(idСессии)
  }
}

// Пример использования
const менеджерСессий = new МенеджерСессий()
const idСессии = менеджерСессий.создатьСессию('user123')
console.log('ID Сессии:', idСессии)
// ID Сессии: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

### 5. Генерация API Ключей

Генерация уникальных ключей для доступа к API:

```javascript
// Управление API ключами
class МенеджерКлючейAPI {
  constructor() {
    this.ключиApi = new Map()
  }

  генерироватьКлючAPI(idПользователя, разрешения = []) {
    const ключApi = генерироватьUUID()
    const инфоКлюча = {
      ключ: ключApi,
      idПользователя: idПользователя,
      разрешения: разрешения,
      созданВ: new Date(),
      последнееИспользование: null,
      активен: true
    }
    
    this.ключиApi.set(ключApi, инфоКлюча)
    return ключApi
  }

  валидироватьКлючAPI(ключApi) {
    const инфоКлюча = this.ключиApi.get(ключApi)
    if (инфоКлюча && инфоКлюча.активен) {
      инфоКлюча.последнееИспользование = new Date()
      return инфоКлюча
    }
    return null
  }

  отозватьКлючAPI(ключApi) {
    const инфоКлюча = this.ключиApi.get(ключApi)
    if (инфоКлюча) {
      инфоКлюча.активен = false
      return true
    }
    return false
  }
}

// Пример использования
const менеджерApi = new МенеджерКлючейAPI()
const ключApi = менеджерApi.генерироватьКлючAPI('user123', ['read', 'write'])
console.log('API Ключ:', ключApi)
// API Ключ: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

## 🔧 Технические Детали

### Версии UUID

Различные версии UUID имеют разные методы генерации:

**UUID v1 (Основанный на Времени):**
- Содержит временную метку (60 бит)
- Содержит последовательность часов (14 бит)
- Содержит идентификатор узла (48 бит, обычно MAC адрес)
- Время и место генерации могут быть выведены

**UUID v4 (Случайный):**
- 122 бита случайных чисел
- 6 бит идентификаторов версии и варианта
- Вероятность коллизии примерно 1/2^122
- Наиболее часто используемая версия

**UUID v5 (Основанный на Имени):**
- Использует алгоритм хеширования SHA-1
- Генерируется на основе пространства имен и имени
- Одинаковый ввод всегда генерирует одинаковый UUID

### Структура Формата

Стандартный формат UUID: `xxxxxxxx-xxxx-Mxxx-Nxxx-xxxxxxxxxxxx`

- **M** : Номер версии (1, 4, 5 и т.д.)
- **N** : Идентификатор варианта (обычно 8, 9, A, B)
- **x** : Шестнадцатеричная цифра (0-9, A-F)

### Вероятность Коллизии

Вероятность коллизии UUID v4 крайне низка:

- Всего 2^122 возможных типов UUID
- Вероятность коллизии примерно 50% при генерации 10^18 UUID
- Может считаться уникальным в практических приложениях

## 💡 Советы по Использованию

- **Выбор Версии** : Обычно использовать UUID v4, использовать v1 когда нужна информация времени
- **Оптимизация Хранения** : Использовать BINARY(16) в базе данных для экономии места
- **Производительность Индекса** : Обращать внимание на влияние на производительность индекса базы данных при использовании UUID как первичного ключа
- **Унификация Формата** : Поддерживать согласованность формата UUID в рамках одной системы

## ⚠️ Важные Замечания

- **Ограничения Точности** : Обращать внимание на точность для очень больших чисел или сложных десятичных дробей
- **Региональные Различия** : Материковый Китай и Тайвань/Гонконг могут иметь разные обозначения
- **Правовые Требования** : Использовать обозначения согласно правовым требованиям в финансовых документах
- **Кодировка Символов** : Обеспечить правильное отображение китайских символов

## 🚀 Как Использовать

1. **Выбор Версии** : Выберите версию UUID согласно требованиям
2. **Настройка Количества** : Выберите количество UUID для генерации
3. **Выбор Формата** : Выберите стандартный формат или формат без дефисов
4. **Генерировать UUID** : Нажмите кнопку генерации для создания UUID
5. **Использование Копирования** : Нажмите кнопку копирования для копирования UUID в буфер обмена

> **Совет** : Этот инструмент генерирует UUID локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и безопасность.
