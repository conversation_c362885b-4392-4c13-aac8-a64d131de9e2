# Инструмент Netscape Cookies

Этот инструмент позволяет просматривать, редактировать и управлять cookies в формате Netscape. Формат Netscape - широко используемый стандарт для экспорта и импорта cookies между различными браузерами и инструментами веб-разработки.

## ✨ Основные Характеристики

- 📋 **Просмотр Cookies** : Отображает cookies в читаемом табличном формате
- ✏️ **Редактирование Cookies** : Позволяет изменять значения, домены и даты
- 📁 **Импорт/Экспорт** : Полная поддержка формата Netscape
- 🔍 **Продвинутые Фильтры** : Фильтрация по домену, имени или статусу
- 📋 **Копирование Одним Кликом** : Cookies могут быть скопированы напрямую

## 📖 Примеры Использования

### Формат Netscape Cookie

**Структура Файла:**
```
# Netscape HTTP Cookie File
# This is a generated file! Do not edit.

.example.com	TRUE	/	FALSE	1735689600	session_id	abc123def456
.google.com	TRUE	/	FALSE	1735689600	NID	507=xyz789
toolmi.com	FALSE	/tools	TRUE	1735689600	user_pref	dark_theme
```

**Поля Cookie:**
1. **Домен** : `.example.com`
2. **Включить Поддомены** : `TRUE/FALSE`
3. **Путь** : `/`
4. **Безопасный (HTTPS)** : `TRUE/FALSE`
5. **Истечение** : `1735689600` (Unix timestamp)
6. **Имя** : `session_id`
7. **Значение** : `abc123def456`

### Просмотр в Таблице

| Домен | Имя | Значение | Путь | Безопасный | Истекает | Поддомены |
|-------|-----|----------|------|------------|----------|-----------|
| .example.com | session_id | abc123def456 | / | Нет | 2024-12-31 | Да |
| .google.com | NID | 507=xyz789 | / | Нет | 2024-12-31 | Да |
| toolmi.com | user_pref | dark_theme | /tools | Да | 2024-12-31 | Нет |

## 🎯 Сценарии Применения

### 1. Веб-Разработка

```javascript
// Менеджер cookies для разработки
class МенеджерCookies {
  constructor() {
    this.cookies = [];
  }

  // Парсить файл Netscape
  парситьФайлNetscape(содержимое) {
    const строки = содержимое.split('\n');
    const cookies = [];

    for (const строка of строки) {
      // Игнорировать комментарии и пустые строки
      if (строка.startsWith('#') || строка.trim() === '') {
        continue;
      }

      const поля = строка.split('\t');
      if (поля.length === 7) {
        cookies.push({
          домен: поля[0],
          включитьПоддомены: поля[1] === 'TRUE',
          путь: поля[2],
          безопасный: поля[3] === 'TRUE',
          истечение: parseInt(поля[4]),
          имя: поля[5],
          значение: поля[6]
        });
      }
    }

    this.cookies = cookies;
    return cookies;
  }

  // Генерировать файл Netscape
  генерироватьФайлNetscape() {
    let содержимое = '# Netscape HTTP Cookie File\n';
    содержимое += '# This is a generated file! Do not edit.\n\n';

    for (const cookie of this.cookies) {
      const строка = [
        cookie.домен,
        cookie.включитьПоддомены ? 'TRUE' : 'FALSE',
        cookie.путь,
        cookie.безопасный ? 'TRUE' : 'FALSE',
        cookie.истечение,
        cookie.имя,
        cookie.значение
      ].join('\t');

      содержимое += строка + '\n';
    }

    return содержимое;
  }

  // Фильтровать cookies по домену
  фильтроватьПоДомену(домен) {
    return this.cookies.filter(cookie => 
      cookie.домен === домен || 
      cookie.домен === '.' + домен ||
      (cookie.включитьПоддомены && домен.endsWith(cookie.домен.substring(1)))
    );
  }

  // Добавить новый cookie
  добавитьCookie(cookie) {
    // Валидировать обязательные поля
    if (!cookie.домен || !cookie.имя) {
      throw new Error('Домен и имя обязательны');
    }

    // Установить значения по умолчанию
    const новыйCookie = {
      домен: cookie.домен,
      включитьПоддомены: cookie.включитьПоддомены || false,
      путь: cookie.путь || '/',
      безопасный: cookie.безопасный || false,
      истечение: cookie.истечение || Math.floor(Date.now() / 1000) + 86400, // 24ч
      имя: cookie.имя,
      значение: cookie.значение || ''
    };

    // Удалить существующий cookie с тем же именем и доменом
    this.удалитьCookie(cookie.домен, cookie.имя);

    this.cookies.push(новыйCookie);
    return новыйCookie;
  }

  // Удалить cookie
  удалитьCookie(домен, имя) {
    this.cookies = this.cookies.filter(cookie => 
      !(cookie.домен === домен && cookie.имя === имя)
    );
  }

  // Проверить истек ли cookie
  истек(cookie) {
    return cookie.истечение < Math.floor(Date.now() / 1000);
  }

  // Очистить истекшие cookies
  очиститьИстекшие() {
    const до = this.cookies.length;
    this.cookies = this.cookies.filter(cookie => !this.истек(cookie));
    return до - this.cookies.length;
  }

  // Получить статистику
  получитьСтатистику() {
    const всего = this.cookies.length;
    const истекшие = this.cookies.filter(c => this.истек(c)).length;
    const безопасные = this.cookies.filter(c => c.безопасный).length;
    const домены = [...new Set(this.cookies.map(c => c.домен))].length;

    return {
      всего,
      активные: всего - истекшие,
      истекшие,
      безопасные,
      уникальныеДомены: домены
    };
  }

  // Экспорт в JSON
  экспортВJSON() {
    return JSON.stringify(this.cookies, null, 2);
  }

  // Импорт из JSON
  импортИзJSON(json) {
    try {
      const данные = JSON.parse(json);
      if (Array.isArray(данные)) {
        this.cookies = данные;
        return true;
      }
      return false;
    } catch (ошибка) {
      console.error('Ошибка импорта JSON:', ошибка);
      return false;
    }
  }
}

// Пример использования
const менеджер = new МенеджерCookies();

// Добавить примеры cookies
менеджер.добавитьCookie({
  домен: '.toolmi.com',
  имя: 'session_id',
  значение: 'abc123def456',
  включитьПоддомены: true,
  безопасный: true,
  истечение: Math.floor(Date.now() / 1000) + 86400
});

менеджер.добавитьCookie({
  домен: 'toolmi.com',
  имя: 'user_preferences',
  значение: JSON.stringify({ тема: 'темная', язык: 'ru-RU' }),
  путь: '/tools',
  безопасный: true
});

// Генерировать файл Netscape
const файлNetscape = менеджер.генерироватьФайлNetscape();
console.log('Файл Netscape:', файлNetscape);

// Получить статистику
const статистика = менеджер.получитьСтатистику();
console.log('Статистика:', статистика);
```

### 2. Автоматизация Тестирования

```python
# Python скрипт для автоматизации с cookies
import requests
import time
from urllib.parse import urlparse

class МенеджерNetscapeCookie:
    def __init__(self):
        self.cookies = []
    
    def загрузить_из_файла(self, путь_файла):
        """Загрузить cookies из файла Netscape"""
        cookies = []
        
        with open(путь_файла, 'r', encoding='utf-8') as файл:
            for строка in файл:
                строка = строка.strip()
                
                # Игнорировать комментарии и пустые строки
                if строка.startswith('#') or not строка:
                    continue
                
                части = строка.split('\t')
                if len(части) == 7:
                    cookies.append({
                        'домен': части[0],
                        'включить_поддомены': части[1] == 'TRUE',
                        'путь': части[2],
                        'безопасный': части[3] == 'TRUE',
                        'истечение': int(части[4]),
                        'имя': части[5],
                        'значение': части[6]
                    })
        
        self.cookies = cookies
        return cookies
    
    def сохранить_в_файл(self, путь_файла):
        """Сохранить cookies в файл Netscape"""
        with open(путь_файла, 'w', encoding='utf-8') as файл:
            файл.write('# Netscape HTTP Cookie File\n')
            файл.write('# This is a generated file! Do not edit.\n\n')
            
            for cookie in self.cookies:
                строка = '\t'.join([
                    cookie['домен'],
                    'TRUE' if cookie['включить_поддомены'] else 'FALSE',
                    cookie['путь'],
                    'TRUE' if cookie['безопасный'] else 'FALSE',
                    str(cookie['истечение']),
                    cookie['имя'],
                    cookie['значение']
                ])
                файл.write(строка + '\n')
    
    def в_requests_cookies(self, url):
        """Конвертировать в формат requests"""
        parsed_url = urlparse(url)
        домен = parsed_url.netloc
        
        cookies = {}
        for cookie in self.cookies:
            # Проверить применимость к домену
            if self._домен_подходит(домен, cookie['домен'], cookie['включить_поддомены']):
                # Проверить что не истек
                if cookie['истечение'] > time.time():
                    cookies[cookie['имя']] = cookie['значение']
        
        return cookies
    
    def _домен_подходит(self, домен_запроса, домен_cookie, включить_поддомены):
        """Проверить соответствие домена cookie"""
        if домен_cookie.startswith('.'):
            # Cookie домена
            базовый_домен = домен_cookie[1:]
            if включить_поддомены:
                return домен_запроса == базовый_домен or домен_запроса.endswith('.' + базовый_домен)
            else:
                return домен_запроса == базовый_домен
        else:
            # Cookie конкретного хоста
            return домен_запроса == домен_cookie
    
    def добавить_cookie(self, домен, имя, значение, **kwargs):
        """Добавить новый cookie"""
        cookie = {
            'домен': домен,
            'имя': имя,
            'значение': значение,
            'включить_поддомены': kwargs.get('включить_поддомены', False),
            'путь': kwargs.get('путь', '/'),
            'безопасный': kwargs.get('безопасный', False),
            'истечение': kwargs.get('истечение', int(time.time()) + 86400)
        }
        
        # Удалить существующий cookie
        self.cookies = [c for c in self.cookies if not (c['домен'] == домен and c['имя'] == имя)]
        self.cookies.append(cookie)
        
        return cookie

# Пример использования в автоматизированных тестах
def тест_с_cookies():
    # Загрузить сохраненные cookies
    менеджер_cookie = МенеджерNetscapeCookie()
    менеджер_cookie.загрузить_из_файла('cookies.txt')
    
    # Сделать запрос с cookies
    url = 'https://toolmi.com/api/user/profile'
    cookies = менеджер_cookie.в_requests_cookies(url)
    
    response = requests.get(url, cookies=cookies)
    
    if response.status_code == 200:
        print('Запрос успешен с cookies')
        print('Данные пользователя:', response.json())
    else:
        print('Ошибка запроса:', response.status_code)
    
    # Сохранить обновленные cookies
    менеджер_cookie.сохранить_в_файл('cookies_updated.txt')

# Выполнить тест
тест_с_cookies()
```

### 3. Миграция Между Браузерами

```bash
#!/bin/bash
# Скрипт для миграции cookies между браузерами

# Функция конвертации cookies Chrome в Netscape
convert_chrome_to_netscape() {
    local chrome_db="$1"
    local output_file="$2"
    
    echo "# Netscape HTTP Cookie File" > "$output_file"
    echo "# Converted from Chrome cookies" >> "$output_file"
    echo "" >> "$output_file"
    
    sqlite3 "$chrome_db" "
    SELECT 
        host_key,
        CASE WHEN host_key LIKE '.%' THEN 'TRUE' ELSE 'FALSE' END,
        path,
        CASE WHEN is_secure = 1 THEN 'TRUE' ELSE 'FALSE' END,
        expires_utc / 1000000 - 11644473600,
        name,
        value
    FROM cookies 
    WHERE expires_utc > 0
    ORDER BY host_key, name;
    " | while IFS='|' read -r domain subdomains path secure expires name value; do
        echo -e "$domain\t$subdomains\t$path\t$secure\t$expires\t$name\t$value" >> "$output_file"
    done
    
    echo "Конвертация завершена: $output_file"
}

# Функция конвертации Netscape в JSON
convert_netscape_to_json() {
    local netscape_file="$1"
    local json_file="$2"
    
    echo "[" > "$json_file"
    
    local first=true
    while IFS=$'\t' read -r domain subdomains path secure expires name value; do
        # Игнорировать комментарии
        if [[ $domain == \#* ]] || [[ -z $domain ]]; then
            continue
        fi
        
        if [ "$first" = false ]; then
            echo "," >> "$json_file"
        fi
        first=false
        
        cat >> "$json_file" << EOF
  {
    "домен": "$domain",
    "включитьПоддомены": $([ "$subdomains" = "TRUE" ] && echo "true" || echo "false"),
    "путь": "$path",
    "безопасный": $([ "$secure" = "TRUE" ] && echo "true" || echo "false"),
    "истечение": $expires,
    "имя": "$name",
    "значение": "$value"
  }
EOF
    done < "$netscape_file"
    
    echo "" >> "$json_file"
    echo "]" >> "$json_file"
    
    echo "Конвертация в JSON завершена: $json_file"
}

# Использование скрипта
if [ "$#" -ne 3 ]; then
    echo "Использование: $0 <команда> <исходный_файл> <целевой_файл>"
    echo "Команды:"
    echo "  chrome-to-netscape - Конвертировать cookies Chrome в Netscape"
    echo "  netscape-to-json   - Конвертировать Netscape в JSON"
    exit 1
fi

case "$1" in
    chrome-to-netscape)
        convert_chrome_to_netscape "$2" "$3"
        ;;
    netscape-to-json)
        convert_netscape_to_json "$2" "$3"
        ;;
    *)
        echo "Неверная команда: $1"
        exit 1
        ;;
esac
```

## 🔧 Технические Детали

### Формат Файла Netscape

**Структура:**
- Простой текстовый файл
- Поля разделены TAB
- Одна строка на cookie
- Комментарии начинаются с `#`

**Поля (по порядку):**
1. **Домен** : Хост или домен cookie
2. **Поддомены** : TRUE/FALSE для включения поддоменов
3. **Путь** : Путь где cookie действителен
4. **Безопасный** : TRUE/FALSE только для HTTPS
5. **Истечение** : Unix timestamp (секунды с 1970)
6. **Имя** : Имя cookie
7. **Значение** : Значение cookie

### Совместимость

**Поддерживаемые Браузеры:**
- Chrome/Chromium
- Firefox
- Safari
- Edge
- Opera

**Совместимые Инструменты:**
- curl
- wget
- Postman
- Insomnia

## 💡 Советы по Использованию

- **Резервное Копирование** : Всегда делайте резервную копию cookies перед редактированием
- **Приватность** : Удаляйте чувствительные cookies перед публикацией
- **Истечение** : Проверяйте даты истечения при импорте старых cookies
- **Домены** : Используйте domain cookies (с точкой) для поддоменов

## ⚠️ Важные Замечания

- **Безопасность** : Cookies могут содержать чувствительную информацию
- **Приватность** : Не делитесь файлами cookies публично
- **Истечение** : Истекшие cookies игнорируются браузерами
- **Формат** : Сохраняйте точную структуру формата Netscape

## 🚀 Как Использовать

1. **Импорт** : Вставьте содержимое файла Netscape или загрузите файл
2. **Просмотр** : Просмотрите cookies в табличном формате
3. **Редактирование** : Изменяйте значения, домены или даты по необходимости
4. **Фильтры** : Используйте фильтры для поиска конкретных cookies
5. **Экспорт** : Скачайте измененный файл или скопируйте содержимое

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и безопасность ваших cookies.
