# Конвертер Временных Меток

Этот инструмент предоставляет двунаправленную конвертацию между Unix временными метками и читаемыми человеком форматами даты. Необходим для разработки приложений, анализа логов и отладки систем, работающих с временными данными.

## ✨ Основные Характеристики

- 🔄 **Двунаправленная Конвертация** : Unix временная метка ⇄ Читаемая дата/время
- 🌍 **Множественные Часовые Пояса** : Поддержка глобальных часовых поясов
- 📅 **Разнообразные Форматы** : ISO 8601, локальный формат, пользовательский
- ⚡ **Реальное Время** : Отображение текущей временной метки в реальном времени
- 📋 **Копирование Одним Кликом** : Результаты могут быть скопированы напрямую

## 📖 Примеры Использования

### Конвертация Временной Метки в Дату

**Unix Временная Метка:**
```
1705123200
```

**Конвертированная Дата:**
```
2024-01-13 08:00:00 UTC
13 января 2024, 08:00:00
Суббота, 13 января 2024
```

### Конвертация Даты во Временную Метку

**Входная Дата:**
```
2024-12-25 15:30:00
```

**Unix Временная Метка:**
```
1735134600
```

### Множественные Форматы Вывода

**Временная Метка:** `1705123200`

**Доступные Форматы:**
- **ISO 8601**: `2024-01-13T08:00:00.000Z`
- **RFC 2822**: `Sat, 13 Jan 2024 08:00:00 GMT`
- **Локальный Формат**: `13.01.2024 08:00:00`
- **Расширенный Формат**: `Суббота, 13 января 2024 в 08:00:00`

## 🎯 Сценарии Применения

### 1. Анализ Системных Логов

```javascript
// Анализатор логов с конвертацией временных меток
class АнализаторЛогов {
  constructor() {
    this.логи = [];
    this.фильтры = {
      датаНачала: null,
      датаОкончания: null,
      уровень: null,
      сервис: null
    };
  }

  // Обработать строку лога
  обработатьСтрокуЛога(строка) {
    const regex = /^(\d{10})\s+\[(\w+)\]\s+(\w+):\s+(.+)$/;
    const совпадение = строка.match(regex);
    
    if (совпадение) {
      const [, временнаяМетка, уровень, сервис, сообщение] = совпадение;
      
      return {
        временнаяМетка: parseInt(временнаяМетка),
        дата: this.временнаяМеткаВДату(parseInt(временнаяМетка)),
        уровень,
        сервис,
        сообщение,
        отформатированнаяДата: this.форматироватьДату(parseInt(временнаяМетка))
      };
    }
    
    return null;
  }

  // Конвертировать временную метку в объект Date
  временнаяМеткаВДату(временнаяМетка) {
    return new Date(временнаяМетка * 1000);
  }

  // Форматировать дату для отображения
  форматироватьДату(временнаяМетка, формат = 'полный') {
    const дата = this.временнаяМеткаВДату(временнаяМетка);
    
    const опции = {
      полный: {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZoneName: 'short'
      },
      короткий: {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      },
      iso: null // Использовать toISOString()
    };

    if (формат === 'iso') {
      return дата.toISOString();
    }

    return дата.toLocaleDateString('ru-RU', опции[формат]);
  }

  // Анализировать файл лога
  анализироватьФайлЛога(содержимоеЛога) {
    const строки = содержимоеЛога.split('\n');
    const обработанныеЛоги = [];
    
    for (const строка of строки) {
      if (строка.trim()) {
        const обработанныйЛог = this.обработатьСтрокуЛога(строка);
        if (обработанныйЛог) {
          обработанныеЛоги.push(обработанныйЛог);
        }
      }
    }

    this.логи = обработанныеЛоги;
    return this.генерироватьОтчетАнализа();
  }

  // Фильтровать логи по периоду
  фильтроватьПоПериоду(временнаяМеткаНачала, временнаяМеткаОкончания) {
    return this.логи.filter(лог => 
      лог.временнаяМетка >= временнаяМеткаНачала && лог.временнаяМетка <= временнаяМеткаОкончания
    );
  }

  // Группировать логи по часам
  группироватьПоЧасам() {
    const группы = {};
    
    for (const лог of this.логи) {
      const дата = this.временнаяМеткаВДату(лог.временнаяМетка);
      const ключЧаса = `${дата.getFullYear()}-${String(дата.getMonth() + 1).padStart(2, '0')}-${String(дата.getDate()).padStart(2, '0')} ${String(дата.getHours()).padStart(2, '0')}:00`;
      
      if (!группы[ключЧаса]) {
        группы[ключЧаса] = {
          период: ключЧаса,
          временнаяМетка: Math.floor(дата.getTime() / 1000),
          всего: 0,
          поУровням: {}
        };
      }
      
      группы[ключЧаса].всего++;
      группы[ключЧаса].поУровням[лог.уровень] = (группы[ключЧаса].поУровням[лог.уровень] || 0) + 1;
    }

    return Object.values(группы).sort((a, b) => a.временнаяМетка - b.временнаяМетка);
  }

  // Генерировать отчет анализа
  генерироватьОтчетАнализа() {
    const сейчас = Math.floor(Date.now() / 1000);
    const последнийЧас = сейчас - 3600;
    const последнийДень = сейчас - 86400;
    const последняяНеделя = сейчас - 604800;

    return {
      резюме: {
        всегоЛогов: this.логи.length,
        первыйЛог: this.логи.length > 0 ? this.форматироватьДату(Math.min(...this.логи.map(l => l.временнаяМетка))) : null,
        последнийЛог: this.логи.length > 0 ? this.форматироватьДату(Math.max(...this.логи.map(l => l.временнаяМетка))) : null
      },
      статистикаПоПериодам: {
        последнийЧас: this.фильтроватьПоПериоду(последнийЧас, сейчас).length,
        последнийДень: this.фильтроватьПоПериоду(последнийДень, сейчас).length,
        последняяНеделя: this.фильтроватьПоПериоду(последняяНеделя, сейчас).length
      },
      распределениеПоУровням: this.получитьРаспределениеПоУровням(),
      временнаяШкалаПоЧасам: this.группироватьПоЧасам()
    };
  }

  // Получить распределение по уровням
  получитьРаспределениеПоУровням() {
    const распределение = {};
    
    for (const лог of this.логи) {
      распределение[лог.уровень] = (распределение[лог.уровень] || 0) + 1;
    }

    return распределение;
  }
}

// Пример использования
const анализатор = new АнализаторЛогов();

const примерЛога = `
1705123200 [INFO] auth: Пользователь успешно вошел в систему - ID: 12345
1705123260 [WARN] database: Обнаружено медленное соединение - 2.5s
1705123320 [ERROR] payment: Ошибка обработки платежа - Gateway timeout
1705123380 [INFO] auth: Пользователь вышел из системы - ID: 12345
1705123440 [DEBUG] cache: Кеш инвалидирован для ключа: user_12345
`;

const отчет = анализатор.анализироватьФайлЛога(примерЛога);
console.log('Отчет Анализа:', отчет);
```

### 2. Система Планирования

```javascript
// Система планирования с временными метками
class СистемаПланирования {
  constructor() {
    this.события = new Map();
    this.часовойПоясПоУмолчанию = 'Europe/Moscow';
  }

  // Создать запланированное событие
  создатьСобытие(название, датаВремя, часовойПояс = this.часовойПоясПоУмолчанию, опции = {}) {
    const временнаяМетка = this.датаВВременнуюМетку(датаВремя, часовойПояс);
    const id = this.генерироватьIdСобытия();

    const событие = {
      id,
      название,
      временнаяМетка,
      часовойПояс,
      оригинальнаяДата: датаВремя,
      создано: Math.floor(Date.now() / 1000),
      статус: 'запланировано',
      повторение: опции.повторение || null,
      напоминания: опции.напоминания || [],
      метаданные: опции.метаданные || {}
    };

    this.события.set(id, событие);
    this.запланироватьНапоминания(событие);

    return событие;
  }

  // Конвертировать дату во временную метку
  датаВВременнуюМетку(датаВремя, часовойПояс) {
    let дата;
    
    if (typeof датаВремя === 'string') {
      // Предполагать формат ISO или создать с часовым поясом
      дата = new Date(датаВремя);
    } else if (датаВремя instanceof Date) {
      дата = датаВремя;
    } else {
      throw new Error('Недопустимый формат даты');
    }

    return Math.floor(дата.getTime() / 1000);
  }

  // Конвертировать временную метку в отформатированную дату
  временнаяМеткаВОтформатированнуюДату(временнаяМетка, часовойПояс = this.часовойПоясПоУмолчанию) {
    const дата = new Date(временнаяМетка * 1000);
    
    return {
      iso: дата.toISOString(),
      локальная: дата.toLocaleDateString('ru-RU', {
        timeZone: часовойПояс,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      относительная: this.получитьОтносительноеВремя(временнаяМетка),
      временнаяМетка: временнаяМетка
    };
  }

  // Получить относительное время (например, "через 2 часа", "3 дня назад")
  получитьОтносительноеВремя(временнаяМетка) {
    const сейчас = Math.floor(Date.now() / 1000);
    const разница = временнаяМетка - сейчас;
    const абсолютнаяРазница = Math.abs(разница);

    const единицы = [
      { название: 'год', секунды: 31536000 },
      { название: 'месяц', секунды: 2592000 },
      { название: 'неделя', секунды: 604800 },
      { название: 'день', секунды: 86400 },
      { название: 'час', секунды: 3600 },
      { название: 'минута', секунды: 60 },
      { название: 'секунда', секунды: 1 }
    ];

    for (const единица of единицы) {
      const количество = Math.floor(абсолютнаяРазница / единица.секунды);
      
      if (количество >= 1) {
        const окончание = this.получитьОкончание(количество, единица.название);
        const префикс = разница > 0 ? 'через' : 'назад';
        return `${префикс} ${количество} ${окончание}`;
      }
    }

    return 'сейчас';
  }

  // Получить правильное окончание для русского языка
  получитьОкончание(количество, единица) {
    const окончания = {
      'год': ['год', 'года', 'лет'],
      'месяц': ['месяц', 'месяца', 'месяцев'],
      'неделя': ['неделю', 'недели', 'недель'],
      'день': ['день', 'дня', 'дней'],
      'час': ['час', 'часа', 'часов'],
      'минута': ['минуту', 'минуты', 'минут'],
      'секунда': ['секунду', 'секунды', 'секунд']
    };

    const формы = окончания[единица];
    if (!формы) return единица;

    if (количество % 10 === 1 && количество % 100 !== 11) {
      return формы[0];
    } else if ([2, 3, 4].includes(количество % 10) && ![12, 13, 14].includes(количество % 100)) {
      return формы[1];
    } else {
      return формы[2];
    }
  }

  // Список событий по периоду
  списокСобытийПоПериоду(временнаяМеткаНачала, временнаяМеткаОкончания) {
    const события = Array.from(this.события.values())
      .filter(событие => событие.временнаяМетка >= временнаяМеткаНачала && событие.временнаяМетка <= временнаяМеткаОкончания)
      .sort((a, b) => a.временнаяМетка - b.временнаяМетка);

    return события.map(событие => ({
      ...событие,
      отформатированнаяДата: this.временнаяМеткаВОтформатированнуюДату(событие.временнаяМетка, событие.часовойПояс)
    }));
  }

  // Получить ближайшие события
  получитьБлижайшиеСобытия(лимит = 10) {
    const сейчас = Math.floor(Date.now() / 1000);
    
    return Array.from(this.события.values())
      .filter(событие => событие.временнаяМетка > сейчас && событие.статус === 'запланировано')
      .sort((a, b) => a.временнаяМетка - b.временнаяМетка)
      .slice(0, лимит)
      .map(событие => ({
        ...событие,
        отформатированнаяДата: this.временнаяМеткаВОтформатированнуюДату(событие.временнаяМетка, событие.часовойПояс)
      }));
  }

  // Запланировать напоминания
  запланироватьНапоминания(событие) {
    for (const напоминание of событие.напоминания) {
      const временнаяМеткаНапоминания = событие.временнаяМетка - напоминание.заранее;
      
      // Симулировать планирование напоминания
      console.log(`Напоминание запланировано на ${this.временнаяМеткаВОтформатированнуюДату(временнаяМеткаНапоминания).локальная}: ${напоминание.сообщение}`);
    }
  }

  // Генерировать уникальный ID для события
  генерироватьIdСобытия() {
    return `событие_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Конвертировать между часовыми поясами
  конвертироватьЧасовойПояс(временнаяМетка, исходныйПояс, целевойПояс) {
    const дата = new Date(временнаяМетка * 1000);
    
    return {
      исходный: {
        временнаяМетка,
        отформатированный: дата.toLocaleDateString('ru-RU', {
          timeZone: исходныйПояс,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        })
      },
      конвертированный: {
        временнаяМетка,
        отформатированный: дата.toLocaleDateString('ru-RU', {
          timeZone: целевойПояс,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        })
      }
    };
  }
}

// Пример использования
const планирование = new СистемаПланирования();

// Создать события
const совещание = планирование.создатьСобытие(
  'Совещание команды',
  '2024-01-15T14:00:00',
  'Europe/Moscow',
  {
    напоминания: [
      { заранее: 3600, сообщение: 'Совещание через 1 час' },
      { заранее: 900, сообщение: 'Совещание через 15 минут' }
    ],
    метаданные: {
      место: 'Конференц-зал А',
      участники: ['<EMAIL>', '<EMAIL>']
    }
  }
);

const вебинар = планирование.создатьСобытие(
  'Вебинар ToolMi',
  '2024-01-20T10:00:00',
  'Europe/Moscow'
);

// Список ближайших событий
const ближайшиеСобытия = планирование.получитьБлижайшиеСобытия();
console.log('Ближайшие События:', ближайшиеСобытия);

// Конвертировать часовой пояс
const конвертация = планирование.конвертироватьЧасовойПояс(
  совещание.временнаяМетка,
  'Europe/Moscow',
  'America/New_York'
);
console.log('Конвертация Часового Пояса:', конвертация);
```

## 🔧 Технические Детали

### Форматы Временных Меток

**Unix Временная Метка:**
- **Секунды**: `1705123200` (стандарт)
- **Миллисекунды**: `1705123200000` (JavaScript)
- **Микросекунды**: `1705123200000000` (некоторые системы)

**Форматы Даты:**
- **ISO 8601**: `2024-01-13T08:00:00.000Z`
- **RFC 2822**: `Sat, 13 Jan 2024 08:00:00 GMT`
- **Локальный Формат**: `13.01.2024 08:00:00`

### Распространенные Часовые Пояса

**Россия:**
- `Europe/Moscow` (Москва)
- `Asia/Yekaterinburg` (Екатеринбург)
- `Asia/Novosibirsk` (Новосибирск)
- `Asia/Vladivostok` (Владивосток)

**Международные:**
- `UTC` (Всемирное координированное время)
- `America/New_York` (Нью-Йорк)
- `Europe/London` (Лондон)
- `Asia/Tokyo` (Токио)

### Точность и Ограничения

**Ограничения Unix Временной Метки:**
- **Проблема 2038 года**: 32-битные временные метки ограничены до 2038 года
- **Точность**: Секунды (стандарт), миллисекунды (JavaScript)
- **Часовой Пояс**: Всегда UTC, требуется конвертация для локальных

## 💡 Советы по Использованию

- **Хранение** : Всегда храните временные метки в UTC в базе данных
- **Отображение** : Конвертируйте в локальный часовой пояс только при представлении
- **Валидация** : Проверяйте, что временная метка находится в допустимом диапазоне
- **Точность** : Используйте миллисекунды когда нужна высокая точность

## ⚠️ Важные Замечания

- **Летнее Время** : Учитывайте изменения летнего времени при конвертации
- **Високосные Секунды** : Високосные секунды могут влиять на точные вычисления
- **Формат** : Убедитесь в использовании правильного формата (секунды vs миллисекунды)
- **Валидация** : Всегда валидируйте временные метки перед обработкой

## 🚀 Как Использовать

1. **Ввод Данных** : Введите временную метку или дату/время
2. **Выбор Формата** : Выберите формат ввода и вывода
3. **Настройка Пояса** : Выберите подходящий часовой пояс
4. **Проверка Результатов** : Посмотрите конвертацию в множественных форматах
5. **Использование Копирования** : Нажмите "Копировать" для использования результата

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и быстроту конвертации.
