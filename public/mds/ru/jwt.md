# Инструмент Анализа JWT

JWT (JSON Web Token) - это компактный и безопасный для URL формат токена, используемый для безопасной передачи информации между сторонами. Он в основном используется в веб-аутентификации, API аутентификации, обмене информацией и других сценариях, являясь одной из важных технологий современной веб-разработки.

## ✨ Основные Характеристики

- 🔍 **Полный Анализ** : Детальный анализ Header, Payload и Signature
- ✅ **Валидация Формата** : Автоматическая проверка корректности структуры JWT
- 📊 **Визуализация** : Структурированная информация отображается в формате JSON
- 🔧 **Поддержка Отладки** : Проверка и отладка токенов во время разработки
- 📋 **Копирование Одним Кликом** : Результаты анализа могут быть скопированы напрямую

## 📖 Примеры Использования

### Стандартный JWT

**Входной JWT:**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6ItCY0LLQsNC9INCf0LXRgtGA0L7QsiIsImlhdCI6MTUxNjIzOTAyMn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

**Результат Анализа:**

**Header:**
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

**Payload:**
```json
{
  "sub": "1234567890",
  "name": "Иван Петров",
  "iat": 1516239022
}
```

**Signature:**
```
SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

### Сложный Payload

**Входной JWT (Сложные Claims):**
```
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1In0.eyJpc3MiOiJodHRwczovL2F1dGgudG9vbG1pLmNvbSIsInN1YiI6InVzZXJfMTIzNDUiLCJhdWQiOlsiYXBpLnRvb2xtaS5jb20iLCJhZG1pbi50b29sbWkuY29tIl0sImV4cCI6MTY4NzUzNjAwMCwiaWF0IjoxNjg3NDQ5NjAwLCJuYmYiOjE2ODc0NDk2MDAsImp0aSI6ImFiY2RlZi0xMjM0NTYiLCJuYW1lIjoi0JzQsNGA0LjRjyDQn9C10YLRgNC+0LLQsCIsImVtYWlsIjoibWFyaWFAZXhhbXBsZS5jb20iLCJyb2xlcyI6WyJ1c2VyIiwiYWRtaW4iXSwicGVybWlzc2lvbnMiOlsicmVhZCIsIndyaXRlIiwiZGVsZXRlIl19.signature_here
```

**Результат Анализа:**

**Header:**
```json
{
  "alg": "RS256",
  "typ": "JWT",
  "kid": "12345"
}
```

**Payload:**
```json
{
  "iss": "https://auth.toolmi.com",
  "sub": "user_12345",
  "aud": ["api.toolmi.com", "admin.toolmi.com"],
  "exp": 1687536000,
  "iat": 1687449600,
  "nbf": 1687449600,
  "jti": "abcdef-123456",
  "name": "Мария Петрова",
  "email": "<EMAIL>",
  "roles": ["user", "admin"],
  "permissions": ["read", "write", "delete"]
}
```

## 🎯 Сценарии Применения

### 1. Система Веб-аутентификации

Аутентификация пользователей с использованием JWT:

```javascript
// Процесс входа
const войти = async (email, пароль) => {
  try {
    const ответ = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, пароль })
    });

    if (ответ.ok) {
      const данные = await ответ.json();
      const токен = данные.token;
      
      // Сохранить JWT в локальном хранилище
      localStorage.setItem('authToken', токен);
      
      // Анализировать содержимое токена
      const payload = анализироватьJWT(токен);
      console.log('Информация пользователя:', payload);
      
      return { успех: true, пользователь: payload };
    } else {
      throw new Error('Ошибка входа');
    }
  } catch (ошибка) {
    console.error('Ошибка входа:', ошибка);
    return { успех: false, ошибка: ошибка.message };
  }
};

// Функция анализа JWT
const анализироватьJWT = (токен) => {
  try {
    const base64Url = токен.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    return JSON.parse(jsonPayload);
  } catch (ошибка) {
    console.error('Ошибка анализа JWT:', ошибка);
    return null;
  }
};

// Проверить состояние аутентификации
const проверитьСостояниеAuth = () => {
  const токен = localStorage.getItem('authToken');
  if (!токен) {
    return { аутентифицирован: false };
  }

  const payload = анализироватьJWT(токен);
  if (!payload) {
    return { аутентифицирован: false };
  }

  // Проверить дату истечения токена
  const текущееВремя = Math.floor(Date.now() / 1000);
  if (payload.exp && payload.exp < текущееВремя) {
    localStorage.removeItem('authToken');
    return { аутентифицирован: false, причина: 'истек' };
  }

  return { 
    аутентифицирован: true, 
    пользователь: payload,
    истекаетЧерез: payload.exp 
  };
};
```

### 2. API Аутентификация

Использование JWT в API запросах:

```javascript
// Класс API клиента
class КлиентAPI {
  constructor(базовыйUrl) {
    this.базовыйUrl = базовыйUrl;
    this.токен = localStorage.getItem('authToken');
  }

  // Запрос с заголовком аутентификации
  async запрос(endpoint, опции = {}) {
    const url = `${this.базовыйUrl}${endpoint}`;
    const заголовки = {
      'Content-Type': 'application/json',
      ...опции.заголовки
    };

    // Добавить JWT токен в заголовок Authorization
    if (this.токен) {
      заголовки.Authorization = `Bearer ${this.токен}`;
    }

    try {
      const ответ = await fetch(url, {
        ...опции,
        headers: заголовки
      });

      // Если ошибка 401, токен недействителен
      if (ответ.status === 401) {
        this.обработатьНеавторизованный();
        throw new Error('Требуется аутентификация');
      }

      if (!ответ.ok) {
        throw new Error(`HTTP ошибка! статус: ${ответ.status}`);
      }

      return await ответ.json();
    } catch (ошибка) {
      console.error('Ошибка API запроса:', ошибка);
      throw ошибка;
    }
  }

  // Обработать ошибку аутентификации
  обработатьНеавторизованный() {
    localStorage.removeItem('authToken');
    this.токен = null;
    // Перенаправить на страницу входа
    window.location.href = '/login';
  }

  // Обновить токен
  обновитьТокен(новыйТокен) {
    this.токен = новыйТокен;
    localStorage.setItem('authToken', новыйТокен);
  }

  // Получить профиль пользователя
  async получитьПрофильПользователя() {
    return await this.запрос('/api/user/profile');
  }

  // Получить данные
  async получитьДанные(endpoint) {
    return await this.запрос(endpoint);
  }

  // Обновить данные
  async обновитьДанные(endpoint, данные) {
    return await this.запрос(endpoint, {
      method: 'PUT',
      body: JSON.stringify(данные)
    });
  }
}

// Пример использования
const клиентApi = new КлиентAPI('https://api.toolmi.com');

// Получить профиль пользователя
клиентApi.получитьПрофильПользователя()
  .then(профиль => {
    console.log('Профиль пользователя:', профиль);
  })
  .catch(ошибка => {
    console.error('Ошибка получения профиля:', ошибка);
  });
```

## 🔧 Технические Детали

### Структура JWT

JWT состоит из трех частей:

**1. Header (Заголовок)**
```json
{
  "alg": "HS256",    // Алгоритм подписи
  "typ": "JWT"       // Тип токена
}
```

**2. Payload (Полезная нагрузка)**
```json
{
  "iss": "issuer",           // Эмитент
  "sub": "subject",          // Субъект
  "aud": "audience",         // Аудитория
  "exp": 1234567890,         // Дата истечения
  "iat": 1234567890,         // Время выдачи
  "nbf": 1234567890,         // Не действителен до
  "jti": "jwt-id"            // ID JWT
}
```

**3. Signature (Подпись)**
```
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret
)
```

### Стандартные Claims

Стандартные claims, используемые в JWT:

- **iss (Issuer)** : Эмитент токена
- **sub (Subject)** : Субъект токена (обычно ID пользователя)
- **aud (Audience)** : Аудитория токена
- **exp (Expiration Time)** : Дата истечения (Unix timestamp)
- **iat (Issued At)** : Время выдачи
- **nbf (Not Before)** : Время начала действия
- **jti (JWT ID)** : Уникальный идентификатор JWT

## 💡 Советы по Использованию

- **Проверка Истечения** : Проверять дату истечения токена с помощью claim exp
- **Безопасность** : Не включать конфиденциальную информацию в payload
- **Проверка Подписи** : Всегда проверять подпись в продакшн среде
- **Размер Токена** : Следить, чтобы payload не был слишком объемным

## ⚠️ Важные Замечания

- **Конфиденциальная Информация** : Payload JWT не зашифрован, поэтому не должен включать конфиденциальную информацию
- **Проверка Подписи** : Этот инструмент только анализирует, не проверяет подписи
- **Дата Истечения** : Не использовать истекшие токены
- **Место Хранения** : Обращать внимание на место хранения токенов в браузере (предотвращение XSS)

## 🚀 Как Использовать

1. **Ввод JWT** : Вставьте JWT токен для анализа в область ввода
2. **Автоматический Анализ** : Header, Payload и Signature анализируются автоматически при вводе
3. **Проверка Результатов** : Проверьте детальную информацию каждой части в формате JSON
4. **Использование Копирования** : Используйте кнопку "Копировать" для копирования результатов анализа в буфер обмена
5. **Использование в Отладке** : Используйте для проверки и отладки токенов во время разработки

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет JWT токены на сервер, гарантируя безопасность.
