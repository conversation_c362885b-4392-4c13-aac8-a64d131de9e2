# Инструмент Формата YAML

Этот инструмент предоставляет форматирование, валидацию и украшение YAML файлов. YAML (YAML Ain't Markup Language) - это формат сериализации данных, читаемый человеком, широко используемый для конфигурационных файлов, API документации и DevOps пайплайнов.

## ✨ Основные Характеристики

- ✅ **Валидация Синтаксиса** : Автоматическое обнаружение синтаксических ошибок YAML
- 🎨 **Интеллектуальное Форматирование** : Последовательные отступы и чистая структура
- 📋 **Копирование Одним Кликом** : Отформатированный YAML может быть скопирован напрямую
- 🔍 **Обнаружение Ошибок** : Четкое указание проблем синтаксиса
- 🌐 **Поддержка Unicode** : Совместим со специальными символами и акцентами

## 📖 Примеры Использования

### Форматирование Базовой Конфигурации

**Входной YAML (плохо отформатированный):**
```yaml
приложение:
название: ToolMi
версия: 1.0.0
сервер:
хост: localhost
порт: 3000
ssl: false
функции:
- base64
- uuid
- json
```

**Отформатированный YAML:**
```yaml
приложение:
  название: ToolMi
  версия: 1.0.0
сервер:
  хост: localhost
  порт: 3000
  ssl: false
функции:
  - base64
  - uuid
  - json
```

### Форматирование Сложной Конфигурации

**Входной YAML:**
```yaml
база_данных:
тип: postgresql
хост: db.toolmi.com
порт: 5432
учетные_данные:
пользователь: admin
пароль: безопасный_пароль
конфигурация:
отладка: true
уровень_логов: info
включенные_функции:
- аутентификация
- кеширование
- мониторинг
```

**Отформатированный YAML:**
```yaml
база_данных:
  тип: postgresql
  хост: db.toolmi.com
  порт: 5432
  учетные_данные:
    пользователь: admin
    пароль: безопасный_пароль
конфигурация:
  отладка: true
  уровень_логов: info
  включенные_функции:
    - аутентификация
    - кеширование
    - мониторинг
```

## 🎯 Сценарии Применения

### 1. Конфигурация Приложения

```yaml
# config/приложение.yml
приложение:
  название: "Система ToolMi"
  версия: "2.1.0"
  среда: продакшн
  отладка: false
  обслуживание: false

сервер:
  хост: "0.0.0.0"
  порт: 8080
  воркеры: 4
  таймаут: 30
  ssl:
    включен: true
    сертификат: "/etc/ssl/certs/toolmi.crt"
    приватный_ключ: "/etc/ssl/private/toolmi.key"

база_данных:
  по_умолчанию: postgresql
  соединения:
    postgresql:
      драйвер: postgresql
      хост: ${DB_HOST:-localhost}
      порт: ${DB_PORT:-5432}
      база: ${DB_NAME:-toolmi_db}
      пользователь: ${DB_USER:-postgres}
      пароль: ${DB_PASSWORD}
      опции:
        кодировка: utf8
        часовой_пояс: Europe/Moscow
      пул:
        мин: 2
        макс: 10
        таймаут: 5000
    
    redis:
      драйвер: redis
      хост: ${REDIS_HOST:-localhost}
      порт: ${REDIS_PORT:-6379}
      база: ${REDIS_DB:-0}
      пароль: ${REDIS_PASSWORD}
      ttl: 3600

логирование:
  уровень: info
  формат: json
  выводы:
    - консоль
    - файл
  файл:
    путь: "/var/log/toolmi/app.log"
    ротация: ежедневная
    хранение: 30
    максимальный_размер: "100MB"
  фильтры:
    - уровень: error
      уведомлять: true
    - уровень: warning
      уведомлять: false

функции:
  аутентификация:
    включена: true
    провайдер: jwt
    конфигурация:
      алгоритм: HS256
      истечение: 86400
      обновление_истечение: 604800
      эмитент: "toolmi.com"
  
  ограничение_запросов:
    включено: true
    конфигурация:
      глобальный_лимит: 1000
      окно: 3600
      лимит_по_ip: 100
      белый_список:
        - "127.0.0.1"
        - "10.0.0.0/8"
  
  cors:
    включен: true
    конфигурация:
      источники:
        - "https://toolmi.com"
        - "https://www.toolmi.com"
        - "https://app.toolmi.com"
      методы:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      заголовки:
        - "Content-Type"
        - "Authorization"
        - "X-Requested-With"
      учетные_данные: true

мониторинг:
  метрики:
    включены: true
    эндпоинт: "/metrics"
    интервал: 30
  
  проверка_здоровья:
    включена: true
    эндпоинт: "/health"
    проверки:
      - название: "база_данных"
        тип: "postgresql"
        таймаут: 5
      - название: "redis"
        тип: "redis"
        таймаут: 3
      - название: "диск"
        тип: "filesystem"
        лимит: 90

уведомления:
  email:
    включен: true
    smtp:
      хост: ${SMTP_HOST}
      порт: ${SMTP_PORT:-587}
      пользователь: ${SMTP_USER}
      пароль: ${SMTP_PASSWORD}
      tls: true
    отправитель:
      имя: "Система ToolMi"
      email: "<EMAIL>"
  
  slack:
    включен: false
    webhook_url: ${SLACK_WEBHOOK_URL}
    канал: "#оповещения"
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NODE_ENV: production
        BUILD_VERSION: ${BUILD_VERSION:-latest}
    image: toolmi/app:${TAG:-latest}
    container_name: toolmi-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-3000}:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - toolmi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15-alpine
    container_name: toolmi-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-toolmi}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=ru_RU.UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - toolmi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-toolmi}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: toolmi-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - toolmi-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  nginx:
    image: nginx:alpine
    container_name: toolmi-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites:/etc/nginx/sites-available:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - toolmi-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  toolmi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 3. Kubernetes Deployment

```yaml
# k8s/namespace.yml
apiVersion: v1
kind: Namespace
metadata:
  name: toolmi
  labels:
    name: toolmi
    environment: production

---
# k8s/configmap.yml
apiVersion: v1
kind: ConfigMap
metadata:
  name: toolmi-config
  namespace: toolmi
data:
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  REDIS_HOST: "redis-service"
  DB_HOST: "postgres-service"
  app.yml: |
    приложение:
      название: "ToolMi Kubernetes"
      версия: "2.1.0"
      среда: продакшн
    сервер:
      порт: 3000
      воркеры: 4
    функции:
      аутентификация:
        включена: true
        истечение: 86400

---
# k8s/secret.yml
apiVersion: v1
kind: Secret
metadata:
  name: toolmi-secrets
  namespace: toolmi
type: Opaque
data:
  DB_PASSWORD: cG9zdGdyZXNfc2VjcmV0 # base64 encoded
  JWT_SECRET: and0X3NlY3JldF9rZXk= # base64 encoded
  REDIS_PASSWORD: cmVkaXNfc2VjcmV0 # base64 encoded

---
# k8s/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  namespace: toolmi
  labels:
    app: toolmi
    component: backend
    version: v2.1.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: toolmi
      component: backend
  template:
    metadata:
      labels:
        app: toolmi
        component: backend
        version: v2.1.0
    spec:
      containers:
        - name: app
          image: toolmi/app:v2.1.0
          ports:
            - containerPort: 3000
              name: http
              protocol: TCP
          env:
            - name: NODE_ENV
              valueFrom:
                configMapKeyRef:
                  name: toolmi-config
                  key: NODE_ENV
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: toolmi-config
                  key: DB_HOST
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: toolmi-secrets
                  key: DB_PASSWORD
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: toolmi-secrets
                  key: JWT_SECRET
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          volumeMounts:
            - name: config-volume
              mountPath: /app/config
              readOnly: true
            - name: logs-volume
              mountPath: /app/logs
      volumes:
        - name: config-volume
          configMap:
            name: toolmi-config
        - name: logs-volume
          emptyDir: {}

---
# k8s/service.yml
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
  namespace: toolmi
  labels:
    app: toolmi
    component: backend
spec:
  selector:
    app: toolmi
    component: backend
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP

---
# k8s/ingress.yml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: toolmi-ingress
  namespace: toolmi
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
    - hosts:
        - toolmi.com
        - www.toolmi.com
      secretName: toolmi-tls
  rules:
    - host: toolmi.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: toolmi-service
                port:
                  number: 80
    - host: www.toolmi.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: toolmi-service
                port:
                  number: 80
```

## 🔧 Технические Детали

### Синтаксис YAML

**Базовая Структура:**
- **Отступы**: Использует пробелы (не табы) для определения иерархии
- **Списки**: Обозначаются дефисом `-`
- **Объекты**: Пары ключ-значение разделенные двоеточием `:`
- **Комментарии**: Начинаются с `#`

**Типы Данных:**
- **String**: `имя: "Иван Петров"` или `имя: Иван Петров`
- **Number**: `возраст: 30` или `цена: 19.99`
- **Boolean**: `активен: true` или `активен: false`
- **Null**: `значение: null` или `значение: ~`
- **Array**: `[элемент1, элемент2]` или формат списка
- **Object**: Вложенная структура с отступами

### Специальные Строки

**Многострочные Строки:**
```yaml
# Сохраняет переносы строк
описание: |
  Это описание
  которое сохраняет
  оригинальные переносы строк

# Сворачивает в одну строку
резюме: >
  Этот текст будет
  свернут в одну
  строку
```

**Строки с Экранированием:**
```yaml
путь_windows: "C:\\Users\\<USER>\\Documents"
regex: "\\d+\\.\\d+"
json_string: '{"имя": "Иван", "возраст": 30}'
```

### Якоря и Ссылки

```yaml
# Определить якорь
по_умолчанию: &стандартная_конфигурация
  таймаут: 30
  повторы: 3
  отладка: false

# Использовать ссылку
разработка:
  <<: *стандартная_конфигурация
  отладка: true

продакшн:
  <<: *стандартная_конфигурация
  таймаут: 60
```

## 💡 Советы по Использованию

- **Последовательные Отступы** : Всегда используйте 2 или 4 пробела, никогда табы
- **Кавычки** : Используйте кавычки при необходимости для избежания неправильной интерпретации
- **Валидация** : Всегда валидируйте YAML перед использованием в продакшене
- **Комментарии** : Используйте комментарии для документирования сложных конфигураций

## ⚠️ Важные Замечания

- **Чувствительность к Отступам** : YAML крайне чувствителен к отступам
- **Табы против Пробелов** : Никогда не смешивайте табы и пробелы
- **Специальные Символы** : Некоторые символы могут нуждаться в кавычках
- **Кодировка** : Используйте UTF-8 для полной поддержки специальных символов

## 🚀 Как Использовать

1. **Ввод YAML** : Вставьте содержимое YAML в область ввода
2. **Автоматическая Валидация** : Синтаксические ошибки обнаруживаются автоматически
3. **Форматирование** : Нажмите "Форматировать" для украшения YAML
4. **Проверка Результатов** : Отформатированный YAML появляется в области вывода
5. **Использование Копирования** : Нажмите "Копировать" для использования отформатированного YAML

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и безопасность ваших конфигурационных файлов.
