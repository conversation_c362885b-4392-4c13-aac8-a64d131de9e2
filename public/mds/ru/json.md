# Инструмент Формата JSON

Этот инструмент предоставляет форматирование, валидацию, сжатие и украшение JSON данных. JSON (JavaScript Object Notation) - самый популярный формат обмена данными в современной веб-разработке, используемый в API, конфигурациях и хранении данных.

## ✨ Основные Характеристики

- ✅ **Валидация Синтаксиса** : Автоматическое обнаружение синтаксических ошибок JSON
- 🎨 **Красивое Форматирование** : Интеллектуальные отступы и чистая структура
- 🗜️ **Сжатие** : Удаляет ненужные пробелы для оптимизации
- 📋 **Копирование Одним Кликом** : Отформатированный JSON может быть скопирован напрямую
- 🔍 **Анализ Структуры** : Четкая визуализация иерархии данных

## 📖 Примеры Использования

### Форматирование Базового JSON

**Входной JSON (сжатый):**
```json
{"пользователь":{"id":1,"имя":"Иван Петров","email":"<EMAIL>","активен":true},"настройки":{"тема":"темная","уведомления":true,"язык":"ru-RU"},"данные":[{"тип":"профиль","значение":"полный"},{"тип":"предпочтения","значение":"сохранено"}]}
```

**Отформатированный JSON:**
```json
{
  "пользователь": {
    "id": 1,
    "имя": "Иван Петров",
    "email": "<EMAIL>",
    "активен": true
  },
  "настройки": {
    "тема": "темная",
    "уведомления": true,
    "язык": "ru-RU"
  },
  "данные": [
    {
      "тип": "профиль",
      "значение": "полный"
    },
    {
      "тип": "предпочтения",
      "значение": "сохранено"
    }
  ]
}
```

### Форматирование Сложного JSON

**Входной JSON:**
```json
{"api":{"версия":"2.1.0","эндпоинты":[{"метод":"GET","путь":"/пользователи","описание":"Список пользователей","параметры":{"лимит":{"тип":"integer","по_умолчанию":10},"страница":{"тип":"integer","по_умолчанию":1}},"ответ":{"200":{"тип":"object","свойства":{"пользователи":{"тип":"array"},"всего":{"тип":"integer"},"текущая_страница":{"тип":"integer"}}}}},{"метод":"POST","путь":"/пользователи","описание":"Создать пользователя","тело":{"имя":{"тип":"string","обязательно":true},"email":{"тип":"string","обязательно":true},"пароль":{"тип":"string","обязательно":true}},"ответ":{"201":{"тип":"object","свойства":{"id":{"тип":"integer"},"имя":{"тип":"string"},"email":{"тип":"string"},"дата_создания":{"тип":"string","формат":"datetime"}}}}}]}}
```

**Отформатированный JSON:**
```json
{
  "api": {
    "версия": "2.1.0",
    "эндпоинты": [
      {
        "метод": "GET",
        "путь": "/пользователи",
        "описание": "Список пользователей",
        "параметры": {
          "лимит": {
            "тип": "integer",
            "по_умолчанию": 10
          },
          "страница": {
            "тип": "integer",
            "по_умолчанию": 1
          }
        },
        "ответ": {
          "200": {
            "тип": "object",
            "свойства": {
              "пользователи": {
                "тип": "array"
              },
              "всего": {
                "тип": "integer"
              },
              "текущая_страница": {
                "тип": "integer"
              }
            }
          }
        }
      },
      {
        "метод": "POST",
        "путь": "/пользователи",
        "описание": "Создать пользователя",
        "тело": {
          "имя": {
            "тип": "string",
            "обязательно": true
          },
          "email": {
            "тип": "string",
            "обязательно": true
          },
          "пароль": {
            "тип": "string",
            "обязательно": true
          }
        },
        "ответ": {
          "201": {
            "тип": "object",
            "свойства": {
              "id": {
                "тип": "integer"
              },
              "имя": {
                "тип": "string"
              },
              "email": {
                "тип": "string"
              },
              "дата_создания": {
                "тип": "string",
                "формат": "datetime"
              }
            }
          }
        }
      }
    ]
  }
}
```

## 🎯 Сценарии Применения

### 1. Разработка API

```json
{
  "openapi": "3.0.3",
  "info": {
    "title": "API ToolMi",
    "description": "API для онлайн инструментов разработки",
    "version": "2.1.0",
    "contact": {
      "name": "Команда ToolMi",
      "email": "<EMAIL>",
      "url": "https://toolmi.com/контакты"
    },
    "license": {
      "name": "MIT",
      "url": "https://opensource.org/licenses/MIT"
    }
  },
  "servers": [
    {
      "url": "https://api.toolmi.com/v2",
      "description": "Продакшн Сервер"
    },
    {
      "url": "https://staging-api.toolmi.com/v2",
      "description": "Тестовый Сервер"
    }
  ],
  "paths": {
    "/инструменты": {
      "get": {
        "summary": "Список доступных инструментов",
        "description": "Возвращает пагинированный список всех доступных инструментов",
        "parameters": [
          {
            "name": "категория",
            "in": "query",
            "description": "Фильтр по категории",
            "required": false,
            "schema": {
              "type": "string",
              "enum": ["кодирование", "генерация", "форматирование", "валидация"]
            }
          },
          {
            "name": "лимит",
            "in": "query",
            "description": "Максимальное количество элементов на странице",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 1,
              "maximum": 100,
              "default": 20
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Список инструментов успешно получен",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "инструменты": {
                      "type": "array",
                      "items": {
                        "$ref": "#/components/schemas/Инструмент"
                      }
                    },
                    "пагинация": {
                      "$ref": "#/components/schemas/Пагинация"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/инструменты/{id}/выполнить": {
      "post": {
        "summary": "Выполнить инструмент",
        "description": "Выполняет конкретный инструмент с предоставленными параметрами",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "ID инструмента",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "description": "Входные параметры для инструмента",
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "вход": {
                    "type": "string",
                    "description": "Входные данные для обработки"
                  },
                  "опции": {
                    "type": "object",
                    "description": "Специфичные опции инструмента"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Инструмент выполнен успешно",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/РезультатВыполнения"
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "Инструмент": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "description": "Уникальный идентификатор инструмента"
          },
          "название": {
            "type": "string",
            "description": "Название инструмента"
          },
          "описание": {
            "type": "string",
            "description": "Описание функциональности"
          },
          "категория": {
            "type": "string",
            "enum": ["кодирование", "генерация", "форматирование", "валидация"]
          },
          "параметры": {
            "type": "object",
            "description": "Схема принимаемых параметров"
          }
        }
      },
      "Пагинация": {
        "type": "object",
        "properties": {
          "текущая_страница": {
            "type": "integer"
          },
          "всего_страниц": {
            "type": "integer"
          },
          "всего_элементов": {
            "type": "integer"
          },
          "элементов_на_странице": {
            "type": "integer"
          }
        }
      },
      "РезультатВыполнения": {
        "type": "object",
        "properties": {
          "успех": {
            "type": "boolean"
          },
          "результат": {
            "type": "string",
            "description": "Результат обработки"
          },
          "время_выполнения": {
            "type": "number",
            "description": "Время выполнения в миллисекундах"
          },
          "метаданные": {
            "type": "object",
            "description": "Дополнительная информация об обработке"
          }
        }
      }
    }
  }
}
```

### 2. Конфигурация Приложения

```json
{
  "приложение": {
    "название": "ToolMi",
    "версия": "2.1.0",
    "среда": "продакшн",
    "отладка": false,
    "конфигурация": {
      "сервер": {
        "хост": "0.0.0.0",
        "порт": 3000,
        "ssl": {
          "включен": true,
          "сертификат": "/etc/ssl/certs/toolmi.crt",
          "приватный_ключ": "/etc/ssl/private/toolmi.key"
        }
      },
      "база_данных": {
        "тип": "postgresql",
        "хост": "localhost",
        "порт": 5432,
        "название": "toolmi_db",
        "пользователь": "postgres",
        "пул": {
          "мин": 2,
          "макс": 10,
          "таймаут": 5000
        }
      },
      "кеш": {
        "тип": "redis",
        "хост": "localhost",
        "порт": 6379,
        "база": 0,
        "ttl": 3600
      },
      "логирование": {
        "уровень": "info",
        "формат": "json",
        "выводы": ["консоль", "файл"],
        "файл": {
          "путь": "/var/log/toolmi.log",
          "ротация": "ежедневная",
          "хранение": 30
        }
      }
    },
    "функции": {
      "аутентификация": {
        "включена": true,
        "провайдер": "jwt",
        "конфигурация": {
          "алгоритм": "HS256",
          "истечение": 86400,
          "эмитент": "toolmi.com"
        }
      },
      "ограничение_запросов": {
        "включено": true,
        "лимит": 1000,
        "окно": 3600
      },
      "cors": {
        "включен": true,
        "источники": [
          "https://toolmi.com",
          "https://www.toolmi.com"
        ],
        "методы": ["GET", "POST", "PUT", "DELETE"],
        "заголовки": ["Content-Type", "Authorization"]
      }
    },
    "инструменты": [
      {
        "id": "base64",
        "название": "Кодирование Base64",
        "категория": "кодирование",
        "включен": true,
        "конфигурация": {
          "максимальный_размер": 1048576,
          "поддерживаемые_форматы": ["текст", "файл"]
        }
      },
      {
        "id": "uuid",
        "название": "Генератор UUID",
        "категория": "генерация",
        "включен": true,
        "конфигурация": {
          "поддерживаемые_версии": [1, 4, 5],
          "максимальное_количество": 1000
        }
      },
      {
        "id": "json-format",
        "название": "Форматировщик JSON",
        "категория": "форматирование",
        "включен": true,
        "конфигурация": {
          "максимальный_размер": 2097152,
          "отступ": 2
        }
      }
    ]
  }
}
```

### 3. Тестовые Данные и Моки

```json
{
  "пользователи": [
    {
      "id": 1,
      "имя": "Иван Петров",
      "email": "<EMAIL>",
      "возраст": 28,
      "активен": true,
      "профиль": {
        "био": "Full Stack разработчик, увлеченный технологиями",
        "аватар": "https://example.com/avatars/ivan.jpg",
        "социальные_сети": {
          "github": "ivanpetrov",
          "linkedin": "ivan-petrov-dev",
          "twitter": "@ivandev"
        }
      },
      "адрес": {
        "улица": "ул. Тверская, 123",
        "город": "Москва",
        "регион": "Московская область",
        "почтовый_код": "101000",
        "страна": "Россия"
      },
      "предпочтения": {
        "тема": "темная",
        "язык": "ru-RU",
        "уведомления": {
          "email": true,
          "push": false,
          "sms": false
        }
      },
      "статистика": {
        "использованных_инструментов": 15,
        "общее_время_использования": 3600,
        "любимый_инструмент": "base64",
        "последний_доступ": "2024-01-15T10:30:00Z"
      }
    },
    {
      "id": 2,
      "имя": "Мария Иванова",
      "email": "<EMAIL>",
      "возраст": 32,
      "активен": true,
      "профиль": {
        "био": "UX/UI дизайнер с фокусом на пользовательский опыт",
        "аватар": "https://example.com/avatars/maria.jpg",
        "социальные_сети": {
          "behance": "mariaivanova",
          "dribbble": "maria_ux",
          "linkedin": "maria-ivanova-ux"
        }
      },
      "адрес": {
        "улица": "Невский проспект, 456",
        "город": "Санкт-Петербург",
        "регион": "Ленинградская область",
        "почтовый_код": "190000",
        "страна": "Россия"
      },
      "предпочтения": {
        "тема": "светлая",
        "язык": "ru-RU",
        "уведомления": {
          "email": true,
          "push": true,
          "sms": false
        }
      },
      "статистика": {
        "использованных_инструментов": 8,
        "общее_время_использования": 1800,
        "любимый_инструмент": "qr-code",
        "последний_доступ": "2024-01-14T16:45:00Z"
      }
    }
  ],
  "инструменты": [
    {
      "id": "base64",
      "название": "Кодирование Base64",
      "описание": "Кодирует и декодирует данные в формате Base64",
      "категория": "кодирование",
      "теги": ["кодирование", "base64", "текст"],
      "популярность": 95,
      "месячное_использование": 15420,
      "рейтинг": 4.8,
      "возможности": [
        "Кодирование текста",
        "Декодирование текста",
        "Поддержка файлов",
        "Автоматическая валидация"
      ]
    },
    {
      "id": "uuid",
      "название": "Генератор UUID",
      "описание": "Генерирует универсальные уникальные идентификаторы",
      "категория": "генерация",
      "теги": ["uuid", "идентификатор", "генерация"],
      "популярность": 87,
      "месячное_использование": 12350,
      "рейтинг": 4.7,
      "возможности": [
        "UUID v1 (на основе времени)",
        "UUID v4 (случайный)",
        "UUID v5 (на основе хеша)",
        "Пакетная генерация"
      ]
    }
  ],
  "глобальная_статистика": {
    "всего_пользователей": 25430,
    "активных_пользователей_в_месяц": 18920,
    "всего_выполнений": 1250000,
    "самые_используемые_инструменты": [
      {
        "id": "base64",
        "название": "Кодирование Base64",
        "процент_использования": 28.5
      },
      {
        "id": "uuid",
        "название": "Генератор UUID",
        "процент_использования": 22.1
      },
      {
        "id": "json-format",
        "название": "Форматировщик JSON",
        "процент_использования": 18.7
      }
    ]
  }
}
```

## 🔧 Технические Детали

### Синтаксис JSON

**Поддерживаемые Типы Данных:**
- **String**: `"текст"` (всегда в двойных кавычках)
- **Number**: `123`, `45.67`, `-10`, `1.23e-4`
- **Boolean**: `true`, `false`
- **Null**: `null`
- **Array**: `[элемент1, элемент2, элемент3]`
- **Object**: `{"ключ": "значение"}`

**Правила Синтаксиса:**
- Строки должны быть в двойных кавычках
- Комментарии не разрешены
- Завершающие запятые не разрешены
- Ключи объектов должны быть строками

### Валидация и Частые Ошибки

**Частые Синтаксические Ошибки:**
- Одинарные кавычки вместо двойных
- Завершающие запятые в массивах или объектах
- Ключи без кавычек
- Недопустимые комментарии

**Пример Недействительного JSON:**
```json
{
  'имя': 'Иван',  // Одинарные кавычки и комментарий
  возраст: 30,    // Ключ без кавычек
  активен: true,  // Завершающая запятая
}
```

**Исправленная Версия:**
```json
{
  "имя": "Иван",
  "возраст": 30,
  "активен": true
}
```

## 💡 Советы по Использованию

- **Валидация** : Всегда валидируйте JSON перед использованием в продакшене
- **Отступы** : Используйте 2 или 4 пробела для лучшей читаемости
- **Сжатие** : Используйте сжатый JSON для API в продакшене
- **Структура** : Поддерживайте последовательную структуру в похожих данных

## ⚠️ Важные Замечания

- **Двойные Кавычки** : JSON требует двойные кавычки, не одинарные
- **Без Комментариев** : JSON не поддерживает комментарии нативно
- **Кодировка** : Используйте UTF-8 для полной поддержки специальных символов
- **Размер** : Учитывайте размер файла для производительности

## 🚀 Как Использовать

1. **Ввод JSON** : Вставьте содержимое JSON в область ввода
2. **Автоматическая Валидация** : Синтаксические ошибки обнаруживаются автоматически
3. **Выбор Операции** : Выберите "Форматировать" или "Сжать"
4. **Проверка Результатов** : Обработанный JSON появляется в области вывода
5. **Использование Копирования** : Нажмите "Копировать" для использования отформатированного JSON

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и безопасность ваших JSON данных.
