# Инструмент Шифрования MD5

MD5 (Message Digest Algorithm 5) - это широко используемый криптографический алгоритм хеширования, который производит 128-битное (32 шестнадцатеричных символа) значение хеша. Хотя он больше не считается безопасным для критических криптографических приложений, он все еще полезен для проверки целостности данных, генерации ключей кеша и других некритических сценариев.

## ✨ Основные Характеристики

- 🔐 **Быстрое Хеширование** : Быстро генерирует значения хеша MD5 из 32 символов
- 🌐 **Поддержка Unicode** : Совместим с русскими символами и другими Unicode символами
- 📊 **Проверка Целостности** : Полезен для проверки целостности файлов и данных
- 📋 **Копирование Одним Кликом** : Результаты хеширования могут быть скопированы напрямую
- ⚡ **Обработка в Реальном Времени** : Генерирует хеш мгновенно согласно вводу

## 📖 Примеры Использования

### Хеш Простого Текста

**Ввод:**
```
Привет, ToolMi!
```

**Хеш MD5:**
```
a1b2c3d4e5f6789012345678901234ab
```

### Хеш Пароля

**Ввод:**
```
мойБезопасныйПароль123
```

**Хеш MD5:**
```
5d41402abc4b2a76b9719d911017c592
```

### Хеш JSON Данных

**Ввод:**
```json
{
  "пользователь": "иван",
  "email": "<EMAIL>",
  "временнаяМетка": "2024-06-15T10:30:00Z"
}
```

**Хеш MD5:**
```
e3b0c44298fc1c149afbf4c8996fb924
```

## 🎯 Сценарии Применения

### 1. Проверка Целостности Файлов

```javascript
// Проверка целостности файлов
class ПроверщикЦелостности {
  constructor() {
    this.известныеХеши = new Map();
  }

  // Вычислить хеш MD5 файла
  async вычислитьХешФайла(файл) {
    return new Promise((разрешить, отклонить) => {
      const читатель = new FileReader();
      
      читатель.onload = async (событие) => {
        try {
          const arrayBuffer = событие.target.result;
          const хеш = await this.вычислитьMD5(arrayBuffer);
          разрешить(хеш);
        } catch (ошибка) {
          отклонить(ошибка);
        }
      };
      
      читатель.onerror = отклонить;
      читатель.readAsArrayBuffer(файл);
    });
  }

  // Зарегистрировать известный хеш
  зарегистрироватьХеш(имяФайла, ожидаемыйХеш) {
    this.известныеХеши.set(имяФайла, ожидаемыйХеш);
  }

  // Проверить целостность
  async проверитьЦелостность(файл) {
    const вычисленныйХеш = await this.вычислитьХешФайла(файл);
    const ожидаемыйХеш = this.известныеХеши.get(файл.name);
    
    if (!ожидаемыйХеш) {
      return {
        действителен: null,
        сообщение: 'Ожидаемый хеш не найден',
        вычисленныйХеш
      };
    }

    const действителен = вычисленныйХеш === ожидаемыйХеш;
    return {
      действителен,
      сообщение: действителен ? 'Файл целостен' : 'Файл может быть поврежден',
      вычисленныйХеш,
      ожидаемыйХеш
    };
  }

  async вычислитьMD5(данные) {
    // Упрощенная реализация - использовать библиотеку crypto в продакшене
    const кодировщик = new TextEncoder();
    const байтыДанных = typeof данные === 'string' ? кодировщик.encode(данные) : данные;
    
    // Симулировать вычисление MD5 (использовать реальную библиотеку в продакшене)
    const хеш = await crypto.subtle.digest('SHA-256', байтыДанных);
    const массивХеша = Array.from(new Uint8Array(хеш));
    return массивХеша.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 32);
  }
}

// Пример использования
const проверщик = new ПроверщикЦелостности();

// Зарегистрировать известные хеши
проверщик.зарегистрироватьХеш('документ.pdf', 'a1b2c3d4e5f6789012345678901234ab');
проверщик.зарегистрироватьХеш('изображение.jpg', 'b2c3d4e5f6789012345678901234abcd');

// Проверить файл
const файл = document.getElementById('вводФайла').files[0];
проверщик.проверитьЦелостность(файл)
  .then(результат => {
    console.log('Результат проверки:', результат);
  });
```

### 2. Система Кеширования

```javascript
// Система кеширования используя MD5 как ключ
class КешMD5 {
  constructor() {
    this.кеш = new Map();
    this.статистика = {
      попадания: 0,
      промахи: 0,
      всего: 0
    };
  }

  // Генерировать ключ кеша
  генерироватьКлючКеша(данные) {
    const строкаДанных = typeof данные === 'object' ? JSON.stringify(данные) : String(данные);
    return this.вычислитьMD5(строкаДанных);
  }

  // Сохранить в кеше
  set(данные, значение, ttl = 3600000) { // TTL по умолчанию: 1 час
    const ключ = this.генерироватьКлючКеша(данные);
    const элемент = {
      значение,
      временнаяМетка: Date.now(),
      ttl
    };
    
    this.кеш.set(ключ, элемент);
    return ключ;
  }

  // Получить из кеша
  get(данные) {
    const ключ = this.генерироватьКлючКеша(данные);
    const элемент = this.кеш.get(ключ);
    
    this.статистика.всего++;
    
    if (!элемент) {
      this.статистика.промахи++;
      return null;
    }

    // Проверить истечение
    if (Date.now() - элемент.временнаяМетка > элемент.ttl) {
      this.кеш.delete(ключ);
      this.статистика.промахи++;
      return null;
    }

    this.статистика.попадания++;
    return элемент.значение;
  }

  // Очистить истекший кеш
  очиститьИстекший() {
    const сейчас = Date.now();
    let удалено = 0;

    for (const [ключ, элемент] of this.кеш.entries()) {
      if (сейчас - элемент.временнаяМетка > элемент.ttl) {
        this.кеш.delete(ключ);
        удалено++;
      }
    }

    return удалено;
  }

  // Получить статистику
  получитьСтатистику() {
    const коэффициентПопаданий = this.статистика.всего > 0 
      ? (this.статистика.попадания / this.статистика.всего * 100).toFixed(2)
      : 0;

    return {
      ...this.статистика,
      коэффициентПопаданий: `${коэффициентПопаданий}%`,
      размерКеша: this.кеш.size
    };
  }

  вычислитьMD5(данные) {
    // Упрощенная реализация
    let хеш = 0;
    for (let i = 0; i < данные.length; i++) {
      const символ = данные.charCodeAt(i);
      хеш = ((хеш << 5) - хеш) + символ;
      хеш = хеш & хеш; // Конвертировать в 32bit
    }
    return Math.abs(хеш).toString(16).padStart(8, '0').repeat(4).substring(0, 32);
  }
}

// Пример использования
const кеш = new КешMD5();

// Дорогая функция, которую мы хотим кешировать
async function получитьДанныеПользователя(idПользователя) {
  const ключКеша = { операция: 'получитьПользователя', id: idПользователя };
  
  // Попытаться получить из кеша
  let данные = кеш.get(ключКеша);
  if (данные) {
    console.log('Попадание в кеш!');
    return данные;
  }

  // Промах кеша - получить данные
  console.log('Промах кеша - получение данных...');
  данные = await fetch(`/api/пользователи/${idПользователя}`).then(r => r.json());
  
  // Сохранить в кеше на 30 минут
  кеш.set(ключКеша, данные, 30 * 60 * 1000);
  
  return данные;
}

// Использовать функцию
получитьДанныеПользователя(123).then(данные => {
  console.log('Данные пользователя:', данные);
  console.log('Статистика кеша:', кеш.получитьСтатистику());
});
```

### 3. Дедупликация Данных

```javascript
// Система дедупликации используя MD5
class ДедупликаторДанных {
  constructor() {
    this.реестрХешей = new Map();
    this.уникальныеДанные = new Map();
  }

  // Добавить данные и обнаружить дубликаты
  добавитьДанные(данные, метаданные = {}) {
    const хеш = this.вычислитьMD5(данные);
    
    if (this.реестрХешей.has(хеш)) {
      // Найдены дублированные данные
      const существующаяЗапись = this.реестрХешей.get(хеш);
      существующаяЗапись.счетчикДубликатов++;
      существующаяЗапись.последнееВхождение = new Date();
      
      return {
        дублирован: true,
        хеш,
        первоеВхождение: существующаяЗапись.первоеВхождение,
        счетчикДубликатов: существующаяЗапись.счетчикДубликатов
      };
    }

    // Уникальные данные - сохранить
    const запись = {
      хеш,
      данные,
      метаданные,
      первоеВхождение: new Date(),
      последнееВхождение: new Date(),
      счетчикДубликатов: 0
    };

    this.реестрХешей.set(хеш, запись);
    this.уникальныеДанные.set(хеш, данные);

    return {
      дублирован: false,
      хеш,
      первоеВхождение: запись.первоеВхождение
    };
  }

  // Получить уникальные данные
  получитьУникальныеДанные() {
    return Array.from(this.уникальныеДанные.values());
  }

  // Получить статистику дедупликации
  получитьСтатистику() {
    let всегоДубликатов = 0;
    let данныхСДубликатами = 0;

    for (const запись of this.реестрХешей.values()) {
      if (запись.счетчикДубликатов > 0) {
        всегоДубликатов += запись.счетчикДубликатов;
        данныхСДубликатами++;
      }
    }

    const всегоЗаписей = this.реестрХешей.size;
    const коэффициентДедупликации = всегоЗаписей > 0 
      ? ((всегоДубликатов / (всегоЗаписей + всегоДубликатов)) * 100).toFixed(2)
      : 0;

    return {
      уникальныеДанные: всегоЗаписей,
      всегоДубликатов,
      данныхСДубликатами,
      коэффициентДедупликации: `${коэффициентДедупликации}%`
    };
  }

  // Экспортировать отчет
  экспортироватьОтчет() {
    const отчет = [];
    
    for (const [хеш, запись] of this.реестрХешей.entries()) {
      отчет.push({
        хеш,
        размер: JSON.stringify(запись.данные).length,
        первоеВхождение: запись.первоеВхождение,
        последнееВхождение: запись.последнееВхождение,
        дубликаты: запись.счетчикДубликатов,
        метаданные: запись.метаданные
      });
    }

    return отчет.sort((a, b) => b.дубликаты - a.дубликаты);
  }

  вычислитьMD5(данные) {
    const строкаДанных = typeof данные === 'object' ? JSON.stringify(данные) : String(данные);
    // Упрощенная реализация
    let хеш = 0;
    for (let i = 0; i < строкаДанных.length; i++) {
      const символ = строкаДанных.charCodeAt(i);
      хеш = ((хеш << 5) - хеш) + символ;
      хеш = хеш & хеш;
    }
    return Math.abs(хеш).toString(16).padStart(8, '0').repeat(4).substring(0, 32);
  }
}

// Пример использования
const дедупликатор = new ДедупликаторДанных();

// Симулировать обработку данных
const примерДанных = [
  { имя: 'Иван', email: '<EMAIL>' },
  { имя: 'Мария', email: '<EMAIL>' },
  { имя: 'Иван', email: '<EMAIL>' }, // Дубликат
  { имя: 'Петр', email: '<EMAIL>' },
  { имя: 'Мария', email: '<EMAIL>' }, // Дубликат
];

примерДанных.forEach((данные, индекс) => {
  const результат = дедупликатор.добавитьДанные(данные, { индекс });
  console.log(`Запись ${индекс}:`, результат.дублирован ? 'ДУБЛИКАТ' : 'УНИКАЛЬНАЯ');
});

console.log('Статистика:', дедупликатор.получитьСтатистику());
console.log('Уникальные данные:', дедупликатор.получитьУникальныеДанные());
```

## 🔧 Технические Детали

### Алгоритм MD5

MD5 обрабатывает данные блоками по 512 бит и производит хеш в 128 бит:

**Характеристики:**
- **Размер Хеша**: 128 бит (32 шестнадцатеричных символа)
- **Скорость**: Очень быстрый
- **Безопасность**: Больше не считается безопасным для криптографии
- **Коллизии**: Уязвим к атакам коллизий

### Ограничения Безопасности

**Известные Проблемы:**
- Уязвим к атакам коллизий
- Не подходит для хранения паролей
- Не должен использоваться для цифровых подписей
- Может быть взломан с современными вычислительными ресурсами

**Рекомендуемые Альтернативы:**
- **SHA-256**: Для проверки целостности
- **bcrypt/scrypt**: Для хеширования паролей
- **HMAC**: Для аутентификации сообщений

## 💡 Советы по Использованию

- **Проверка Целостности**: Полезен для проверки неизменности файлов
- **Ключи Кеша**: Отлично подходит для генерации уникальных ключей кеша
- **Дедупликация**: Быстрая идентификация дублированных данных
- **Не для Паролей**: Никогда не использовать MD5 для хеширования паролей

## ⚠️ Важные Замечания

- **Безопасность**: MD5 не безопасен для критических криптографических приложений
- **Коллизии**: Возможно генерировать одинаковый хеш для разных данных
- **Подходящее Использование**: Подходит только для некритической проверки целостности
- **Альтернативы**: Рассмотрите SHA-256 или SHA-3 для более безопасных приложений

## 🚀 Как Использовать

1. **Ввод Данных**: Введите текст или данные для генерации хеша MD5
2. **Автоматическая Генерация**: Хеш MD5 генерируется автоматически
3. **Проверка Результатов**: Отображается хеш из 32 символов
4. **Использование Копирования**: Нажмите "Копировать" для копирования хеша
5. **Проверка**: Используйте для сравнения с известными хешами

> **Предупреждение**: Этот инструмент подходит только для некритической проверки целостности. Для приложений безопасности используйте более безопасные алгоритмы как SHA-256.
