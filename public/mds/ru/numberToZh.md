# Конвертер Чисел в Китайские

Этот инструмент конвертирует арабские цифры в китайские иероглифы в различных форматах. Поддерживает простые числа, финансовые числа (защита от мошенничества) и традиционные числа, необходим для официальных документов, контрактов и приложений, требующих китайскую нумерацию.

## ✨ Основные Характеристики

- 🔢 **Множественные Форматы** : Простые, финансовые и традиционные числа
- 💰 **Формат Защиты от Мошенничества** : Специальные символы для финансовых документов
- 📋 **Копирование Одним Кликом** : Результаты могут быть скопированы напрямую
- ✅ **Валидация Ввода** : Проверяет валидные числа перед конвертацией
- 🌐 **Полная Поддержка** : Целые числа и десятичные дроби

## 📖 Примеры Использования

### Конвертация Базовых Чисел

**Число:** `12345`

**Доступные Форматы:**
- **Простой**: 一万二千三百四十五
- **Финансовый**: 壹万贰仟叁佰肆拾伍
- **Традиционный**: 一萬二千三百四十五

### Конвертация Денежных Значений

**Число:** `1234.56`

**Доступные Форматы:**
- **Простой**: 一千二百三十四点五六
- **Финансовый**: 壹仟贰佰叁拾肆元伍角陆分
- **С Валютой**: 壹仟贰佰叁拾肆元伍角陆分整

### Большие Числа

**Число:** `987654321`

**Доступные Форматы:**
- **Простой**: 九亿八千七百六十五万四千三百二十一
- **Финансовый**: 玖亿捌仟柒佰陆拾伍万肆仟叁佰贰拾壹
- **Традиционный**: 九億八千七百六十五萬四千三百二十一

## 🎯 Сценарии Применения

### 1. Система Выставления Счетов

```javascript
// Конвертер чисел в китайский
class КонвертерЧиселВКитайский {
  constructor() {
    // Простые цифры
    this.простыеЦифры = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    
    // Финансовые цифры (защита от мошенничества)
    this.финансовыеЦифры = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    
    // Единицы
    this.единицы = ['', '十', '百', '千'];
    this.финансовыеЕдиницы = ['', '拾', '佰', '仟'];
    
    // Большие единицы
    this.большиеЕдиницы = ['', '万', '亿', '兆'];
    
    // Денежные единицы
    this.денежныеЕдиницы = {
      юань: '元',
      цзяо: '角',
      фэнь: '分',
      чжэн: '整'
    };
  }

  // Конвертировать число в простой китайский
  вПростойКитайский(число) {
    if (число === 0) return '零';
    
    const строкаЧисла = Math.abs(число).toString();
    const [целаяЧасть, десятичнаяЧасть] = строкаЧисла.split('.');
    
    let результат = '';
    
    // Добавить знак минус если нужно
    if (число < 0) {
      результат += '负';
    }
    
    // Конвертировать целую часть
    результат += this.конвертироватьЦелуюЧасть(целаяЧасть, 'простой');
    
    // Конвертировать десятичную часть если есть
    if (десятичнаяЧасть) {
      результат += '点';
      for (const цифра of десятичнаяЧасть) {
        результат += this.простыеЦифры[parseInt(цифра)];
      }
    }
    
    return результат;
  }

  // Конвертировать число в финансовый китайский
  вФинансовыйКитайский(число, включитьВалюту = false) {
    if (число === 0) return включитьВалюту ? '零元整' : '零';
    
    const строкаЧисла = Math.abs(число).toFixed(2);
    const [целаяЧасть, десятичнаяЧасть] = строкаЧисла.split('.');
    
    let результат = '';
    
    // Добавить знак минус если нужно
    if (число < 0) {
      результат += '负';
    }
    
    // Конвертировать целую часть
    результат += this.конвертироватьЦелуюЧасть(целаяЧасть, 'финансовый');
    
    if (включитьВалюту) {
      результат += this.денежныеЕдиницы.юань;
      
      // Конвертировать копейки
      const цзяо = parseInt(десятичнаяЧасть[0]);
      const фэнь = parseInt(десятичнаяЧасть[1]);
      
      if (цзяо > 0) {
        результат += this.финансовыеЦифры[цзяо] + this.денежныеЕдиницы.цзяо;
      }
      
      if (фэнь > 0) {
        результат += this.финансовыеЦифры[фэнь] + this.денежныеЕдиницы.фэнь;
      }
      
      // Добавить "整" если нет копеек
      if (цзяо === 0 && фэнь === 0) {
        результат += this.денежныеЕдиницы.чжэн;
      }
    } else {
      // Конвертировать десятичную часть обычно
      if (десятичнаяЧасть && десятичнаяЧасть !== '00') {
        результат += '点';
        for (const цифра of десятичнаяЧасть) {
          результат += this.финансовыеЦифры[parseInt(цифра)];
        }
      }
    }
    
    return результат;
  }

  // Конвертировать целую часть
  конвертироватьЦелуюЧасть(строкаЧисла, тип) {
    const цифры = тип === 'финансовый' ? this.финансовыеЦифры : this.простыеЦифры;
    const единицы = тип === 'финансовый' ? this.финансовыеЕдиницы : this.единицы;
    
    // Разделить на группы по 4 цифры
    const группы = [];
    for (let i = строкаЧисла.length; i > 0; i -= 4) {
      const начало = Math.max(0, i - 4);
      группы.unshift(строкаЧисла.slice(начало, i));
    }
    
    let результат = '';
    
    for (let i = 0; i < группы.length; i++) {
      const группа = группы[i];
      const большаяЕдиница = this.большиеЕдиницы[группы.length - 1 - i];
      
      const конвертированнаяГруппа = this.конвертироватьГруппу(группа, цифры, единицы);
      
      if (конвертированнаяГруппа && конвертированнаяГруппа !== '零') {
        // Добавить ноль если нужно
        if (результат && группа.length < 4 && parseInt(группа) < 1000) {
          результат += '零';
        }
        
        результат += конвертированнаяГруппа + большаяЕдиница;
      }
    }
    
    return результат || '零';
  }

  // Конвертировать группу до 4 цифр
  конвертироватьГруппу(группа, цифры, единицы) {
    const число = parseInt(группа);
    if (число === 0) return '';
    
    let результат = '';
    const строкаГруппы = группа.padStart(4, '0');
    
    for (let i = 0; i < строкаГруппы.length; i++) {
      const цифра = parseInt(строкаГруппы[i]);
      const позиция = строкаГруппы.length - 1 - i;
      
      if (цифра > 0) {
        // Добавить ноль если нужно
        if (результат && результат[результат.length - 1] !== '零' && this.нужноНоль(строкаГруппы, i)) {
          результат += '零';
        }
        
        результат += цифры[цифра];
        
        if (позиция > 0) {
          результат += единицы[позиция];
        }
      }
    }
    
    return результат;
  }

  // Проверить нужно ли добавить ноль
  нужноНоль(строкаГруппы, текущаяПозиция) {
    for (let i = текущаяПозиция - 1; i >= 0; i--) {
      if (parseInt(строкаГруппы[i]) > 0) {
        return false;
      }
    }
    return true;
  }

  // Конвертировать дату в китайский
  датуВКитайский(дата, формат = 'простой') {
    const год = дата.getFullYear();
    const месяц = дата.getMonth() + 1;
    const день = дата.getDate();
    
    const конвертер = формат === 'финансовый' ? 
      this.вФинансовыйКитайский.bind(this) : 
      this.вПростойКитайский.bind(this);
    
    return `${конвертер(год)}年${конвертер(месяц)}月${конвертер(день)}日`;
  }

  // Конвертировать время в китайский
  времяВКитайский(часы, минуты, секунды = null, формат = 'простой') {
    const конвертер = формат === 'финансовый' ? 
      this.вФинансовыйКитайский.bind(this) : 
      this.вПростойКитайский.bind(this);
    
    let результат = `${конвертер(часы)}时${конвертер(минуты)}分`;
    
    if (секунды !== null) {
      результат += `${конвертер(секунды)}秒`;
    }
    
    return результат;
  }

  // Валидировать ввод
  валидироватьЧисло(ввод) {
    const число = parseFloat(ввод);
    
    if (isNaN(число)) {
      return { валидно: false, ошибка: 'Не является валидным числом' };
    }
    
    if (Math.abs(число) >= 1e16) {
      return { валидно: false, ошибка: 'Число слишком большое' };
    }
    
    return { валидно: true, число };
  }
}

// Пример использования в системе выставления счетов
class СистемаВыставленияСчетов {
  constructor() {
    this.конвертер = new КонвертерЧиселВКитайский();
  }

  генерироватьСчет(данные) {
    const валидация = this.конвертер.валидироватьЧисло(данные.сумма);
    
    if (!валидация.валидно) {
      throw new Error(`Неверная сумма: ${валидация.ошибка}`);
    }

    const сумма = валидация.число;
    const суммаПрописью = this.конвертер.вФинансовыйКитайский(сумма, true);
    const датаВыставления = this.конвертер.датуВКитайский(new Date(), 'финансовый');

    return {
      номер: данные.номерСчета,
      клиент: данные.клиент,
      сумма: сумма,
      суммаПрописью: суммаПрописью,
      датаВыставления: датаВыставления,
      описание: данные.описание
    };
  }

  форматироватьСчет(счет) {
    return `
发票号码：${счет.номер}
客户：${счет.клиент}
金额：￥${счет.сумма.toFixed(2)}
金额大写：${счет.суммаПрописью}
开票日期：${счет.датаВыставления}
描述：${счет.описание}
    `.trim();
  }
}

// Пример использования
const система = new СистемаВыставленияСчетов();

const данныеСчета = {
  номерСчета: 'INV-2024-001',
  клиент: '北京科技有限公司',
  сумма: 12345.67,
  описание: '软件开发服务费'
};

const счет = система.генерироватьСчет(данныеСчета);
console.log(система.форматироватьСчет(счет));
```

### 2. Система Контрактов

```javascript
// Генератор контрактов с числами на китайском
class ГенераторКонтрактов {
  constructor() {
    this.конвертер = new КонвертерЧиселВКитайский();
    this.шаблоны = new Map();
    this.инициализироватьШаблоны();
  }

  инициализироватьШаблоны() {
    // Шаблон договора купли-продажи
    this.шаблоны.set('купля_продажа', `
合同编号：{номерКонтракта}
甲方：{сторонаА}
乙方：{сторонаБ}

根据《中华人民共和国合同法》及相关法律法规，甲乙双方经友好协商，就以下事项达成一致：

一、标的物及价格
商品名称：{названиеТовара}
数量：{количествоПрописью}
单价：人民币{ценаЗаЕдиницуПрописью}
总价：人民币{общаяЦенаПрописью}

二、交付时间
交付日期：{датаДоставкиПрописью}

三、付款方式
付款期限：{условияПлатежа}

本合同一式{количествоЭкземпляровПрописью}份，甲乙双方各执{экземпляровДляСторонПрописью}份。

甲方签字：_________________ 日期：{датаПодписанияПрописью}
乙方签字：_________________ 日期：{датаПодписанияПрописью}
    `);

    // Шаблон трудового договора
    this.шаблоны.set('трудовой', `
劳动合同

合同编号：{номерКонтракта}
甲方（用人单位）：{компания}
乙方（劳动者）：{сотрудник}

根据《中华人民共和国劳动法》，甲乙双方经平等协商一致，自愿签订本合同：

一、合同期限
合同期限：{срокКонтрактаПрописью}
试用期：{испытательныйСрокПрописью}

二、工作内容和工作地点
职位：{должность}
工作地点：{местоРаботы}

三、工作时间和休息休假
每周工作{часовВНеделюПрописью}小时
每日工作{часовВДеньПрописью}小时

四、劳动报酬
月工资：人民币{зарплатаПрописью}
年终奖金：人民币{бонусПрописью}

本合同自{датаНачалаПрописью}起生效。

甲方（盖章）：_________________ 
乙方（签字）：_________________ 
签订日期：{датаПодписанияПрописью}
    `);
  }

  генерироватьКонтракт(тип, данные) {
    const шаблон = this.шаблоны.get(тип);
    if (!шаблон) {
      throw new Error(`Тип контракта не найден: ${тип}`);
    }

    // Обработать данные и конвертировать числа
    const обработанныеДанные = this.обработатьДанные(данные);
    
    // Заменить заполнители
    let контракт = шаблон;
    for (const [ключ, значение] of Object.entries(обработанныеДанные)) {
      const заполнитель = `{${ключ}}`;
      контракт = контракт.replace(new RegExp(заполнитель, 'g'), значение);
    }

    return контракт;
  }

  обработатьДанные(данные) {
    const обработанные = { ...данные };

    // Конвертировать числа в финансовый китайский
    const числовыеПоля = [
      'количество', 'ценаЗаЕдиницу', 'общаяЦена', 'зарплата', 'бонус',
      'количествоЭкземпляров', 'экземпляровДляСторон', 'срокКонтракта', 'испытательныйСрок',
      'часовВНеделю', 'часовВДень'
    ];

    for (const поле of числовыеПоля) {
      if (данные[поле] !== undefined) {
        if (поле.includes('цена') || поле.includes('зарплата') || поле.includes('бонус')) {
          обработанные[поле + 'Прописью'] = this.конвертер.вФинансовыйКитайский(данные[поле], true);
        } else {
          обработанные[поле + 'Прописью'] = this.конвертер.вФинансовыйКитайский(данные[поле]);
        }
      }
    }

    // Конвертировать даты
    const поляДат = ['датаДоставки', 'датаПодписания', 'датаНачала'];
    for (const поле of поляДат) {
      if (данные[поле]) {
        const дата = new Date(данные[поле]);
        обработанные[поле + 'Прописью'] = this.конвертер.датуВКитайский(дата, 'финансовый');
      }
    }

    return обработанные;
  }
}

// Пример использования
const генератор = new ГенераторКонтрактов();

// Данные для договора купли-продажи
const данныеКуплиПродажи = {
  номерКонтракта: 'CV-2024-001',
  сторонаА: '北京科技有限公司',
  сторонаБ: '上海贸易有限公司',
  названиеТовара: '笔记本电脑',
  количество: 50,
  ценаЗаЕдиницу: 5999.99,
  общаяЦена: 299999.50,
  датаДоставки: '2024-03-15',
  количествоЭкземпляров: 2,
  экземпляровДляСторон: 1,
  датаПодписания: '2024-01-15'
};

const контракт = генератор.генерироватьКонтракт('купля_продажа', данныеКуплиПродажи);
console.log(контракт);
```

## 🔧 Технические Детали

### Системы Китайской Нумерации

**Простые Числа (简体):**
- Используются в неформальных контекстах
- Символы: 零一二三四五六七八九十百千万亿

**Финансовые Числа (大写):**
- Используются в официальных и финансовых документах
- Символы: 零壹贰叁肆伍陆柒捌玖拾佰仟万亿
- Предотвращают мошеннические изменения

**Традиционные Числа (繁体):**
- Используются в Тайване, Гонконге и формальных контекстах
- Символы: 零一二三四五六七八九十百千萬億

### Единицы и Группировка

**Базовые Единицы:**
- 十 (10)
- 百 (100) 
- 千 (1,000)
- 万 (10,000)
- 亿 (100,000,000)

**Правила Группировки:**
- Числа группируются по 4 цифры
- Ноль используется для обозначения пустых позиций
- Единицы опускаются когда уместно

### Денежные Единицы

**Китайская Валюта (人民币):**
- 元 (юань) - основная единица
- 角 (цзяо) - 1/10 юаня
- 分 (фэнь) - 1/100 юаня
- 整 (чжэн) - обозначает точную сумму

## 💡 Советы по Использованию

- **Контекст** : Используйте финансовый формат для официальных документов
- **Точность** : Проверяйте конвертацию для важных значений
- **Культура** : Понимайте культурный контекст китайских чисел
- **Валидация** : Всегда валидируйте ввод перед конвертацией

## ⚠️ Важные Замечания

- **Финансовый Формат** : Обязателен в юридических документах в Китае
- **Точность** : Очень большие числа могут иметь ограничения
- **Культурный Контекст** : Некоторые числа имеют особые значения
- **Юридическая Валидация** : Консультируйтесь с местными регулированиями для официального использования

## 🚀 Как Использовать

1. **Ввод Числа** : Введите число которое хотите конвертировать
2. **Выбор Формата** : Выберите между простым, финансовым или традиционным
3. **Денежные Опции** : Выберите включать ли денежные единицы
4. **Проверка** : Просмотрите результат конвертации
5. **Использование Копирования** : Нажмите "Копировать" для использования китайского текста

> **Совет** : Этот инструмент обрабатывает локально на стороне клиента и не отправляет данные на сервер, гарантируя конфиденциальность и быстроту конвертации.
