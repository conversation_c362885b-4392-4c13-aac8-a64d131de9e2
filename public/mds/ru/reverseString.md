# Инструмент Обращения Строк

Этот инструмент обращает текстовые строки различными способами. Предлагает простое обращение символов, обращение слов, обращение строк и другие продвинутые опции для манипуляции текстом, полезен для разработки, тестирования и обработки данных.

## ✨ Основные Характеристики

- 🔄 **Множественные Типы Обращения** : Символы, слова, строки и многое другое
- 🌐 **Поддержка Unicode** : Совместим с эмодзи и специальными символами
- 📋 **Копирование Одним Кликом** : Результат может быть скопирован напрямую
- ⚡ **Быстрая Обработка** : Мгновенное обращение больших текстов
- 🎯 **Продвинутые Опции** : Сохранение пробелов, заглавных букв и пунктуации

## 📖 Примеры Использования

### Простое Обращение Символов

**Исходный Текст:**
```
Привет, мир!
```

**Обращенный Текст:**
```
!рим ,тевирП
```

### Обращение Слов

**Исходный Текст:**
```
Это пример предложения для демонстрации
```

**Обращенный Текст:**
```
демонстрации для предложения пример Это
```

### Обращение Строк

**Исходный Текст:**
```
Первая строка
Вторая строка
Третья строка
```

**Обращенный Текст:**
```
Третья строка
Вторая строка
Первая строка
```

### Обращение Каждого Слова

**Исходный Текст:**
```
Программирование это увлекательно
```

**Обращенный Текст:**
```
еинавориммарgorП отэ онлетакелвеу
```

## 🎯 Сценарии Применения

### 1. Разработка и Тестирование

```javascript
// Утилита обращения строк для разработки
class ОбращательСтрок {
  constructor() {
    this.опции = {
      сохранятьПробелы: false,
      сохранятьЗаглавные: false,
      сохранятьПунктуацию: false,
      обращатьПоСловам: false,
      обращатьСтроки: false
    };
  }

  // Простое обращение символов
  обратитьСимволы(текст) {
    return текст.split('').reverse().join('');
  }

  // Обращение слов
  обратитьСлова(текст) {
    return текст.split(/\s+/).reverse().join(' ');
  }

  // Обращение строк
  обратитьСтроки(текст) {
    return текст.split('\n').reverse().join('\n');
  }

  // Обращение каждого слова по отдельности
  обратитьКаждоеСлово(текст) {
    return текст.split(/(\s+)/).map(часть => {
      // Сохранить пробелы
      if (/^\s+$/.test(часть)) {
        return часть;
      }
      return this.обратитьСимволы(часть);
    }).join('');
  }

  // Обращение с сохранением заглавных букв
  обратитьСохраняяЗаглавные(текст) {
    const обращенныйТекст = this.обратитьСимволы(текст.toLowerCase());
    let результат = '';
    
    for (let i = 0; i < обращенныйТекст.length; i++) {
      const исходныйСимвол = текст[текст.length - 1 - i];
      const обращенныйСимвол = обращенныйТекст[i];
      
      if (исходныйСимвол === исходныйСимвол.toUpperCase()) {
        результат += обращенныйСимвол.toUpperCase();
      } else {
        результат += обращенныйСимвол;
      }
    }
    
    return результат;
  }

  // Обращение с сохранением позиций пробелов
  обратитьСохраняяПробелы(текст) {
    const символы = текст.split('');
    const позиции = [];
    const буквы = [];
    
    // Определить позиции пробелов и извлечь буквы
    for (let i = 0; i < символы.length; i++) {
      if (символы[i] === ' ') {
        позиции.push(i);
      } else {
        буквы.push(символы[i]);
      }
    }
    
    // Обратить только буквы
    буквы.reverse();
    
    // Восстановить строку с пробелами в исходных позициях
    const результат = [];
    let индексБуквы = 0;
    
    for (let i = 0; i < символы.length; i++) {
      if (позиции.includes(i)) {
        результат.push(' ');
      } else {
        результат.push(буквы[индексБуквы++]);
      }
    }
    
    return результат.join('');
  }

  // Проверить палиндром
  этоПалиндром(текст) {
    const очищенныйТекст = текст.toLowerCase().replace(/[^а-яёa-z]/g, '');
    const обращенныйТекст = this.обратитьСимволы(очищенныйТекст);
    return очищенныйТекст === обращенныйТекст;
  }

  // Найти палиндромы в тексте
  найтиПалиндромы(текст, минимальнаяДлина = 3) {
    const слова = текст.toLowerCase().match(/[а-яёa-z]+/g) || [];
    const палиндромы = [];

    for (const слово of слова) {
      if (слово.length >= минимальнаяДлина && this.этоПалиндром(слово)) {
        палиндромы.push(слово);
      }
    }

    return [...new Set(палиндромы)]; // Удалить дубликаты
  }
}

// Пример использования в тестах
const обращатель = new ОбращательСтрок();

// Тестировать функцию валидации
function валидироватьEmail(email) {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

// Генерировать тестовые случаи с обращенными строками
const тестовыеEmails = [
  'пользователь@пример.com',
  'тест@домен.com.ru',
  'админ@сайт.org'
];

console.log('=== Тест с Обращенными Строками ===');
тестовыеEmails.forEach(email => {
  const обращенныйEmail = обращатель.обратитьСимволы(email);
  console.log(`Исходный: ${email} - Валидный: ${валидироватьEmail(email)}`);
  console.log(`Обращенный: ${обращенныйEmail} - Валидный: ${валидироватьEmail(обращенныйEmail)}`);
  console.log('---');
});

// Найти палиндромы
const текст = 'Казак на коне ехал к реке. Шалаш стоял у дороги.';
const палиндромы = обращатель.найтиПалиндромы(текст);
console.log('Найденные палиндромы:', палиндромы);
```

### 2. Обработка Данных

```python
# Обработчик данных с обращением строк
import re
from typing import List, Dict, Any

class ОбработчикТекста:
    def __init__(self):
        self.статистика = {
            'всего_обработано': 0,
            'найдено_палиндромов': 0,
            'выполнено_обращений': 0
        }
    
    def обратить_символы(self, текст: str) -> str:
        """Обратить порядок символов"""
        return текст[::-1]
    
    def обратить_слова(self, текст: str) -> str:
        """Обратить порядок слов"""
        return ' '.join(текст.split()[::-1])
    
    def обратить_строки(self, текст: str) -> str:
        """Обратить порядок строк"""
        return '\n'.join(текст.split('\n')[::-1])
    
    def это_палиндром(self, текст: str) -> bool:
        """Проверить является ли текст палиндромом"""
        очищенный_текст = re.sub(r'[^а-яёa-z]', '', текст.lower())
        return очищенный_текст == очищенный_текст[::-1]
    
    def найти_палиндромы(self, текст: str, минимальная_длина: int = 3) -> List[str]:
        """Найти палиндромы в тексте"""
        слова = re.findall(r'[а-яёa-z]+', текст.lower())
        палиндромы = []
        
        for слово in слова:
            if len(слово) >= минимальная_длина and self.это_палиндром(слово):
                палиндромы.append(слово)
        
        return list(set(палиндромы))
    
    def сгенерировать_отчет_анализа(self, текст: str) -> Dict[str, Any]:
        """Сгенерировать полный отчет анализа текста"""
        палиндромы = self.найти_палиндромы(текст)
        
        # Анализ частоты символов
        частота_символов = {}
        for символ in текст.lower():
            if символ.isalpha():
                частота_символов[символ] = частота_символов.get(символ, 0) + 1
        
        # Анализ слов
        слова = re.findall(r'\b\w+\b', текст.lower())
        частота_слов = {}
        for слово in слова:
            частота_слов[слово] = частота_слов.get(слово, 0) + 1
        
        return {
            'исходный_текст': текст,
            'обращенные_символы': self.обратить_символы(текст),
            'обращенные_слова': self.обратить_слова(текст),
            'обращенные_строки': self.обратить_строки(текст),
            'статистика': {
                'всего_символов': len(текст),
                'всего_слов': len(слова),
                'всего_строк': len(текст.split('\n')),
                'найдено_палиндромов': len(палиндромы),
                'палиндромы': палиндромы
            },
            'частота_символов': dict(sorted(частота_символов.items(), key=lambda x: x[1], reverse=True)[:10]),
            'частота_слов': dict(sorted(частота_слов.items(), key=lambda x: x[1], reverse=True)[:10])
        }

# Пример использования
обработчик = ОбработчикТекста()

пример_текста = """
Программирование это искусство которое требует логики и креативности.
Казак, шалаш и топот являются примерами палиндромов.
Разработка программного обеспечения как строительство дома.
"""

отчет = обработчик.сгенерировать_отчет_анализа(пример_текста)
print("=== ОТЧЕТ АНАЛИЗА ТЕКСТА ===")
print(f"Всего символов: {отчет['статистика']['всего_символов']}")
print(f"Найденные палиндромы: {отчет['статистика']['палиндромы']}")
```

### 3. Простая Криптография

```javascript
// Система криптографии с использованием обращения
class КриптографияОбращения {
  constructor() {
    this.ключПоворота = 13;
  }

  // Криптография обращением + ROT13
  зашифровать(текст) {
    const текстRot13 = this.применитьRot13(текст);
    return this.обратитьСимволы(текстRot13);
  }

  // Расшифровать
  расшифровать(зашифрованныйТекст) {
    const обращенныйТекст = this.обратитьСимволы(зашифрованныйТекст);
    return this.применитьRot13(обращенныйТекст);
  }

  // Применить ROT13
  применитьRot13(текст) {
    return текст.replace(/[a-zA-Zа-яёА-ЯЁ]/g, символ => {
      if (/[a-zA-Z]/.test(символ)) {
        const начало = символ <= 'Z' ? 65 : 97;
        return String.fromCharCode(((символ.charCodeAt(0) - начало + 13) % 26) + начало);
      } else {
        // Для кириллицы
        const начало = символ <= 'Я' ? 'А'.charCodeAt(0) : 'а'.charCodeAt(0);
        const размерАлфавита = 33; // 33 буквы в русском алфавите
        return String.fromCharCode(((символ.charCodeAt(0) - начало + 13) % размерАлфавита) + начало);
      }
    });
  }

  // Обратить символы
  обратитьСимволы(текст) {
    return текст.split('').reverse().join('');
  }

  // Криптография блоками
  зашифроватьБлоками(текст, размерБлока = 5) {
    const блоки = [];
    
    for (let i = 0; i < текст.length; i += размерБлока) {
      const блок = текст.slice(i, i + размерБлока);
      блоки.push(this.обратитьСимволы(блок));
    }
    
    return блоки.join('|');
  }

  // Расшифровать блоки
  расшифроватьБлоки(зашифрованныйТекст) {
    const блоки = зашифрованныйТекст.split('|');
    return блоки.map(блок => this.обратитьСимволы(блок)).join('');
  }
}

// Пример использования
const крипто = new КриптографияОбращения();
const сообщение = "Это секретное сообщение которое нужно защитить!";

console.log("Исходное:", сообщение);
const зашифрованное = крипто.зашифровать(сообщение);
console.log("Зашифрованное:", зашифрованное);
console.log("Расшифрованное:", крипто.расшифровать(зашифрованное));
```

## 🔧 Технические Детали

### Типы Обращения

**Обращение Символов:**
- Обращает порядок всех символов
- Сохраняет специальные символы
- Пример: "abc" → "cba"

**Обращение Слов:**
- Обращает порядок слов
- Сохраняет пробелы между словами
- Пример: "привет мир" → "мир привет"

**Обращение Строк:**
- Обращает порядок строк
- Сохраняет содержимое каждой строки
- Полезно для текстовых файлов

### Обработка Unicode

**Полная Поддержка:**
- Unicode символы сохраняются
- Эмодзи обрабатываются корректно
- Акценты и символы сохраняются

**Составные Символы:**
- Некоторые символы могут иметь особое поведение
- Всегда тестируйте с реальными данными
- Рассмотрите нормализацию Unicode

## 💡 Советы по Использованию

- **Тесты** : Генерируйте тестовые случаи с обращенными данными
- **Валидация** : Тестируйте устойчивость функций
- **Палиндромы** : Определяйте паттерны в текстах
- **Отладка** : Используйте для поиска проблем в строках

## ⚠️ Важные Замечания

- **Производительность** : Большие тексты могут обрабатываться медленно
- **Unicode** : Некоторые символы могут обращаться некорректно
- **Кодировка** : Используйте UTF-8 для специальных символов
- **Безопасность** : Не используйте для действительно чувствительных данных

## 🚀 Как Использовать

1. **Ввод** : Вставьте текст который хотите обратить
2. **Тип** : Выберите тип обращения
3. **Опции** : Настройте продвинутые опции
4. **Результат** : Просмотрите обращенный текст
5. **Копирование** : Используйте кнопку копирования для использования результата

> **Совет** : Этот инструмент обрабатывает локально и не отправляет данные на сервер, гарантируя конфиденциальность.
