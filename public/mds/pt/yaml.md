# Ferramenta de Formato YAML

Esta ferramenta fornece formatação, validação e embelezamento de arquivos YAML. YAML (YAML Ain't Markup Language) é um formato de serialização de dados legível por humanos, amplamente usado para arquivos de configuração, documentação de API e pipelines DevOps.

## ✨ Características Principais

- ✅ **Validação de Sintaxe** : Detecção automática de erros de sintaxe YAML
- 🎨 **Formatação Inteligente** : Indentação consistente e estrutura limpa
- 📋 **Cópia com Um Clique** : YAML formatado pode ser copiado diretamente
- 🔍 **Detecção de Erros** : Indicação clara de problemas de sintaxe
- 🌐 **Suporte Unicode** : Compatível com caracteres especiais e acentos

## 📖 Exemplos de Uso

### Formatação de Configuração Básica

**YAML de Entrada (mal formatado):**
```yaml
aplicacao:
nome: ToolMi
versao: 1.0.0
servidor:
host: localhost
porta: 3000
ssl: false
recursos:
- base64
- uuid
- json
```

**YAML Formatado:**
```yaml
aplicacao:
  nome: ToolMi
  versao: 1.0.0
servidor:
  host: localhost
  porta: 3000
  ssl: false
recursos:
  - base64
  - uuid
  - json
```

### Formatação de Configuração Complexa

**YAML de Entrada:**
```yaml
banco_dados:
tipo: postgresql
host: db.toolmi.com
porta: 5432
credenciais:
usuario: admin
senha: senha_segura
configuracao:
debug: true
log_nivel: info
recursos_habilitados:
- autenticacao
- cache
- monitoramento
```

**YAML Formatado:**
```yaml
banco_dados:
  tipo: postgresql
  host: db.toolmi.com
  porta: 5432
  credenciais:
    usuario: admin
    senha: senha_segura
configuracao:
  debug: true
  log_nivel: info
  recursos_habilitados:
    - autenticacao
    - cache
    - monitoramento
```

## 🎯 Cenários de Aplicação

### 1. Configuração de Aplicação

```yaml
# config/aplicacao.yml
aplicacao:
  nome: "Sistema ToolMi"
  versao: "2.1.0"
  ambiente: producao
  debug: false
  manutencao: false

servidor:
  host: "0.0.0.0"
  porta: 8080
  workers: 4
  timeout: 30
  ssl:
    habilitado: true
    certificado: "/etc/ssl/certs/toolmi.crt"
    chave_privada: "/etc/ssl/private/toolmi.key"

banco_dados:
  padrao: postgresql
  conexoes:
    postgresql:
      driver: postgresql
      host: ${DB_HOST:-localhost}
      porta: ${DB_PORT:-5432}
      database: ${DB_NAME:-toolmi_db}
      usuario: ${DB_USER:-postgres}
      senha: ${DB_PASSWORD}
      opcoes:
        charset: utf8
        timezone: America/Sao_Paulo
      pool:
        min: 2
        max: 10
        timeout: 5000
    
    redis:
      driver: redis
      host: ${REDIS_HOST:-localhost}
      porta: ${REDIS_PORT:-6379}
      database: ${REDIS_DB:-0}
      senha: ${REDIS_PASSWORD}
      ttl: 3600

logging:
  nivel: info
  formato: json
  saidas:
    - console
    - arquivo
  arquivo:
    caminho: "/var/log/toolmi/app.log"
    rotacao: diaria
    retencao: 30
    tamanho_maximo: "100MB"
  filtros:
    - nivel: error
      notificar: true
    - nivel: warning
      notificar: false

recursos:
  autenticacao:
    habilitado: true
    provedor: jwt
    configuracao:
      algoritmo: HS256
      expiracao: 86400
      refresh_expiracao: 604800
      emissor: "toolmi.com"
  
  rate_limiting:
    habilitado: true
    configuracao:
      limite_global: 1000
      janela: 3600
      limite_por_ip: 100
      whitelist:
        - "127.0.0.1"
        - "10.0.0.0/8"
  
  cors:
    habilitado: true
    configuracao:
      origens:
        - "https://toolmi.com"
        - "https://www.toolmi.com"
        - "https://app.toolmi.com"
      metodos:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      cabecalhos:
        - "Content-Type"
        - "Authorization"
        - "X-Requested-With"
      credenciais: true

monitoramento:
  metricas:
    habilitado: true
    endpoint: "/metrics"
    intervalo: 30
  
  health_check:
    habilitado: true
    endpoint: "/health"
    verificacoes:
      - nome: "banco_dados"
        tipo: "postgresql"
        timeout: 5
      - nome: "redis"
        tipo: "redis"
        timeout: 3
      - nome: "disco"
        tipo: "filesystem"
        limite: 90

notificacoes:
  email:
    habilitado: true
    smtp:
      host: ${SMTP_HOST}
      porta: ${SMTP_PORT:-587}
      usuario: ${SMTP_USER}
      senha: ${SMTP_PASSWORD}
      tls: true
    remetente:
      nome: "Sistema ToolMi"
      email: "<EMAIL>"
  
  slack:
    habilitado: false
    webhook_url: ${SLACK_WEBHOOK_URL}
    canal: "#alertas"
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NODE_ENV: production
        BUILD_VERSION: ${BUILD_VERSION:-latest}
    image: toolmi/app:${TAG:-latest}
    container_name: toolmi-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-3000}:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - toolmi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15-alpine
    container_name: toolmi-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-toolmi}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=pt_BR.UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - toolmi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-toolmi}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: toolmi-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - toolmi-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  nginx:
    image: nginx:alpine
    container_name: toolmi-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites:/etc/nginx/sites-available:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - toolmi-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  toolmi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 3. Kubernetes Deployment

```yaml
# k8s/namespace.yml
apiVersion: v1
kind: Namespace
metadata:
  name: toolmi
  labels:
    name: toolmi
    environment: production

---
# k8s/configmap.yml
apiVersion: v1
kind: ConfigMap
metadata:
  name: toolmi-config
  namespace: toolmi
data:
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  REDIS_HOST: "redis-service"
  DB_HOST: "postgres-service"
  app.yml: |
    aplicacao:
      nome: "ToolMi Kubernetes"
      versao: "2.1.0"
      ambiente: producao
    servidor:
      porta: 3000
      workers: 4
    recursos:
      autenticacao:
        habilitado: true
        expiracao: 86400

---
# k8s/secret.yml
apiVersion: v1
kind: Secret
metadata:
  name: toolmi-secrets
  namespace: toolmi
type: Opaque
data:
  DB_PASSWORD: cG9zdGdyZXNfc2VjcmV0 # base64 encoded
  JWT_SECRET: and0X3NlY3JldF9rZXk= # base64 encoded
  REDIS_PASSWORD: cmVkaXNfc2VjcmV0 # base64 encoded

---
# k8s/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  namespace: toolmi
  labels:
    app: toolmi
    component: backend
    version: v2.1.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: toolmi
      component: backend
  template:
    metadata:
      labels:
        app: toolmi
        component: backend
        version: v2.1.0
    spec:
      containers:
        - name: app
          image: toolmi/app:v2.1.0
          ports:
            - containerPort: 3000
              name: http
              protocol: TCP
          env:
            - name: NODE_ENV
              valueFrom:
                configMapKeyRef:
                  name: toolmi-config
                  key: NODE_ENV
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: toolmi-config
                  key: DB_HOST
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: toolmi-secrets
                  key: DB_PASSWORD
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: toolmi-secrets
                  key: JWT_SECRET
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          volumeMounts:
            - name: config-volume
              mountPath: /app/config
              readOnly: true
            - name: logs-volume
              mountPath: /app/logs
      volumes:
        - name: config-volume
          configMap:
            name: toolmi-config
        - name: logs-volume
          emptyDir: {}

---
# k8s/service.yml
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
  namespace: toolmi
  labels:
    app: toolmi
    component: backend
spec:
  selector:
    app: toolmi
    component: backend
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP

---
# k8s/ingress.yml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: toolmi-ingress
  namespace: toolmi
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
    - hosts:
        - toolmi.com
        - www.toolmi.com
      secretName: toolmi-tls
  rules:
    - host: toolmi.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: toolmi-service
                port:
                  number: 80
    - host: www.toolmi.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: toolmi-service
                port:
                  number: 80
```

## 🔧 Detalhes Técnicos

### Sintaxe YAML

**Estrutura Básica:**
- **Indentação**: Usa espaços (não tabs) para definir hierarquia
- **Listas**: Indicadas com hífen `-`
- **Objetos**: Pares chave-valor separados por dois pontos `:`
- **Comentários**: Iniciados com `#`

**Tipos de Dados:**
- **String**: `nome: "João Silva"` ou `nome: João Silva`
- **Number**: `idade: 30` ou `preco: 19.99`
- **Boolean**: `ativo: true` ou `ativo: false`
- **Null**: `valor: null` ou `valor: ~`
- **Array**: `[item1, item2]` ou formato de lista
- **Object**: Estrutura aninhada com indentação

### Strings Especiais

**Strings Multilinha:**
```yaml
# Preserva quebras de linha
descricao: |
  Esta é uma descrição
  que mantém as quebras
  de linha originais

# Dobra em uma linha
resumo: >
  Este texto será
  dobrado em uma
  única linha
```

**Strings com Escape:**
```yaml
caminho_windows: "C:\\Users\\<USER>\\Documents"
regex: "\\d+\\.\\d+"
json_string: '{"nome": "João", "idade": 30}'
```

### Âncoras e Referências

```yaml
# Definir âncora
padrao: &configuracao_padrao
  timeout: 30
  retries: 3
  debug: false

# Usar referência
desenvolvimento:
  <<: *configuracao_padrao
  debug: true

producao:
  <<: *configuracao_padrao
  timeout: 60
```

## 💡 Dicas de Uso

- **Indentação Consistente** : Use sempre 2 ou 4 espaços, nunca tabs
- **Aspas** : Use aspas quando necessário para evitar interpretação incorreta
- **Validação** : Sempre valide YAML antes de usar em produção
- **Comentários** : Use comentários para documentar configurações complexas

## ⚠️ Notas Importantes

- **Sensibilidade à Indentação** : YAML é extremamente sensível à indentação
- **Tabs vs Espaços** : Nunca misture tabs e espaços
- **Caracteres Especiais** : Alguns caracteres podem precisar de aspas
- **Encoding** : Use UTF-8 para suporte completo a caracteres especiais

## 🚀 Como Usar

1. **Entrada YAML** : Cole o conteúdo YAML na área de entrada
2. **Validação Automática** : Erros de sintaxe são detectados automaticamente
3. **Formatação** : Clique em "Formatar" para embelezar o YAML
4. **Verificação dos Resultados** : O YAML formatado aparece na área de saída
5. **Uso de Cópia** : Clique em "Copiar" para usar o YAML formatado

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e segurança dos seus arquivos de configuração.
