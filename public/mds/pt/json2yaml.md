# Ferramenta de Conversão JSON ⇄ YAML

Esta ferramenta fornece conversão bidirecional entre formatos JSON e YAML, dois formatos de dados amplamente usados no desenvolvimento moderno. JSON é ideal para APIs e comunicação web, enquanto YAML é preferido para arquivos de configuração devido à sua legibilidade humana.

## ✨ Características Principais

- 🔄 **Conversão Bidirecional** : Conversão perfeita entre JSON e YAML
- 🌐 **Suporte Unicode** : Compatível com caracteres portugueses e outros caracteres especiais
- ✅ **Validação de Formato** : Detecção automática de erros de sintaxe
- 📋 **Cópia com Um Clique** : Resultados podem ser copiados diretamente
- 🎨 **Formatação Bonita** : Saída bem formatada e legível

## 📖 Exemplos de Uso

### Conversão JSON → YAML

**JSON de Entrada:**
```json
{
  "aplicacao": {
    "nome": "ToolMi",
    "versao": "1.0.0",
    "descricao": "Ferramentas online de nova geração"
  },
  "servidor": {
    "host": "localhost",
    "porta": 3000,
    "ssl": false
  },
  "recursos": [
    "codificacao-base64",
    "geracao-uuid",
    "formatacao-json"
  ]
}
```

**YAML Convertido:**
```yaml
aplicacao:
  nome: ToolMi
  versao: 1.0.0
  descricao: Ferramentas online de nova geração
servidor:
  host: localhost
  porta: 3000
  ssl: false
recursos:
  - codificacao-base64
  - geracao-uuid
  - formatacao-json
```

### Conversão YAML → JSON

**YAML de Entrada:**
```yaml
banco_dados:
  tipo: postgresql
  host: db.toolmi.com
  porta: 5432
  credenciais:
    usuario: admin
    senha: senha_segura
configuracao:
  debug: true
  log_nivel: info
  recursos_habilitados:
    - autenticacao
    - cache
    - monitoramento
```

**JSON Convertido:**
```json
{
  "banco_dados": {
    "tipo": "postgresql",
    "host": "db.toolmi.com",
    "porta": 5432,
    "credenciais": {
      "usuario": "admin",
      "senha": "senha_segura"
    }
  },
  "configuracao": {
    "debug": true,
    "log_nivel": "info",
    "recursos_habilitados": [
      "autenticacao",
      "cache",
      "monitoramento"
    ]
  }
}
```

## 🎯 Cenários de Aplicação

### 1. Arquivos de Configuração

```yaml
# config/aplicacao.yml
aplicacao:
  nome: "Sistema ToolMi"
  ambiente: producao
  debug: false
  
servidor:
  host: "0.0.0.0"
  porta: 8080
  workers: 4
  timeout: 30

banco_dados:
  padrao: postgresql
  conexoes:
    postgresql:
      driver: postgresql
      host: localhost
      porta: 5432
      database: toolmi_db
      usuario: postgres
      senha: ${DB_PASSWORD}
      pool:
        min: 2
        max: 10

cache:
  driver: redis
  host: localhost
  porta: 6379
  database: 0
  ttl: 3600

logging:
  nivel: info
  formato: json
  saidas:
    - console
    - arquivo
  arquivo:
    caminho: /var/log/toolmi.log
    rotacao: diaria
    retencao: 30

recursos:
  autenticacao:
    habilitado: true
    provedor: jwt
    expiracao: 86400
  
  rate_limiting:
    habilitado: true
    limite: 1000
    janela: 3600
  
  cors:
    habilitado: true
    origens:
      - "https://toolmi.com"
      - "https://www.toolmi.com"
    metodos:
      - GET
      - POST
      - PUT
      - DELETE
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: toolmi
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: senha_segura
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    driver: bridge
```

### 3. CI/CD Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy ToolMi

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: toolmi/app

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout código
        uses: actions/checkout@v3
      
      - name: Configurar Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Instalar dependências
        run: npm ci
      
      - name: Executar testes
        run: npm test
      
      - name: Executar linting
        run: npm run lint
      
      - name: Verificar cobertura
        run: npm run coverage

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout código
        uses: actions/checkout@v3
      
      - name: Login no Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extrair metadados
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
      
      - name: Build e push da imagem
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: Deploy para produção
        run: |
          echo "Deploying to production..."
          # Comandos de deploy aqui
```

### 4. Kubernetes Manifests

```yaml
# k8s/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  namespace: toolmi
  labels:
    app: toolmi
    component: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: toolmi
      component: backend
  template:
    metadata:
      labels:
        app: toolmi
        component: backend
    spec:
      containers:
        - name: app
          image: toolmi/app:latest
          ports:
            - containerPort: 3000
              name: http
          env:
            - name: NODE_ENV
              value: "production"
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: toolmi-secrets
                  key: db-host
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: toolmi-secrets
                  key: db-password
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
  namespace: toolmi
spec:
  selector:
    app: toolmi
    component: backend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: toolmi-ingress
  namespace: toolmi
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
    - hosts:
        - toolmi.com
        - www.toolmi.com
      secretName: toolmi-tls
  rules:
    - host: toolmi.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: toolmi-service
                port:
                  number: 80
    - host: www.toolmi.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: toolmi-service
                port:
                  number: 80
```

## 🔧 Detalhes Técnicos

### Diferenças de Sintaxe

**JSON:**
- Usa chaves `{}` para objetos
- Usa colchetes `[]` para arrays
- Requer aspas duplas para strings
- Não permite comentários
- Mais verboso mas amplamente suportado

**YAML:**
- Usa indentação para estrutura
- Suporta comentários com `#`
- Strings podem ser sem aspas
- Mais legível para humanos
- Sensível à indentação

### Tipos de Dados Suportados

**Tipos Básicos:**
- **String**: `"texto"` ou `texto`
- **Number**: `123`, `45.67`
- **Boolean**: `true`, `false`
- **Null**: `null` ou `~`
- **Array**: `[item1, item2]` ou lista com `-`
- **Object**: `{chave: valor}` ou estrutura indentada

### Casos Especiais

**Strings Multilinha em YAML:**
```yaml
descricao: |
  Esta é uma string
  que abrange múltiplas
  linhas preservando quebras

resumo: >
  Esta é uma string
  que será dobrada
  em uma única linha
```

**Âncoras e Referências:**
```yaml
padrao: &padrao
  timeout: 30
  retries: 3

servicos:
  api:
    <<: *padrao
    porta: 3000
  
  worker:
    <<: *padrao
    porta: 3001
```

## 💡 Dicas de Uso

- **Indentação YAML** : Use espaços, não tabs, para indentação
- **Validação** : Sempre valide a sintaxe após conversão
- **Comentários** : Use YAML para configurações que precisam de documentação
- **Performance** : JSON é mais rápido para parsing em aplicações

## ⚠️ Notas Importantes

- **Sensibilidade à Indentação** : YAML é muito sensível à indentação correta
- **Tipos de Dados** : Alguns tipos YAML podem não ter equivalente direto em JSON
- **Comentários** : Comentários YAML são perdidos na conversão para JSON
- **Escape de Caracteres** : Caracteres especiais podem precisar de escape

## 🚀 Como Usar

1. **Entrada de Dados** : Cole o conteúdo JSON ou YAML na área de entrada
2. **Seleção de Conversão** : Escolha a direção da conversão (JSON→YAML ou YAML→JSON)
3. **Verificação dos Resultados** : O resultado convertido aparece na área de saída
4. **Validação** : Erros de sintaxe são destacados automaticamente
5. **Uso de Cópia** : Clique em "Copiar" para copiar o resultado

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e segurança dos seus arquivos de configuração.
