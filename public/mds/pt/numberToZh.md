# Conversor de Números para Chinês

Esta ferramenta converte números arábicos para caracteres chineses em diferentes formatos. Suporta números simples, números financeiros (anti-fraude) e números tradicionais, essencial para documentos oficiais, contratos e aplicações que requerem numeração chinesa.

## ✨ Características Principais

- 🔢 **Múltiplos Formatos** : Números simples, financeiros e tradicionais
- 💰 **Formato Anti-Fraude** : Caracteres especiais para documentos financeiros
- 📋 **Cópia com Um Clique** : Resultados podem ser copiados diretamente
- ✅ **Validação de Entrada** : Verifica números válidos antes da conversão
- 🌐 **Suporte Completo** : Números inteiros e decimais

## 📖 Exemplos de Uso

### Conversão de Números Básicos

**Número:** `12345`

**Formatos Disponíveis:**
- **Simples**: 一万二千三百四十五
- **Financeiro**: 壹万贰仟叁佰肆拾伍
- **Tradicional**: 一萬二千三百四十五

### Conversão de Valores Monetários

**Número:** `1234.56`

**Formatos Disponíveis:**
- **Simples**: 一千二百三十四点五六
- **Financeiro**: 壹仟贰佰叁拾肆元伍角陆分
- **Com Moeda**: 壹仟贰佰叁拾肆元伍角陆分整

### Números Grandes

**Número:** `987654321`

**Formatos Disponíveis:**
- **Simples**: 九亿八千七百六十五万四千三百二十一
- **Financeiro**: 玖亿捌仟柒佰陆拾伍万肆仟叁佰贰拾壹
- **Tradicional**: 九億八千七百六十五萬四千三百二十一

## 🎯 Cenários de Aplicação

### 1. Sistema de Faturamento

```javascript
// Conversor de números para chinês
class ConversorNumeroChines {
  constructor() {
    // Dígitos simples
    this.digitosSimples = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    
    // Dígitos financeiros (anti-fraude)
    this.digitosFinanceiros = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    
    // Unidades
    this.unidades = ['', '十', '百', '千'];
    this.unidadesFinanceiras = ['', '拾', '佰', '仟'];
    
    // Unidades grandes
    this.unidadesGrandes = ['', '万', '亿', '兆'];
    
    // Unidades monetárias
    this.unidadesMonetarias = {
      yuan: '元',
      jiao: '角',
      fen: '分',
      zheng: '整'
    };
  }

  // Converter número para chinês simples
  paraChinesSimples(numero) {
    if (numero === 0) return '零';
    
    const numeroStr = Math.abs(numero).toString();
    const [parteInteira, parteDecimal] = numeroStr.split('.');
    
    let resultado = '';
    
    // Adicionar sinal negativo se necessário
    if (numero < 0) {
      resultado += '负';
    }
    
    // Converter parte inteira
    resultado += this.converterParteInteira(parteInteira, 'simples');
    
    // Converter parte decimal se existir
    if (parteDecimal) {
      resultado += '点';
      for (const digito of parteDecimal) {
        resultado += this.digitosSimples[parseInt(digito)];
      }
    }
    
    return resultado;
  }

  // Converter número para chinês financeiro
  paraChinesFinanceiro(numero, incluirMoeda = false) {
    if (numero === 0) return incluirMoeda ? '零元整' : '零';
    
    const numeroStr = Math.abs(numero).toFixed(2);
    const [parteInteira, parteDecimal] = numeroStr.split('.');
    
    let resultado = '';
    
    // Adicionar sinal negativo se necessário
    if (numero < 0) {
      resultado += '负';
    }
    
    // Converter parte inteira
    resultado += this.converterParteInteira(parteInteira, 'financeiro');
    
    if (incluirMoeda) {
      resultado += this.unidadesMonetarias.yuan;
      
      // Converter centavos
      const jiao = parseInt(parteDecimal[0]);
      const fen = parseInt(parteDecimal[1]);
      
      if (jiao > 0) {
        resultado += this.digitosFinanceiros[jiao] + this.unidadesMonetarias.jiao;
      }
      
      if (fen > 0) {
        resultado += this.digitosFinanceiros[fen] + this.unidadesMonetarias.fen;
      }
      
      // Adicionar "整" se não há centavos
      if (jiao === 0 && fen === 0) {
        resultado += this.unidadesMonetarias.zheng;
      }
    } else {
      // Converter parte decimal normalmente
      if (parteDecimal && parteDecimal !== '00') {
        resultado += '点';
        for (const digito of parteDecimal) {
          resultado += this.digitosFinanceiros[parseInt(digito)];
        }
      }
    }
    
    return resultado;
  }

  // Converter parte inteira
  converterParteInteira(numeroStr, tipo) {
    const digitos = tipo === 'financeiro' ? this.digitosFinanceiros : this.digitosSimples;
    const unidades = tipo === 'financeiro' ? this.unidadesFinanceiras : this.unidades;
    
    // Dividir em grupos de 4 dígitos
    const grupos = [];
    for (let i = numeroStr.length; i > 0; i -= 4) {
      const inicio = Math.max(0, i - 4);
      grupos.unshift(numeroStr.slice(inicio, i));
    }
    
    let resultado = '';
    
    for (let i = 0; i < grupos.length; i++) {
      const grupo = grupos[i];
      const unidadeGrande = this.unidadesGrandes[grupos.length - 1 - i];
      
      const grupoConvertido = this.converterGrupo(grupo, digitos, unidades);
      
      if (grupoConvertido && grupoConvertido !== '零') {
        // Adicionar zero se necessário
        if (resultado && grupo.length < 4 && parseInt(grupo) < 1000) {
          resultado += '零';
        }
        
        resultado += grupoConvertido + unidadeGrande;
      }
    }
    
    return resultado || '零';
  }

  // Converter grupo de até 4 dígitos
  converterGrupo(grupo, digitos, unidades) {
    const numero = parseInt(grupo);
    if (numero === 0) return '';
    
    let resultado = '';
    const grupoStr = grupo.padStart(4, '0');
    
    for (let i = 0; i < grupoStr.length; i++) {
      const digito = parseInt(grupoStr[i]);
      const posicao = grupoStr.length - 1 - i;
      
      if (digito > 0) {
        // Adicionar zero se necessário
        if (resultado && resultado[resultado.length - 1] !== '零' && this.precisaZero(grupoStr, i)) {
          resultado += '零';
        }
        
        resultado += digitos[digito];
        
        if (posicao > 0) {
          resultado += unidades[posicao];
        }
      }
    }
    
    return resultado;
  }

  // Verificar se precisa adicionar zero
  precisaZero(grupoStr, posicaoAtual) {
    for (let i = posicaoAtual - 1; i >= 0; i--) {
      if (parseInt(grupoStr[i]) > 0) {
        return false;
      }
    }
    return true;
  }

  // Converter data para chinês
  dataParaChines(data, formato = 'simples') {
    const ano = data.getFullYear();
    const mes = data.getMonth() + 1;
    const dia = data.getDate();
    
    const conversor = formato === 'financeiro' ? 
      this.paraChinesFinanceiro.bind(this) : 
      this.paraChinesSimples.bind(this);
    
    return `${conversor(ano)}年${conversor(mes)}月${conversor(dia)}日`;
  }

  // Converter tempo para chinês
  tempoParaChines(horas, minutos, segundos = null, formato = 'simples') {
    const conversor = formato === 'financeiro' ? 
      this.paraChinesFinanceiro.bind(this) : 
      this.paraChinesSimples.bind(this);
    
    let resultado = `${conversor(horas)}时${conversor(minutos)}分`;
    
    if (segundos !== null) {
      resultado += `${conversor(segundos)}秒`;
    }
    
    return resultado;
  }

  // Validar entrada
  validarNumero(entrada) {
    const numero = parseFloat(entrada);
    
    if (isNaN(numero)) {
      return { valido: false, erro: 'Não é um número válido' };
    }
    
    if (Math.abs(numero) >= 1e16) {
      return { valido: false, erro: 'Número muito grande' };
    }
    
    return { valido: true, numero };
  }
}

// Exemplo de uso em sistema de faturamento
class SistemaFaturamento {
  constructor() {
    this.conversor = new ConversorNumeroChines();
  }

  gerarFatura(dados) {
    const validacao = this.conversor.validarNumero(dados.valor);
    
    if (!validacao.valido) {
      throw new Error(`Valor inválido: ${validacao.erro}`);
    }

    const valor = validacao.numero;
    const valorPorExtenso = this.conversor.paraChinesFinanceiro(valor, true);
    const dataEmissao = this.conversor.dataParaChines(new Date(), 'financeiro');

    return {
      numero: dados.numeroFatura,
      cliente: dados.cliente,
      valor: valor,
      valorPorExtenso: valorPorExtenso,
      dataEmissao: dataEmissao,
      descricao: dados.descricao
    };
  }

  formatarFatura(fatura) {
    return `
发票号码：${fatura.numero}
客户：${fatura.cliente}
金额：￥${fatura.valor.toFixed(2)}
金额大写：${fatura.valorPorExtenso}
开票日期：${fatura.dataEmissao}
描述：${fatura.descricao}
    `.trim();
  }
}

// Exemplo de uso
const sistema = new SistemaFaturamento();

const dadosFatura = {
  numeroFatura: 'INV-2024-001',
  cliente: '北京科技有限公司',
  valor: 12345.67,
  descricao: '软件开发服务费'
};

const fatura = sistema.gerarFatura(dadosFatura);
console.log(sistema.formatarFatura(fatura));
```

### 2. Sistema de Contratos

```javascript
// Gerador de contratos com números em chinês
class GeradorContratos {
  constructor() {
    this.conversor = new ConversorNumeroChines();
    this.templates = new Map();
    this.inicializarTemplates();
  }

  inicializarTemplates() {
    // Template de contrato de compra e venda
    this.templates.set('compra_venda', `
合同编号：{numeroContrato}
甲方：{parteA}
乙方：{parteB}

根据《中华人民共和国合同法》及相关法律法规，甲乙双方经友好协商，就以下事项达成一致：

一、标的物及价格
商品名称：{nomeProduto}
数量：{quantidadePorExtenso}
单价：人民币{precoUnitarioPorExtenso}
总价：人民币{precoTotalPorExtenso}

二、交付时间
交付日期：{dataEntregaPorExtenso}

三、付款方式
付款期限：{prazosPagamento}

本合同一式{numeroViasPorExtenso}份，甲乙双方各执{viasPartesPorExtenso}份。

甲方签字：_________________ 日期：{dataAssinaturaPorExtenso}
乙方签字：_________________ 日期：{dataAssinaturaPorExtenso}
    `);

    // Template de contrato de trabalho
    this.templates.set('trabalho', `
劳动合同

合同编号：{numeroContrato}
甲方（用人单位）：{empresa}
乙方（劳动者）：{funcionario}

根据《中华人民共和国劳动法》，甲乙双方经平等协商一致，自愿签订本合同：

一、合同期限
合同期限：{duracaoContratoPorExtenso}
试用期：{periodoExperienciaPorExtenso}

二、工作内容和工作地点
职位：{cargo}
工作地点：{localTrabalho}

三、工作时间和休息休假
每周工作{horasSemanaisPorExtenso}小时
每日工作{horasDiariasPorExtenso}小时

四、劳动报酬
月工资：人民币{salarioPorExtenso}
年终奖金：人民币{bonusPorExtenso}

本合同自{dataInicioPorExtenso}起生效。

甲方（盖章）：_________________ 
乙方（签字）：_________________ 
签订日期：{dataAssinaturaPorExtenso}
    `);
  }

  gerarContrato(tipo, dados) {
    const template = this.templates.get(tipo);
    if (!template) {
      throw new Error(`Tipo de contrato não encontrado: ${tipo}`);
    }

    // Processar dados e converter números
    const dadosProcessados = this.processarDados(dados);
    
    // Substituir placeholders
    let contrato = template;
    for (const [chave, valor] of Object.entries(dadosProcessados)) {
      const placeholder = `{${chave}}`;
      contrato = contrato.replace(new RegExp(placeholder, 'g'), valor);
    }

    return contrato;
  }

  processarDados(dados) {
    const processados = { ...dados };

    // Converter números para chinês financeiro
    const camposNumericos = [
      'quantidade', 'precoUnitario', 'precoTotal', 'salario', 'bonus',
      'numeroVias', 'viasPartes', 'duracaoContrato', 'periodoExperiencia',
      'horasSemanais', 'horasDiarias'
    ];

    for (const campo of camposNumericos) {
      if (dados[campo] !== undefined) {
        const sufixo = campo.includes('preco') || campo.includes('salario') || campo.includes('bonus') ? 
          'PorExtenso' : 'PorExtenso';
        
        if (campo.includes('preco') || campo.includes('salario') || campo.includes('bonus')) {
          processados[campo + 'PorExtenso'] = this.conversor.paraChinesFinanceiro(dados[campo], true);
        } else {
          processados[campo + 'PorExtenso'] = this.conversor.paraChinesFinanceiro(dados[campo]);
        }
      }
    }

    // Converter datas
    const camposDatas = ['dataEntrega', 'dataAssinatura', 'dataInicio'];
    for (const campo of camposDatas) {
      if (dados[campo]) {
        const data = new Date(dados[campo]);
        processados[campo + 'PorExtenso'] = this.conversor.dataParaChines(data, 'financeiro');
      }
    }

    // Processar prazos de pagamento
    if (dados.prazos && Array.isArray(dados.prazos)) {
      const prazosTexto = dados.prazos.map((prazo, index) => {
        const parcela = this.conversor.paraChinesFinanceiro(index + 1);
        const valor = this.conversor.paraChinesFinanceiro(prazo.valor, true);
        const data = this.conversor.dataParaChines(new Date(prazo.data), 'financeiro');
        return `第${parcela}期：${valor}，付款日期：${data}`;
      }).join('；');
      
      processados.prazosPagamento = prazosTexto;
    }

    return processados;
  }

  validarDados(tipo, dados) {
    const erros = [];

    // Validações comuns
    if (!dados.numeroContrato) {
      erros.push('Número do contrato é obrigatório');
    }

    // Validações específicas por tipo
    switch (tipo) {
      case 'compra_venda':
        if (!dados.parteA || !dados.parteB) {
          erros.push('Partes do contrato são obrigatórias');
        }
        if (!dados.precoTotal || dados.precoTotal <= 0) {
          erros.push('Preço total deve ser maior que zero');
        }
        break;

      case 'trabalho':
        if (!dados.empresa || !dados.funcionario) {
          erros.push('Empresa e funcionário são obrigatórios');
        }
        if (!dados.salario || dados.salario <= 0) {
          erros.push('Salário deve ser maior que zero');
        }
        break;
    }

    return erros;
  }
}

// Exemplo de uso
const gerador = new GeradorContratos();

// Dados para contrato de compra e venda
const dadosCompraVenda = {
  numeroContrato: 'CV-2024-001',
  parteA: '北京科技有限公司',
  parteB: '上海贸易有限公司',
  nomeProduto: '笔记本电脑',
  quantidade: 50,
  precoUnitario: 5999.99,
  precoTotal: 299999.50,
  dataEntrega: '2024-03-15',
  numeroVias: 2,
  viasPartes: 1,
  dataAssinatura: '2024-01-15',
  prazos: [
    { valor: 149999.75, data: '2024-02-01' },
    { valor: 149999.75, data: '2024-03-01' }
  ]
};

// Validar e gerar contrato
const erros = gerador.validarDados('compra_venda', dadosCompraVenda);
if (erros.length > 0) {
  console.error('Erros de validação:', erros);
} else {
  const contrato = gerador.gerarContrato('compra_venda', dadosCompraVenda);
  console.log(contrato);
}
```

## 🔧 Detalhes Técnicos

### Sistemas de Numeração Chinesa

**Números Simples (简体):**
- Usado em contextos informais
- Caracteres: 零一二三四五六七八九十百千万亿

**Números Financeiros (大写):**
- Usado em documentos oficiais e financeiros
- Caracteres: 零壹贰叁肆伍陆柒捌玖拾佰仟万亿
- Previne alterações fraudulentas

**Números Tradicionais (繁体):**
- Usado em Taiwan, Hong Kong e contextos formais
- Caracteres: 零一二三四五六七八九十百千萬億

### Unidades e Agrupamento

**Unidades Básicas:**
- 十 (10)
- 百 (100) 
- 千 (1,000)
- 万 (10,000)
- 亿 (100,000,000)

**Regras de Agrupamento:**
- Números são agrupados em grupos de 4 dígitos
- Zero é usado para indicar posições vazias
- Unidades são omitidas quando apropriado

### Unidades Monetárias

**Moeda Chinesa (人民币):**
- 元 (yuan) - unidade principal
- 角 (jiao) - 1/10 yuan
- 分 (fen) - 1/100 yuan
- 整 (zheng) - indica valor exato

## 💡 Dicas de Uso

- **Contexto** : Use formato financeiro para documentos oficiais
- **Precisão** : Verifique a conversão para valores importantes
- **Cultura** : Entenda o contexto cultural dos números chineses
- **Validação** : Sempre valide a entrada antes da conversão

## ⚠️ Notas Importantes

- **Formato Financeiro** : Obrigatório em documentos legais na China
- **Precisão** : Números muito grandes podem ter limitações
- **Contexto Cultural** : Alguns números têm significados especiais
- **Validação Legal** : Consulte regulamentações locais para uso oficial

## 🚀 Como Usar

1. **Entrada de Número** : Digite o número que deseja converter
2. **Seleção de Formato** : Escolha entre simples, financeiro ou tradicional
3. **Opções Monetárias** : Selecione se deseja incluir unidades monetárias
4. **Verificação** : Revise o resultado da conversão
5. **Uso de Cópia** : Clique em "Copiar" para usar o texto chinês

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e rapidez na conversão.
