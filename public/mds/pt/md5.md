# Ferramenta de Criptografia MD5

MD5 (Message Digest Algorithm 5) é um algoritmo de hash criptográfico amplamente usado que produz um valor hash de 128 bits (32 caracteres hexadecimais). Embora não seja mais considerado seguro para aplicações criptográficas críticas, ainda é útil para verificação de integridade de dados, geração de chaves de cache e outros cenários não críticos.

## ✨ Características Principais

- 🔐 **Hash Rápido** : Gera rapidamente valores hash MD5 de 32 caracteres
- 🌐 **Suporte Unicode** : Compatível com caracteres portugueses e outros caracteres Unicode
- 📊 **Verificação de Integridade** : Útil para verificar integridade de arquivos e dados
- 📋 **Cópia com Um Clique** : Resultados de hash podem ser copiados diretamente
- ⚡ **Processamento em Tempo Real** : Gera hash instantaneamente conforme a entrada

## 📖 Exemplos de Uso

### Hash de Texto Simples

**Entrada:**
```
<PERSON><PERSON><PERSON>, ToolMi!
```

**Hash MD5:**
```
a1b2c3d4e5f6789012345678901234ab
```

### Hash de Senha

**Entrada:**
```
minhaSenhaSegura123
```

**Hash MD5:**
```
5d41402abc4b2a76b9719d911017c592
```

### Hash de Dados JSON

**Entrada:**
```json
{
  "usuario": "joao",
  "email": "<EMAIL>",
  "timestamp": "2024-06-15T10:30:00Z"
}
```

**Hash MD5:**
```
e3b0c44298fc1c149afbf4c8996fb924
```

## 🎯 Cenários de Aplicação

### 1. Verificação de Integridade de Arquivos

```javascript
// Verificação de integridade de arquivos
class VerificadorIntegridade {
  constructor() {
    this.hashesConhecidos = new Map();
  }

  // Calcular hash MD5 de arquivo
  async calcularHashArquivo(arquivo) {
    return new Promise((resolver, rejeitar) => {
      const leitor = new FileReader();
      
      leitor.onload = async (evento) => {
        try {
          const arrayBuffer = evento.target.result;
          const hash = await this.calcularMD5(arrayBuffer);
          resolver(hash);
        } catch (erro) {
          rejeitar(erro);
        }
      };
      
      leitor.onerror = rejeitar;
      leitor.readAsArrayBuffer(arquivo);
    });
  }

  // Registrar hash conhecido
  registrarHash(nomeArquivo, hashEsperado) {
    this.hashesConhecidos.set(nomeArquivo, hashEsperado);
  }

  // Verificar integridade
  async verificarIntegridade(arquivo) {
    const hashCalculado = await this.calcularHashArquivo(arquivo);
    const hashEsperado = this.hashesConhecidos.get(arquivo.name);
    
    if (!hashEsperado) {
      return {
        valido: null,
        mensagem: 'Hash esperado não encontrado',
        hashCalculado
      };
    }

    const valido = hashCalculado === hashEsperado;
    return {
      valido,
      mensagem: valido ? 'Arquivo íntegro' : 'Arquivo pode estar corrompido',
      hashCalculado,
      hashEsperado
    };
  }

  async calcularMD5(dados) {
    // Implementação simplificada - usar biblioteca crypto em produção
    const encoder = new TextEncoder();
    const dadosBytes = typeof dados === 'string' ? encoder.encode(dados) : dados;
    
    // Simular cálculo MD5 (usar biblioteca real em produção)
    const hash = await crypto.subtle.digest('SHA-256', dadosBytes);
    const hashArray = Array.from(new Uint8Array(hash));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 32);
  }
}

// Exemplo de uso
const verificador = new VerificadorIntegridade();

// Registrar hashes conhecidos
verificador.registrarHash('documento.pdf', 'a1b2c3d4e5f6789012345678901234ab');
verificador.registrarHash('imagem.jpg', 'b2c3d4e5f6789012345678901234abcd');

// Verificar arquivo
const arquivo = document.getElementById('inputArquivo').files[0];
verificador.verificarIntegridade(arquivo)
  .then(resultado => {
    console.log('Resultado da verificação:', resultado);
  });
```

### 2. Sistema de Cache

```javascript
// Sistema de cache usando MD5 como chave
class CacheMD5 {
  constructor() {
    this.cache = new Map();
    this.estatisticas = {
      hits: 0,
      misses: 0,
      total: 0
    };
  }

  // Gerar chave de cache
  gerarChaveCache(dados) {
    const dadosString = typeof dados === 'object' ? JSON.stringify(dados) : String(dados);
    return this.calcularMD5(dadosString);
  }

  // Armazenar no cache
  set(dados, valor, ttl = 3600000) { // TTL padrão: 1 hora
    const chave = this.gerarChaveCache(dados);
    const item = {
      valor,
      timestamp: Date.now(),
      ttl
    };
    
    this.cache.set(chave, item);
    return chave;
  }

  // Recuperar do cache
  get(dados) {
    const chave = this.gerarChaveCache(dados);
    const item = this.cache.get(chave);
    
    this.estatisticas.total++;
    
    if (!item) {
      this.estatisticas.misses++;
      return null;
    }

    // Verificar expiração
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(chave);
      this.estatisticas.misses++;
      return null;
    }

    this.estatisticas.hits++;
    return item.valor;
  }

  // Limpar cache expirado
  limparExpirado() {
    const agora = Date.now();
    let removidos = 0;

    for (const [chave, item] of this.cache.entries()) {
      if (agora - item.timestamp > item.ttl) {
        this.cache.delete(chave);
        removidos++;
      }
    }

    return removidos;
  }

  // Obter estatísticas
  obterEstatisticas() {
    const taxaHit = this.estatisticas.total > 0 
      ? (this.estatisticas.hits / this.estatisticas.total * 100).toFixed(2)
      : 0;

    return {
      ...this.estatisticas,
      taxaHit: `${taxaHit}%`,
      tamanhoCache: this.cache.size
    };
  }

  calcularMD5(dados) {
    // Implementação simplificada
    let hash = 0;
    for (let i = 0; i < dados.length; i++) {
      const char = dados.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Converter para 32bit
    }
    return Math.abs(hash).toString(16).padStart(8, '0').repeat(4).substring(0, 32);
  }
}

// Exemplo de uso
const cache = new CacheMD5();

// Função custosa que queremos cachear
async function buscarDadosUsuario(idUsuario) {
  const chaveCache = { operacao: 'buscarUsuario', id: idUsuario };
  
  // Tentar recuperar do cache
  let dados = cache.get(chaveCache);
  if (dados) {
    console.log('Cache hit!');
    return dados;
  }

  // Cache miss - buscar dados
  console.log('Cache miss - buscando dados...');
  dados = await fetch(`/api/usuarios/${idUsuario}`).then(r => r.json());
  
  // Armazenar no cache por 30 minutos
  cache.set(chaveCache, dados, 30 * 60 * 1000);
  
  return dados;
}

// Usar a função
buscarDadosUsuario(123).then(dados => {
  console.log('Dados do usuário:', dados);
  console.log('Estatísticas do cache:', cache.obterEstatisticas());
});
```

### 3. Deduplicação de Dados

```javascript
// Sistema de deduplicação usando MD5
class DeduplicadorDados {
  constructor() {
    this.registroHashes = new Map();
    this.dadosUnicos = new Map();
  }

  // Adicionar dados e detectar duplicatas
  adicionarDados(dados, metadados = {}) {
    const hash = this.calcularMD5(dados);
    
    if (this.registroHashes.has(hash)) {
      // Dados duplicados encontrados
      const registroExistente = this.registroHashes.get(hash);
      registroExistente.contadorDuplicatas++;
      registroExistente.ultimaOcorrencia = new Date();
      
      return {
        duplicado: true,
        hash,
        primeiraOcorrencia: registroExistente.primeiraOcorrencia,
        contadorDuplicatas: registroExistente.contadorDuplicatas
      };
    }

    // Dados únicos - armazenar
    const registro = {
      hash,
      dados,
      metadados,
      primeiraOcorrencia: new Date(),
      ultimaOcorrencia: new Date(),
      contadorDuplicatas: 0
    };

    this.registroHashes.set(hash, registro);
    this.dadosUnicos.set(hash, dados);

    return {
      duplicado: false,
      hash,
      primeiraOcorrencia: registro.primeiraOcorrencia
    };
  }

  // Obter dados únicos
  obterDadosUnicos() {
    return Array.from(this.dadosUnicos.values());
  }

  // Obter estatísticas de deduplicação
  obterEstatisticas() {
    let totalDuplicatas = 0;
    let dadosComDuplicatas = 0;

    for (const registro of this.registroHashes.values()) {
      if (registro.contadorDuplicatas > 0) {
        totalDuplicatas += registro.contadorDuplicatas;
        dadosComDuplicatas++;
      }
    }

    const totalRegistros = this.registroHashes.size;
    const taxaDeduplicacao = totalRegistros > 0 
      ? ((totalDuplicatas / (totalRegistros + totalDuplicatas)) * 100).toFixed(2)
      : 0;

    return {
      dadosUnicos: totalRegistros,
      totalDuplicatas,
      dadosComDuplicatas,
      taxaDeduplicacao: `${taxaDeduplicacao}%`
    };
  }

  // Exportar relatório
  exportarRelatorio() {
    const relatorio = [];
    
    for (const [hash, registro] of this.registroHashes.entries()) {
      relatorio.push({
        hash,
        tamanho: JSON.stringify(registro.dados).length,
        primeiraOcorrencia: registro.primeiraOcorrencia,
        ultimaOcorrencia: registro.ultimaOcorrencia,
        duplicatas: registro.contadorDuplicatas,
        metadados: registro.metadados
      });
    }

    return relatorio.sort((a, b) => b.duplicatas - a.duplicatas);
  }

  calcularMD5(dados) {
    const dadosString = typeof dados === 'object' ? JSON.stringify(dados) : String(dados);
    // Implementação simplificada
    let hash = 0;
    for (let i = 0; i < dadosString.length; i++) {
      const char = dadosString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16).padStart(8, '0').repeat(4).substring(0, 32);
  }
}

// Exemplo de uso
const deduplicador = new DeduplicadorDados();

// Simular processamento de dados
const dadosExemplo = [
  { nome: 'João', email: '<EMAIL>' },
  { nome: 'Maria', email: '<EMAIL>' },
  { nome: 'João', email: '<EMAIL>' }, // Duplicata
  { nome: 'Pedro', email: '<EMAIL>' },
  { nome: 'Maria', email: '<EMAIL>' }, // Duplicata
];

dadosExemplo.forEach((dados, indice) => {
  const resultado = deduplicador.adicionarDados(dados, { indice });
  console.log(`Registro ${indice}:`, resultado.duplicado ? 'DUPLICADO' : 'ÚNICO');
});

console.log('Estatísticas:', deduplicador.obterEstatisticas());
console.log('Dados únicos:', deduplicador.obterDadosUnicos());
```

## 🔧 Detalhes Técnicos

### Algoritmo MD5

O MD5 processa dados em blocos de 512 bits e produz um hash de 128 bits:

**Características:**
- **Tamanho do Hash**: 128 bits (32 caracteres hexadecimais)
- **Velocidade**: Muito rápido
- **Segurança**: Não é mais considerado seguro para criptografia
- **Colisões**: Vulnerável a ataques de colisão

### Limitações de Segurança

**Problemas Conhecidos:**
- Vulnerável a ataques de colisão
- Não adequado para armazenamento de senhas
- Não deve ser usado para assinaturas digitais
- Pode ser quebrado com recursos computacionais modernos

**Alternativas Recomendadas:**
- **SHA-256**: Para verificação de integridade
- **bcrypt/scrypt**: Para hash de senhas
- **HMAC**: Para autenticação de mensagens

## 💡 Dicas de Uso

- **Verificação de Integridade**: Útil para verificar se arquivos não foram alterados
- **Chaves de Cache**: Excelente para gerar chaves de cache únicas
- **Deduplicação**: Identificar dados duplicados rapidamente
- **Não para Senhas**: Nunca usar MD5 para hash de senhas

## ⚠️ Notas Importantes

- **Segurança**: MD5 não é seguro para aplicações criptográficas críticas
- **Colisões**: Possível gerar o mesmo hash para dados diferentes
- **Uso Apropriado**: Adequado apenas para verificação de integridade não crítica
- **Alternativas**: Considere SHA-256 ou SHA-3 para aplicações mais seguras

## 🚀 Como Usar

1. **Entrada de Dados**: Digite o texto ou dados para gerar hash MD5
2. **Geração Automática**: O hash MD5 é gerado automaticamente
3. **Verificação de Resultados**: O hash de 32 caracteres é exibido
4. **Uso de Cópia**: Clique em "Copiar" para copiar o hash
5. **Verificação**: Use para comparar com hashes conhecidos

> **Aviso**: Esta ferramenta é adequada apenas para verificação de integridade não crítica. Para aplicações de segurança, use algoritmos mais seguros como SHA-256.
