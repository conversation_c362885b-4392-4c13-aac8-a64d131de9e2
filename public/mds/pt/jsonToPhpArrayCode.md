# Conversor JSON para Código de Array PHP

Esta ferramenta converte dados JSON em código de array PHP válido. Essencial para desenvolvedores PHP que precisam migrar dados JSON para estruturas de array nativas do PHP, facilitando a integração e manipulação de dados em aplicações PHP.

## ✨ Características Principais

- 🔄 **Conversão Direta** : JSON para código de array PHP válido
- 🎨 **Formatação Bonita** : Código PHP bem formatado e indentado
- 📋 **Cópia com Um Clique** : Código gerado pode ser copiado diretamente
- ✅ **Validação JSON** : Verifica a validade do JSON antes da conversão
- 🌐 **Suporte Unicode** : Compatível com caracteres especiais e acentos

## 📖 Exemplos de Uso

### Conversão de Objeto Simples

**JSON de Entrada:**
```json
{
  "nome": "<PERSON>",
  "idade": 30,
  "email": "<EMAIL>",
  "ativo": true
}
```

**Código PHP Gerado:**
```php
<?php
$array = [
    'nome' => '<PERSON>',
    'idade' => 30,
    'email' => '<EMAIL>',
    'ativo' => true
];
?>
```

### Conversão de Array Complexo

**JSON de Entrada:**
```json
{
  "usuarios": [
    {
      "id": 1,
      "nome": "Maria Santos",
      "perfil": {
        "bio": "Desenvolvedora PHP",
        "skills": ["PHP", "MySQL", "Laravel"]
      }
    },
    {
      "id": 2,
      "nome": "Pedro Costa",
      "perfil": {
        "bio": "Designer UX/UI",
        "skills": ["Figma", "Photoshop", "CSS"]
      }
    }
  ],
  "configuracao": {
    "tema": "escuro",
    "idioma": "pt-BR"
  }
}
```

**Código PHP Gerado:**
```php
<?php
$array = [
    'usuarios' => [
        [
            'id' => 1,
            'nome' => 'Maria Santos',
            'perfil' => [
                'bio' => 'Desenvolvedora PHP',
                'skills' => [
                    'PHP',
                    'MySQL',
                    'Laravel'
                ]
            ]
        ],
        [
            'id' => 2,
            'nome' => 'Pedro Costa',
            'perfil' => [
                'bio' => 'Designer UX/UI',
                'skills' => [
                    'Figma',
                    'Photoshop',
                    'CSS'
                ]
            ]
        ]
    ],
    'configuracao' => [
        'tema' => 'escuro',
        'idioma' => 'pt-BR'
    ]
];
?>
```

## 🎯 Cenários de Aplicação

### 1. Migração de Dados de API

```php
<?php
// Dados recebidos de uma API externa
$apiResponse = '{
    "produtos": [
        {
            "id": 101,
            "nome": "Smartphone XYZ",
            "preco": 899.99,
            "categoria": "eletrônicos",
            "especificacoes": {
                "tela": "6.1 polegadas",
                "memoria": "128GB",
                "camera": "48MP"
            },
            "disponivel": true
        },
        {
            "id": 102,
            "nome": "Notebook ABC",
            "preco": 2499.99,
            "categoria": "computadores",
            "especificacoes": {
                "processador": "Intel i7",
                "memoria": "16GB RAM",
                "armazenamento": "512GB SSD"
            },
            "disponivel": false
        }
    ],
    "metadata": {
        "total": 2,
        "pagina": 1,
        "timestamp": "2024-01-15T10:30:00Z"
    }
}';

// Converter para array PHP (resultado da ferramenta)
$produtos = [
    'produtos' => [
        [
            'id' => 101,
            'nome' => 'Smartphone XYZ',
            'preco' => 899.99,
            'categoria' => 'eletrônicos',
            'especificacoes' => [
                'tela' => '6.1 polegadas',
                'memoria' => '128GB',
                'camera' => '48MP'
            ],
            'disponivel' => true
        ],
        [
            'id' => 102,
            'nome' => 'Notebook ABC',
            'preco' => 2499.99,
            'categoria' => 'computadores',
            'especificacoes' => [
                'processador' => 'Intel i7',
                'memoria' => '16GB RAM',
                'armazenamento' => '512GB SSD'
            ],
            'disponivel' => false
        ]
    ],
    'metadata' => [
        'total' => 2,
        'pagina' => 1,
        'timestamp' => '2024-01-15T10:30:00Z'
    ]
];

// Usar os dados no PHP
foreach ($produtos['produtos'] as $produto) {
    echo "Produto: " . $produto['nome'] . "\n";
    echo "Preço: R$ " . number_format($produto['preco'], 2, ',', '.') . "\n";
    echo "Disponível: " . ($produto['disponivel'] ? 'Sim' : 'Não') . "\n\n";
}
?>
```

### 2. Configuração de Aplicação

```php
<?php
// Arquivo de configuração gerado a partir de JSON
$config = [
    'aplicacao' => [
        'nome' => 'Sistema ToolMi',
        'versao' => '2.1.0',
        'ambiente' => 'producao',
        'debug' => false
    ],
    'banco_dados' => [
        'driver' => 'mysql',
        'host' => 'localhost',
        'porta' => 3306,
        'nome' => 'toolmi_db',
        'usuario' => 'root',
        'senha' => '',
        'charset' => 'utf8mb4',
        'opcoes' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    ],
    'cache' => [
        'driver' => 'redis',
        'host' => '127.0.0.1',
        'porta' => 6379,
        'database' => 0,
        'ttl' => 3600
    ],
    'email' => [
        'driver' => 'smtp',
        'host' => 'smtp.gmail.com',
        'porta' => 587,
        'usuario' => '<EMAIL>',
        'senha' => 'senha_secreta',
        'criptografia' => 'tls',
        'remetente' => [
            'nome' => 'Sistema ToolMi',
            'email' => '<EMAIL>'
        ]
    ],
    'recursos' => [
        'autenticacao' => [
            'habilitado' => true,
            'provedor' => 'jwt',
            'expiracao' => 86400,
            'algoritmo' => 'HS256'
        ],
        'rate_limiting' => [
            'habilitado' => true,
            'limite' => 1000,
            'janela' => 3600
        ],
        'cors' => [
            'habilitado' => true,
            'origens' => [
                'https://toolmi.com',
                'https://www.toolmi.com'
            ],
            'metodos' => ['GET', 'POST', 'PUT', 'DELETE'],
            'cabecalhos' => ['Content-Type', 'Authorization']
        ]
    ]
];

// Usar a configuração
class ConfigManager 
{
    private $config;
    
    public function __construct($config) 
    {
        $this->config = $config;
    }
    
    public function get($chave, $padrao = null) 
    {
        $chaves = explode('.', $chave);
        $valor = $this->config;
        
        foreach ($chaves as $k) {
            if (!isset($valor[$k])) {
                return $padrao;
            }
            $valor = $valor[$k];
        }
        
        return $valor;
    }
    
    public function getDatabaseConfig() 
    {
        return $this->get('banco_dados');
    }
    
    public function isDebugEnabled() 
    {
        return $this->get('aplicacao.debug', false);
    }
}

$configManager = new ConfigManager($config);
echo "Nome da aplicação: " . $configManager->get('aplicacao.nome') . "\n";
echo "Debug habilitado: " . ($configManager->isDebugEnabled() ? 'Sim' : 'Não') . "\n";
?>
```

### 3. Dados de Teste e Fixtures

```php
<?php
// Dados de teste para desenvolvimento
$dadosTeste = [
    'usuarios' => [
        [
            'id' => 1,
            'nome' => 'Admin Sistema',
            'email' => '<EMAIL>',
            'senha' => password_hash('admin123', PASSWORD_DEFAULT),
            'tipo' => 'administrador',
            'ativo' => true,
            'criado_em' => '2024-01-01 00:00:00',
            'permissoes' => [
                'usuarios.criar',
                'usuarios.editar',
                'usuarios.excluir',
                'sistema.configurar'
            ]
        ],
        [
            'id' => 2,
            'nome' => 'João Desenvolvedor',
            'email' => '<EMAIL>',
            'senha' => password_hash('dev123', PASSWORD_DEFAULT),
            'tipo' => 'desenvolvedor',
            'ativo' => true,
            'criado_em' => '2024-01-02 08:30:00',
            'permissoes' => [
                'ferramentas.usar',
                'relatorios.visualizar'
            ]
        ],
        [
            'id' => 3,
            'nome' => 'Maria Designer',
            'email' => '<EMAIL>',
            'senha' => password_hash('design123', PASSWORD_DEFAULT),
            'tipo' => 'designer',
            'ativo' => true,
            'criado_em' => '2024-01-03 14:15:00',
            'permissoes' => [
                'ferramentas.usar',
                'temas.editar'
            ]
        ]
    ],
    'ferramentas' => [
        [
            'id' => 'base64',
            'nome' => 'Codificação Base64',
            'categoria' => 'codificacao',
            'ativo' => true,
            'uso_mensal' => 15420,
            'avaliacao' => 4.8
        ],
        [
            'id' => 'uuid',
            'nome' => 'Gerador UUID',
            'categoria' => 'geracao',
            'ativo' => true,
            'uso_mensal' => 12350,
            'avaliacao' => 4.7
        ],
        [
            'id' => 'json-format',
            'nome' => 'Formatador JSON',
            'categoria' => 'formatacao',
            'ativo' => true,
            'uso_mensal' => 9870,
            'avaliacao' => 4.9
        ]
    ],
    'configuracoes_sistema' => [
        'manutencao' => false,
        'registro_habilitado' => true,
        'tema_padrao' => 'claro',
        'idioma_padrao' => 'pt-BR',
        'timezone' => 'America/Sao_Paulo',
        'limites' => [
            'upload_maximo' => '10MB',
            'requisicoes_por_minuto' => 60,
            'sessao_duracao' => 7200
        ]
    ]
];

// Classe para popular banco de dados com dados de teste
class DatabaseSeeder 
{
    private $pdo;
    
    public function __construct($pdo) 
    {
        $this->pdo = $pdo;
    }
    
    public function seed($dados) 
    {
        $this->pdo->beginTransaction();
        
        try {
            // Popular usuários
            foreach ($dados['usuarios'] as $usuario) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO usuarios (id, nome, email, senha, tipo, ativo, criado_em) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $usuario['id'],
                    $usuario['nome'],
                    $usuario['email'],
                    $usuario['senha'],
                    $usuario['tipo'],
                    $usuario['ativo'],
                    $usuario['criado_em']
                ]);
                
                // Popular permissões
                foreach ($usuario['permissoes'] as $permissao) {
                    $stmt = $this->pdo->prepare("
                        INSERT INTO usuario_permissoes (usuario_id, permissao) 
                        VALUES (?, ?)
                    ");
                    $stmt->execute([$usuario['id'], $permissao]);
                }
            }
            
            // Popular ferramentas
            foreach ($dados['ferramentas'] as $ferramenta) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO ferramentas (id, nome, categoria, ativo, uso_mensal, avaliacao) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $ferramenta['id'],
                    $ferramenta['nome'],
                    $ferramenta['categoria'],
                    $ferramenta['ativo'],
                    $ferramenta['uso_mensal'],
                    $ferramenta['avaliacao']
                ]);
            }
            
            $this->pdo->commit();
            echo "Dados de teste inseridos com sucesso!\n";
            
        } catch (Exception $e) {
            $this->pdo->rollback();
            echo "Erro ao inserir dados: " . $e->getMessage() . "\n";
        }
    }
}
?>
```

## 🔧 Detalhes Técnicos

### Tipos de Dados Suportados

**Conversões JSON → PHP:**
- **String**: `"texto"` → `'texto'`
- **Number**: `123` → `123`
- **Boolean**: `true/false` → `true/false`
- **Null**: `null` → `null`
- **Array**: `[1,2,3]` → `[1, 2, 3]`
- **Object**: `{"a":"b"}` → `['a' => 'b']`

### Formatação de Código

**Características da Saída:**
- Indentação de 4 espaços
- Aspas simples para strings
- Sintaxe de array curta `[]`
- Vírgulas finais opcionais
- Comentários preservados quando possível

### Tratamento de Caracteres Especiais

**Escape Automático:**
- Aspas simples: `'` → `\'`
- Barras invertidas: `\` → `\\`
- Caracteres Unicode preservados
- Quebras de linha mantidas

## 💡 Dicas de Uso

- **Validação** : Sempre valide o JSON antes da conversão
- **Teste** : Teste o código PHP gerado antes de usar em produção
- **Formatação** : Use o código gerado como base e ajuste conforme necessário
- **Performance** : Para arrays grandes, considere usar `var_export()` no PHP

## ⚠️ Notas Importantes

- **Sintaxe PHP** : O código gerado usa sintaxe PHP 5.4+
- **Encoding** : Certifique-se de que o arquivo PHP use UTF-8
- **Validação** : Sempre valide dados antes de usar em produção
- **Segurança** : Não inclua dados sensíveis em arrays estáticos

## 🚀 Como Usar

1. **Entrada JSON** : Cole o JSON válido na área de entrada
2. **Validação Automática** : O JSON é validado automaticamente
3. **Conversão** : Clique em "Converter" para gerar o código PHP
4. **Verificação** : Revise o código PHP gerado
5. **Uso de Cópia** : Clique em "Copiar" para usar o código

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e segurança dos seus dados.
