# Conversor de Timestamp

Esta ferramenta fornece conversão bidirecional entre timestamps Unix e formatos de data legíveis por humanos. Essencial para desenvolvimento de aplicações, análise de logs e depuração de sistemas que trabalham com dados temporais.

## ✨ Características Principais

- 🔄 **Conversão Bidirecional** : Timestamp Unix ⇄ Data/Hora legível
- 🌍 **Múl<PERSON>los Fusos Horários** : Suporte a fusos horários globais
- 📅 **Formatos Diversos** : ISO 8601, formato local, personalizado
- ⚡ **Tempo Real** : Exibição do timestamp atual em tempo real
- 📋 **Cópia com Um Clique** : Resultados podem ser copiados diretamente

## 📖 Exemplos de Uso

### Conversão de Timestamp para Data

**Timestamp Unix:**
```
1705123200
```

**Data Convertida:**
```
2024-01-13 08:00:00 UTC
13 de Janeiro de 2024, 08:00:00
Sábado, 13 de Janeiro de 2024
```

### Conversão de Data para Timestamp

**Data de Entrada:**
```
2024-12-25 15:30:00
```

**Timestamp Unix:**
```
1735134600
```

### Múltiplos Formatos de Saída

**Timestamp:** `1705123200`

**Formatos Disponíveis:**
- **ISO 8601**: `2024-01-13T08:00:00.000Z`
- **RFC 2822**: `Sat, 13 Jan 2024 08:00:00 GMT`
- **Formato Local**: `13/01/2024 08:00:00`
- **Formato Extenso**: `Sábado, 13 de Janeiro de 2024 às 08:00:00`

## 🎯 Cenários de Aplicação

### 1. Análise de Logs de Sistema

```javascript
// Analisador de logs com conversão de timestamp
class AnalisadorLogs {
  constructor() {
    this.logs = [];
    this.filtros = {
      dataInicio: null,
      dataFim: null,
      nivel: null,
      servico: null
    };
  }

  // Processar linha de log
  processarLinhaLog(linha) {
    const regex = /^(\d{10})\s+\[(\w+)\]\s+(\w+):\s+(.+)$/;
    const match = linha.match(regex);
    
    if (match) {
      const [, timestamp, nivel, servico, mensagem] = match;
      
      return {
        timestamp: parseInt(timestamp),
        data: this.timestampParaData(parseInt(timestamp)),
        nivel,
        servico,
        mensagem,
        dataFormatada: this.formatarData(parseInt(timestamp))
      };
    }
    
    return null;
  }

  // Converter timestamp para objeto Date
  timestampParaData(timestamp) {
    return new Date(timestamp * 1000);
  }

  // Formatar data para exibição
  formatarData(timestamp, formato = 'completo') {
    const data = this.timestampParaData(timestamp);
    
    const opcoes = {
      completo: {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZoneName: 'short'
      },
      curto: {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      },
      iso: null // Usar toISOString()
    };

    if (formato === 'iso') {
      return data.toISOString();
    }

    return data.toLocaleDateString('pt-BR', opcoes[formato]);
  }

  // Analisar arquivo de log
  analisarArquivoLog(conteudoLog) {
    const linhas = conteudoLog.split('\n');
    const logsProcessados = [];
    
    for (const linha of linhas) {
      if (linha.trim()) {
        const logProcessado = this.processarLinhaLog(linha);
        if (logProcessado) {
          logsProcessados.push(logProcessado);
        }
      }
    }

    this.logs = logsProcessados;
    return this.gerarRelatorioAnalise();
  }

  // Filtrar logs por período
  filtrarPorPeriodo(timestampInicio, timestampFim) {
    return this.logs.filter(log => 
      log.timestamp >= timestampInicio && log.timestamp <= timestampFim
    );
  }

  // Agrupar logs por hora
  agruparPorHora() {
    const grupos = {};
    
    for (const log of this.logs) {
      const data = this.timestampParaData(log.timestamp);
      const chaveHora = `${data.getFullYear()}-${String(data.getMonth() + 1).padStart(2, '0')}-${String(data.getDate()).padStart(2, '0')} ${String(data.getHours()).padStart(2, '0')}:00`;
      
      if (!grupos[chaveHora]) {
        grupos[chaveHora] = {
          periodo: chaveHora,
          timestamp: Math.floor(data.getTime() / 1000),
          total: 0,
          porNivel: {}
        };
      }
      
      grupos[chaveHora].total++;
      grupos[chaveHora].porNivel[log.nivel] = (grupos[chaveHora].porNivel[log.nivel] || 0) + 1;
    }

    return Object.values(grupos).sort((a, b) => a.timestamp - b.timestamp);
  }

  // Gerar relatório de análise
  gerarRelatorioAnalise() {
    const agora = Math.floor(Date.now() / 1000);
    const ultimaHora = agora - 3600;
    const ultimoDia = agora - 86400;
    const ultimaSemana = agora - 604800;

    return {
      resumo: {
        totalLogs: this.logs.length,
        primeiroLog: this.logs.length > 0 ? this.formatarData(Math.min(...this.logs.map(l => l.timestamp))) : null,
        ultimoLog: this.logs.length > 0 ? this.formatarData(Math.max(...this.logs.map(l => l.timestamp))) : null
      },
      estatisticasPorPeriodo: {
        ultimaHora: this.filtrarPorPeriodo(ultimaHora, agora).length,
        ultimoDia: this.filtrarPorPeriodo(ultimoDia, agora).length,
        ultimaSemana: this.filtrarPorPeriodo(ultimaSemana, agora).length
      },
      distribuicaoPorNivel: this.obterDistribuicaoPorNivel(),
      timelineHoraria: this.agruparPorHora()
    };
  }

  // Obter distribuição por nível
  obterDistribuicaoPorNivel() {
    const distribuicao = {};
    
    for (const log of this.logs) {
      distribuicao[log.nivel] = (distribuicao[log.nivel] || 0) + 1;
    }

    return distribuicao;
  }
}

// Exemplo de uso
const analisador = new AnalisadorLogs();

const exemploLog = `
1705123200 [INFO] auth: Usuário logado com sucesso - ID: 12345
1705123260 [WARN] database: Conexão lenta detectada - 2.5s
1705123320 [ERROR] payment: Falha no processamento do pagamento - Gateway timeout
1705123380 [INFO] auth: Usuário deslogado - ID: 12345
1705123440 [DEBUG] cache: Cache invalidado para chave: user_12345
`;

const relatorio = analisador.analisarArquivoLog(exemploLog);
console.log('Relatório de Análise:', relatorio);
```

### 2. Sistema de Agendamento

```javascript
// Sistema de agendamento com timestamps
class SistemaAgendamento {
  constructor() {
    this.eventos = new Map();
    this.fusoHorarioPadrao = 'America/Sao_Paulo';
  }

  // Criar evento agendado
  criarEvento(nome, dataHora, fusoHorario = this.fusoHorarioPadrao, opcoes = {}) {
    const timestamp = this.dataParaTimestamp(dataHora, fusoHorario);
    const id = this.gerarIdEvento();

    const evento = {
      id,
      nome,
      timestamp,
      fusoHorario,
      dataOriginal: dataHora,
      criado: Math.floor(Date.now() / 1000),
      status: 'agendado',
      recorrencia: opcoes.recorrencia || null,
      lembretes: opcoes.lembretes || [],
      metadados: opcoes.metadados || {}
    };

    this.eventos.set(id, evento);
    this.agendarLembretes(evento);

    return evento;
  }

  // Converter data para timestamp
  dataParaTimestamp(dataHora, fusoHorario) {
    let data;
    
    if (typeof dataHora === 'string') {
      // Assumir formato ISO ou criar com fuso horário
      data = new Date(dataHora);
    } else if (dataHora instanceof Date) {
      data = dataHora;
    } else {
      throw new Error('Formato de data inválido');
    }

    return Math.floor(data.getTime() / 1000);
  }

  // Converter timestamp para data formatada
  timestampParaDataFormatada(timestamp, fusoHorario = this.fusoHorarioPadrao) {
    const data = new Date(timestamp * 1000);
    
    return {
      iso: data.toISOString(),
      local: data.toLocaleDateString('pt-BR', {
        timeZone: fusoHorario,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      relativo: this.obterTempoRelativo(timestamp),
      timestamp: timestamp
    };
  }

  // Obter tempo relativo (ex: "em 2 horas", "há 3 dias")
  obterTempoRelativo(timestamp) {
    const agora = Math.floor(Date.now() / 1000);
    const diferenca = timestamp - agora;
    const diferencaAbs = Math.abs(diferenca);

    const unidades = [
      { nome: 'ano', segundos: 31536000 },
      { nome: 'mês', segundos: 2592000 },
      { nome: 'semana', segundos: 604800 },
      { nome: 'dia', segundos: 86400 },
      { nome: 'hora', segundos: 3600 },
      { nome: 'minuto', segundos: 60 },
      { nome: 'segundo', segundos: 1 }
    ];

    for (const unidade of unidades) {
      const quantidade = Math.floor(diferencaAbs / unidade.segundos);
      
      if (quantidade >= 1) {
        const plural = quantidade > 1 ? 's' : '';
        const prefixo = diferenca > 0 ? 'em' : 'há';
        return `${prefixo} ${quantidade} ${unidade.nome}${plural}`;
      }
    }

    return 'agora';
  }

  // Listar eventos por período
  listarEventosPorPeriodo(timestampInicio, timestampFim) {
    const eventos = Array.from(this.eventos.values())
      .filter(evento => evento.timestamp >= timestampInicio && evento.timestamp <= timestampFim)
      .sort((a, b) => a.timestamp - b.timestamp);

    return eventos.map(evento => ({
      ...evento,
      dataFormatada: this.timestampParaDataFormatada(evento.timestamp, evento.fusoHorario)
    }));
  }

  // Obter próximos eventos
  obterProximosEventos(limite = 10) {
    const agora = Math.floor(Date.now() / 1000);
    
    return Array.from(this.eventos.values())
      .filter(evento => evento.timestamp > agora && evento.status === 'agendado')
      .sort((a, b) => a.timestamp - b.timestamp)
      .slice(0, limite)
      .map(evento => ({
        ...evento,
        dataFormatada: this.timestampParaDataFormatada(evento.timestamp, evento.fusoHorario)
      }));
  }

  // Agendar lembretes
  agendarLembretes(evento) {
    for (const lembrete of evento.lembretes) {
      const timestampLembrete = evento.timestamp - lembrete.antecedencia;
      
      // Simular agendamento de lembrete
      console.log(`Lembrete agendado para ${this.timestampParaDataFormatada(timestampLembrete).local}: ${lembrete.mensagem}`);
    }
  }

  // Gerar ID único para evento
  gerarIdEvento() {
    return `evento_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Converter entre fusos horários
  converterFusoHorario(timestamp, fusoOrigem, fusoDestino) {
    const data = new Date(timestamp * 1000);
    
    return {
      original: {
        timestamp,
        formatado: data.toLocaleDateString('pt-BR', {
          timeZone: fusoOrigem,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        })
      },
      convertido: {
        timestamp,
        formatado: data.toLocaleDateString('pt-BR', {
          timeZone: fusoDestino,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        })
      }
    };
  }
}

// Exemplo de uso
const agendamento = new SistemaAgendamento();

// Criar eventos
const reuniao = agendamento.criarEvento(
  'Reunião de Equipe',
  '2024-01-15T14:00:00',
  'America/Sao_Paulo',
  {
    lembretes: [
      { antecedencia: 3600, mensagem: 'Reunião em 1 hora' },
      { antecedencia: 900, mensagem: 'Reunião em 15 minutos' }
    ],
    metadados: {
      local: 'Sala de Conferências A',
      participantes: ['<EMAIL>', '<EMAIL>']
    }
  }
);

const webinar = agendamento.criarEvento(
  'Webinar ToolMi',
  '2024-01-20T10:00:00',
  'America/Sao_Paulo'
);

// Listar próximos eventos
const proximosEventos = agendamento.obterProximosEventos();
console.log('Próximos Eventos:', proximosEventos);

// Converter fuso horário
const conversao = agendamento.converterFusoHorario(
  reuniao.timestamp,
  'America/Sao_Paulo',
  'Europe/London'
);
console.log('Conversão de Fuso:', conversao);
```

## 🔧 Detalhes Técnicos

### Formatos de Timestamp

**Unix Timestamp:**
- **Segundos**: `1705123200` (padrão)
- **Milissegundos**: `1705123200000` (JavaScript)
- **Microssegundos**: `1705123200000000` (alguns sistemas)

**Formatos de Data:**
- **ISO 8601**: `2024-01-13T08:00:00.000Z`
- **RFC 2822**: `Sat, 13 Jan 2024 08:00:00 GMT`
- **Formato Local**: `13/01/2024 08:00:00`

### Fusos Horários Comuns

**Brasil:**
- `America/Sao_Paulo` (Brasília)
- `America/Manaus` (Amazonas)
- `America/Fortaleza` (Ceará)

**Internacional:**
- `UTC` (Tempo Universal Coordenado)
- `America/New_York` (Nova York)
- `Europe/London` (Londres)
- `Asia/Tokyo` (Tóquio)

### Precisão e Limitações

**Limitações do Unix Timestamp:**
- **Problema do Ano 2038**: Timestamps de 32 bits limitados até 2038
- **Precisão**: Segundos (padrão), milissegundos (JavaScript)
- **Fuso Horário**: Sempre UTC, conversão necessária para locais

## 💡 Dicas de Uso

- **Armazenamento** : Sempre armazene timestamps em UTC no banco de dados
- **Exibição** : Converta para fuso horário local apenas na apresentação
- **Validação** : Verifique se o timestamp está dentro de um intervalo válido
- **Precisão** : Use milissegundos quando precisão alta for necessária

## ⚠️ Notas Importantes

- **Horário de Verão** : Considere mudanças de horário de verão nas conversões
- **Leap Seconds** : Segundos intercalares podem afetar cálculos precisos
- **Formato** : Certifique-se de usar o formato correto (segundos vs milissegundos)
- **Validação** : Sempre valide timestamps antes de processar

## 🚀 Como Usar

1. **Entrada de Dados** : Digite o timestamp ou data/hora
2. **Seleção de Formato** : Escolha o formato de entrada e saída
3. **Configuração de Fuso** : Selecione o fuso horário apropriado
4. **Verificação dos Resultados** : Veja a conversão em múltiplos formatos
5. **Uso de Cópia** : Clique em "Copiar" para usar o resultado

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e rapidez nas conversões.
