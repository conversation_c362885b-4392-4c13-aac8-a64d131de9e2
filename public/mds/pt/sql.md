# Ferramenta de Formato SQL

Esta ferramenta fornece formatação, embelezamento e compressão de declarações SQL. Compatível com múltiplos dialetos de banco de dados, oferece coloração sintática, detecção de erros e formatação inteligente para melhorar a legibilidade e manutenibilidade do código SQL.

## ✨ Características Principais

- 🎨 **Formatação Inteligente** : Formatação automática com indentação adequada
- 🗜️ **Compressão SQL** : Remove espaços desnecessários para otimização
- 🌐 **Múltiplos Dialetos** : Compatível com MySQL, PostgreSQL, SQL Server, Oracle
- 📋 **Cópia com Um Clique** : Código formatado pode ser copiado diretamente
- 🔍 **Detecção de Erros** : Identifica problemas de sintaxe automaticamente

## 📖 Exemplos de Uso

### Formatação de Consulta Básica

**SQL de Entrada (não formatado):**
```sql
select u.id,u.nome,u.email,p.titulo from usuarios u inner join posts p on u.id=p.usuario_id where u.ativo=1 and p.publicado=1 order by p.data_criacao desc limit 10;
```

**SQL Formatado:**
```sql
SELECT 
    u.id,
    u.nome,
    u.email,
    p.titulo
FROM usuarios u
INNER JOIN posts p ON u.id = p.usuario_id
WHERE u.ativo = 1
    AND p.publicado = 1
ORDER BY p.data_criacao DESC
LIMIT 10;
```

### Formatação de Consulta Complexa

**SQL de Entrada:**
```sql
with vendas_mensais as (select extract(year from data_venda) as ano,extract(month from data_venda) as mes,sum(valor_total) as total_vendas from vendas where data_venda >= '2024-01-01' group by extract(year from data_venda),extract(month from data_venda)),ranking_vendas as (select ano,mes,total_vendas,rank() over (order by total_vendas desc) as ranking from vendas_mensais) select v.ano,v.mes,v.total_vendas,v.ranking,case when v.ranking <= 3 then 'Top 3' when v.ranking <= 6 then 'Médio' else 'Baixo' end as categoria from ranking_vendas v order by v.total_vendas desc;
```

**SQL Formatado:**
```sql
WITH vendas_mensais AS (
    SELECT 
        EXTRACT(YEAR FROM data_venda) AS ano,
        EXTRACT(MONTH FROM data_venda) AS mes,
        SUM(valor_total) AS total_vendas
    FROM vendas
    WHERE data_venda >= '2024-01-01'
    GROUP BY 
        EXTRACT(YEAR FROM data_venda),
        EXTRACT(MONTH FROM data_venda)
),
ranking_vendas AS (
    SELECT 
        ano,
        mes,
        total_vendas,
        RANK() OVER (ORDER BY total_vendas DESC) AS ranking
    FROM vendas_mensais
)
SELECT 
    v.ano,
    v.mes,
    v.total_vendas,
    v.ranking,
    CASE 
        WHEN v.ranking <= 3 THEN 'Top 3'
        WHEN v.ranking <= 6 THEN 'Médio'
        ELSE 'Baixo'
    END AS categoria
FROM ranking_vendas v
ORDER BY v.total_vendas DESC;
```

## 🎯 Cenários de Aplicação

### 1. Desenvolvimento de Aplicações

```sql
-- Sistema de gerenciamento de usuários e permissões
CREATE TABLE usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    senha_hash VARCHAR(255) NOT NULL,
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(50) UNIQUE NOT NULL,
    descricao TEXT,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE permissoes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) UNIQUE NOT NULL,
    recurso VARCHAR(100) NOT NULL,
    acao VARCHAR(50) NOT NULL,
    descricao TEXT
);

CREATE TABLE usuario_roles (
    usuario_id INTEGER REFERENCES usuarios(id) ON DELETE CASCADE,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    data_atribuicao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (usuario_id, role_id)
);

CREATE TABLE role_permissoes (
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    permissao_id INTEGER REFERENCES permissoes(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permissao_id)
);

-- Consulta para verificar permissões de usuário
SELECT DISTINCT
    u.id AS usuario_id,
    u.nome AS usuario_nome,
    p.nome AS permissao,
    p.recurso,
    p.acao
FROM usuarios u
INNER JOIN usuario_roles ur ON u.id = ur.usuario_id
INNER JOIN roles r ON ur.role_id = r.id
INNER JOIN role_permissoes rp ON r.id = rp.role_id
INNER JOIN permissoes p ON rp.permissao_id = p.id
WHERE u.ativo = TRUE
    AND u.email = $1
ORDER BY p.recurso, p.acao;
```

### 2. Análise de Dados e Relatórios

```sql
-- Relatório de vendas com análise temporal
WITH dados_vendas AS (
    SELECT 
        v.id,
        v.data_venda,
        v.valor_total,
        c.nome AS cliente_nome,
        c.segmento,
        p.nome AS produto_nome,
        p.categoria,
        vp.quantidade,
        vp.preco_unitario,
        EXTRACT(YEAR FROM v.data_venda) AS ano,
        EXTRACT(MONTH FROM v.data_venda) AS mes,
        EXTRACT(QUARTER FROM v.data_venda) AS trimestre
    FROM vendas v
    INNER JOIN clientes c ON v.cliente_id = c.id
    INNER JOIN venda_produtos vp ON v.id = vp.venda_id
    INNER JOIN produtos p ON vp.produto_id = p.id
    WHERE v.data_venda >= DATE_TRUNC('year', CURRENT_DATE - INTERVAL '2 years')
),
metricas_mensais AS (
    SELECT 
        ano,
        mes,
        COUNT(DISTINCT id) AS total_vendas,
        SUM(valor_total) AS receita_total,
        AVG(valor_total) AS ticket_medio,
        COUNT(DISTINCT cliente_nome) AS clientes_unicos
    FROM dados_vendas
    GROUP BY ano, mes
),
crescimento_mensal AS (
    SELECT 
        *,
        LAG(receita_total) OVER (ORDER BY ano, mes) AS receita_mes_anterior,
        ROUND(
            ((receita_total - LAG(receita_total) OVER (ORDER BY ano, mes)) / 
             NULLIF(LAG(receita_total) OVER (ORDER BY ano, mes), 0)) * 100, 2
        ) AS crescimento_percentual
    FROM metricas_mensais
)
SELECT 
    ano,
    mes,
    TO_CHAR(DATE_FROM_PARTS(ano, mes, 1), 'Month YYYY') AS periodo,
    total_vendas,
    ROUND(receita_total, 2) AS receita_total,
    ROUND(ticket_medio, 2) AS ticket_medio,
    clientes_unicos,
    COALESCE(crescimento_percentual, 0) AS crescimento_percentual,
    CASE 
        WHEN crescimento_percentual > 10 THEN 'Excelente'
        WHEN crescimento_percentual > 0 THEN 'Positivo'
        WHEN crescimento_percentual = 0 THEN 'Estável'
        ELSE 'Negativo'
    END AS status_crescimento
FROM crescimento_mensal
ORDER BY ano DESC, mes DESC;
```

### 3. Otimização de Performance

```sql
-- Análise de performance de consultas
EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
SELECT 
    u.id,
    u.nome,
    u.email,
    COUNT(p.id) AS total_posts,
    MAX(p.data_criacao) AS ultimo_post,
    AVG(p.visualizacoes) AS media_visualizacoes
FROM usuarios u
LEFT JOIN posts p ON u.id = p.usuario_id 
    AND p.publicado = TRUE
    AND p.data_criacao >= CURRENT_DATE - INTERVAL '1 year'
WHERE u.ativo = TRUE
GROUP BY u.id, u.nome, u.email
HAVING COUNT(p.id) > 0
ORDER BY total_posts DESC, media_visualizacoes DESC
LIMIT 50;

-- Criação de índices para otimização
CREATE INDEX CONCURRENTLY idx_posts_usuario_publicado_data 
ON posts (usuario_id, publicado, data_criacao DESC)
WHERE publicado = TRUE;

CREATE INDEX CONCURRENTLY idx_usuarios_ativo_nome 
ON usuarios (ativo, nome)
WHERE ativo = TRUE;

-- Análise de uso de índices
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan AS utilizacoes_indice,
    idx_tup_read AS tuplas_lidas,
    idx_tup_fetch AS tuplas_buscadas,
    ROUND(
        CASE 
            WHEN idx_scan = 0 THEN 0
            ELSE (idx_tup_fetch::FLOAT / idx_scan)
        END, 2
    ) AS eficiencia_indice
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC, eficiencia_indice DESC;
```

### 4. Migração e Manutenção de Dados

```sql
-- Script de migração de dados
BEGIN;

-- Backup da estrutura atual
CREATE TABLE usuarios_backup AS 
SELECT * FROM usuarios;

-- Adicionar nova coluna
ALTER TABLE usuarios 
ADD COLUMN telefone VARCHAR(20),
ADD COLUMN data_ultimo_login TIMESTAMP,
ADD COLUMN tentativas_login INTEGER DEFAULT 0;

-- Migrar dados existentes
UPDATE usuarios 
SET data_ultimo_login = data_atualizacao
WHERE data_atualizacao IS NOT NULL;

-- Criar nova tabela de auditoria
CREATE TABLE auditoria_usuarios (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    acao VARCHAR(50) NOT NULL,
    dados_anteriores JSONB,
    dados_novos JSONB,
    usuario_responsavel INTEGER,
    data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_origem INET
);

-- Função de trigger para auditoria
CREATE OR REPLACE FUNCTION fn_auditoria_usuarios()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO auditoria_usuarios (
            usuario_id, acao, dados_novos, usuario_responsavel
        ) VALUES (
            NEW.id, 'INSERT', row_to_json(NEW), NEW.id
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO auditoria_usuarios (
            usuario_id, acao, dados_anteriores, dados_novos, usuario_responsavel
        ) VALUES (
            NEW.id, 'UPDATE', row_to_json(OLD), row_to_json(NEW), NEW.id
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO auditoria_usuarios (
            usuario_id, acao, dados_anteriores
        ) VALUES (
            OLD.id, 'DELETE', row_to_json(OLD)
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger
CREATE TRIGGER tg_auditoria_usuarios
    AFTER INSERT OR UPDATE OR DELETE ON usuarios
    FOR EACH ROW EXECUTE FUNCTION fn_auditoria_usuarios();

-- Validar migração
DO $$
DECLARE
    total_original INTEGER;
    total_migrado INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_original FROM usuarios_backup;
    SELECT COUNT(*) INTO total_migrado FROM usuarios;
    
    IF total_original != total_migrado THEN
        RAISE EXCEPTION 'Erro na migração: contagem não confere. Original: %, Migrado: %', 
            total_original, total_migrado;
    END IF;
    
    RAISE NOTICE 'Migração concluída com sucesso. Total de registros: %', total_migrado;
END $$;

COMMIT;
```

## 🔧 Detalhes Técnicos

### Dialetos SQL Suportados

**MySQL:**
- Sintaxe específica: `LIMIT`, `AUTO_INCREMENT`
- Funções: `CONCAT()`, `DATE_FORMAT()`
- Tipos de dados: `TINYINT`, `MEDIUMTEXT`

**PostgreSQL:**
- Sintaxe específica: `SERIAL`, `RETURNING`
- Funções: `EXTRACT()`, `GENERATE_SERIES()`
- Tipos avançados: `JSONB`, `ARRAY`, `UUID`

**SQL Server:**
- Sintaxe específica: `TOP`, `IDENTITY`
- Funções: `DATEPART()`, `STRING_AGG()`
- Recursos: `CTE`, `MERGE`, `OUTPUT`

**Oracle:**
- Sintaxe específica: `ROWNUM`, `DUAL`
- Funções: `DECODE()`, `NVL()`
- Recursos: `CONNECT BY`, `PIVOT`

### Regras de Formatação

**Indentação:**
- 4 espaços para cada nível de indentação
- Subconsultas indentadas adequadamente
- Cláusulas alinhadas verticalmente

**Quebras de Linha:**
- Cada cláusula principal em nova linha
- Listas de colunas quebradas quando necessário
- JOINs em linhas separadas

**Capitalização:**
- Palavras-chave SQL em maiúsculas
- Nomes de tabelas e colunas preservados
- Funções em maiúsculas

## 💡 Dicas de Uso

- **Legibilidade** : Use formatação consistente para facilitar manutenção
- **Performance** : Considere compressão para consultas em produção
- **Versionamento** : Mantenha histórico de mudanças em scripts SQL
- **Documentação** : Adicione comentários explicativos em consultas complexas

## ⚠️ Notas Importantes

- **Backup** : Sempre faça backup antes de executar scripts de modificação
- **Testes** : Teste consultas em ambiente de desenvolvimento primeiro
- **Permissões** : Verifique permissões necessárias antes da execução
- **Transações** : Use transações para operações críticas

## 🚀 Como Usar

1. **Entrada SQL** : Cole o código SQL na área de entrada
2. **Seleção de Operação** : Escolha "Formatar" ou "Comprimir"
3. **Configuração de Dialeto** : Selecione o dialeto SQL apropriado
4. **Verificação dos Resultados** : O SQL formatado aparece na área de saída
5. **Uso de Cópia** : Clique em "Copiar" para usar o código formatado

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não executa consultas SQL, garantindo segurança dos seus dados.
