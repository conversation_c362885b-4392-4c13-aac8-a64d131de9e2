# Gerador de Códigos QR

O código QR (Quick Response Code) é um código de barras bidimensional inventado pela Denso Wave do Japão em 1994. Esta ferramenta pode rapidamente converter texto, URLs, informações de contato e outros conteúdos diversos em imagens de códigos QR.

## ✨ Características Principais

- 📱 **Suporte Multi-conteúdo** : Compatível com texto, URL, contatos, senhas WiFi, etc.
- 🎨 **Saída de Alta Qualidade** : Gera imagens de códigos QR de alta qualidade
- 📏 **Tamanhos Ajustáve<PERSON>** : Compatível com múltiplas especificações de tamanho
- 🎯 **Níveis de Correção de Erro** : Compatível com diferentes configurações de níveis de correção de erro
- 💾 **Download com Um Clique** : Baixa e salva diretamente os códigos QR gerados
- 🔧 **Visualização em Tempo Real** : Gera visualização de código QR instantaneamente conforme o conteúdo de entrada

## 📖 Exemplos de Uso

### Código QR de URL

**Conteúdo de Entrada:**
```
https://www.toolmi.com
```

**Efeito Gerado:**
- Após escaneamento, redireciona diretamente para o site ToolMi
- Aplicável à promoção de sites, compartilhamento de links

### Código QR de Texto

**Conteúdo de Entrada:**
```
Bem-vindos às ferramentas online ToolMi!
Aqui estão ferramentas de desenvolvimento ricas e funções práticas.
```

**Efeito Gerado:**
- Após escaneamento, exibe o conteúdo completo do texto
- Aplicável à transmissão de informações, exibição de instruções

### Código QR de Contato

**Conteúdo de Entrada:**
```
BEGIN:VCARD
VERSION:3.0
FN:João Silva
ORG:Tecnologia ToolMi
TEL:+55-11-99999-9999
EMAIL:<EMAIL>
URL:https://www.toolmi.com
END:VCARD
```

**Efeito Gerado:**
- Após escaneamento, pode ser adicionado diretamente aos contatos
- Inclui informações como nome, empresa, telefone, e-mail

## 🎯 Cenários de Aplicação

### 1. Promoção de Sites

Gerar códigos QR promocionais para sites e aplicações:

```html
<!-- Código QR em cartaz promocional -->
<div class="promocao">
  <h3>Escaneie o código QR para acessar ToolMi</h3>
  <img src="qr-code.png" alt="Código QR ToolMi">
  <p>Descubra mais ferramentas práticas</p>
</div>

<!-- Cenários de aplicação -->
- Cartazes promocionais
- Design de cartões de visita
- Embalagem de produtos
- Publicações publicitárias
```

### 2. Pagamentos Móveis

Gerar códigos QR de pagamento:

```javascript
// Exemplo de link de pagamento PayPal
const paypalUrl = 'https://paypal.me/usuario/50BRL'

// Exemplo de link de pagamento móvel brasileiro
const pixUrl = 'pix://pay?amount=50&recipient=<EMAIL>'

// Gerar código QR de pagamento
gerarCodigoQR(paypalUrl, {
  tamanho: 200,
  nivelCorrecaoErro: 'M'
})
```

### 3. Compartilhar Senha WiFi

Gerar código QR de conexão WiFi:

```
Formato de código QR WiFi:
WIFI:T:WPA;S:nome_rede;P:senha;H:false;;

Exemplo:
WIFI:T:WPA;S:ToolMi_5G;P:12345678;H:false;;

Explicação dos parâmetros:
- T: Tipo de criptografia (WPA/WEP/nopass)
- S: Nome da rede (SSID)
- P: Senha
- H: Se ocultar a rede (true/false)
```

### 4. Check-in de Eventos

Gerar código QR de check-in de eventos:

```json
{
  "tipo": "checkin_evento",
  "id_evento": "encontro_tech_2024",
  "nome_evento": "Encontro Tecnológico",
  "local": "Centro de Convenções São Paulo",
  "data": "2024-06-15",
  "url_checkin": "https://evento.toolmi.com/checkin/encontro_tech_2024"
}
```

### 5. Rastreabilidade de Produtos

Gerar código QR de rastreabilidade de produtos:

```javascript
// Informações do produto
const infoProduto = {
  id: 'TM2024001',
  nome: 'Caixa de Ferramentas Inteligente',
  lote: 'L20240615',
  data_producao: '2024-06-15',
  fabricante: 'Tecnologia ToolMi',
  controle_qualidade: 'APROVADO',
  url_rastreamento: 'https://rastreamento.toolmi.com/produto/TM2024001'
}

// Gerar código QR de rastreabilidade
const dadosRastreamento = JSON.stringify(infoProduto)
gerarCodigoQR(dadosRastreamento)
```

## 🔧 Detalhes Técnicos

### Estrutura do Código QR

Estrutura básica do código QR:

**Padrões Funcionais:**
- Padrões de detecção de posição: Quadrados grandes nos três cantos
- Separadores de padrões de detecção de posição: Molduras brancas
- Padrões de alinhamento: Pequenos pontos pretos para determinar direção

**Área de Dados:**
- Informações de formato: Nível de correção de erro e informações de máscara
- Informações de versão: Número da versão do código QR
- Palavras de código de dados e correção de erro: Dados realmente armazenados

**Especificações de Capacidade:**
- Versão 1: 21×21 módulos, máximo 25 caracteres
- Versão 40: 177×177 módulos, máximo 4296 caracteres
- Compatível com dados numéricos, alfabéticos, kanji e binários

### Níveis de Correção de Erro

O código QR suporta 4 níveis de correção de erro:

| Nível | Taxa de Correção | Cenário de Aplicação |
|-------|------------------|----------------------|
| L | ~7% | Ambiente limpo, impressão de alta qualidade |
| M | ~15% | Ambiente geral, impressão padrão |
| Q | ~25% | Ambiente difícil, possível contaminação |
| H | ~30% | Ambiente muito difícil, contaminação severa |

**Recomendações de Seleção:**
- Compartilhamento de URL: Usar nível L ou M
- Publicidade externa: Usar nível Q ou H
- Etiquetas de produtos: Usar nível M ou Q

## 💡 Dicas de Uso

- **Otimização de Conteúdo** : Usar conteúdo o mais curto possível para melhorar taxa de sucesso do escaneamento
- **Seleção de Tamanho** : Escolher tamanho apropriado conforme distância de uso
- **Nível de Correção de Erro** : Escolher nível de correção de erro apropriado conforme ambiente de uso
- **Verificação de Testes** : Após geração, testar efeito de escaneamento com múltiplos dispositivos

## ⚠️ Notas Importantes

- **Comprimento do Conteúdo** : Conteúdo muito longo tornará o código QR complexo, afetando o escaneamento
- **Qualidade de Impressão** : Garantir que a impressão seja clara, evitar desfoque e deformação
- **Contraste de Cor** : Manter contraste de cor suficiente, combinação preto e branco recomendada
- **Margem Circundante** : Deixar espaço branco suficiente ao redor do código QR

## 🚀 Como Usar

1. **Entrada de Conteúdo** : Digite o conteúdo para gerar código QR na área de entrada
2. **Ajuste de Configuração** : Selecione tamanho apropriado e nível de correção de erro
3. **Gerar Visualização** : Clique no botão gerar para ver visualização do código QR
4. **Download e Salvar** : Clique no botão download para salvar imagem do código QR
5. **Teste de Escaneamento** : Use smartphone para escanear e testar efeito gerado

> **Dica** : Esta ferramenta gera códigos QR localmente no lado do cliente e não faz upload de dados para o servidor, garantindo privacidade e segurança.
