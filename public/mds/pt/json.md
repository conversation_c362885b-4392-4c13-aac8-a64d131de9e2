# Ferramenta de Formato JSON

Esta ferramenta fornece formatação, validação, compressão e embelezamento de dados JSON. JSON (JavaScript Object Notation) é o formato de intercâmbio de dados mais popular na web moderna, usado em APIs, configurações e armazenamento de dados.

## ✨ Características Principais

- ✅ **Validação de Sintaxe** : Detecção automática de erros de sintaxe JSON
- 🎨 **Formatação Bonita** : Indentação inteligente e estrutura limpa
- 🗜️ **Compressão** : Remove espaços desnecessários para otimização
- 📋 **Cópia com Um Clique** : JSON formatado pode ser copiado diretamente
- 🔍 **Análise de Estrutura** : Visualização clara da hierarquia de dados

## 📖 Exemplos de Uso

### Formatação de JSON Básico

**JSON de Entrada (comprimido):**
```json
{"usuario":{"id":1,"nome":"<PERSON>","email":"<EMAIL>","ativo":true},"configuracoes":{"tema":"escuro","notificacoes":true,"idioma":"pt-BR"},"dados":[{"tipo":"perfil","valor":"completo"},{"tipo":"preferencias","valor":"salvo"}]}
```

**JSON Formatado:**
```json
{
  "usuario": {
    "id": 1,
    "nome": "João Silva",
    "email": "<EMAIL>",
    "ativo": true
  },
  "configuracoes": {
    "tema": "escuro",
    "notificacoes": true,
    "idioma": "pt-BR"
  },
  "dados": [
    {
      "tipo": "perfil",
      "valor": "completo"
    },
    {
      "tipo": "preferencias",
      "valor": "salvo"
    }
  ]
}
```

### Formatação de JSON Complexo

**JSON de Entrada:**
```json
{"api":{"versao":"2.1.0","endpoints":[{"metodo":"GET","caminho":"/usuarios","descricao":"Listar usuários","parametros":{"limite":{"tipo":"integer","padrao":10},"pagina":{"tipo":"integer","padrao":1}},"resposta":{"200":{"tipo":"object","propriedades":{"usuarios":{"tipo":"array"},"total":{"tipo":"integer"},"pagina_atual":{"tipo":"integer"}}}}},{"metodo":"POST","caminho":"/usuarios","descricao":"Criar usuário","corpo":{"nome":{"tipo":"string","obrigatorio":true},"email":{"tipo":"string","obrigatorio":true},"senha":{"tipo":"string","obrigatorio":true}},"resposta":{"201":{"tipo":"object","propriedades":{"id":{"tipo":"integer"},"nome":{"tipo":"string"},"email":{"tipo":"string"},"data_criacao":{"tipo":"string","formato":"datetime"}}}}}]}}
```

**JSON Formatado:**
```json
{
  "api": {
    "versao": "2.1.0",
    "endpoints": [
      {
        "metodo": "GET",
        "caminho": "/usuarios",
        "descricao": "Listar usuários",
        "parametros": {
          "limite": {
            "tipo": "integer",
            "padrao": 10
          },
          "pagina": {
            "tipo": "integer",
            "padrao": 1
          }
        },
        "resposta": {
          "200": {
            "tipo": "object",
            "propriedades": {
              "usuarios": {
                "tipo": "array"
              },
              "total": {
                "tipo": "integer"
              },
              "pagina_atual": {
                "tipo": "integer"
              }
            }
          }
        }
      },
      {
        "metodo": "POST",
        "caminho": "/usuarios",
        "descricao": "Criar usuário",
        "corpo": {
          "nome": {
            "tipo": "string",
            "obrigatorio": true
          },
          "email": {
            "tipo": "string",
            "obrigatorio": true
          },
          "senha": {
            "tipo": "string",
            "obrigatorio": true
          }
        },
        "resposta": {
          "201": {
            "tipo": "object",
            "propriedades": {
              "id": {
                "tipo": "integer"
              },
              "nome": {
                "tipo": "string"
              },
              "email": {
                "tipo": "string"
              },
              "data_criacao": {
                "tipo": "string",
                "formato": "datetime"
              }
            }
          }
        }
      }
    ]
  }
}
```

## 🎯 Cenários de Aplicação

### 1. Desenvolvimento de APIs

```json
{
  "openapi": "3.0.3",
  "info": {
    "title": "API ToolMi",
    "description": "API para ferramentas online de desenvolvimento",
    "version": "2.1.0",
    "contact": {
      "name": "Equipe ToolMi",
      "email": "<EMAIL>",
      "url": "https://toolmi.com/contato"
    },
    "license": {
      "name": "MIT",
      "url": "https://opensource.org/licenses/MIT"
    }
  },
  "servers": [
    {
      "url": "https://api.toolmi.com/v2",
      "description": "Servidor de Produção"
    },
    {
      "url": "https://staging-api.toolmi.com/v2",
      "description": "Servidor de Teste"
    }
  ],
  "paths": {
    "/ferramentas": {
      "get": {
        "summary": "Listar ferramentas disponíveis",
        "description": "Retorna uma lista paginada de todas as ferramentas disponíveis",
        "parameters": [
          {
            "name": "categoria",
            "in": "query",
            "description": "Filtrar por categoria",
            "required": false,
            "schema": {
              "type": "string",
              "enum": ["codificacao", "geracao", "formatacao", "validacao"]
            }
          },
          {
            "name": "limite",
            "in": "query",
            "description": "Número máximo de itens por página",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 1,
              "maximum": 100,
              "default": 20
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Lista de ferramentas recuperada com sucesso",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "ferramentas": {
                      "type": "array",
                      "items": {
                        "$ref": "#/components/schemas/Ferramenta"
                      }
                    },
                    "paginacao": {
                      "$ref": "#/components/schemas/Paginacao"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/ferramentas/{id}/executar": {
      "post": {
        "summary": "Executar ferramenta",
        "description": "Executa uma ferramenta específica com os parâmetros fornecidos",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "ID da ferramenta",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "description": "Parâmetros de entrada para a ferramenta",
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "entrada": {
                    "type": "string",
                    "description": "Dados de entrada para processamento"
                  },
                  "opcoes": {
                    "type": "object",
                    "description": "Opções específicas da ferramenta"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Ferramenta executada com sucesso",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ResultadoExecucao"
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "Ferramenta": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "description": "Identificador único da ferramenta"
          },
          "nome": {
            "type": "string",
            "description": "Nome da ferramenta"
          },
          "descricao": {
            "type": "string",
            "description": "Descrição da funcionalidade"
          },
          "categoria": {
            "type": "string",
            "enum": ["codificacao", "geracao", "formatacao", "validacao"]
          },
          "parametros": {
            "type": "object",
            "description": "Esquema dos parâmetros aceitos"
          }
        }
      },
      "Paginacao": {
        "type": "object",
        "properties": {
          "pagina_atual": {
            "type": "integer"
          },
          "total_paginas": {
            "type": "integer"
          },
          "total_itens": {
            "type": "integer"
          },
          "itens_por_pagina": {
            "type": "integer"
          }
        }
      },
      "ResultadoExecucao": {
        "type": "object",
        "properties": {
          "sucesso": {
            "type": "boolean"
          },
          "resultado": {
            "type": "string",
            "description": "Resultado do processamento"
          },
          "tempo_execucao": {
            "type": "number",
            "description": "Tempo de execução em milissegundos"
          },
          "metadados": {
            "type": "object",
            "description": "Informações adicionais sobre o processamento"
          }
        }
      }
    }
  }
}
```

### 2. Configuração de Aplicação

```json
{
  "aplicacao": {
    "nome": "ToolMi",
    "versao": "2.1.0",
    "ambiente": "producao",
    "debug": false,
    "configuracao": {
      "servidor": {
        "host": "0.0.0.0",
        "porta": 3000,
        "ssl": {
          "habilitado": true,
          "certificado": "/etc/ssl/certs/toolmi.crt",
          "chave_privada": "/etc/ssl/private/toolmi.key"
        }
      },
      "banco_dados": {
        "tipo": "postgresql",
        "host": "localhost",
        "porta": 5432,
        "nome": "toolmi_db",
        "usuario": "postgres",
        "pool": {
          "min": 2,
          "max": 10,
          "timeout": 5000
        }
      },
      "cache": {
        "tipo": "redis",
        "host": "localhost",
        "porta": 6379,
        "database": 0,
        "ttl": 3600
      },
      "logging": {
        "nivel": "info",
        "formato": "json",
        "saidas": ["console", "arquivo"],
        "arquivo": {
          "caminho": "/var/log/toolmi.log",
          "rotacao": "diaria",
          "retencao": 30
        }
      }
    },
    "recursos": {
      "autenticacao": {
        "habilitado": true,
        "provedor": "jwt",
        "configuracao": {
          "algoritmo": "HS256",
          "expiracao": 86400,
          "emissor": "toolmi.com"
        }
      },
      "rate_limiting": {
        "habilitado": true,
        "limite": 1000,
        "janela": 3600
      },
      "cors": {
        "habilitado": true,
        "origens": [
          "https://toolmi.com",
          "https://www.toolmi.com"
        ],
        "metodos": ["GET", "POST", "PUT", "DELETE"],
        "cabecalhos": ["Content-Type", "Authorization"]
      }
    },
    "ferramentas": [
      {
        "id": "base64",
        "nome": "Codificação Base64",
        "categoria": "codificacao",
        "habilitado": true,
        "configuracao": {
          "tamanho_maximo": 1048576,
          "formatos_suportados": ["texto", "arquivo"]
        }
      },
      {
        "id": "uuid",
        "nome": "Gerador UUID",
        "categoria": "geracao",
        "habilitado": true,
        "configuracao": {
          "versoes_suportadas": [1, 4, 5],
          "quantidade_maxima": 1000
        }
      },
      {
        "id": "json-format",
        "nome": "Formatador JSON",
        "categoria": "formatacao",
        "habilitado": true,
        "configuracao": {
          "tamanho_maximo": 2097152,
          "indentacao": 2
        }
      }
    ]
  }
}
```

### 3. Dados de Teste e Mock

```json
{
  "usuarios": [
    {
      "id": 1,
      "nome": "João Silva",
      "email": "<EMAIL>",
      "idade": 28,
      "ativo": true,
      "perfil": {
        "bio": "Desenvolvedor Full Stack apaixonado por tecnologia",
        "avatar": "https://example.com/avatars/joao.jpg",
        "redes_sociais": {
          "github": "joaosilva",
          "linkedin": "joao-silva-dev",
          "twitter": "@joaodev"
        }
      },
      "endereco": {
        "rua": "Rua das Flores, 123",
        "cidade": "São Paulo",
        "estado": "SP",
        "cep": "01234-567",
        "pais": "Brasil"
      },
      "preferencias": {
        "tema": "escuro",
        "idioma": "pt-BR",
        "notificacoes": {
          "email": true,
          "push": false,
          "sms": false
        }
      },
      "estatisticas": {
        "ferramentas_usadas": 15,
        "tempo_total_uso": 3600,
        "ferramenta_favorita": "base64",
        "ultimo_acesso": "2024-01-15T10:30:00Z"
      }
    },
    {
      "id": 2,
      "nome": "Maria Santos",
      "email": "<EMAIL>",
      "idade": 32,
      "ativo": true,
      "perfil": {
        "bio": "Designer UX/UI com foco em experiência do usuário",
        "avatar": "https://example.com/avatars/maria.jpg",
        "redes_sociais": {
          "behance": "mariasantos",
          "dribbble": "maria_ux",
          "linkedin": "maria-santos-ux"
        }
      },
      "endereco": {
        "rua": "Avenida Paulista, 456",
        "cidade": "São Paulo",
        "estado": "SP",
        "cep": "01310-100",
        "pais": "Brasil"
      },
      "preferencias": {
        "tema": "claro",
        "idioma": "pt-BR",
        "notificacoes": {
          "email": true,
          "push": true,
          "sms": false
        }
      },
      "estatisticas": {
        "ferramentas_usadas": 8,
        "tempo_total_uso": 1800,
        "ferramenta_favorita": "qr-code",
        "ultimo_acesso": "2024-01-14T16:45:00Z"
      }
    }
  ],
  "ferramentas": [
    {
      "id": "base64",
      "nome": "Codificação Base64",
      "descricao": "Codifica e decodifica dados em formato Base64",
      "categoria": "codificacao",
      "tags": ["codificacao", "base64", "texto"],
      "popularidade": 95,
      "uso_mensal": 15420,
      "avaliacao": 4.8,
      "recursos": [
        "Codificação de texto",
        "Decodificação de texto",
        "Suporte a arquivos",
        "Validação automática"
      ]
    },
    {
      "id": "uuid",
      "nome": "Gerador UUID",
      "descricao": "Gera identificadores únicos universais",
      "categoria": "geracao",
      "tags": ["uuid", "identificador", "geracao"],
      "popularidade": 87,
      "uso_mensal": 12350,
      "avaliacao": 4.7,
      "recursos": [
        "UUID v1 (baseado em tempo)",
        "UUID v4 (aleatório)",
        "UUID v5 (baseado em hash)",
        "Geração em lote"
      ]
    }
  ],
  "estatisticas_globais": {
    "total_usuarios": 25430,
    "usuarios_ativos_mes": 18920,
    "total_execucoes": 1250000,
    "ferramentas_mais_usadas": [
      {
        "id": "base64",
        "nome": "Codificação Base64",
        "uso_percentual": 28.5
      },
      {
        "id": "uuid",
        "nome": "Gerador UUID",
        "uso_percentual": 22.1
      },
      {
        "id": "json-format",
        "nome": "Formatador JSON",
        "uso_percentual": 18.7
      }
    ]
  }
}
```

## 🔧 Detalhes Técnicos

### Sintaxe JSON

**Tipos de Dados Suportados:**
- **String**: `"texto"` (sempre entre aspas duplas)
- **Number**: `123`, `45.67`, `-10`, `1.23e-4`
- **Boolean**: `true`, `false`
- **Null**: `null`
- **Array**: `[item1, item2, item3]`
- **Object**: `{"chave": "valor"}`

**Regras de Sintaxe:**
- Strings devem estar entre aspas duplas
- Não são permitidos comentários
- Vírgulas finais não são permitidas
- Chaves de objeto devem ser strings

### Validação e Erros Comuns

**Erros de Sintaxe Frequentes:**
- Aspas simples em vez de duplas
- Vírgulas finais em arrays ou objetos
- Chaves não quotadas
- Comentários não permitidos

**Exemplo de JSON Inválido:**
```json
{
  'nome': 'João',  // Aspas simples e comentário
  idade: 30,       // Chave sem aspas
  ativo: true,     // Vírgula final
}
```

**Versão Corrigida:**
```json
{
  "nome": "João",
  "idade": 30,
  "ativo": true
}
```

## 💡 Dicas de Uso

- **Validação** : Sempre valide JSON antes de usar em produção
- **Indentação** : Use 2 ou 4 espaços para melhor legibilidade
- **Compressão** : Use JSON comprimido para APIs em produção
- **Estrutura** : Mantenha estrutura consistente em dados similares

## ⚠️ Notas Importantes

- **Aspas Duplas** : JSON requer aspas duplas, não simples
- **Sem Comentários** : JSON não suporta comentários nativamente
- **Encoding** : Use UTF-8 para suporte completo a caracteres especiais
- **Tamanho** : Considere o tamanho do arquivo para performance

## 🚀 Como Usar

1. **Entrada JSON** : Cole o conteúdo JSON na área de entrada
2. **Validação Automática** : Erros de sintaxe são detectados automaticamente
3. **Seleção de Operação** : Escolha "Formatar" ou "Comprimir"
4. **Verificação dos Resultados** : O JSON processado aparece na área de saída
5. **Uso de Cópia** : Clique em "Copiar" para usar o JSON formatado

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e segurança dos seus dados JSON.
