# Ferramenta de Inversão de String

Esta ferramenta inverte strings de texto de várias maneiras diferentes. Oferece inversão simples de caracteres, inversão de palavras, inversão de linhas e outras opções avançadas para manipulação de texto, útil para desenvolvimento, testes e processamento de dados.

## ✨ Características Principais

- 🔄 **Múltiplos Tipos de Inversão** : Caracteres, palavras, linhas e mais
- 🌐 **Suporte Unicode** : Compatível com emojis e caracteres especiais
- 📋 **Cópia com Um Clique** : Resultado pode ser copiado diretamente
- ⚡ **Processamento Rápido** : Inversão instantânea de textos grandes
- 🎯 **Opções Avançadas** : Preservar espaços, maiúsculas e pontuação

## 📖 Exemplos de Uso

### Inversão Simples de Caracteres

**Texto Original:**
```
Ol<PERSON>, mundo!
```

**Texto Invertido:**
```
!odnum ,álO
```

### Inversão de Palavras

**Texto Original:**
```
Esta é uma frase de exemplo
```

**Texto Invertido:**
```
exemplo de frase uma é Esta
```

### Inversão de Linhas

**Texto Original:**
```
Primeira linha
Segunda linha
Terceira linha
```

**Texto Invertido:**
```
Terceira linha
Segunda linha
Primeira linha
```

### Inversão de Cada Palavra

**Texto Original:**
```
Programação é divertida
```

**Texto Invertido:**
```
oãçamargorP é aditrevid
```

## 🎯 Cenários de Aplicação

### 1. Desenvolvimento e Testes

```javascript
// Utilitário de inversão de strings para desenvolvimento
class InversorString {
  constructor() {
    this.opcoes = {
      preservarEspacos: false,
      preservarMaiusculas: false,
      preservarPontuacao: false,
      inverterPorPalavra: false,
      inverterLinhas: false
    };
  }

  // Inversão simples de caracteres
  inverterCaracteres(texto) {
    return texto.split('').reverse().join('');
  }

  // Inversão de palavras
  inverterPalavras(texto) {
    return texto.split(/\s+/).reverse().join(' ');
  }

  // Inversão de linhas
  inverterLinhas(texto) {
    return texto.split('\n').reverse().join('\n');
  }

  // Inversão de cada palavra individualmente
  inverterCadaPalavra(texto) {
    return texto.split(/(\s+)/).map(parte => {
      // Preservar espaços em branco
      if (/^\s+$/.test(parte)) {
        return parte;
      }
      return this.inverterCaracteres(parte);
    }).join('');
  }

  // Inversão com preservação de maiúsculas
  inverterPreservandoMaiusculas(texto) {
    const textoInvertido = this.inverterCaracteres(texto.toLowerCase());
    let resultado = '';
    
    for (let i = 0; i < textoInvertido.length; i++) {
      const charOriginal = texto[texto.length - 1 - i];
      const charInvertido = textoInvertido[i];
      
      if (charOriginal === charOriginal.toUpperCase()) {
        resultado += charInvertido.toUpperCase();
      } else {
        resultado += charInvertido;
      }
    }
    
    return resultado;
  }

  // Inversão preservando posição de espaços
  inverterPreservandoEspacos(texto) {
    const caracteres = texto.split('');
    const posicoes = [];
    const letras = [];
    
    // Identificar posições de espaços e extrair letras
    for (let i = 0; i < caracteres.length; i++) {
      if (caracteres[i] === ' ') {
        posicoes.push(i);
      } else {
        letras.push(caracteres[i]);
      }
    }
    
    // Inverter apenas as letras
    letras.reverse();
    
    // Reconstruir string com espaços nas posições originais
    const resultado = [];
    let indiceLetra = 0;
    
    for (let i = 0; i < caracteres.length; i++) {
      if (posicoes.includes(i)) {
        resultado.push(' ');
      } else {
        resultado.push(letras[indiceLetra++]);
      }
    }
    
    return resultado.join('');
  }

  // Inversão com múltiplas opções
  inverter(texto, opcoes = {}) {
    const config = { ...this.opcoes, ...opcoes };
    let resultado = texto;

    if (config.inverterLinhas) {
      resultado = this.inverterLinhas(resultado);
    } else if (config.inverterPorPalavra) {
      resultado = this.inverterCadaPalavra(resultado);
    } else if (config.preservarEspacos) {
      resultado = this.inverterPreservandoEspacos(resultado);
    } else if (config.preservarMaiusculas) {
      resultado = this.inverterPreservandoMaiusculas(resultado);
    } else {
      resultado = this.inverterCaracteres(resultado);
    }

    return resultado;
  }

  // Gerar casos de teste
  gerarCasosTeste(texto) {
    return {
      original: texto,
      caracteresSimples: this.inverterCaracteres(texto),
      palavras: this.inverterPalavras(texto),
      linhas: this.inverterLinhas(texto),
      cadaPalavra: this.inverterCadaPalavra(texto),
      preservandoEspacos: this.inverterPreservandoEspacos(texto),
      preservandoMaiusculas: this.inverterPreservandoMaiusculas(texto)
    };
  }

  // Validar palíndromo
  ehPalindromo(texto) {
    const textoLimpo = texto.toLowerCase().replace(/[^a-záàâãéêíóôõúç]/g, '');
    const textoInvertido = this.inverterCaracteres(textoLimpo);
    return textoLimpo === textoInvertido;
  }

  // Encontrar palíndromos em texto
  encontrarPalindromos(texto, tamanhoMinimo = 3) {
    const palavras = texto.toLowerCase().match(/[a-záàâãéêíóôõúç]+/g) || [];
    const palindromos = [];

    for (const palavra of palavras) {
      if (palavra.length >= tamanhoMinimo && this.ehPalindromo(palavra)) {
        palindromos.push(palavra);
      }
    }

    return [...new Set(palindromos)]; // Remover duplicatas
  }
}

// Exemplo de uso em testes
class TestadorString {
  constructor() {
    this.inversor = new InversorString();
  }

  // Testar função de validação de email (exemplo)
  testarValidacaoEmail() {
    const emailsValidos = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    const emailsInvalidos = [
      'email-invalido',
      '@dominio.com',
      'usuario@'
    ];

    console.log('=== Teste de Validação de Email ===');
    
    // Testar emails válidos
    emailsValidos.forEach(email => {
      const emailInvertido = this.inversor.inverterCaracteres(email);
      console.log(`Original: ${email}`);
      console.log(`Invertido: ${emailInvertido}`);
      console.log(`É palíndromo: ${this.inversor.ehPalindromo(email)}`);
      console.log('---');
    });
  }

  // Testar robustez de função com strings invertidas
  testarRobustezFuncao(funcao, entradas) {
    const resultados = [];

    for (const entrada of entradas) {
      const casos = this.inversor.gerarCasosTeste(entrada);
      
      for (const [tipo, texto] of Object.entries(casos)) {
        try {
          const resultado = funcao(texto);
          resultados.push({
            tipo,
            entrada: texto,
            resultado,
            sucesso: true
          });
        } catch (erro) {
          resultados.push({
            tipo,
            entrada: texto,
            erro: erro.message,
            sucesso: false
          });
        }
      }
    }

    return resultados;
  }

  // Gerar dados de teste aleatórios
  gerarDadosTeste(quantidade = 10) {
    const caracteres = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 .,!?';
    const dados = [];

    for (let i = 0; i < quantidade; i++) {
      let texto = '';
      const tamanho = Math.floor(Math.random() * 50) + 5;
      
      for (let j = 0; j < tamanho; j++) {
        texto += caracteres[Math.floor(Math.random() * caracteres.length)];
      }
      
      dados.push(texto);
    }

    return dados;
  }
}

// Exemplo de uso
const testador = new TestadorString();

// Testar função de processamento de texto
function processarTexto(texto) {
  return texto.trim().toLowerCase().replace(/\s+/g, ' ');
}

const dadosTeste = [
  '  Texto com espaços  ',
  'TEXTO EM MAIÚSCULAS',
  'texto\ncom\nquebras\nde\nlinha'
];

const resultados = testador.testarRobustezFuncao(processarTexto, dadosTeste);
console.log('Resultados dos testes:', resultados);

// Encontrar palíndromos
const texto = 'A arara azul voou para o radar da Ana. Ovo e osso são palavras interessantes.';
const palindromos = testador.inversor.encontrarPalindromos(texto);
console.log('Palíndromos encontrados:', palindromos);
```

### 2. Processamento de Dados

```python
# Processador de dados com inversão de strings
import re
from typing import List, Dict, Any

class ProcessadorTexto:
    def __init__(self):
        self.estatisticas = {
            'total_processado': 0,
            'palindromos_encontrados': 0,
            'inversoes_realizadas': 0
        }
    
    def inverter_caracteres(self, texto: str) -> str:
        """Inverte a ordem dos caracteres"""
        return texto[::-1]
    
    def inverter_palavras(self, texto: str) -> str:
        """Inverte a ordem das palavras"""
        return ' '.join(texto.split()[::-1])
    
    def inverter_linhas(self, texto: str) -> str:
        """Inverte a ordem das linhas"""
        return '\n'.join(texto.split('\n')[::-1])
    
    def inverter_cada_palavra(self, texto: str) -> str:
        """Inverte cada palavra individualmente"""
        palavras = texto.split()
        palavras_invertidas = [self.inverter_caracteres(palavra) for palavra in palavras]
        return ' '.join(palavras_invertidas)
    
    def eh_palindromo(self, texto: str) -> bool:
        """Verifica se o texto é um palíndromo"""
        texto_limpo = re.sub(r'[^a-záàâãéêíóôõúç]', '', texto.lower())
        return texto_limpo == texto_limpo[::-1]
    
    def encontrar_palindromos(self, texto: str, tamanho_minimo: int = 3) -> List[str]:
        """Encontra palíndromos no texto"""
        palavras = re.findall(r'[a-záàâãéêíóôõúç]+', texto.lower())
        palindromos = []
        
        for palavra in palavras:
            if len(palavra) >= tamanho_minimo and self.eh_palindromo(palavra):
                palindromos.append(palavra)
        
        return list(set(palindromos))  # Remove duplicatas
    
    def processar_arquivo_csv(self, caminho_arquivo: str, coluna_texto: str) -> Dict[str, Any]:
        """Processa arquivo CSV aplicando inversões"""
        import pandas as pd
        
        df = pd.read_csv(caminho_arquivo)
        resultados = []
        
        for index, row in df.iterrows():
            texto_original = str(row[coluna_texto])
            
            resultado = {
                'linha': index + 1,
                'texto_original': texto_original,
                'caracteres_invertidos': self.inverter_caracteres(texto_original),
                'palavras_invertidas': self.inverter_palavras(texto_original),
                'eh_palindromo': self.eh_palindromo(texto_original),
                'palindromos_encontrados': self.encontrar_palindromos(texto_original)
            }
            
            resultados.append(resultado)
            self.estatisticas['total_processado'] += 1
            self.estatisticas['inversoes_realizadas'] += 2
            
            if resultado['eh_palindromo']:
                self.estatisticas['palindromos_encontrados'] += 1
        
        return {
            'resultados': resultados,
            'estatisticas': self.estatisticas
        }
    
    def gerar_relatorio_analise(self, texto: str) -> Dict[str, Any]:
        """Gera relatório completo de análise de texto"""
        palindromos = self.encontrar_palindromos(texto)
        
        # Análise de frequência de caracteres
        freq_chars = {}
        for char in texto.lower():
            if char.isalpha():
                freq_chars[char] = freq_chars.get(char, 0) + 1
        
        # Análise de palavras
        palavras = re.findall(r'\b\w+\b', texto.lower())
        freq_palavras = {}
        for palavra in palavras:
            freq_palavras[palavra] = freq_palavras.get(palavra, 0) + 1
        
        return {
            'texto_original': texto,
            'caracteres_invertidos': self.inverter_caracteres(texto),
            'palavras_invertidas': self.inverter_palavras(texto),
            'linhas_invertidas': self.inverter_linhas(texto),
            'cada_palavra_invertida': self.inverter_cada_palavra(texto),
            'estatisticas': {
                'total_caracteres': len(texto),
                'total_palavras': len(palavras),
                'total_linhas': len(texto.split('\n')),
                'palindromos_encontrados': len(palindromos),
                'palindromos': palindromos
            },
            'frequencia_caracteres': dict(sorted(freq_chars.items(), key=lambda x: x[1], reverse=True)[:10]),
            'frequencia_palavras': dict(sorted(freq_palavras.items(), key=lambda x: x[1], reverse=True)[:10])
        }
    
    def limpar_estatisticas(self):
        """Limpa as estatísticas acumuladas"""
        self.estatisticas = {
            'total_processado': 0,
            'palindromos_encontrados': 0,
            'inversoes_realizadas': 0
        }

# Exemplo de uso
processador = ProcessadorTexto()

# Texto de exemplo
texto_exemplo = """
A programação é uma arte que requer lógica e criatividade.
Radar, arara e ovo são exemplos de palíndromos.
Desenvolver software é como construir uma casa:
precisa de uma base sólida e atenção aos detalhes.
"""

# Gerar relatório completo
relatorio = processador.gerar_relatorio_analise(texto_exemplo)

print("=== RELATÓRIO DE ANÁLISE DE TEXTO ===")
print(f"Texto original: {relatorio['texto_original'][:100]}...")
print(f"Total de caracteres: {relatorio['estatisticas']['total_caracteres']}")
print(f"Total de palavras: {relatorio['estatisticas']['total_palavras']}")
print(f"Palíndromos encontrados: {relatorio['estatisticas']['palindromos']}")
print(f"Caracteres mais frequentes: {list(relatorio['frequencia_caracteres'].keys())[:5]}")
```

### 3. Criptografia Simples

```javascript
// Sistema de criptografia simples usando inversão
class CriptografiaInversao {
  constructor() {
    this.chaveRotacao = 13; // ROT13 padrão
  }

  // Criptografia por inversão simples
  criptografarInversao(texto) {
    return texto.split('').reverse().join('');
  }

  // Criptografia por inversão de palavras
  criptografarPalavras(texto) {
    return texto.split(' ').reverse().join(' ');
  }

  // Criptografia combinada: ROT13 + Inversão
  criptografarCombinada(texto) {
    // Aplicar ROT13
    const textoRot13 = this.aplicarRot13(texto);
    
    // Aplicar inversão
    return this.criptografarInversao(textoRot13);
  }

  // Aplicar cifra ROT13
  aplicarRot13(texto) {
    return texto.replace(/[a-zA-Z]/g, char => {
      const inicio = char <= 'Z' ? 65 : 97;
      return String.fromCharCode(((char.charCodeAt(0) - inicio + 13) % 26) + inicio);
    });
  }

  // Descriptografar inversão simples
  descriptografarInversao(textoCriptografado) {
    return textoCriptografado.split('').reverse().join('');
  }

  // Descriptografar palavras
  descriptografarPalavras(textoCriptografado) {
    return textoCriptografado.split(' ').reverse().join(' ');
  }

  // Descriptografar combinada
  descriptografarCombinada(textoCriptografado) {
    // Reverter inversão
    const textoInvertido = this.descriptografarInversao(textoCriptografado);
    
    // Reverter ROT13
    return this.aplicarRot13(textoInvertido);
  }

  // Criptografia por blocos com inversão
  criptografarBlocos(texto, tamanhoBloco = 5) {
    const blocos = [];
    
    for (let i = 0; i < texto.length; i += tamanhoBloco) {
      const bloco = texto.slice(i, i + tamanhoBloco);
      blocos.push(this.criptografarInversao(bloco));
    }
    
    return blocos.join('|');
  }

  // Descriptografar blocos
  descriptografarBlocos(textoCriptografado) {
    const blocos = textoCriptografado.split('|');
    
    return blocos.map(bloco => 
      this.descriptografarInversao(bloco)
    ).join('');
  }

  // Gerar chave baseada em texto
  gerarChave(textoChave) {
    let soma = 0;
    for (let i = 0; i < textoChave.length; i++) {
      soma += textoChave.charCodeAt(i);
    }
    return soma % 26;
  }

  // Criptografia com chave personalizada
  criptografarComChave(texto, chave) {
    const rotacao = this.gerarChave(chave);
    
    // Aplicar rotação personalizada
    const textoRotacionado = texto.replace(/[a-zA-Z]/g, char => {
      const inicio = char <= 'Z' ? 65 : 97;
      return String.fromCharCode(((char.charCodeAt(0) - inicio + rotacao) % 26) + inicio);
    });
    
    // Aplicar inversão
    return this.criptografarInversao(textoRotacionado);
  }

  // Descriptografar com chave personalizada
  descriptografarComChave(textoCriptografado, chave) {
    const rotacao = this.gerarChave(chave);
    
    // Reverter inversão
    const textoInvertido = this.descriptografarInversao(textoCriptografado);
    
    // Reverter rotação
    return textoInvertido.replace(/[a-zA-Z]/g, char => {
      const inicio = char <= 'Z' ? 65 : 97;
      return String.fromCharCode(((char.charCodeAt(0) - inicio - rotacao + 26) % 26) + inicio);
    });
  }

  // Análise de frequência para quebra de cifra
  analisarFrequencia(texto) {
    const frequencia = {};
    const textoLimpo = texto.toLowerCase().replace(/[^a-z]/g, '');
    
    for (const char of textoLimpo) {
      frequencia[char] = (frequencia[char] || 0) + 1;
    }
    
    // Ordenar por frequência
    return Object.entries(frequencia)
      .sort(([,a], [,b]) => b - a)
      .reduce((obj, [char, freq]) => {
        obj[char] = freq;
        return obj;
      }, {});
  }

  // Tentar quebrar cifra simples
  tentarQuebrarCifra(textoCriptografado) {
    const tentativas = [];
    
    // Tentar apenas inversão
    tentativas.push({
      metodo: 'Inversão simples',
      resultado: this.descriptografarInversao(textoCriptografado)
    });
    
    // Tentar ROT13 + inversão
    tentativas.push({
      metodo: 'ROT13 + Inversão',
      resultado: this.descriptografarCombinada(textoCriptografado)
    });
    
    // Tentar diferentes rotações
    for (let i = 1; i < 26; i++) {
      const textoInvertido = this.descriptografarInversao(textoCriptografado);
      const resultado = textoInvertido.replace(/[a-zA-Z]/g, char => {
        const inicio = char <= 'Z' ? 65 : 97;
        return String.fromCharCode(((char.charCodeAt(0) - inicio - i + 26) % 26) + inicio);
      });
      
      tentativas.push({
        metodo: `ROT${i} + Inversão`,
        resultado: resultado
      });
    }
    
    return tentativas;
  }
}

// Exemplo de uso
const cripto = new CriptografiaInversao();

const mensagemSecreta = "Esta é uma mensagem secreta que precisa ser protegida!";
console.log("Mensagem original:", mensagemSecreta);

// Diferentes métodos de criptografia
const criptografias = {
  inversao: cripto.criptografarInversao(mensagemSecreta),
  palavras: cripto.criptografarPalavras(mensagemSecreta),
  combinada: cripto.criptografarCombinada(mensagemSecreta),
  blocos: cripto.criptografarBlocos(mensagemSecreta),
  comChave: cripto.criptografarComChave(mensagemSecreta, "minhaChave")
};

console.log("\n=== MENSAGENS CRIPTOGRAFADAS ===");
Object.entries(criptografias).forEach(([metodo, texto]) => {
  console.log(`${metodo}: ${texto}`);
});

// Descriptografar
console.log("\n=== MENSAGENS DESCRIPTOGRAFADAS ===");
console.log("Inversão:", cripto.descriptografarInversao(criptografias.inversao));
console.log("Palavras:", cripto.descriptografarPalavras(criptografias.palavras));
console.log("Combinada:", cripto.descriptografarCombinada(criptografias.combinada));
console.log("Blocos:", cripto.descriptografarBlocos(criptografias.blocos));
console.log("Com chave:", cripto.descriptografarComChave(criptografias.comChave, "minhaChave"));
```

## 🔧 Detalhes Técnicos

### Tipos de Inversão Suportados

**Inversão de Caracteres:**
- Inverte a ordem de todos os caracteres
- Preserva caracteres especiais e espaços
- Exemplo: "abc" → "cba"

**Inversão de Palavras:**
- Inverte a ordem das palavras
- Mantém espaços entre palavras
- Exemplo: "olá mundo" → "mundo olá"

**Inversão de Linhas:**
- Inverte a ordem das linhas
- Preserva conteúdo de cada linha
- Útil para arquivos de texto

**Inversão por Palavra:**
- Inverte cada palavra individualmente
- Mantém posição das palavras
- Exemplo: "olá mundo" → "álo odnum"

### Tratamento de Caracteres Especiais

**Unicode e Emojis:**
- Suporte completo a caracteres Unicode
- Preservação de emojis e símbolos
- Tratamento correto de caracteres compostos

**Espaços e Pontuação:**
- Opção para preservar posição de espaços
- Tratamento especial de pontuação
- Manutenção de formatação

## 💡 Dicas de Uso

- **Testes** : Use para gerar casos de teste com dados invertidos
- **Validação** : Teste robustez de funções com entradas invertidas
- **Palíndromos** : Identifique palíndromos em textos
- **Criptografia** : Use como base para algoritmos simples de criptografia

## ⚠️ Notas Importantes

- **Unicode** : Alguns caracteres compostos podem não inverter corretamente
- **Performance** : Textos muito grandes podem demorar para processar
- **Encoding** : Certifique-se de usar UTF-8 para caracteres especiais
- **Segurança** : Inversão simples não é segura para dados sensíveis

## 🚀 Como Usar

1. **Entrada de Texto** : Cole ou digite o texto que deseja inverter
2. **Seleção de Tipo** : Escolha o tipo de inversão desejado
3. **Opções Avançadas** : Configure opções como preservar espaços
4. **Verificação** : Revise o resultado da inversão
5. **Uso de Cópia** : Clique em "Copiar" para usar o texto invertido

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e rapidez no processamento.
