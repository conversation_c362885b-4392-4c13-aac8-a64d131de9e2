# Ferramenta de Análise JWT

JWT (JSON Web Token) é um formato de token compacto e seguro para URL usado para transferir informações de forma segura entre partes. É principalmente usado em autenticação web, autenticação de API, troca de informações e outros cenários, sendo uma das tecnologias importantes do desenvolvimento web moderno.

## ✨ Características Principais

- 🔍 **Análise Completa** : Análise de<PERSON>er, Payload e Signature
- ✅ **Validação de Formato** : Verificação automática da correção da estrutura JWT
- 📊 **Visualização** : Informações estruturadas exibidas em formato JSON
- 🔧 **Suporte de Debug** : Verificação e debug de tokens durante o desenvolvimento
- 📋 **Cópia com Um Clique** : Os resultados de análise podem ser copiados diretamente

## 📖 Exemplos de Uso

### JWT Padrão

**JWT de Entrada:**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvYW8gU2lsdmEiLCJpYXQiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

**Resultado da Análise:**

**Header:**
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

**Payload:**
```json
{
  "sub": "1234567890",
  "name": "João Silva",
  "iat": 1516239022
}
```

**Signature:**
```
SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

### Payload Complexo

**JWT de Entrada (Claims Complexos):**
```
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.signature_here
```

**Resultado da Análise:**

**Header:**
```json
{
  "alg": "RS256",
  "typ": "JWT",
  "kid": "12345"
}
```

**Payload:**
```json
{
  "iss": "https://auth.toolmi.com",
  "sub": "user_12345",
  "aud": ["api.toolmi.com", "admin.toolmi.com"],
  "exp": 1687536000,
  "iat": 1687449600,
  "nbf": 1687449600,
  "jti": "abcdef-123456",
  "name": "Maria Silva",
  "email": "<EMAIL>",
  "roles": ["user", "admin"],
  "permissions": ["read", "write", "delete"]
}
```

## 🎯 Cenários de Aplicação

### 1. Sistema de Autenticação Web

Autenticação de usuários usando JWT:

```javascript
// Processo de login
const fazerLogin = async (email, senha) => {
  try {
    const resposta = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, senha })
    });

    if (resposta.ok) {
      const dados = await resposta.json();
      const token = dados.token;
      
      // Salvar JWT no armazenamento local
      localStorage.setItem('authToken', token);
      
      // Analisar conteúdo do token
      const payload = analisarJWT(token);
      console.log('Informações do usuário:', payload);
      
      return { sucesso: true, usuario: payload };
    } else {
      throw new Error('Erro de login');
    }
  } catch (erro) {
    console.error('Erro de login:', erro);
    return { sucesso: false, erro: erro.message };
  }
};

// Função de análise JWT
const analisarJWT = (token) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    return JSON.parse(jsonPayload);
  } catch (erro) {
    console.error('Erro de análise JWT:', erro);
    return null;
  }
};

// Verificar estado de autenticação
const verificarEstadoAuth = () => {
  const token = localStorage.getItem('authToken');
  if (!token) {
    return { autenticado: false };
  }

  const payload = analisarJWT(token);
  if (!payload) {
    return { autenticado: false };
  }

  // Verificar data de expiração do token
  const tempoAtual = Math.floor(Date.now() / 1000);
  if (payload.exp && payload.exp < tempoAtual) {
    localStorage.removeItem('authToken');
    return { autenticado: false, motivo: 'expirado' };
  }

  return { 
    autenticado: true, 
    usuario: payload,
    expiraEm: payload.exp 
  };
};
```

### 2. Autenticação de API

Uso de JWT em requisições de API:

```javascript
// Classe cliente API
class ClienteAPI {
  constructor(urlBase) {
    this.urlBase = urlBase;
    this.token = localStorage.getItem('authToken');
  }

  // Requisição com cabeçalho de autenticação
  async requisicao(endpoint, opcoes = {}) {
    const url = `${this.urlBase}${endpoint}`;
    const cabecalhos = {
      'Content-Type': 'application/json',
      ...opcoes.cabecalhos
    };

    // Adicionar token JWT ao cabeçalho Authorization
    if (this.token) {
      cabecalhos.Authorization = `Bearer ${this.token}`;
    }

    try {
      const resposta = await fetch(url, {
        ...opcoes,
        headers: cabecalhos
      });

      // Se erro 401, token é inválido
      if (resposta.status === 401) {
        this.lidarComNaoAutorizado();
        throw new Error('Autenticação necessária');
      }

      if (!resposta.ok) {
        throw new Error(`Erro HTTP! status: ${resposta.status}`);
      }

      return await resposta.json();
    } catch (erro) {
      console.error('Erro de requisição API:', erro);
      throw erro;
    }
  }

  // Lidar com erro de autenticação
  lidarComNaoAutorizado() {
    localStorage.removeItem('authToken');
    this.token = null;
    // Redirecionar para página de login
    window.location.href = '/login';
  }

  // Atualizar token
  atualizarToken(novoToken) {
    this.token = novoToken;
    localStorage.setItem('authToken', novoToken);
  }

  // Obter perfil do usuário
  async obterPerfilUsuario() {
    return await this.requisicao('/api/user/profile');
  }

  // Obter dados
  async obterDados(endpoint) {
    return await this.requisicao(endpoint);
  }

  // Atualizar dados
  async atualizarDados(endpoint, dados) {
    return await this.requisicao(endpoint, {
      method: 'PUT',
      body: JSON.stringify(dados)
    });
  }
}

// Exemplo de uso
const clienteApi = new ClienteAPI('https://api.toolmi.com');

// Obter perfil do usuário
clienteApi.obterPerfilUsuario()
  .then(perfil => {
    console.log('Perfil do usuário:', perfil);
  })
  .catch(erro => {
    console.error('Erro ao obter perfil:', erro);
  });
```

## 🔧 Detalhes Técnicos

### Estrutura JWT

JWT é composto por três partes:

**1. Header (Cabeçalho)**
```json
{
  "alg": "HS256",    // Algoritmo de assinatura
  "typ": "JWT"       // Tipo de token
}
```

**2. Payload (Carga útil)**
```json
{
  "iss": "issuer",           // Emissor
  "sub": "subject",          // Assunto
  "aud": "audience",         // Audiência
  "exp": 1234567890,         // Data de expiração
  "iat": 1234567890,         // Tempo de emissão
  "nbf": 1234567890,         // Não válido antes
  "jti": "jwt-id"            // ID JWT
}
```

**3. Signature (Assinatura)**
```
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret
)
```

### Claims Padrão

Claims padrão usados em JWT:

- **iss (Issuer)** : Emissor do token
- **sub (Subject)** : Assunto do token (geralmente ID do usuário)
- **aud (Audience)** : Audiência do token
- **exp (Expiration Time)** : Data de expiração (timestamp Unix)
- **iat (Issued At)** : Tempo de emissão
- **nbf (Not Before)** : Tempo de início de validade
- **jti (JWT ID)** : Identificador único do JWT

## 💡 Dicas de Uso

- **Verificação de Expiração** : Verificar data de expiração do token com o claim exp
- **Segurança** : Não incluir informações confidenciais no payload
- **Verificação de Assinatura** : Sempre verificar assinatura em ambiente de produção
- **Tamanho do Token** : Prestar atenção para que o payload não seja muito volumoso

## ⚠️ Notas Importantes

- **Informações Confidenciais** : O payload do JWT não é criptografado, portanto não deve incluir informações confidenciais
- **Verificação de Assinatura** : Esta ferramenta apenas analisa, não verifica assinaturas
- **Data de Expiração** : Não usar tokens expirados
- **Local de Armazenamento** : Prestar atenção ao local de armazenamento de tokens no navegador (prevenção XSS)

## 🚀 Como Usar

1. **Entrada JWT** : Cole o token JWT a ser analisado na área de entrada
2. **Análise Automática** : Header, Payload e Signature são analisados automaticamente durante a entrada
3. **Verificação dos Resultados** : Verifique as informações detalhadas de cada parte em formato JSON
4. **Uso de Cópia** : Use o botão "Copiar" para copiar os resultados de análise para a área de transferência
5. **Uso em Debug** : Use para verificação e debug de tokens durante o desenvolvimento

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia tokens JWT para o servidor, garantindo segurança.
