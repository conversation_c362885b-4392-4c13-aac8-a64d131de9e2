# Ferramenta de Codificação e Decodificação Base64

Base64 é um método de codificação usado para representar dados binários em forma de texto. É principalmente usado no envio de e-mails, desenvolvimento web, armazenamento de dados e outros cenários para transferir e armazenar com segurança dados binários em forma de texto.

## ✨ Características Principais

- 🔄 **Conversão Bidirecional** : Compatível com codificação e decodificação Base64 de texto
- 🌐 **Suporte Multilíngue** : Compatível com caracteres Unicode como português, chinês, inglês
- 📁 **Processamento de Arquivos** : Conversão Base64 de arquivos como imagens e documentos
- 📋 **Cópia com Um Clique** : Os resultados de conversão podem ser copiados diretamente para uso
- ⚡ **Conversão em Tempo Real** : Exibe resultados ao mesmo tempo que a entrada

## 📖 Exemplos de Uso

### Codificação de Texto

**Texto de Entrada:**
```
Olá, ToolMi!
```

**Resultado de Codificação Base64:**
```
T2zDoSwgVG9vbE1pIQ==
```

### Codificação de URL

**URL de Entrada:**
```
https://www.toolmi.com/pesquisar?q=ferramenta Base64
```

**Resultado de Codificação Base64:**
```
aHR0cHM6Ly93d3cudG9vbG1pLmNvbS9wZXNxdWlzYXI/cT1mZXJyYW1lbnRhIEJhc2U2NA==
```

### Codificação de Dados JSON

**JSON de Entrada:**
```json
{
  "nome": "João Silva",
  "email": "<EMAIL>",
  "idade": 30
}
```

**Resultado de Codificação Base64:**
```
ewogICJub21lIjogIkpvw6NvIFNpbHZhIiwKICAiZW1haWwiOiAiam9hb0BleGFtcGxlLmNvbSIsCiAgImlkYWRlIjogMzAKfQ==
```

## 🎯 Cenários de Aplicação

### 1. Desenvolvimento Web

Exemplos de uso de Base64 no desenvolvimento web:

```html
<!-- Incorporação de imagem Base64 -->
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA6VP8EQAAAABJRU5ErkJggg==" alt="Imagem transparente 1x1">

<!-- Incorporação de fonte Base64 em CSS -->
@font-face {
  font-family: 'FontePersonalizada';
  src: url('data:font/woff2;base64,d09GMgABAAAAAA...') format('woff2');
}

<!-- Processamento de dados Base64 em JavaScript -->
<script>
// Codificação Base64
const codificar = btoa('Olá!');
console.log(codificar); // T2zDoSE=

// Decodificação Base64
const decodificar = atob(codificar);
console.log(decodificar); // Olá!
</script>
```

### 2. Desenvolvimento de API

Exemplo de uso de Base64 na comunicação API:

```javascript
// API de upload de arquivos
const uploadArquivo = async (arquivo) => {
  const leitor = new FileReader();
  
  return new Promise((resolver, rejeitar) => {
    leitor.onload = async () => {
      const dadosBase64 = leitor.result.split(',')[1];
      
      try {
        const resposta = await fetch('/api/upload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            nomeArquivo: arquivo.name,
            dados: dadosBase64,
            tipoMime: arquivo.type
          })
        });
        
        const resultado = await resposta.json();
        resolver(resultado);
      } catch (erro) {
        rejeitar(erro);
      }
    };
    
    leitor.onerror = rejeitar;
    leitor.readAsDataURL(arquivo);
  });
};

// Geração de cabeçalho de autenticação
const criarCabecalhoAuth = (usuario, senha) => {
  const credenciais = `${usuario}:${senha}`;
  const codificado = btoa(credenciais);
  return `Basic ${codificado}`;
};

// Exemplo de uso
const cabecalhoAuth = criarCabecalhoAuth('<EMAIL>', 'senha123');
console.log(cabecalhoAuth); // Basic ****************************************
```

### 3. Armazenamento de Dados

Uso de Base64 no armazenamento local:

```javascript
// Salvar dados de configuração
const salvarConfigUsuario = (configuracao) => {
  const configuracaoJson = JSON.stringify(configuracao);
  const configuracaoCodificada = btoa(configuracaoJson);
  localStorage.setItem('configuracaoUsuario', configuracaoCodificada);
};

// Carregar dados de configuração
const carregarConfigUsuario = () => {
  const configuracaoCodificada = localStorage.getItem('configuracaoUsuario');
  if (configuracaoCodificada) {
    try {
      const configuracaoJson = atob(configuracaoCodificada);
      return JSON.parse(configuracaoJson);
    } catch (erro) {
      console.error('Erro ao carregar configuração:', erro);
      return null;
    }
  }
  return null;
};

// Exemplo de uso
const configuracaoUsuario = {
  tema: 'escuro',
  idioma: 'pt',
  notificacoes: true
};

salvarConfigUsuario(configuracaoUsuario);
const configuracaoCarregada = carregarConfigUsuario();
console.log(configuracaoCarregada);
```

## 🔧 Detalhes Técnicos

### Princípio de Codificação Base64

Funcionamento da codificação Base64:

**Conjunto de Caracteres:**
- A-Z (26 caracteres)
- a-z (26 caracteres)  
- 0-9 (10 caracteres)
- +, / (2 caracteres)
- = (caractere de preenchimento)

**Etapas de Codificação:**
1. Ler dados de entrada em grupos de 8 bits
2. Dividir 3 bytes (24 bits) em 4 grupos de 6 bits
3. Converter cada grupo de 6 bits no caractere Base64 correspondente
4. Adicionar caracteres de preenchimento "=" se necessário

### Codificação de Caracteres

Processamento de caracteres portugueses:

```javascript
// Conversão Base64 com codificação UTF-8
function codificarUTF8ParaBase64(str) {
  // Converter string para array de bytes UTF-8
  const bytesUtf8 = new TextEncoder().encode(str);
  
  // Converter array de bytes para string
  let binario = '';
  bytesUtf8.forEach(byte => {
    binario += String.fromCharCode(byte);
  });
  
  // Codificação Base64
  return btoa(binario);
}

function decodificarBase64ParaUtf8(base64) {
  // Decodificação Base64
  const binario = atob(base64);
  
  // Converter string para array de bytes
  const bytes = new Uint8Array(binario.length);
  for (let i = 0; i < binario.length; i++) {
    bytes[i] = binario.charCodeAt(i);
  }
  
  // Decodificação UTF-8
  return new TextDecoder().decode(bytes);
}

// Exemplo de uso
const original = "Olá, mundo!";
const codificado = codificarUTF8ParaBase64(original);
const decodificado = decodificarBase64ParaUtf8(codificado);

console.log('String original:', original);
console.log('Resultado codificado:', codificado);
console.log('Resultado decodificado:', decodificado);
```

## 💡 Dicas de Uso

- **Codificação de Caracteres** : Prestar atenção à codificação UTF-8 ao processar caracteres portugueses
- **Tamanho dos Dados** : O tamanho após codificação Base64 é aproximadamente 1,33 vezes o dos dados originais
- **Performance** : Prestar atenção ao uso de memória ao processar arquivos grandes
- **Segurança** : Base64 não é criptografia, tem apenas efeito de ofuscação de dados

## ⚠️ Notas Importantes

- **Segurança** : Base64 não é criptografia, portanto não é adequado para proteger dados confidenciais
- **Aumento de Tamanho** : O tamanho dos dados após codificação aumenta cerca de 33%
- **Limitações de Caracteres** : Alguns sistemas podem ter limitações em strings Base64 longas
- **Processamento de Quebras de Linha** : Diferentes sistemas podem lidar com quebras de linha em strings Base64 de forma diferente

## 🚀 Como Usar

1. **Entrada de Texto** : Digite o texto a ser codificado/decodificado na área de entrada
2. **Seleção de Operação** : Clique no botão "Codificar" ou "Decodificar"
3. **Verificação dos Resultados** : Os resultados de conversão são exibidos na área de saída
4. **Uso de Cópia** : Clique no botão "Copiar" para copiar os resultados para a área de transferência
5. **Processamento de Arquivos** : Arraste e solte arquivos para conversão Base64

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e segurança.
