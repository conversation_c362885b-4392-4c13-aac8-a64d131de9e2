# Seletor de Cores

Esta ferramenta fornece seleção, conversão e análise de cores em múltiplos formatos. Essencial para designers, desenvolvedores web e qualquer pessoa que trabalhe com cores digitais, oferecendo conversão entre HEX, RGB, HSL, HSV e outros formatos de cor.

## ✨ Características Principais

- 🎨 **Seletor Visual** : Interface intuitiva para seleção de cores
- 🔄 **Conversão Múltipla** : HEX, RGB, HSL, HSV, CMYK e mais
- 🎯 **Paletas de Cores** : Geração de paletas harmoniosas
- 📋 **Cópia com Um Clique** : Códigos de cor podem ser copiados diretamente
- 🌈 **Análise de Cor** : Informações detalhadas sobre luminosidade e contraste

## 📖 Exemplos de Uso

### Conversão de Formatos de Cor

**Cor Base:** Azul Médio

**Formatos Disponíveis:**
- **HEX**: `#3498db`
- **RGB**: `rgb(52, 152, 219)`
- **HSL**: `hsl(204, 70%, 53%)`
- **HSV**: `hsv(204, 76%, 86%)`
- **CMYK**: `cmyk(76%, 31%, 0%, 14%)`

### Paleta de Cores Harmoniosas

**Cor Principal:** `#e74c3c` (Vermelho)

**Paleta Complementar:**
- **Principal**: `#e74c3c`
- **Complementar**: `#3ce7a8`
- **Análoga 1**: `#e7a83c`
- **Análoga 2**: `#a83ce7`
- **Triádica 1**: `#3ce74c`
- **Triádica 2**: `#4c3ce7`

## 🎯 Cenários de Aplicação

### 1. Design de Interface

```css
/* Sistema de cores para interface */
:root {
  /* Cores Primárias */
  --cor-primaria: #3498db;
  --cor-primaria-escura: #2980b9;
  --cor-primaria-clara: #5dade2;
  
  /* Cores Secundárias */
  --cor-secundaria: #e74c3c;
  --cor-secundaria-escura: #c0392b;
  --cor-secundaria-clara: #ec7063;
  
  /* Cores Neutras */
  --cor-texto-primario: #2c3e50;
  --cor-texto-secundario: #7f8c8d;
  --cor-fundo: #ecf0f1;
  --cor-fundo-escuro: #34495e;
  
  /* Cores de Estado */
  --cor-sucesso: #27ae60;
  --cor-aviso: #f39c12;
  --cor-erro: #e74c3c;
  --cor-info: #3498db;
  
  /* Gradientes */
  --gradiente-primario: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradiente-secundario: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradiente-neutro: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* Componentes com sistema de cores */
.botao-primario {
  background-color: var(--cor-primaria);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.botao-primario:hover {
  background-color: var(--cor-primaria-escura);
}

.cartao {
  background-color: white;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.alerta-sucesso {
  background-color: rgba(39, 174, 96, 0.1);
  border-left: 4px solid var(--cor-sucesso);
  color: var(--cor-sucesso);
  padding: 16px;
  border-radius: 4px;
}

.tema-escuro {
  --cor-fundo: #2c3e50;
  --cor-texto-primario: #ecf0f1;
  --cor-texto-secundario: #bdc3c7;
}
```

### 2. Gerador de Paletas

```javascript
// Gerador avançado de paletas de cores
class GeradorPaletas {
  constructor() {
    this.formatosSuportados = ['hex', 'rgb', 'hsl', 'hsv'];
  }

  // Converter HEX para RGB
  hexParaRgb(hex) {
    const resultado = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return resultado ? {
      r: parseInt(resultado[1], 16),
      g: parseInt(resultado[2], 16),
      b: parseInt(resultado[3], 16)
    } : null;
  }

  // Converter RGB para HSL
  rgbParaHsl(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0;
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    };
  }

  // Converter HSL para RGB
  hslParaRgb(h, s, l) {
    h /= 360;
    s /= 100;
    l /= 100;

    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l;
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  }

  // Converter RGB para HEX
  rgbParaHex(r, g, b) {
    return "#" + [r, g, b].map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    }).join("");
  }

  // Gerar paleta complementar
  gerarPaletaComplementar(corBase) {
    const rgb = this.hexParaRgb(corBase);
    const hsl = this.rgbParaHsl(rgb.r, rgb.g, rgb.b);
    
    const complementar = {
      h: (hsl.h + 180) % 360,
      s: hsl.s,
      l: hsl.l
    };

    const rgbComplementar = this.hslParaRgb(complementar.h, complementar.s, complementar.l);
    
    return {
      original: corBase,
      complementar: this.rgbParaHex(rgbComplementar.r, rgbComplementar.g, rgbComplementar.b),
      informacoes: {
        original: { hex: corBase, rgb, hsl },
        complementar: { 
          hex: this.rgbParaHex(rgbComplementar.r, rgbComplementar.g, rgbComplementar.b),
          rgb: rgbComplementar,
          hsl: complementar
        }
      }
    };
  }

  // Gerar paleta triádica
  gerarPaletaTriadica(corBase) {
    const rgb = this.hexParaRgb(corBase);
    const hsl = this.rgbParaHsl(rgb.r, rgb.g, rgb.b);
    
    const cores = [
      { h: hsl.h, s: hsl.s, l: hsl.l },
      { h: (hsl.h + 120) % 360, s: hsl.s, l: hsl.l },
      { h: (hsl.h + 240) % 360, s: hsl.s, l: hsl.l }
    ];

    return cores.map((cor, indice) => {
      const rgbCor = this.hslParaRgb(cor.h, cor.s, cor.l);
      return {
        nome: `Triádica ${indice + 1}`,
        hex: this.rgbParaHex(rgbCor.r, rgbCor.g, rgbCor.b),
        rgb: rgbCor,
        hsl: cor
      };
    });
  }

  // Gerar paleta análoga
  gerarPaletaAnaloga(corBase, quantidade = 5) {
    const rgb = this.hexParaRgb(corBase);
    const hsl = this.rgbParaHsl(rgb.r, rgb.g, rgb.b);
    
    const cores = [];
    const incremento = 30; // 30 graus de diferença
    const inicio = -(quantidade - 1) / 2;

    for (let i = 0; i < quantidade; i++) {
      const novoH = (hsl.h + (inicio + i) * incremento + 360) % 360;
      const novaCor = { h: novoH, s: hsl.s, l: hsl.l };
      const rgbCor = this.hslParaRgb(novaCor.h, novaCor.s, novaCor.l);
      
      cores.push({
        nome: i === Math.floor(quantidade / 2) ? 'Principal' : `Análoga ${i + 1}`,
        hex: this.rgbParaHex(rgbCor.r, rgbCor.g, rgbCor.b),
        rgb: rgbCor,
        hsl: novaCor
      });
    }

    return cores;
  }

  // Gerar paleta monocromática
  gerarPaletaMonocromatica(corBase, quantidade = 5) {
    const rgb = this.hexParaRgb(corBase);
    const hsl = this.rgbParaHsl(rgb.r, rgb.g, rgb.b);
    
    const cores = [];
    const incrementoLuminosidade = 80 / (quantidade - 1);

    for (let i = 0; i < quantidade; i++) {
      const novaLuminosidade = Math.max(10, Math.min(90, 10 + i * incrementoLuminosidade));
      const novaCor = { h: hsl.h, s: hsl.s, l: novaLuminosidade };
      const rgbCor = this.hslParaRgb(novaCor.h, novaCor.s, novaCor.l);
      
      cores.push({
        nome: `Monocromática ${i + 1}`,
        hex: this.rgbParaHex(rgbCor.r, rgbCor.g, rgbCor.b),
        rgb: rgbCor,
        hsl: novaCor
      });
    }

    return cores;
  }

  // Calcular contraste entre duas cores
  calcularContraste(cor1, cor2) {
    const luminancia = (cor) => {
      const rgb = this.hexParaRgb(cor);
      const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    const lum1 = luminancia(cor1);
    const lum2 = luminancia(cor2);
    const contraste = (Math.max(lum1, lum2) + 0.05) / (Math.min(lum1, lum2) + 0.05);

    return {
      razao: contraste,
      nivel: contraste >= 7 ? 'AAA' : contraste >= 4.5 ? 'AA' : contraste >= 3 ? 'AA Large' : 'Falha',
      adequado: contraste >= 4.5
    };
  }

  // Gerar paleta de material design
  gerarPaletaMaterialDesign(corBase) {
    const rgb = this.hexParaRgb(corBase);
    const hsl = this.rgbParaHsl(rgb.r, rgb.g, rgb.b);
    
    const tons = {
      50: { s: Math.max(10, hsl.s - 40), l: 95 },
      100: { s: Math.max(20, hsl.s - 30), l: 90 },
      200: { s: Math.max(30, hsl.s - 20), l: 80 },
      300: { s: Math.max(40, hsl.s - 10), l: 70 },
      400: { s: hsl.s, l: 60 },
      500: { s: hsl.s, l: hsl.l }, // Cor base
      600: { s: Math.min(100, hsl.s + 10), l: Math.max(10, hsl.l - 10) },
      700: { s: Math.min(100, hsl.s + 20), l: Math.max(10, hsl.l - 20) },
      800: { s: Math.min(100, hsl.s + 30), l: Math.max(10, hsl.l - 30) },
      900: { s: Math.min(100, hsl.s + 40), l: Math.max(10, hsl.l - 40) }
    };

    const paleta = {};
    
    for (const [tom, valores] of Object.entries(tons)) {
      const rgbTom = this.hslParaRgb(hsl.h, valores.s, valores.l);
      paleta[tom] = {
        hex: this.rgbParaHex(rgbTom.r, rgbTom.g, rgbTom.b),
        rgb: rgbTom,
        hsl: { h: hsl.h, s: valores.s, l: valores.l }
      };
    }

    return paleta;
  }
}

// Exemplo de uso
const gerador = new GeradorPaletas();

// Gerar diferentes tipos de paletas
const corBase = '#3498db';

console.log('Paleta Complementar:', gerador.gerarPaletaComplementar(corBase));
console.log('Paleta Triádica:', gerador.gerarPaletaTriadica(corBase));
console.log('Paleta Análoga:', gerador.gerarPaletaAnaloga(corBase));
console.log('Paleta Monocromática:', gerador.gerarPaletaMonocromatica(corBase));
console.log('Paleta Material Design:', gerador.gerarPaletaMaterialDesign(corBase));

// Calcular contraste
const contraste = gerador.calcularContraste('#3498db', '#ffffff');
console.log('Análise de Contraste:', contraste);
```

### 3. Sistema de Temas

```javascript
// Sistema de temas com paletas de cores
class SistemaTemas {
  constructor() {
    this.temas = new Map();
    this.temaAtivo = null;
  }

  // Criar tema personalizado
  criarTema(nome, paletaCores, configuracoes = {}) {
    const tema = {
      nome,
      cores: paletaCores,
      configuracoes: {
        tipografia: configuracoes.tipografia || 'Inter, sans-serif',
        espacamento: configuracoes.espacamento || 'normal',
        bordas: configuracoes.bordas || 'arredondadas',
        sombras: configuracoes.sombras || 'suaves',
        ...configuracoes
      },
      criadoEm: new Date().toISOString()
    };

    this.temas.set(nome, tema);
    return tema;
  }

  // Aplicar tema
  aplicarTema(nomeTema) {
    const tema = this.temas.get(nomeTema);
    if (!tema) {
      throw new Error(`Tema '${nomeTema}' não encontrado`);
    }

    // Aplicar variáveis CSS
    const root = document.documentElement;
    
    Object.entries(tema.cores).forEach(([propriedade, valor]) => {
      root.style.setProperty(`--${propriedade}`, valor);
    });

    // Aplicar configurações adicionais
    if (tema.configuracoes.tipografia) {
      root.style.setProperty('--fonte-principal', tema.configuracoes.tipografia);
    }

    this.temaAtivo = nomeTema;
    
    // Salvar preferência
    localStorage.setItem('tema-ativo', nomeTema);
    
    // Disparar evento
    window.dispatchEvent(new CustomEvent('tema-alterado', {
      detail: { tema: nomeTema, configuracao: tema }
    }));

    return tema;
  }

  // Gerar tema automático a partir de cor base
  gerarTemaAutomatico(nome, corPrimaria, estilo = 'moderno') {
    const gerador = new GeradorPaletas();
    const paletaMaterial = gerador.gerarPaletaMaterialDesign(corPrimaria);
    
    let paletaCores;
    
    switch (estilo) {
      case 'moderno':
        paletaCores = {
          'cor-primaria': paletaMaterial[500].hex,
          'cor-primaria-clara': paletaMaterial[300].hex,
          'cor-primaria-escura': paletaMaterial[700].hex,
          'cor-secundaria': gerador.gerarPaletaComplementar(corPrimaria).complementar,
          'cor-fundo': '#ffffff',
          'cor-fundo-secundario': paletaMaterial[50].hex,
          'cor-texto': '#2c3e50',
          'cor-texto-secundario': '#7f8c8d',
          'cor-borda': paletaMaterial[200].hex,
          'cor-sucesso': '#27ae60',
          'cor-aviso': '#f39c12',
          'cor-erro': '#e74c3c'
        };
        break;
        
      case 'escuro':
        paletaCores = {
          'cor-primaria': paletaMaterial[400].hex,
          'cor-primaria-clara': paletaMaterial[300].hex,
          'cor-primaria-escura': paletaMaterial[600].hex,
          'cor-secundaria': gerador.gerarPaletaComplementar(corPrimaria).complementar,
          'cor-fundo': '#1a1a1a',
          'cor-fundo-secundario': '#2d2d2d',
          'cor-texto': '#ffffff',
          'cor-texto-secundario': '#b0b0b0',
          'cor-borda': '#404040',
          'cor-sucesso': '#4caf50',
          'cor-aviso': '#ff9800',
          'cor-erro': '#f44336'
        };
        break;
        
      case 'minimalista':
        paletaCores = {
          'cor-primaria': paletaMaterial[600].hex,
          'cor-primaria-clara': paletaMaterial[400].hex,
          'cor-primaria-escura': paletaMaterial[800].hex,
          'cor-secundaria': '#6c757d',
          'cor-fundo': '#fafafa',
          'cor-fundo-secundario': '#f5f5f5',
          'cor-texto': '#212529',
          'cor-texto-secundario': '#6c757d',
          'cor-borda': '#dee2e6',
          'cor-sucesso': '#198754',
          'cor-aviso': '#fd7e14',
          'cor-erro': '#dc3545'
        };
        break;
    }

    return this.criarTema(nome, paletaCores, {
      estilo,
      geradoAutomaticamente: true,
      corBase: corPrimaria
    });
  }

  // Exportar tema
  exportarTema(nomeTema) {
    const tema = this.temas.get(nomeTema);
    if (!tema) {
      throw new Error(`Tema '${nomeTema}' não encontrado`);
    }

    return {
      versao: '1.0',
      tema: tema,
      exportadoEm: new Date().toISOString()
    };
  }

  // Importar tema
  importarTema(dadosTema) {
    if (!dadosTema.tema || !dadosTema.tema.nome) {
      throw new Error('Dados de tema inválidos');
    }

    const tema = dadosTema.tema;
    this.temas.set(tema.nome, tema);
    
    return tema;
  }

  // Listar temas disponíveis
  listarTemas() {
    return Array.from(this.temas.entries()).map(([nome, tema]) => ({
      nome,
      estilo: tema.configuracoes.estilo || 'personalizado',
      corPrimaria: tema.cores['cor-primaria'],
      criadoEm: tema.criadoEm
    }));
  }
}

// Exemplo de uso
const sistemaTemas = new SistemaTemas();

// Gerar temas automáticos
sistemaTemas.gerarTemaAutomatico('Azul Moderno', '#3498db', 'moderno');
sistemaTemas.gerarTemaAutomatico('Verde Escuro', '#27ae60', 'escuro');
sistemaTemas.gerarTemaAutomatico('Roxo Minimalista', '#9b59b6', 'minimalista');

// Aplicar tema
sistemaTemas.aplicarTema('Azul Moderno');

// Listar temas
console.log('Temas Disponíveis:', sistemaTemas.listarTemas());
```

## 🔧 Detalhes Técnicos

### Formatos de Cor Suportados

**HEX (Hexadecimal):**
- Formato: `#RRGGBB` ou `#RGB`
- Exemplo: `#3498db`, `#fff`

**RGB (Red, Green, Blue):**
- Formato: `rgb(r, g, b)`
- Valores: 0-255 para cada canal
- Exemplo: `rgb(52, 152, 219)`

**HSL (Hue, Saturation, Lightness):**
- Formato: `hsl(h, s%, l%)`
- H: 0-360°, S: 0-100%, L: 0-100%
- Exemplo: `hsl(204, 70%, 53%)`

**HSV/HSB (Hue, Saturation, Value/Brightness):**
- Similar ao HSL, mas com Value em vez de Lightness
- Exemplo: `hsv(204, 76%, 86%)`

### Teoria das Cores

**Harmonia de Cores:**
- **Complementar**: Cores opostas no círculo cromático
- **Análoga**: Cores adjacentes no círculo cromático
- **Triádica**: Três cores equidistantes no círculo
- **Monocromática**: Variações de uma única cor

**Contraste e Acessibilidade:**
- **WCAG AA**: Razão de contraste mínima de 4.5:1
- **WCAG AAA**: Razão de contraste mínima de 7:1
- **Texto Grande**: Razão mínima de 3:1

## 💡 Dicas de Uso

- **Acessibilidade** : Sempre verifique o contraste entre texto e fundo
- **Consistência** : Use um sistema de cores consistente em todo o projeto
- **Contexto** : Considere o significado cultural das cores
- **Testes** : Teste cores em diferentes dispositivos e condições de iluminação

## ⚠️ Notas Importantes

- **Daltonismo** : Considere usuários com deficiência visual de cores
- **Impressão** : Cores podem aparecer diferentes quando impressas
- **Monitores** : Calibração de monitor afeta a percepção das cores
- **Contexto Cultural** : Significados de cores variam entre culturas

## 🚀 Como Usar

1. **Seleção de Cor** : Use o seletor visual ou digite um código de cor
2. **Conversão de Formato** : Veja a cor em diferentes formatos automaticamente
3. **Geração de Paleta** : Escolha um tipo de harmonia para gerar paletas
4. **Análise de Contraste** : Verifique a acessibilidade das combinações
5. **Uso de Cópia** : Clique em qualquer código para copiá-lo

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e rapidez na seleção de cores.
