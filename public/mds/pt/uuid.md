# Gerador UUID

UUID (Universally Unique Identifier, Identificador Único Universal) é uma informação de identificação padronizada usada para identificar unicamente informações em sistemas distribuídos. Um UUID é composto por 128 bits, geralmente representado por 32 dígitos hexadecimais, divididos em 5 grupos separados por hífens.

## ✨ Características Principais

- 🔢 **Suporte Múltiplas Versões** : Compatível com diferentes versões como UUID v1, v4, etc.
- ⚡ **Geração em Lote** : Compatível com geração de múltiplos UUID de uma vez
- 📋 **Múltiplos Formatos** : Compatível com formato padrão, formato sem hífens, etc.
- 💾 **Cópia com Um Clique** : UUIDs gerados podem ser copiados diretamente para uso
- 🔧 **Geração em Tempo Real** : Clique para gerar novos UUIDs instantaneamente

## 📖 Exemplos de Uso

### UUID v4 Padrão

**Exemplo Gerado:**
```
f47ac10b-58cc-4372-a567-0e02b2c3d479
```

**Características:**
- Geração aleatória, sem informações de tempo
- Probabilidade de colisão extremamente baixa
- Versão UUID mais comumente usada

### UUID v1 (Baseado em Tempo)

**Exemplo Gerado:**
```
6ba7b810-9dad-11d1-80b4-00c04fd430c8
```

**Características:**
- Contém informações de timestamp
- Contém informações de endereço MAC
- Tempo de geração pode ser inferido

### Formato Sem Hífens

**Exemplo Gerado:**
```
f47ac10b58cc4372a5670e02b2c3d479
```

**Características:**
- 32 caracteres hexadecimais contínuos
- Aplicável a certas bases de dados e sistemas

## 🎯 Cenários de Aplicação

### 1. Chave Primária de Base de Dados

Usar UUID como chave primária em base de dados:

```sql
-- Criar tabela usando UUID como chave primária
CREATE TABLE usuarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nome_usuario VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inserir dados (UUID gerado automaticamente)
INSERT INTO usuarios (nome_usuario, email) 
VALUES ('joao', '<EMAIL>');

-- Consultar dados
SELECT * FROM usuarios WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
```

### 2. Sistemas Distribuídos

Gerar identificadores únicos em sistemas distribuídos:

```javascript
// Rastreador de requisições em microserviços
class RastreadorRequisicao {
  constructor() {
    this.idRequisicao = gerarUUID()
    this.timestamp = new Date().toISOString()
  }

  log(mensagem) {
    console.log(`[${this.idRequisicao}] ${this.timestamp}: ${mensagem}`)
  }
}

// Exemplo de uso
const rastreador = new RastreadorRequisicao()
rastreador.log('Início do processamento da requisição do usuário')
rastreador.log('Chamada ao serviço de usuários')
rastreador.log('Chamada ao serviço de pedidos')
rastreador.log('Processamento da requisição concluído')

// Saída de exemplo:
// [f47ac10b-58cc-4372-a567-0e02b2c3d479] 2024-06-15T10:30:00.000Z: Início do processamento da requisição do usuário
```

### 3. Nomenclatura de Arquivos

Gerar nomes únicos para arquivos carregados:

```javascript
// Processamento de upload de arquivos
function gerirUploadArquivo(arquivo) {
  const extensaoArquivo = arquivo.name.split('.').pop()
  const nomeArquivoUnico = `${gerarUUID()}.${extensaoArquivo}`
  
  // Salvar arquivo
  const caminhoArquivo = `/uploads/${nomeArquivoUnico}`
  salvarArquivo(arquivo, caminhoArquivo)
  
  return {
    nomeOriginal: arquivo.name,
    nomeArquivo: nomeArquivoUnico,
    caminhoArquivo: caminhoArquivo,
    tempoUpload: new Date().toISOString()
  }
}

// Exemplo de uso
const resultadoUpload = gerirUploadArquivo(arquivoUsuario)
console.log(resultadoUpload)
// {
//   nomeOriginal: "documento.pdf",
//   nomeArquivo: "f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   caminhoArquivo: "/uploads/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   tempoUpload: "2024-06-15T10:30:00.000Z"
// }
```

### 4. Gestão de Sessões

Gerar identificadores únicos para sessões de usuário:

```javascript
// Sistema de gestão de sessões
class GestorSessoes {
  constructor() {
    this.sessoes = new Map()
  }

  criarSessao(idUsuario) {
    const idSessao = gerarUUID()
    const sessao = {
      id: idSessao,
      idUsuario: idUsuario,
      criadaEm: new Date(),
      ultimoAcesso: new Date(),
      dados: {}
    }
    
    this.sessoes.set(idSessao, sessao)
    return idSessao
  }

  obterSessao(idSessao) {
    const sessao = this.sessoes.get(idSessao)
    if (sessao) {
      sessao.ultimoAcesso = new Date()
    }
    return sessao
  }

  destruirSessao(idSessao) {
    return this.sessoes.delete(idSessao)
  }
}

// Exemplo de uso
const gestorSessoes = new GestorSessoes()
const idSessao = gestorSessoes.criarSessao('user123')
console.log('ID da Sessão:', idSessao)
// ID da Sessão: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

### 5. Geração de Chaves API

Gerar chaves únicas para acesso API:

```javascript
// Gestão de chaves API
class GestorChavesAPI {
  constructor() {
    this.chavesApi = new Map()
  }

  gerarChaveAPI(idUsuario, permissoes = []) {
    const chaveApi = gerarUUID()
    const infoChave = {
      chave: chaveApi,
      idUsuario: idUsuario,
      permissoes: permissoes,
      criadaEm: new Date(),
      ultimaUtilizacao: null,
      estaAtiva: true
    }
    
    this.chavesApi.set(chaveApi, infoChave)
    return chaveApi
  }

  validarChaveAPI(chaveApi) {
    const infoChave = this.chavesApi.get(chaveApi)
    if (infoChave && infoChave.estaAtiva) {
      infoChave.ultimaUtilizacao = new Date()
      return infoChave
    }
    return null
  }

  revogarChaveAPI(chaveApi) {
    const infoChave = this.chavesApi.get(chaveApi)
    if (infoChave) {
      infoChave.estaAtiva = false
      return true
    }
    return false
  }
}

// Exemplo de uso
const gestorApi = new GestorChavesAPI()
const chaveApi = gestorApi.gerarChaveAPI('user123', ['read', 'write'])
console.log('Chave API:', chaveApi)
// Chave API: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

## 🔧 Detalhes Técnicos

### Versões UUID

Diferentes versões UUID têm diferentes métodos de geração:

**UUID v1 (Baseado em Tempo):**
- Contém timestamp (60 bits)
- Contém sequência de relógio (14 bits)
- Contém identificador de nó (48 bits, geralmente endereço MAC)
- Tempo e local de geração podem ser inferidos

**UUID v4 (Aleatório):**
- 122 bits de números aleatórios
- 6 bits de identificadores de versão e variante
- Probabilidade de colisão de aproximadamente 1/2^122
- Versão mais comumente usada

**UUID v5 (Baseado em Nome):**
- Usa algoritmo de hash SHA-1
- Gerado baseado em namespace e nome
- A mesma entrada gera sempre o mesmo UUID

### Estrutura de Formato

Formato UUID padrão: `xxxxxxxx-xxxx-Mxxx-Nxxx-xxxxxxxxxxxx`

- **M** : Número da versão (1, 4, 5, etc.)
- **N** : Identificador de variante (geralmente 8, 9, A, B)
- **x** : Dígito hexadecimal (0-9, A-F)

### Probabilidade de Colisão

A probabilidade de colisão de UUID v4 é extremamente baixa:

- Total de 2^122 tipos de UUID possíveis
- Probabilidade de colisão de aproximadamente 50% ao gerar 10^18 UUIDs
- Pode ser considerado único em aplicações práticas

## 💡 Dicas de Uso

- **Seleção de Versão** : Geralmente usar UUID v4, usar v1 quando informações de tempo são necessárias
- **Otimização de Armazenamento** : Usar BINARY(16) em base de dados para economizar espaço
- **Performance de Índice** : Prestar atenção ao impacto na performance de índice de base de dados ao usar UUID como chave primária
- **Unificação de Formato** : Manter consistência de formato UUID dentro do mesmo sistema

## ⚠️ Notas Importantes

- **Limites de Precisão** : Prestar atenção à precisão para números muito grandes ou decimais complexos
- **Diferenças Regionais** : China continental e Taiwan/Hong Kong podem ter notações diferentes
- **Exigências Legais** : Usar notação conforme exigências legais em documentos financeiros
- **Codificação de Caracteres** : Garantir exibição correta de caracteres chineses

## 🚀 Como Usar

1. **Seleção de Versão** : Escolha a versão UUID conforme exigências
2. **Configuração de Quantidade** : Selecione o número de UUIDs a gerar
3. **Seleção de Formato** : Escolha formato padrão ou formato sem hífens
4. **Gerar UUID** : Clique no botão gerar para criar UUIDs
5. **Uso de Cópia** : Clique no botão copiar para copiar UUIDs para área de transferência

> **Dica** : Esta ferramenta gera UUIDs localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e segurança.
