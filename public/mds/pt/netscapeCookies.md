# Ferramenta de Cookies Netscape

Esta ferramenta permite visualizar, editar e gerenciar cookies no formato Netscape. O formato Netscape é um padrão amplamente usado para exportar e importar cookies entre diferentes navegadores e ferramentas de desenvolvimento web.

## ✨ Características Principais

- 📋 **Visualização de Cookies** : Exibe cookies em formato tabular legível
- ✏️ **Edição de Cookies** : Permite modificar valores, domínios e datas
- 📁 **Importação/Exportação** : Suporte completo ao formato Netscape
- 🔍 **Filtros Avançados** : Filtragem por domínio, nome ou status
- 📋 **Cópia com Um Clique** : Cookies podem ser copiados diretamente

## 📖 Exemplos de Uso

### Formato de Cookie Netscape

**Estrutura do Arquivo:**
```
# Netscape HTTP Cookie File
# This is a generated file! Do not edit.

.example.com	TRUE	/	FALSE	1735689600	session_id	abc123def456
.google.com	TRUE	/	FALSE	1735689600	NID	507=xyz789
toolmi.com	FALSE	/tools	TRUE	1735689600	user_pref	dark_theme
```

**Campos do Cookie:**
1. **Domínio** : `.example.com`
2. **Incluir Subdomínios** : `TRUE/FALSE`
3. **Caminho** : `/`
4. **Seguro (HTTPS)** : `TRUE/FALSE`
5. **Expiração** : `1735689600` (timestamp Unix)
6. **Nome** : `session_id`
7. **Valor** : `abc123def456`

### Visualização em Tabela

| Domínio | Nome | Valor | Caminho | Seguro | Expira | Subdomínios |
|---------|------|-------|---------|--------|--------|-------------|
| .example.com | session_id | abc123def456 | / | Não | 2024-12-31 | Sim |
| .google.com | NID | 507=xyz789 | / | Não | 2024-12-31 | Sim |
| toolmi.com | user_pref | dark_theme | /tools | Sim | 2024-12-31 | Não |

## 🎯 Cenários de Aplicação

### 1. Desenvolvimento Web

```javascript
// Gerenciador de cookies para desenvolvimento
class GerenciadorCookies {
  constructor() {
    this.cookies = [];
  }

  // Parsear arquivo Netscape
  parsearArquivoNetscape(conteudo) {
    const linhas = conteudo.split('\n');
    const cookies = [];

    for (const linha of linhas) {
      // Ignorar comentários e linhas vazias
      if (linha.startsWith('#') || linha.trim() === '') {
        continue;
      }

      const campos = linha.split('\t');
      if (campos.length === 7) {
        cookies.push({
          dominio: campos[0],
          incluirSubdominios: campos[1] === 'TRUE',
          caminho: campos[2],
          seguro: campos[3] === 'TRUE',
          expiracao: parseInt(campos[4]),
          nome: campos[5],
          valor: campos[6]
        });
      }
    }

    this.cookies = cookies;
    return cookies;
  }

  // Gerar arquivo Netscape
  gerarArquivoNetscape() {
    let conteudo = '# Netscape HTTP Cookie File\n';
    conteudo += '# This is a generated file! Do not edit.\n\n';

    for (const cookie of this.cookies) {
      const linha = [
        cookie.dominio,
        cookie.incluirSubdominios ? 'TRUE' : 'FALSE',
        cookie.caminho,
        cookie.seguro ? 'TRUE' : 'FALSE',
        cookie.expiracao,
        cookie.nome,
        cookie.valor
      ].join('\t');

      conteudo += linha + '\n';
    }

    return conteudo;
  }

  // Filtrar cookies por domínio
  filtrarPorDominio(dominio) {
    return this.cookies.filter(cookie => 
      cookie.dominio === dominio || 
      cookie.dominio === '.' + dominio ||
      (cookie.incluirSubdominios && dominio.endsWith(cookie.dominio.substring(1)))
    );
  }

  // Adicionar novo cookie
  adicionarCookie(cookie) {
    // Validar campos obrigatórios
    if (!cookie.dominio || !cookie.nome) {
      throw new Error('Domínio e nome são obrigatórios');
    }

    // Definir valores padrão
    const novoCookie = {
      dominio: cookie.dominio,
      incluirSubdominios: cookie.incluirSubdominios || false,
      caminho: cookie.caminho || '/',
      seguro: cookie.seguro || false,
      expiracao: cookie.expiracao || Math.floor(Date.now() / 1000) + 86400, // 24h
      nome: cookie.nome,
      valor: cookie.valor || ''
    };

    // Remover cookie existente com mesmo nome e domínio
    this.removerCookie(cookie.dominio, cookie.nome);

    this.cookies.push(novoCookie);
    return novoCookie;
  }

  // Remover cookie
  removerCookie(dominio, nome) {
    this.cookies = this.cookies.filter(cookie => 
      !(cookie.dominio === dominio && cookie.nome === nome)
    );
  }

  // Verificar se cookie está expirado
  estaExpirado(cookie) {
    return cookie.expiracao < Math.floor(Date.now() / 1000);
  }

  // Limpar cookies expirados
  limparExpirados() {
    const antes = this.cookies.length;
    this.cookies = this.cookies.filter(cookie => !this.estaExpirado(cookie));
    return antes - this.cookies.length;
  }

  // Obter estatísticas
  obterEstatisticas() {
    const total = this.cookies.length;
    const expirados = this.cookies.filter(c => this.estaExpirado(c)).length;
    const seguros = this.cookies.filter(c => c.seguro).length;
    const dominios = [...new Set(this.cookies.map(c => c.dominio))].length;

    return {
      total,
      ativos: total - expirados,
      expirados,
      seguros,
      dominiosUnicos: dominios
    };
  }

  // Exportar para JSON
  exportarParaJSON() {
    return JSON.stringify(this.cookies, null, 2);
  }

  // Importar de JSON
  importarDeJSON(json) {
    try {
      const dados = JSON.parse(json);
      if (Array.isArray(dados)) {
        this.cookies = dados;
        return true;
      }
      return false;
    } catch (erro) {
      console.error('Erro ao importar JSON:', erro);
      return false;
    }
  }
}

// Exemplo de uso
const gerenciador = new GerenciadorCookies();

// Adicionar cookies de exemplo
gerenciador.adicionarCookie({
  dominio: '.toolmi.com',
  nome: 'session_id',
  valor: 'abc123def456',
  incluirSubdominios: true,
  seguro: true,
  expiracao: Math.floor(Date.now() / 1000) + 86400
});

gerenciador.adicionarCookie({
  dominio: 'toolmi.com',
  nome: 'user_preferences',
  valor: JSON.stringify({ tema: 'escuro', idioma: 'pt-BR' }),
  caminho: '/tools',
  seguro: true
});

// Gerar arquivo Netscape
const arquivoNetscape = gerenciador.gerarArquivoNetscape();
console.log('Arquivo Netscape:', arquivoNetscape);

// Obter estatísticas
const stats = gerenciador.obterEstatisticas();
console.log('Estatísticas:', stats);
```

### 2. Automação de Testes

```python
# Script Python para automação com cookies
import requests
import time
from urllib.parse import urlparse

class NetscapeCookieManager:
    def __init__(self):
        self.cookies = []
    
    def load_from_file(self, filepath):
        """Carregar cookies de arquivo Netscape"""
        cookies = []
        
        with open(filepath, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                
                # Ignorar comentários e linhas vazias
                if line.startswith('#') or not line:
                    continue
                
                parts = line.split('\t')
                if len(parts) == 7:
                    cookies.append({
                        'domain': parts[0],
                        'include_subdomains': parts[1] == 'TRUE',
                        'path': parts[2],
                        'secure': parts[3] == 'TRUE',
                        'expiration': int(parts[4]),
                        'name': parts[5],
                        'value': parts[6]
                    })
        
        self.cookies = cookies
        return cookies
    
    def save_to_file(self, filepath):
        """Salvar cookies em arquivo Netscape"""
        with open(filepath, 'w', encoding='utf-8') as file:
            file.write('# Netscape HTTP Cookie File\n')
            file.write('# This is a generated file! Do not edit.\n\n')
            
            for cookie in self.cookies:
                line = '\t'.join([
                    cookie['domain'],
                    'TRUE' if cookie['include_subdomains'] else 'FALSE',
                    cookie['path'],
                    'TRUE' if cookie['secure'] else 'FALSE',
                    str(cookie['expiration']),
                    cookie['name'],
                    cookie['value']
                ])
                file.write(line + '\n')
    
    def to_requests_cookies(self, url):
        """Converter para formato do requests"""
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        
        cookies = {}
        for cookie in self.cookies:
            # Verificar se o cookie se aplica ao domínio
            if self._domain_matches(domain, cookie['domain'], cookie['include_subdomains']):
                # Verificar se não está expirado
                if cookie['expiration'] > time.time():
                    cookies[cookie['name']] = cookie['value']
        
        return cookies
    
    def _domain_matches(self, request_domain, cookie_domain, include_subdomains):
        """Verificar se domínio corresponde ao cookie"""
        if cookie_domain.startswith('.'):
            # Cookie de domínio
            base_domain = cookie_domain[1:]
            if include_subdomains:
                return request_domain == base_domain or request_domain.endswith('.' + base_domain)
            else:
                return request_domain == base_domain
        else:
            # Cookie específico do host
            return request_domain == cookie_domain
    
    def add_cookie(self, domain, name, value, **kwargs):
        """Adicionar novo cookie"""
        cookie = {
            'domain': domain,
            'name': name,
            'value': value,
            'include_subdomains': kwargs.get('include_subdomains', False),
            'path': kwargs.get('path', '/'),
            'secure': kwargs.get('secure', False),
            'expiration': kwargs.get('expiration', int(time.time()) + 86400)
        }
        
        # Remover cookie existente
        self.cookies = [c for c in self.cookies if not (c['domain'] == domain and c['name'] == name)]
        self.cookies.append(cookie)
        
        return cookie

# Exemplo de uso em testes automatizados
def test_with_cookies():
    # Carregar cookies salvos
    cookie_manager = NetscapeCookieManager()
    cookie_manager.load_from_file('cookies.txt')
    
    # Fazer requisição com cookies
    url = 'https://toolmi.com/api/user/profile'
    cookies = cookie_manager.to_requests_cookies(url)
    
    response = requests.get(url, cookies=cookies)
    
    if response.status_code == 200:
        print('Requisição bem-sucedida com cookies')
        print('Dados do usuário:', response.json())
    else:
        print('Falha na requisição:', response.status_code)
    
    # Adicionar novos cookies se necessário
    if 'Set-Cookie' in response.headers:
        # Processar novos cookies da resposta
        # (implementação simplificada)
        pass
    
    # Salvar cookies atualizados
    cookie_manager.save_to_file('cookies_updated.txt')

# Executar teste
test_with_cookies()
```

### 3. Migração Entre Navegadores

```bash
#!/bin/bash
# Script para migração de cookies entre navegadores

# Função para converter cookies do Chrome para Netscape
convert_chrome_to_netscape() {
    local chrome_db="$1"
    local output_file="$2"
    
    echo "# Netscape HTTP Cookie File" > "$output_file"
    echo "# Converted from Chrome cookies" >> "$output_file"
    echo "" >> "$output_file"
    
    sqlite3 "$chrome_db" "
    SELECT 
        host_key,
        CASE WHEN host_key LIKE '.%' THEN 'TRUE' ELSE 'FALSE' END,
        path,
        CASE WHEN is_secure = 1 THEN 'TRUE' ELSE 'FALSE' END,
        expires_utc / 1000000 - 11644473600,
        name,
        value
    FROM cookies 
    WHERE expires_utc > 0
    ORDER BY host_key, name;
    " | while IFS='|' read -r domain subdomains path secure expires name value; do
        echo -e "$domain\t$subdomains\t$path\t$secure\t$expires\t$name\t$value" >> "$output_file"
    done
    
    echo "Conversão concluída: $output_file"
}

# Função para converter Netscape para formato JSON
convert_netscape_to_json() {
    local netscape_file="$1"
    local json_file="$2"
    
    echo "[" > "$json_file"
    
    local first=true
    while IFS=$'\t' read -r domain subdomains path secure expires name value; do
        # Ignorar comentários
        if [[ $domain == \#* ]] || [[ -z $domain ]]; then
            continue
        fi
        
        if [ "$first" = false ]; then
            echo "," >> "$json_file"
        fi
        first=false
        
        cat >> "$json_file" << EOF
  {
    "domain": "$domain",
    "includeSubdomains": $([ "$subdomains" = "TRUE" ] && echo "true" || echo "false"),
    "path": "$path",
    "secure": $([ "$secure" = "TRUE" ] && echo "true" || echo "false"),
    "expiration": $expires,
    "name": "$name",
    "value": "$value"
  }
EOF
    done < "$netscape_file"
    
    echo "" >> "$json_file"
    echo "]" >> "$json_file"
    
    echo "Conversão para JSON concluída: $json_file"
}

# Uso do script
if [ "$#" -ne 3 ]; then
    echo "Uso: $0 <comando> <arquivo_origem> <arquivo_destino>"
    echo "Comandos:"
    echo "  chrome-to-netscape - Converter cookies do Chrome para Netscape"
    echo "  netscape-to-json   - Converter Netscape para JSON"
    exit 1
fi

case "$1" in
    chrome-to-netscape)
        convert_chrome_to_netscape "$2" "$3"
        ;;
    netscape-to-json)
        convert_netscape_to_json "$2" "$3"
        ;;
    *)
        echo "Comando inválido: $1"
        exit 1
        ;;
esac
```

## 🔧 Detalhes Técnicos

### Formato de Arquivo Netscape

**Estrutura:**
- Arquivo de texto simples
- Campos separados por TAB
- Uma linha por cookie
- Comentários começam com `#`

**Campos (em ordem):**
1. **Domínio** : Host ou domínio do cookie
2. **Subdomínios** : TRUE/FALSE para incluir subdomínios
3. **Caminho** : Caminho onde o cookie é válido
4. **Seguro** : TRUE/FALSE para HTTPS apenas
5. **Expiração** : Timestamp Unix (segundos desde 1970)
6. **Nome** : Nome do cookie
7. **Valor** : Valor do cookie

### Compatibilidade

**Navegadores Suportados:**
- Chrome/Chromium
- Firefox
- Safari
- Edge
- Opera

**Ferramentas Compatíveis:**
- curl
- wget
- Postman
- Insomnia

## 💡 Dicas de Uso

- **Backup** : Sempre faça backup dos cookies antes de editar
- **Privacidade** : Remova cookies sensíveis antes de compartilhar
- **Expiração** : Verifique datas de expiração ao importar cookies antigos
- **Domínios** : Use cookies de domínio (com ponto) para subdomínios

## ⚠️ Notas Importantes

- **Segurança** : Cookies podem conter informações sensíveis
- **Privacidade** : Não compartilhe arquivos de cookies publicamente
- **Expiração** : Cookies expirados são ignorados pelos navegadores
- **Formato** : Mantenha a estrutura exata do formato Netscape

## 🚀 Como Usar

1. **Importação** : Cole o conteúdo do arquivo Netscape ou carregue um arquivo
2. **Visualização** : Veja os cookies em formato de tabela
3. **Edição** : Modifique valores, domínios ou datas conforme necessário
4. **Filtros** : Use filtros para encontrar cookies específicos
5. **Exportação** : Baixe o arquivo modificado ou copie o conteúdo

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e segurança dos seus cookies.
