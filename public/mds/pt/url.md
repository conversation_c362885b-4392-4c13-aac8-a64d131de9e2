# Ferramenta de Codificação e Decodificação URL

A codificação URL (também conhecida como codificação percentual) é um mecanismo para codificar informações em URLs quando os dados contêm caracteres especiais ou não ASCII. Esta ferramenta fornece conversão bidirecional entre texto normal e formato codificado URL, essencial para desenvolvimento web e comunicação API.

## ✨ Características Principais

- 🔄 **Conversão Bidirecional** : Compatível com codificação e decodificação URL
- 🌐 **Suporte Unicode** : Lida corretamente com caracteres portugueses e símbolos especiais
- 📊 **Múltiplos Formatos** : Compatível com codificação de componentes URL e URL completa
- 📋 **Cópia com Um Clique** : Resultados podem ser copiados diretamente para uso
- ⚡ **Processamento em Tempo Real** : Conversão instantânea conforme a entrada

## 📖 Exemplos de Uso

### Codificação de Texto Português

**Entrada:**
```
Olá, como está? Tudo bem!
```

**URL Codificada:**
```
Ol%C3%A1%2C%20como%20est%C3%A1%3F%20Tudo%20bem%21
```

### Codificação de URL com Parâmetros

**Entrada:**
```
https://www.toolmi.com/buscar?q=ferramenta útil&categoria=desenvolvimento
```

**URL Codificada:**
```
https%3A//www.toolmi.com/buscar%3Fq%3Dferramenta%20%C3%BAtil%26categoria%3Ddesenvolvimento
```

### Codificação de Dados JSON

**Entrada:**
```
{"nome": "João Silva", "email": "<EMAIL>"}
```

**URL Codificada:**
```
%7B%22nome%22%3A%20%22Jo%C3%A3o%20Silva%22%2C%20%22email%22%3A%20%22joao%40example.com%22%7D
```

## 🎯 Cenários de Aplicação

### 1. Desenvolvimento de APIs

```javascript
// Cliente API com codificação automática de parâmetros
class ClienteAPI {
  constructor(urlBase) {
    this.urlBase = urlBase;
  }

  // Construir URL com parâmetros codificados
  construirURL(endpoint, parametros = {}) {
    const url = new URL(endpoint, this.urlBase);
    
    // Adicionar parâmetros de consulta codificados
    Object.entries(parametros).forEach(([chave, valor]) => {
      if (valor !== null && valor !== undefined) {
        url.searchParams.append(chave, valor);
      }
    });

    return url.toString();
  }

  // Fazer requisição GET com parâmetros
  async get(endpoint, parametros = {}) {
    const url = this.construirURL(endpoint, parametros);
    
    try {
      const resposta = await fetch(url);
      if (!resposta.ok) {
        throw new Error(`Erro HTTP: ${resposta.status}`);
      }
      return await resposta.json();
    } catch (erro) {
      console.error('Erro na requisição GET:', erro);
      throw erro;
    }
  }

  // Fazer requisição POST com dados codificados
  async post(endpoint, dados) {
    const url = this.construirURL(endpoint);
    
    try {
      const resposta = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados)
      });

      if (!resposta.ok) {
        throw new Error(`Erro HTTP: ${resposta.status}`);
      }
      return await resposta.json();
    } catch (erro) {
      console.error('Erro na requisição POST:', erro);
      throw erro;
    }
  }

  // Codificar dados para formulário
  codificarFormulario(dados) {
    const parametros = new URLSearchParams();
    
    Object.entries(dados).forEach(([chave, valor]) => {
      if (valor !== null && valor !== undefined) {
        parametros.append(chave, valor);
      }
    });

    return parametros.toString();
  }
}

// Exemplo de uso
const api = new ClienteAPI('https://api.toolmi.com');

// Buscar usuários com filtros
const parametrosBusca = {
  nome: 'João Silva',
  email: '<EMAIL>',
  cidade: 'São Paulo',
  ativo: true
};

api.get('/usuarios', parametrosBusca)
  .then(usuarios => {
    console.log('Usuários encontrados:', usuarios);
  })
  .catch(erro => {
    console.error('Erro na busca:', erro);
  });

// Criar novo usuário
const novoUsuario = {
  nome: 'Maria Santos',
  email: '<EMAIL>',
  bio: 'Desenvolvedora apaixonada por tecnologia!'
};

api.post('/usuarios', novoUsuario)
  .then(resultado => {
    console.log('Usuário criado:', resultado);
  });
```

### 2. Manipulação de URLs

```javascript
// Utilitário para manipulação avançada de URLs
class ManipuladorURL {
  constructor(url) {
    this.url = new URL(url);
  }

  // Adicionar ou atualizar parâmetro
  definirParametro(chave, valor) {
    this.url.searchParams.set(chave, valor);
    return this;
  }

  // Remover parâmetro
  removerParametro(chave) {
    this.url.searchParams.delete(chave);
    return this;
  }

  // Obter parâmetro decodificado
  obterParametro(chave) {
    return this.url.searchParams.get(chave);
  }

  // Obter todos os parâmetros
  obterTodosParametros() {
    const parametros = {};
    for (const [chave, valor] of this.url.searchParams.entries()) {
      parametros[chave] = valor;
    }
    return parametros;
  }

  // Limpar todos os parâmetros
  limparParametros() {
    this.url.search = '';
    return this;
  }

  // Alterar domínio
  alterarDominio(novoDominio) {
    this.url.hostname = novoDominio;
    return this;
  }

  // Alterar protocolo
  alterarProtocolo(novoProtocolo) {
    this.url.protocol = novoProtocolo;
    return this;
  }

  // Adicionar fragmento de hash
  definirHash(hash) {
    this.url.hash = hash;
    return this;
  }

  // Obter URL final
  toString() {
    return this.url.toString();
  }

  // Analisar componentes da URL
  analisar() {
    return {
      protocolo: this.url.protocol,
      dominio: this.url.hostname,
      porta: this.url.port,
      caminho: this.url.pathname,
      parametros: this.obterTodosParametros(),
      hash: this.url.hash,
      urlCompleta: this.url.toString()
    };
  }

  // Validar URL
  static validar(url) {
    try {
      new URL(url);
      return { valida: true };
    } catch (erro) {
      return { 
        valida: false, 
        erro: erro.message 
      };
    }
  }

  // Comparar URLs (ignorando ordem dos parâmetros)
  static comparar(url1, url2) {
    try {
      const u1 = new URL(url1);
      const u2 = new URL(url2);

      // Comparar componentes básicos
      if (u1.protocol !== u2.protocol ||
          u1.hostname !== u2.hostname ||
          u1.port !== u2.port ||
          u1.pathname !== u2.pathname ||
          u1.hash !== u2.hash) {
        return false;
      }

      // Comparar parâmetros (ignorando ordem)
      const params1 = new Set(u1.searchParams.toString().split('&').sort());
      const params2 = new Set(u2.searchParams.toString().split('&').sort());

      if (params1.size !== params2.size) {
        return false;
      }

      for (const param of params1) {
        if (!params2.has(param)) {
          return false;
        }
      }

      return true;
    } catch (erro) {
      return false;
    }
  }
}

// Exemplos de uso
const manipulador = new ManipuladorURL('https://www.toolmi.com/buscar');

// Construir URL de busca
manipulador
  .definirParametro('q', 'ferramenta de desenvolvimento')
  .definirParametro('categoria', 'programação')
  .definirParametro('ordenar', 'relevância')
  .definirHash('resultados');

console.log('URL construída:', manipulador.toString());

// Analisar URL
const analise = manipulador.analisar();
console.log('Análise da URL:', analise);

// Validar URLs
console.log('URL válida:', ManipuladorURL.validar('https://www.toolmi.com'));
console.log('URL inválida:', ManipuladorURL.validar('não-é-uma-url'));

// Comparar URLs
const url1 = 'https://example.com?a=1&b=2';
const url2 = 'https://example.com?b=2&a=1';
console.log('URLs iguais:', ManipuladorURL.comparar(url1, url2)); // true
```

### 3. Sistema de Roteamento

```javascript
// Sistema de roteamento com codificação URL
class SistemaRoteamento {
  constructor() {
    this.rotas = new Map();
    this.middleware = [];
    this.parametrosGlobais = new Map();
  }

  // Registrar rota
  registrarRota(padrao, manipulador, opcoes = {}) {
    const rota = {
      padrao: this.compilarPadrao(padrao),
      manipulador,
      opcoes
    };
    
    this.rotas.set(padrao, rota);
    return this;
  }

  // Compilar padrão de rota para regex
  compilarPadrao(padrao) {
    const parametros = [];
    
    // Converter padrão como '/usuario/:id/perfil' para regex
    const regex = padrao.replace(/:([^/]+)/g, (match, nomeParam) => {
      parametros.push(nomeParam);
      return '([^/]+)';
    });

    return {
      regex: new RegExp(`^${regex}$`),
      parametros
    };
  }

  // Navegar para URL
  async navegar(url, dados = {}) {
    try {
      const urlObj = new URL(url, window.location.origin);
      const caminho = urlObj.pathname;
      
      // Encontrar rota correspondente
      const rotaCorrespondente = this.encontrarRota(caminho);
      
      if (!rotaCorrespondente) {
        throw new Error(`Rota não encontrada: ${caminho}`);
      }

      // Extrair parâmetros da URL
      const parametros = this.extrairParametros(caminho, rotaCorrespondente.rota);
      
      // Extrair parâmetros de consulta
      const parametrosConsulta = {};
      for (const [chave, valor] of urlObj.searchParams.entries()) {
        parametrosConsulta[chave] = valor;
      }

      // Criar contexto de rota
      const contexto = {
        url: urlObj.toString(),
        caminho,
        parametros,
        consulta: parametrosConsulta,
        dados,
        hash: urlObj.hash
      };

      // Executar middleware
      for (const mw of this.middleware) {
        await mw(contexto);
      }

      // Executar manipulador da rota
      await rotaCorrespondente.rota.manipulador(contexto);

      // Atualizar histórico do navegador
      if (rotaCorrespondente.rota.opcoes.atualizarHistorico !== false) {
        history.pushState(dados, '', url);
      }

      return contexto;
    } catch (erro) {
      console.error('Erro na navegação:', erro);
      throw erro;
    }
  }

  // Encontrar rota correspondente
  encontrarRota(caminho) {
    for (const [padrao, rota] of this.rotas.entries()) {
      if (rota.padrao.regex.test(caminho)) {
        return { padrao, rota };
      }
    }
    return null;
  }

  // Extrair parâmetros da URL
  extrairParametros(caminho, rota) {
    const correspondencia = caminho.match(rota.padrao.regex);
    const parametros = {};

    if (correspondencia) {
      rota.padrao.parametros.forEach((nomeParam, indice) => {
        parametros[nomeParam] = decodeURIComponent(correspondencia[indice + 1]);
      });
    }

    return parametros;
  }

  // Adicionar middleware
  usar(middleware) {
    this.middleware.push(middleware);
    return this;
  }

  // Gerar URL para rota
  gerarURL(padrao, parametros = {}, consulta = {}) {
    let url = padrao;

    // Substituir parâmetros na URL
    Object.entries(parametros).forEach(([chave, valor]) => {
      url = url.replace(`:${chave}`, encodeURIComponent(valor));
    });

    // Adicionar parâmetros de consulta
    const urlObj = new URL(url, window.location.origin);
    Object.entries(consulta).forEach(([chave, valor]) => {
      urlObj.searchParams.set(chave, valor);
    });

    return urlObj.toString();
  }
}

// Exemplo de uso
const roteador = new SistemaRoteamento();

// Middleware de autenticação
roteador.usar(async (contexto) => {
  console.log(`Navegando para: ${contexto.caminho}`);
  
  // Verificar autenticação se necessário
  if (contexto.caminho.startsWith('/admin')) {
    const token = localStorage.getItem('authToken');
    if (!token) {
      throw new Error('Acesso negado: autenticação necessária');
    }
  }
});

// Registrar rotas
roteador
  .registrarRota('/', async (ctx) => {
    console.log('Página inicial');
  })
  .registrarRota('/usuario/:id', async (ctx) => {
    console.log(`Perfil do usuário: ${ctx.parametros.id}`);
    console.log('Parâmetros de consulta:', ctx.consulta);
  })
  .registrarRota('/buscar', async (ctx) => {
    console.log(`Buscar por: ${ctx.consulta.q}`);
  });

// Navegar
roteador.navegar('/usuario/123?tab=perfil&edit=true')
  .then(contexto => {
    console.log('Navegação concluída:', contexto);
  })
  .catch(erro => {
    console.error('Erro na navegação:', erro);
  });

// Gerar URLs
const urlPerfil = roteador.gerarURL('/usuario/:id', 
  { id: 'joão-silva' }, 
  { tab: 'configurações' }
);
console.log('URL gerada:', urlPerfil);
```

## 🔧 Detalhes Técnicos

### Codificação Percentual

A codificação URL usa o formato `%XX` onde XX é o valor hexadecimal:

**Caracteres Especiais Comuns:**
- Espaço: `%20`
- `!`: `%21`
- `"`: `%22`
- `#`: `%23`
- `$`: `%24`
- `%`: `%25`
- `&`: `%26`
- `'`: `%27`
- `(`: `%28`
- `)`: `%29`
- `+`: `%2B`

### Caracteres Portugueses

**Acentos e Caracteres Especiais:**
- `á`: `%C3%A1`
- `à`: `%C3%A0`
- `ã`: `%C3%A3`
- `â`: `%C3%A2`
- `é`: `%C3%A9`
- `ê`: `%C3%AA`
- `í`: `%C3%AD`
- `ó`: `%C3%B3`
- `ô`: `%C3%B4`
- `õ`: `%C3%B5`
- `ú`: `%C3%BA`
- `ç`: `%C3%A7`

## 💡 Dicas de Uso

- **Parâmetros de Consulta** : Sempre codificar valores de parâmetros de consulta
- **Componentes de Caminho** : Codificar segmentos de caminho que contêm caracteres especiais
- **Dados de Formulário** : Usar `application/x-www-form-urlencoded` para dados de formulário
- **APIs RESTful** : Codificar IDs e parâmetros em URLs de API

## ⚠️ Notas Importantes

- **Dupla Codificação** : Evitar codificar URLs já codificadas
- **Contexto Apropriado** : Usar `encodeURIComponent()` para parâmetros e `encodeURI()` para URLs completas
- **Decodificação** : Sempre decodificar dados recebidos de URLs
- **Validação** : Validar URLs decodificadas para prevenir ataques

## 🚀 Como Usar

1. **Entrada de Dados** : Digite o texto ou URL para codificar/decodificar
2. **Seleção de Operação** : Escolha "Codificar" ou "Decodificar"
3. **Verificação dos Resultados** : O resultado é exibido instantaneamente
4. **Uso de Cópia** : Clique em "Copiar" para copiar o resultado
5. **Aplicação** : Use em desenvolvimento web, APIs e manipulação de URLs

> **Dica** : Esta ferramenta processa localmente no lado do cliente e não envia dados para o servidor, garantindo privacidade e segurança.
