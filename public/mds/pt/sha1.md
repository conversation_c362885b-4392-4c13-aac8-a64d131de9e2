# Ferramenta de Criptografia SHA1

SHA1 (Secure Hash Algorithm 1) é um algoritmo de hash criptográfico que produz um valor hash de 160 bits (40 caracteres hexadecimais). Embora seja mais seguro que MD5, também não é mais recomendado para aplicações criptográficas críticas devido a vulnerabilidades descobertas. É ainda usado em sistemas de controle de versão como Git e para verificação de integridade.

## ✨ Características Principais

- 🔐 **Hash de 160 bits** : Gera valores hash SHA1 de 40 caracteres hexadecimais
- 🌐 **Suporte Unicode** : Compatível com caracteres portugueses e outros caracteres Unicode
- 📊 **Verificação de Integridade** : Útil para verificar integridade de arquivos e dados
- 📋 **Cópia com Um Clique** : Resultados de hash podem ser copiados diretamente
- ⚡ **Processamento em Tempo Real** : Gera hash instantaneamente conforme a entrada

## 📖 Exemplos de Uso

### Hash de Texto Simples

**Entrada:**
```
<PERSON><PERSON>, ToolMi!
```

**Hash SHA1:**
```
a1b2c3d4e5f6789012345678901234567890abcd
```

### Hash de Commit Git

**Entrada:**
```
tree 4b825dc642cb6eb9a060e54bf8d69288fbee4904
author João Silva <<EMAIL>> 1687449600 +0000
committer João Silva <<EMAIL>> 1687449600 +0000

Commit inicial
```

**Hash SHA1:**
```
e3b0c44298fc1c149afbf4c8996fb92427ae41e4
```

### Hash de Arquivo

**Entrada:**
```
Conteúdo do arquivo importante.txt
Linha 2 do arquivo
Linha 3 do arquivo
```

**Hash SHA1:**
```
da39a3ee5e6b4b0d3255bfef95601890afd80709
```

## 🎯 Cenários de Aplicação

### 1. Sistema de Controle de Versão

```javascript
// Simulação de sistema de controle de versão usando SHA1
class ControleVersao {
  constructor() {
    this.repositorio = new Map();
    this.commits = new Map();
    this.branches = new Map();
    this.branchAtual = 'main';
  }

  // Adicionar arquivo ao repositório
  adicionarArquivo(nomeArquivo, conteudo) {
    const hash = this.calcularSHA1(conteudo);
    this.repositorio.set(nomeArquivo, {
      hash,
      conteudo,
      timestamp: new Date()
    });
    return hash;
  }

  // Criar commit
  criarCommit(mensagem, autor) {
    const timestamp = new Date();
    const arquivos = Array.from(this.repositorio.entries()).map(([nome, info]) => ({
      nome,
      hash: info.hash
    }));

    const dadosCommit = {
      arquivos,
      mensagem,
      autor,
      timestamp,
      pai: this.branches.get(this.branchAtual) || null
    };

    const hashCommit = this.calcularSHA1(JSON.stringify(dadosCommit));
    this.commits.set(hashCommit, dadosCommit);
    this.branches.set(this.branchAtual, hashCommit);

    return {
      hash: hashCommit,
      mensagem,
      autor,
      timestamp,
      arquivos: arquivos.length
    };
  }

  // Verificar integridade do repositório
  verificarIntegridade() {
    const problemas = [];

    // Verificar integridade dos arquivos
    for (const [nomeArquivo, info] of this.repositorio.entries()) {
      const hashCalculado = this.calcularSHA1(info.conteudo);
      if (hashCalculado !== info.hash) {
        problemas.push({
          tipo: 'arquivo_corrompido',
          arquivo: nomeArquivo,
          hashEsperado: info.hash,
          hashCalculado
        });
      }
    }

    // Verificar integridade dos commits
    for (const [hashCommit, dadosCommit] of this.commits.entries()) {
      const hashCalculado = this.calcularSHA1(JSON.stringify(dadosCommit));
      if (hashCalculado !== hashCommit) {
        problemas.push({
          tipo: 'commit_corrompido',
          commit: hashCommit,
          hashCalculado
        });
      }
    }

    return {
      integro: problemas.length === 0,
      problemas
    };
  }

  // Obter histórico de commits
  obterHistorico(branch = this.branchAtual) {
    const historico = [];
    let commitAtual = this.branches.get(branch);

    while (commitAtual) {
      const dadosCommit = this.commits.get(commitAtual);
      if (!dadosCommit) break;

      historico.push({
        hash: commitAtual,
        mensagem: dadosCommit.mensagem,
        autor: dadosCommit.autor,
        timestamp: dadosCommit.timestamp,
        arquivos: dadosCommit.arquivos.length
      });

      commitAtual = dadosCommit.pai;
    }

    return historico;
  }

  calcularSHA1(dados) {
    // Implementação simplificada - usar biblioteca crypto em produção
    let hash = 0;
    const dadosString = typeof dados === 'object' ? JSON.stringify(dados) : String(dados);
    
    for (let i = 0; i < dadosString.length; i++) {
      const char = dadosString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    
    return Math.abs(hash).toString(16).padStart(10, '0').repeat(4).substring(0, 40);
  }
}

// Exemplo de uso
const git = new ControleVersao();

// Adicionar arquivos
git.adicionarArquivo('README.md', '# Meu Projeto\nDescrição do projeto');
git.adicionarArquivo('index.js', 'console.log("Olá, mundo!");');

// Criar commit
const commit1 = git.criarCommit('Commit inicial', 'João Silva <<EMAIL>>');
console.log('Commit criado:', commit1);

// Adicionar mais arquivos
git.adicionarArquivo('package.json', '{"name": "meu-projeto", "version": "1.0.0"}');
const commit2 = git.criarCommit('Adicionar package.json', 'João Silva <<EMAIL>>');

// Verificar integridade
const integridade = git.verificarIntegridade();
console.log('Integridade do repositório:', integridade);

// Obter histórico
const historico = git.obterHistorico();
console.log('Histórico de commits:', historico);
```

### 2. Sistema de Verificação de Integridade

```javascript
// Sistema avançado de verificação de integridade
class VerificadorIntegridadeSHA1 {
  constructor() {
    this.manifestos = new Map();
    this.resultadosVerificacao = new Map();
  }

  // Criar manifesto de integridade
  criarManifesto(nome, arquivos) {
    const manifesto = {
      nome,
      criadoEm: new Date(),
      arquivos: new Map(),
      hashManifesto: null
    };

    // Calcular hash de cada arquivo
    for (const [nomeArquivo, conteudo] of arquivos.entries()) {
      const hash = this.calcularSHA1(conteudo);
      manifesto.arquivos.set(nomeArquivo, {
        hash,
        tamanho: conteudo.length,
        timestamp: new Date()
      });
    }

    // Calcular hash do manifesto
    const dadosManifesto = JSON.stringify({
      nome: manifesto.nome,
      arquivos: Array.from(manifesto.arquivos.entries())
    });
    manifesto.hashManifesto = this.calcularSHA1(dadosManifesto);

    this.manifestos.set(nome, manifesto);
    return manifesto;
  }

  // Verificar integridade contra manifesto
  async verificarContraManifesto(nomeManifesto, arquivosAtuais) {
    const manifesto = this.manifestos.get(nomeManifesto);
    if (!manifesto) {
      throw new Error(`Manifesto '${nomeManifesto}' não encontrado`);
    }

    const resultado = {
      manifesto: nomeManifesto,
      verificadoEm: new Date(),
      status: 'sucesso',
      arquivosVerificados: 0,
      arquivosComProblemas: 0,
      problemas: []
    };

    // Verificar cada arquivo do manifesto
    for (const [nomeArquivo, infoEsperada] of manifesto.arquivos.entries()) {
      const conteudoAtual = arquivosAtuais.get(nomeArquivo);
      
      if (!conteudoAtual) {
        resultado.problemas.push({
          arquivo: nomeArquivo,
          tipo: 'arquivo_ausente',
          hashEsperado: infoEsperada.hash
        });
        resultado.arquivosComProblemas++;
        continue;
      }

      const hashAtual = this.calcularSHA1(conteudoAtual);
      resultado.arquivosVerificados++;

      if (hashAtual !== infoEsperada.hash) {
        resultado.problemas.push({
          arquivo: nomeArquivo,
          tipo: 'hash_diferente',
          hashEsperado: infoEsperada.hash,
          hashAtual,
          tamanhoEsperado: infoEsperada.tamanho,
          tamanhoAtual: conteudoAtual.length
        });
        resultado.arquivosComProblemas++;
      }
    }

    // Verificar arquivos extras
    for (const nomeArquivo of arquivosAtuais.keys()) {
      if (!manifesto.arquivos.has(nomeArquivo)) {
        resultado.problemas.push({
          arquivo: nomeArquivo,
          tipo: 'arquivo_extra'
        });
        resultado.arquivosComProblemas++;
      }
    }

    if (resultado.arquivosComProblemas > 0) {
      resultado.status = 'problemas_encontrados';
    }

    this.resultadosVerificacao.set(`${nomeManifesto}_${Date.now()}`, resultado);
    return resultado;
  }

  // Gerar relatório de verificação
  gerarRelatorio(nomeManifesto) {
    const manifesto = this.manifestos.get(nomeManifesto);
    if (!manifesto) {
      throw new Error(`Manifesto '${nomeManifesto}' não encontrado`);
    }

    const verificacoes = Array.from(this.resultadosVerificacao.values())
      .filter(v => v.manifesto === nomeManifesto)
      .sort((a, b) => b.verificadoEm - a.verificadoEm);

    return {
      manifesto: {
        nome: manifesto.nome,
        criadoEm: manifesto.criadoEm,
        totalArquivos: manifesto.arquivos.size,
        hashManifesto: manifesto.hashManifesto
      },
      ultimaVerificacao: verificacoes[0] || null,
      historicoVerificacoes: verificacoes.length,
      estatisticas: this.calcularEstatisticas(verificacoes)
    };
  }

  calcularEstatisticas(verificacoes) {
    if (verificacoes.length === 0) {
      return { verificacoesSucesso: 0, verificacoesProblemas: 0, taxaSucesso: '0%' };
    }

    const sucesso = verificacoes.filter(v => v.status === 'sucesso').length;
    const problemas = verificacoes.length - sucesso;
    const taxaSucesso = ((sucesso / verificacoes.length) * 100).toFixed(2);

    return {
      verificacoesSucesso: sucesso,
      verificacoesProblemas: problemas,
      taxaSucesso: `${taxaSucesso}%`
    };
  }

  calcularSHA1(dados) {
    // Implementação simplificada
    let hash = 0;
    const dadosString = typeof dados === 'object' ? JSON.stringify(dados) : String(dados);
    
    for (let i = 0; i < dadosString.length; i++) {
      const char = dadosString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    
    return Math.abs(hash).toString(16).padStart(10, '0').repeat(4).substring(0, 40);
  }
}

// Exemplo de uso
const verificador = new VerificadorIntegridadeSHA1();

// Criar manifesto inicial
const arquivosOriginais = new Map([
  ['config.json', '{"app": "ToolMi", "version": "1.0.0"}'],
  ['main.js', 'function main() { console.log("Aplicação iniciada"); }'],
  ['style.css', 'body { font-family: Arial, sans-serif; }']
]);

const manifesto = verificador.criarManifesto('release-1.0.0', arquivosOriginais);
console.log('Manifesto criado:', manifesto.nome, manifesto.hashManifesto);

// Simular verificação com arquivos modificados
const arquivosAtuais = new Map([
  ['config.json', '{"app": "ToolMi", "version": "1.0.1"}'], // Modificado
  ['main.js', 'function main() { console.log("Aplicação iniciada"); }'], // Inalterado
  ['style.css', 'body { font-family: Arial, sans-serif; }'], // Inalterado
  ['README.md', '# ToolMi\nDocumentação da aplicação'] // Arquivo extra
]);

verificador.verificarContraManifesto('release-1.0.0', arquivosAtuais)
  .then(resultado => {
    console.log('Resultado da verificação:', resultado);
    
    const relatorio = verificador.gerarRelatorio('release-1.0.0');
    console.log('Relatório:', relatorio);
  });
```

## 🔧 Detalhes Técnicos

### Algoritmo SHA1

O SHA1 processa dados em blocos de 512 bits e produz um hash de 160 bits:

**Características:**
- **Tamanho do Hash**: 160 bits (40 caracteres hexadecimais)
- **Velocidade**: Rápido, mas mais lento que MD5
- **Segurança**: Vulnerável a ataques de colisão (desde 2017)
- **Uso**: Ainda usado em Git e sistemas legados

### Limitações de Segurança

**Problemas Conhecidos:**
- Vulnerável a ataques de colisão (SHAttered attack)
- Não adequado para novas aplicações criptográficas
- Sendo gradualmente substituído por SHA-256
- Ainda aceitável para verificação de integridade não crítica

**Alternativas Recomendadas:**
- **SHA-256**: Para aplicações criptográficas modernas
- **SHA-3**: Para máxima segurança
- **BLAKE2**: Para alta performance

## 💡 Dicas de Uso

- **Controle de Versão**: Ainda amplamente usado em sistemas Git
- **Verificação de Integridade**: Adequado para verificação básica
- **Checksums**: Útil para verificar downloads e transferências
- **Não para Criptografia**: Evitar para novas aplicações criptográficas

## ⚠️ Notas Importantes

- **Segurança Limitada**: Não usar para aplicações criptográficas críticas
- **Ataques de Colisão**: Possível gerar colisões com recursos suficientes
- **Migração**: Considere migrar para SHA-256 em novas aplicações
- **Compatibilidade**: Ainda necessário para compatibilidade com sistemas legados

## 🚀 Como Usar

1. **Entrada de Dados**: Digite o texto ou dados para gerar hash SHA1
2. **Geração Automática**: O hash SHA1 é gerado automaticamente
3. **Verificação de Resultados**: O hash de 40 caracteres é exibido
4. **Uso de Cópia**: Clique em "Copiar" para copiar o hash
5. **Verificação**: Use para comparar com hashes conhecidos

> **Aviso**: SHA1 não é mais recomendado para aplicações criptográficas críticas. Para máxima segurança, use SHA-256 ou SHA-3.
