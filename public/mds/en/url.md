# URL Encoder Decoder Tool

URL encoding (percent encoding) is an encoding mechanism for safely transmitting data in URLs. When URLs contain special characters, Chinese characters, or spaces, they need to be encoded to ensure correct transmission. This tool provides fast URL encoding and decoding functionality.

## ✨ Key Features

- 🔄 **Bidirectional Conversion**: Supports URL encoding and decoding operations
- 🌐 **Unicode Support**: Perfect handling of Unicode character encoding
- ⚡ **Fast Processing**: Instant encoding and decoding without waiting
- 📋 **One-click Copy**: Encoding results can be directly copied for use
- 🔧 **Error Handling**: Smart detection of format errors with repair suggestions

## 📖 Usage Examples

### URL Encoding Example

**Original URL:**
```
https://example.com/search?q=hello world&type=1
```

**After Encoding:**
```
https%3A//example.com/search%3Fq%3Dhello%20world%26type%3D1
```

### URL Decoding Example

**Encoded URL:**
```
https%3A//www.toolmi.com/en/tools%3Fcategory%3Dencoding
```

**After Decoding:**
```
https://www.toolmi.com/en/tools?category=encoding
```

## 🎯 Application Scenarios

### 1. Web Development

Handling URL parameters in web development:

```javascript
// Build URL with parameters
const baseUrl = 'https://api.example.com/search'
const query = 'online tools'
const category = 'utilities'

// Encode parameters
const encodedQuery = encodeURIComponent(query)
const encodedCategory = encodeURIComponent(category)

const fullUrl = `${baseUrl}?q=${encodedQuery}&category=${encodedCategory}`
console.log(fullUrl)
// Output: https://api.example.com/search?q=online%20tools&category=utilities
```

### 2. Form Data Submission

Handling special characters in forms:

```html
<!-- HTML Form -->
<form action="/submit" method="GET">
  <input name="username" value="john@company">
  <input name="message" value="Hello World!">
  <button type="submit">Submit</button>
</form>

<!-- URL after submission -->
<!-- /submit?username=john%40company&message=Hello%20World%21 -->
```

### 3. API Interface Calls

Encoding parameters in API calls:

```javascript
// Search API call
async function searchTools(keyword) {
  const encodedKeyword = encodeURIComponent(keyword)
  const response = await fetch(`/api/search?q=${encodedKeyword}`)
  return response.json()
}

// Usage example
searchTools('JSON to YAML')
// Actual request: /api/search?q=JSON%20to%20YAML
```

### 4. Social Media Sharing

Generate social media sharing links:

```javascript
// Generate Twitter share URL
function generateTwitterShareUrl(text, url) {
  const encodedText = encodeURIComponent(text)
  const encodedUrl = encodeURIComponent(url)
  
  return `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`
}

// Usage example
const shareUrl = generateTwitterShareUrl(
  'Found a great online tools website!',
  'https://www.toolmi.com'
)
```

## 🔧 Technical Details

### Encoding Rules

URL encoding follows RFC 3986 standard:

**Characters that need encoding:**
- Reserved characters: `:/?#[]@!$&'()*+,;=`
- Non-ASCII characters: Chinese, Japanese, Korean, etc.
- Unsafe characters: spaces, quotes, angle brackets, etc.
- Control characters: newlines, tabs, etc.

**Encoding format:**
- Use percent sign `%` followed by two hexadecimal digits
- Example: space encoded as `%20`, Chinese "你" encoded as `%E4%BD%A0`

**Characters that don't need encoding:**
- Letters: `A-Z`, `a-z`
- Numbers: `0-9`
- Safe characters: `-`, `_`, `.`, `~`

### Encoding Types

Different encoding functions have different purposes:

**encodeURI():**
- Encodes the entire URI
- Does not encode URI reserved characters (like `:/?#`)
- Suitable for encoding complete URLs

**encodeURIComponent():**
- Encodes URI component parts
- Encodes all special characters, including reserved characters
- Suitable for encoding URL parameter values

**Comparison example:**
```javascript
const url = 'https://example.com/search?q=hello world'

console.log(encodeURI(url))
// https://example.com/search?q=hello%20world

console.log(encodeURIComponent(url))
// https%3A%2F%2Fexample.com%2Fsearch%3Fq%3Dhello%20world
```

### Unicode Character Processing

URL encoding process for Unicode characters:

1. **UTF-8 Encoding**: Convert Unicode characters to UTF-8 byte sequence
2. **Percent Encoding**: Convert each byte to `%XX` format

**Example:**
```
Character: 你
UTF-8: E4 BD A0
URL Encoding: %E4%BD%A0
```

## 💡 Usage Tips

- **Choose appropriate encoding function**: Use `encodeURI()` for entire URL, `encodeURIComponent()` for parameter values
- **Avoid double encoding**: Check if string is already encoded to avoid multiple encoding
- **Handle special cases**: Note that plus sign `+` sometimes represents space
- **Test validation**: Test if URL works correctly after encoding

## ⚠️ Important Notes

- **Encoding scope**: Only encode parts that need encoding, avoid using `encodeURIComponent()` on entire URL
- **Decoding security**: Be careful of XSS attacks when decoding, validate decoded results
- **Character set consistency**: Ensure encoding and decoding use the same character set (usually UTF-8)
- **Browser compatibility**: Different browsers may encode certain characters slightly differently

## 🚀 Getting Started

1. **Choose Operation**: Click "URL Encode" or "URL Decode" button
2. **Input Content**: Paste the URL or text to be processed in the input box
3. **Execute Conversion**: Click the corresponding button to encode or decode
4. **Copy Result**: Click the "Copy" button to copy the result to clipboard
5. **Example Demo**: Click "Load Example" to view demo data

> **Tip**: This tool performs encoding and decoding locally on the client side and does not upload your data to the server, ensuring privacy and security.
