# UUID Generator

UUID (Universally Unique Identifier) is a standardized identifier used to uniquely identify information in distributed systems. UUIDs consist of 128 bits, typically represented as 32 hexadecimal digits separated by hyphens into five groups.

## ✨ Key Features

- 🔢 **Multiple Version Support**: Supports UUID v1, v4, and other versions
- ⚡ **Batch Generation**: Supports generating multiple UUIDs at once
- 📋 **Multiple Formats**: Supports standard format, no-hyphen format, etc.
- 💾 **One-click Copy**: Generated UUIDs can be directly copied for use
- 🔧 **Real-time Generation**: Click to generate new UUIDs instantly

## 📖 Usage Examples

### Standard UUID v4

**Generation Example:**
```
f47ac10b-58cc-4372-a567-0e02b2c3d479
```

**Characteristics:**
- Randomly generated, no time information
- Extremely low collision probability
- Most commonly used UUID version

### UUID v1 (Time-based)

**Generation Example:**
```
6ba7b810-9dad-11d1-80b4-00c04fd430c8
```

**Characteristics:**
- Contains timestamp information
- Contains MAC address information
- Generation time can be inferred

### No-hyphen Format

**Generation Example:**
```
f47ac10b58cc4372a5670e02b2c3d479
```

**Characteristics:**
- 32 consecutive hexadecimal characters
- Suitable for certain databases or systems

## 🎯 Application Scenarios

### 1. Database Primary Keys

Using UUIDs as primary keys in databases:

```sql
-- Create table with UUID primary key
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert data (auto-generate UUID)
INSERT INTO users (username, email) 
VALUES ('johnsmith', '<EMAIL>');

-- Query data
SELECT * FROM users WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
```

### 2. Distributed Systems

Generating unique identifiers in distributed systems:

```javascript
// Request tracking in microservices
class RequestTracker {
  constructor() {
    this.requestId = generateUUID()
    this.timestamp = new Date().toISOString()
  }

  log(message) {
    console.log(`[${this.requestId}] ${this.timestamp}: ${message}`)
  }
}

// Usage example
const tracker = new RequestTracker()
tracker.log('Starting user request processing')
tracker.log('Calling user service')
tracker.log('Calling order service')
tracker.log('Request processing completed')

// Output example:
// [f47ac10b-58cc-4372-a567-0e02b2c3d479] 2024-06-15T10:30:00.000Z: Starting user request processing
```

### 3. File Naming

Generating unique names for uploaded files:

```javascript
// File upload handling
function handleFileUpload(file) {
  const fileExtension = file.name.split('.').pop()
  const uniqueFileName = `${generateUUID()}.${fileExtension}`
  
  // Save file
  const filePath = `/uploads/${uniqueFileName}`
  saveFile(file, filePath)
  
  return {
    originalName: file.name,
    fileName: uniqueFileName,
    filePath: filePath,
    uploadTime: new Date().toISOString()
  }
}

// Usage example
const uploadResult = handleFileUpload(userFile)
console.log(uploadResult)
// {
//   originalName: "document.pdf",
//   fileName: "f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   filePath: "/uploads/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   uploadTime: "2024-06-15T10:30:00.000Z"
// }
```

### 4. Session Management

Generating unique identifiers for user sessions:

```javascript
// Session management system
class SessionManager {
  constructor() {
    this.sessions = new Map()
  }

  createSession(userId) {
    const sessionId = generateUUID()
    const session = {
      id: sessionId,
      userId: userId,
      createdAt: new Date(),
      lastAccess: new Date(),
      data: {}
    }
    
    this.sessions.set(sessionId, session)
    return sessionId
  }

  getSession(sessionId) {
    const session = this.sessions.get(sessionId)
    if (session) {
      session.lastAccess = new Date()
    }
    return session
  }

  destroySession(sessionId) {
    return this.sessions.delete(sessionId)
  }
}

// Usage example
const sessionManager = new SessionManager()
const sessionId = sessionManager.createSession('user123')
console.log('Session ID:', sessionId)
// Session ID: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

### 5. API Key Generation

Generating unique keys for API access:

```javascript
// API key management
class ApiKeyManager {
  constructor() {
    this.apiKeys = new Map()
  }

  generateApiKey(userId, permissions = []) {
    const apiKey = generateUUID()
    const keyInfo = {
      key: apiKey,
      userId: userId,
      permissions: permissions,
      createdAt: new Date(),
      lastUsed: null,
      isActive: true
    }
    
    this.apiKeys.set(apiKey, keyInfo)
    return apiKey
  }

  validateApiKey(apiKey) {
    const keyInfo = this.apiKeys.get(apiKey)
    if (keyInfo && keyInfo.isActive) {
      keyInfo.lastUsed = new Date()
      return keyInfo
    }
    return null
  }

  revokeApiKey(apiKey) {
    const keyInfo = this.apiKeys.get(apiKey)
    if (keyInfo) {
      keyInfo.isActive = false
      return true
    }
    return false
  }
}

// Usage example
const apiManager = new ApiKeyManager()
const apiKey = apiManager.generateApiKey('user123', ['read', 'write'])
console.log('API Key:', apiKey)
// API Key: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

## 🔧 Technical Details

### UUID Versions

Different UUID versions have different generation methods:

**UUID v1 (Time-based):**
- Contains timestamp (60 bits)
- Contains clock sequence (14 bits)
- Contains node identifier (48 bits, usually MAC address)
- Generation time and location can be inferred

**UUID v4 (Random):**
- 122 random bits
- 6 bits for version and variant identifiers
- Collision probability approximately 1/2^122
- Most commonly used version

**UUID v5 (Name-based):**
- Uses SHA-1 hash algorithm
- Generated based on namespace and name
- Same input always produces same UUID

### Format Structure

Standard UUID format: `xxxxxxxx-xxxx-Mxxx-Nxxx-xxxxxxxxxxxx`

- **M**: Version number (1, 4, 5, etc.)
- **N**: Variant identifier (usually 8, 9, A, B)
- **x**: Hexadecimal digits (0-9, A-F)

### Collision Probability

UUID v4 has extremely low collision probability:

- Total of 2^122 possible UUIDs
- 50% collision probability when generating 10^18 UUIDs
- Can be considered unique in practical applications

## 💡 Usage Tips

- **Version Selection**: Generally use UUID v4, use v1 when time information is needed
- **Storage Optimization**: Use BINARY(16) storage in databases to save space
- **Index Performance**: Consider index performance impact when using UUIDs as primary keys
- **Format Consistency**: Maintain consistent UUID format within the same system

## ⚠️ Important Notes

- **Performance Impact**: UUIDs as primary keys may affect database performance
- **Sorting Issues**: UUIDs cannot be sorted by generation time (unless using v1)
- **Storage Space**: UUIDs occupy more storage space (36 characters or 16 bytes)
- **Readability**: UUIDs are less intuitive than auto-increment IDs

## 🚀 Getting Started

1. **Choose Version**: Select UUID version based on requirements
2. **Set Quantity**: Choose the number of UUIDs to generate
3. **Select Format**: Choose standard format or no-hyphen format
4. **Generate UUID**: Click generate button to create UUIDs
5. **Copy for Use**: Click copy button to copy UUIDs to clipboard

> **Tip**: This tool generates UUIDs locally on the client side and does not upload any data to the server, ensuring privacy and security.
