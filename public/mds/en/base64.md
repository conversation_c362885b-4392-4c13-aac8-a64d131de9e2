# Base64 Encoder Decoder Tool

Base64 is an encoding method based on 64 printable characters to represent binary data. It is commonly used for storing and transmitting binary data, especially in text protocols.

## 🎯 What is Base64?

Base64 encoding is a **binary-to-text** encoding scheme that converts binary data into ASCII string format. The encoded data is approximately 33% larger than the original data.

### Character Set

Base64 uses the following 64 characters:
- **A-Z**: 26 uppercase letters
- **a-z**: 26 lowercase letters
- **0-9**: 10 digits
- **+** and **/**: 2 special characters
- **=**: padding character

## ✨ Key Features

- 🚀 **Bidirectional Conversion**: Supports both encoding and decoding operations
- 📱 **Multi-format Support**: Supports text, URL, files and other data formats
- 🔒 **Secure and Reliable**: Uses standard Base64 algorithm to ensure data integrity
- 💾 **File Download**: Supports saving results as files
- 🌐 **Unicode Support**: Perfect support for Unicode character encoding and decoding

## 📖 Usage Examples

### Text Encoding Example

**Original Text:**
```
Hello World! 你好世界！
```

**Base64 Encoded:**
```
SGVsbG8gV29ybGQhIOS9oOWlveS4lueVjO+8gQ==
```

### URL Encoding Example

**Original URL:**
```
https://www.gongjumi.com/en/encrypt/base64?text=test
```

**Base64 Encoded:**
```
aHR0cHM6Ly93d3cuZ29uZ2p1bWkuY29tL2VuL2VuY3J5cHQvYmFzZTY0P3RleHQ9dGVzdA==
```

### JSON Data Encoding

**Original JSON:**
```json
{
  "name": "ToolMi",
  "type": "Online Tools",
  "features": ["encode", "decode", "format"]
}
```

**Base64 Encoded:**
```
ewogICJuYW1lIjogIlRvb2xNaSIsCiAgInR5cGUiOiAiT25saW5lIFRvb2xzIiwKICAiZmVhdHVyZXMiOiBbImVuY29kZSIsICJkZWNvZGUiLCAiZm9ybWF0Il0KfQ==
```

## 🎯 Application Scenarios

### 1. Data Transmission

Transmitting binary data in HTTP protocol:

```javascript
// Convert image to Base64 for transmission
const imageBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
```

### 2. Email Attachments

Email systems use Base64 encoding for attachments:

```
Content-Type: image/jpeg
Content-Transfer-Encoding: base64

/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQY...
```

### 3. Data Storage

Storing binary data in JSON or XML:

```json
{
  "user": {
    "name": "John Doe",
    "avatar": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD..."
  }
}
```

### 4. API Interface

Transmitting file data in RESTful API:

```javascript
// Upload file to server
const uploadData = {
  filename: "document.pdf",
  content: "JVBERi0xLjQKJcOkw7zDtsO8CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo..."
}
```

## 🔧 Technical Principles

### Encoding Process

1. **Grouping**: Divide input data into 3-byte (24-bit) groups
2. **Conversion**: Split each 24-bit group into 4 6-bit blocks
3. **Mapping**: Map each 6-bit value to Base64 character table
4. **Padding**: If the last group has less than 3 bytes, pad with `=`

### Decoding Process

1. **Remove Padding**: Remove trailing `=` characters
2. **Reverse Mapping**: Convert Base64 characters back to 6-bit values
3. **Reassemble**: Reassemble 4 6-bit values into 3 8-bit bytes
4. **Output**: Get original binary data

## 💡 Usage Tips

### Encoding Tips
- **Large File Processing**: For large files, consider chunked processing to avoid memory overflow
- **URL Safety**: For URL usage, consider using URL-safe Base64 variant
- **Performance Optimization**: Consider caching results for frequent encoding/decoding

### Decoding Considerations
- **Format Validation**: Ensure input is valid Base64 format
- **Character Check**: Remove non-Base64 characters (spaces, newlines, etc.)
- **Padding Handling**: Properly handle padding characters

## ⚠️ Important Notes

1. **Not Encryption**: Base64 is an encoding method, not an encryption algorithm, and provides no security
2. **Size Increase**: Encoded data size increases by approximately 33%
3. **Character Limitation**: Can only contain characters from Base64 character set
4. **Line Break Handling**: Some systems insert line breaks in long Base64 strings

## 🚀 Getting Started

1. **Encoding**: Enter text to encode in the input box, click "Base64 Encode"
2. **Decoding**: Enter Base64 string in the input box, click "Base64 Decode"
3. **Copy**: Click "Copy" button to copy result to clipboard
4. **Download**: Click "Download" button to save result as file
5. **Example**: Click "Load Example" to view demo data

> **Tip**: The tool supports Unicode characters and automatically handles UTF-8 encoding conversion.
