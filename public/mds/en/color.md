# Color Picker Tool

This tool provides a comprehensive color selection and management system. It supports multiple color format conversions, palette generation, contrast analysis, and theme systems, making it an essential tool for designers and developers.

## ✨ Key Features

- 🎨 **Multi-Format Support** : Convert between HEX, RGB, HSL, HSV and more
- 🌈 **Palette Generation** : Automatically generate harmonious color schemes
- 📊 **Contrast Analysis** : WCAG accessibility standard compliance checking
- 🎯 **Precise Color Picking** : Support for precise color value input and adjustment
- 💾 **Color History** : Automatically save recently used colors

## 📖 Usage Examples

### Basic Color Conversion

**HEX to RGB:**
```
Input: #3498db
Output: rgb(52, 152, 219)
```

**RGB to HSL:**
```
Input: rgb(52, 152, 219)
Output: hsl(204, 70%, 53%)
```

**HSL to HSV:**
```
Input: hsl(204, 70%, 53%)
Output: hsv(204, 76%, 86%)
```

### Palette Generation

**Complementary Color Scheme:**
```
Base Color: #3498db (Blue)
Complement: #db7834 (Orange)
```

**Triadic Color Scheme:**
```
Base Color: #3498db
Triadic 1: #34db98
Triadic 2: #db3456
```

**Analogous Color Scheme:**
```
Base Color: #3498db
Analogous 1: #3456db
Analogous 2: #34dbdb
```

## 🎯 Application Scenarios

### 1. Design System Development

```css
/* Design system generated from color tool */
:root {
  /* Primary colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Semantic colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Neutral colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

/* Component styles */
.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: var(--primary-700);
}

.btn-primary:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}
```

### 2. Brand Color System

```javascript
// Brand color management system
class BrandColorSystem {
  constructor() {
    this.brandColors = {
      primary: '#2563eb',
      secondary: '#7c3aed',
      accent: '#06b6d4',
      neutral: '#6b7280'
    };
    
    this.colorPalettes = new Map();
    this.generatePalettes();
  }

  // Generate complete palettes
  generatePalettes() {
    Object.entries(this.brandColors).forEach(([name, color]) => {
      this.colorPalettes.set(name, this.generateShades(color));
    });
  }

  // Generate color shades
  generateShades(baseColor) {
    const shades = {};
    const lightness = [95, 90, 80, 70, 60, 50, 40, 30, 20, 10];
    
    lightness.forEach((l, index) => {
      const shade = (index + 1) * 100;
      shades[shade] = this.adjustLightness(baseColor, l);
    });
    
    return shades;
  }

  // Check color contrast
  checkContrast(color1, color2) {
    const luminance1 = this.getLuminance(color1);
    const luminance2 = this.getLuminance(color2);
    
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);
    
    const contrast = (lighter + 0.05) / (darker + 0.05);
    
    return {
      ratio: contrast,
      aa: contrast >= 4.5,
      aaa: contrast >= 7,
      aaLarge: contrast >= 3
    };
  }

  // Get relative luminance
  getLuminance(hex) {
    const rgb = this.hexToRgb(hex);
    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  // HEX to RGB conversion
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }
}

// Usage example
const brandSystem = new BrandColorSystem();

// Check contrast
const contrast = brandSystem.checkContrast('#2563eb', '#ffffff');
console.log('Contrast check:', contrast);

// Get primary palette
const primaryPalette = brandSystem.getPalette('primary');
console.log('Primary palette:', primaryPalette);
```

### 3. Dynamic Theme System

```javascript
// Dynamic theme switching system
class ThemeManager {
  constructor() {
    this.themes = new Map();
    this.currentTheme = null;
    this.initializeDefaultThemes();
  }

  // Initialize default themes
  initializeDefaultThemes() {
    // Light theme
    this.addTheme('light', {
      name: 'Light Theme',
      colors: {
        background: '#ffffff',
        surface: '#f8fafc',
        primary: '#3b82f6',
        secondary: '#64748b',
        accent: '#06b6d4',
        text: '#1e293b',
        textSecondary: '#64748b',
        border: '#e2e8f0'
      }
    });

    // Dark theme
    this.addTheme('dark', {
      name: 'Dark Theme',
      colors: {
        background: '#0f172a',
        surface: '#1e293b',
        primary: '#60a5fa',
        secondary: '#94a3b8',
        accent: '#22d3ee',
        text: '#f1f5f9',
        textSecondary: '#94a3b8',
        border: '#334155'
      }
    });
  }

  // Apply theme
  applyTheme(themeId) {
    const theme = this.themes.get(themeId);
    if (!theme) {
      throw new Error(`Theme '${themeId}' not found`);
    }

    const root = document.documentElement;
    
    // Apply color variables
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    this.currentTheme = themeId;
    localStorage.setItem('preferred-theme', themeId);

    return theme;
  }

  // Detect system theme
  detectSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }
}
```

## 🔧 Technical Details

### Supported Color Formats

**HEX (Hexadecimal):**
- Format: `#RRGGBB` or `#RGB`
- Example: `#3498db`, `#fff`

**RGB (Red, Green, Blue):**
- Format: `rgb(r, g, b)`
- Range: 0-255 for each channel
- Example: `rgb(52, 152, 219)`

**HSL (Hue, Saturation, Lightness):**
- Format: `hsl(h, s%, l%)`
- H: 0-360°, S: 0-100%, L: 0-100%
- Example: `hsl(204, 70%, 53%)`

**HSV/HSB (Hue, Saturation, Value/Brightness):**
- Similar to HSL but uses Value instead of Lightness
- Example: `hsv(204, 76%, 86%)`

### Color Theory

**Color Harmony:**
- **Complementary**: Opposite colors on the color wheel
- **Analogous**: Adjacent colors on the color wheel
- **Triadic**: Three equally spaced colors on the wheel
- **Monochromatic**: Variations of a single color

**Contrast and Accessibility:**
- **WCAG AA**: Minimum contrast ratio 4.5:1
- **WCAG AAA**: Minimum contrast ratio 7:1
- **Large Text**: Minimum contrast ratio 3:1

## 💡 Usage Tips

- **Accessibility** : Always check contrast between text and background
- **Consistency** : Use a consistent color system throughout your project
- **Cultural Context** : Consider cultural meanings of colors
- **Device Testing** : Test colors on different devices and lighting conditions

## ⚠️ Important Notes

- **Color Blindness** : Consider users with color vision deficiencies
- **Print Output** : Colors may appear different when printed
- **Monitor Differences** : Monitor calibration affects color perception
- **Cultural Differences** : Color meanings vary across cultures

## 🚀 How to Use

1. **Select Color** : Use the visual picker or input color codes
2. **Format Conversion** : Automatically view colors in different formats
3. **Generate Palettes** : Choose harmony type to generate color schemes
4. **Contrast Analysis** : Check accessibility of color combinations
5. **Copy Usage** : Click any code to copy it

> **Tip** : This tool processes locally on the client side and doesn't send data to servers, ensuring privacy and fast response.
