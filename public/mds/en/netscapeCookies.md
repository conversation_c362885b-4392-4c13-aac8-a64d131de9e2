# Netscape Cookies Formatter Tool

Netscape Cookies format is a classic cookie storage format originally created by Netscape browser, still widely used for browser data import and export. This tool supports bidirectional conversion between Netscape format and JSON format, making it convenient for developers to process and manage cookie data.

## ✨ Key Features

- 🔄 **Bidirectional Conversion**: Supports conversion between Netscape format and JSON format
- 📊 **Format Validation**: Automatically validates cookie format correctness
- 🎨 **Format Beautification**: Automatically formats output results to improve readability
- 📋 **One-click Copy**: Conversion results can be directly copied for use
- 🔧 **Error Handling**: Friendly error messages and repair suggestions

## 📖 Usage Examples

### Netscape Format Example

**Standard Netscape Cookies Format:**
```
# Netscape HTTP Cookie File
# This is a generated file!  Do not edit.

.example.com	TRUE	/	FALSE	1640995200	session_id	abc123def456
.example.com	TRUE	/	FALSE	1640995200	user_pref	dark_theme
example.com	FALSE	/login	TRUE	1640995200	csrf_token	xyz789uvw012
```

### JSON Format Example

**Corresponding JSON Format:**
```json
[
  {
    "domain": ".example.com",
    "flag": true,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "session_id",
    "value": "abc123def456"
  },
  {
    "domain": ".example.com",
    "flag": true,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "user_pref",
    "value": "dark_theme"
  },
  {
    "domain": "example.com",
    "flag": false,
    "path": "/login",
    "secure": true,
    "expiration": 1640995200,
    "name": "csrf_token",
    "value": "xyz789uvw012"
  }
]
```

## 🎯 Application Scenarios

### 1. Browser Data Migration

Migrating cookie data between different browsers:

```
Scenario: Export cookies from Chrome to Firefox

Steps:
1. Export Netscape format cookie file from Chrome
2. Use tool to convert to JSON format for editing
3. Convert back to Netscape format
4. Import to Firefox browser

Applications:
- Browser switching
- Development environment synchronization
- Test data preparation
```

### 2. Web Development Debugging

Managing and debugging cookies during development:

```javascript
// Development scenario: Batch setting test cookies
const testCookies = [
  {
    "domain": "localhost",
    "flag": false,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "debug_mode",
    "value": "true"
  },
  {
    "domain": "localhost",
    "flag": false,
    "path": "/admin",
    "secure": true,
    "expiration": 1640995200,
    "name": "admin_token",
    "value": "dev_token_123"
  }
]

// Convert to Netscape format and import to browser
```

### 3. Automated Testing

Preset cookie states in automated testing:

```python
# Selenium test example
from selenium import webdriver

# Load cookies from Netscape file
def load_cookies_from_netscape(driver, cookie_file):
    with open(cookie_file, 'r') as f:
        for line in f:
            if line.startswith('#') or not line.strip():
                continue
            
            parts = line.strip().split('\t')
            if len(parts) == 7:
                cookie = {
                    'name': parts[5],
                    'value': parts[6],
                    'domain': parts[0],
                    'path': parts[2],
                    'secure': parts[3] == 'TRUE'
                }
                driver.add_cookie(cookie)

# Usage example
driver = webdriver.Chrome()
driver.get("https://example.com")
load_cookies_from_netscape(driver, "test_cookies.txt")
```

### 4. Data Analysis

Analyzing website cookie usage:

```javascript
// Cookie analysis script
function analyzeCookies(netscapeData) {
  const cookies = parseNetscapeCookies(netscapeData)
  
  const analysis = {
    totalCookies: cookies.length,
    domains: [...new Set(cookies.map(c => c.domain))],
    secureCount: cookies.filter(c => c.secure).length,
    sessionCount: cookies.filter(c => c.expiration === 0).length,
    expiredCount: cookies.filter(c => 
      c.expiration > 0 && c.expiration < Date.now() / 1000
    ).length
  }
  
  return analysis
}
```

## 🔧 Technical Details

### Netscape Format Specification

Netscape Cookies file format description:

**File Header:**
```
# Netscape HTTP Cookie File
# This is a generated file!  Do not edit.
```

**Field Format (Tab-separated):**
1. **domain**: Cookie domain
2. **flag**: Whether to include subdomains (TRUE/FALSE)
3. **path**: Cookie path
4. **secure**: HTTPS only transmission (TRUE/FALSE)
5. **expiration**: Expiration time (Unix timestamp)
6. **name**: Cookie name
7. **value**: Cookie value

**Example Line:**
```
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123
```

### Conversion Rules

Format conversion follows these rules:

**Netscape to JSON:**
- Parse tab-separated fields
- Convert boolean values (TRUE/FALSE → true/false)
- Handle timestamp format
- Ignore comment lines and empty lines

**JSON to Netscape:**
- Add standard file header
- Convert boolean values (true/false → TRUE/FALSE)
- Format timestamps
- Use tab separators for fields

**Special Handling:**
- Escaping of null values and special characters
- Domain name format standardization
- Path normalization processing

## 💡 Usage Tips

- **Format Check**: Validate input format correctness before conversion
- **Backup Data**: Please backup important cookie data before conversion
- **Timestamp Conversion**: Pay attention to timestamp format (Unix timestamp)
- **Domain Standards**: Ensure domain format meets standards

## ⚠️ Important Notes

- **Strict Format**: Netscape format has strict requirements for tabs and field order
- **Character Encoding**: Ensure files use UTF-8 encoding
- **Security**: Cookie data may contain sensitive information, pay attention to protection
- **Compatibility**: Different browsers may have varying support for cookie import

## 🚀 Getting Started

1. **Choose Conversion Direction**: Click "Netscape to JSON" or "JSON to Netscape" button
2. **Input Data**: Paste the data to be converted in the input box
3. **Execute Conversion**: Click the conversion button to perform format conversion
4. **Copy Result**: Click the "Copy" button to copy the result to clipboard
5. **Example Demo**: Click "Load Example" to view demo data

> **Tip**: This tool performs conversion locally on the client side and does not upload your cookie data to the server, ensuring privacy and security.
