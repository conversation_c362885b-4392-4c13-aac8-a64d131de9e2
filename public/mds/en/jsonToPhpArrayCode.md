# JSON to PHP Array Converter Tool

This tool allows you to **quickly convert JSON data into PHP array code**, making it easy to use directly in your PHP projects. It supports nested structures, Chinese characters, booleans, numbers, and all common data types.

## ✨ Key Features

- 🚀 **One-Click Conversion**: Paste your JSON content and instantly generate PHP array code
- 🎯 **Clean Formatting**: The generated code is well-formatted and ready to copy and use
- 🌐 **Multi-Type Support**: Handles strings, numbers, booleans, arrays, objects, and more
- 🔧 **Special Character Handling**: Automatically escapes quotes, line breaks, and other special characters
- 📱 **Responsive Design**: Works well on phones, tablets, and desktops

## 📖 Usage Examples

### Basic Example

Suppose you have the following JSON data:

```json
{
  "name": "ChatGPT",
  "features": ["AI", "NLP", "Code"],
  "active": true,
  "version": 4.0
}
```

Converted PHP array code:

```php
<?php

$data = [
  'name' => 'ChatGPT',
  'features' => [
    'AI',
    'NLP',
    'Code'
  ],
  'active' => true,
  'version' => 4.0
];
```

### Complex Nested Example

For more complex nested structures:

```json
{
  "user": {
    "id": 123,
    "profile": {
      "name": "张三",
      "email": "<EMAIL>",
      "preferences": {
        "theme": "dark",
        "notifications": true
      }
    },
    "roles": ["admin", "editor"]
  }
}
```

Converted result:

```php
<?php

$data = [
  'user' => [
    'id' => 123,
    'profile' => [
      'name' => '张三',
      'email' => '<EMAIL>',
      'preferences' => [
        'theme' => 'dark',
        'notifications' => true
      ]
    ],
    'roles' => [
      'admin',
      'editor'
    ]
  ]
];
```

## 🎯 Use Cases

### 1. Config File Generation

Convert JSON configurations to PHP config arrays:

```php
<?php
// config/app.php
return [
  'name' => 'My Application',
  'debug' => true,
  'timezone' => 'Asia/Shanghai'
];
```

### 2. API Mock Data

Quickly create mock data for testing:

```php
<?php
// Mock API response
$mockData = [
  'status' => 'success',
  'data' => [
    'users' => [
      ['id' => 1, 'name' => 'User 1'],
      ['id' => 2, 'name' => 'User 2']
    ]
  ]
];
```

### 3. Database Seed Files

Generate seed data for database initialization:

```php
<?php
// database/seeders/UserSeeder.php
$users = [
  [
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'role' => 'admin'
  ],
  [
    'name' => 'Regular User',
    'email' => '<EMAIL>',
    'role' => 'user'
  ]
];
```

## 🔧 Technical Details

### Data Type Conversion

| JSON Type | PHP Type | Example |
|-----------|----------|---------|
| string    | string   | `"hello"` → `'hello'` |
| number    | int/float| `42` → `42`, `3.14` → `3.14` |
| boolean   | boolean  | `true` → `true`, `false` → `false` |
| null      | null     | `null` → `null` |
| array     | array    | `[1,2,3]` → `[1, 2, 3]` |
| object    | array    | `{"key": "value"}` → `['key' => 'value']` |

### Special Character Handling

The tool automatically handles the following:

- **Single quote**: `'` → `\\'`
- **Backslash**: `\` → `\\\\`
- **Newline**: `\n` → `\\n`
- **Tab**: `\t` → `\\t`
- **Carriage return**: `\r` → `\\r`

## 💡 Tips for Use

1. **Copy & Paste**: Simply copy JSON from API responses, files, etc.
2. **Validation**: The tool validates JSON and shows error messages if format is invalid
3. **One-Click Copy**: Instantly copy generated PHP code
4. **Learn by Example**: Use the "Load Example" button to view real use cases

## 🚀 Get Started

1. Paste or enter your JSON in the input field above
2. Click the "Convert" button
3. Copy the generated PHP array code
4. Paste into your PHP project and you're ready to go

> **Tip**: Make sure the input is valid JSON. The tool will automatically check and notify you of any format errors.
