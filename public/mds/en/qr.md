# QR Code Generator

QR Code (Quick Response Code) is a matrix-type two-dimensional barcode invented by Denso Wave Company of Japan in 1994. QR comes from the abbreviation of Quick Response, meaning fast response. This tool can quickly generate QR code images from text, URLs, contact information, and other data.

## ✨ Key Features

- 📱 **Multi-content Support**: Supports text, URLs, contact info, WiFi passwords, etc.
- 🎨 **High-quality Output**: Generates high-quality QR code images
- 📏 **Adjustable Size**: Supports multiple size specifications
- 🎯 **Error Correction Levels**: Supports different error correction level settings
- 💾 **One-click Download**: Generated QR codes can be directly downloaded
- 🔧 **Real-time Preview**: Instant QR code preview upon content input

## 📖 Usage Examples

### URL QR Code

**Input Content:**
```
https://www.toolmi.com
```

**Generated Effect:**
- <PERSON>an to directly jump to ToolMi website
- Suitable for website promotion and link sharing

### Text QR Code

**Input Content:**
```
Welcome to ToolMi Online Tools!
Discover rich development tools and practical features.
```

**Generated Effect:**
- <PERSON>an to display complete text content
- Suitable for information delivery and instruction display

### Contact QR Code

**Input Content:**
```
BEGIN:VCARD
VERSION:3.0
FN:<PERSON>
ORG:ToolMi Tech
TEL:******-123-4567
EMAIL:<EMAIL>
URL:https://www.toolmi.com
END:VCARD
```

**Generated Effect:**
- Scan to directly add to contacts
- Includes name, company, phone, email, etc.

## 🎯 Application Scenarios

### 1. Website Promotion

Generate promotional QR codes for websites or apps:

```html
<!-- Website promotion example -->
<div class="promotion">
  <h3>Scan to Visit ToolMi</h3>
  <img src="qr-code.png" alt="ToolMi QR Code">
  <p>Discover More Useful Tools</p>
</div>

<!-- Application scenarios -->
- Promotional posters
- Business card design
- Product packaging
- Advertisement placement
```

### 2. Mobile Payment

Generate payment QR codes:

```javascript
// PayPal payment link example
const paypalUrl = 'https://paypal.me/username/amount'

// Stripe payment link example
const stripeUrl = 'https://buy.stripe.com/payment-link'

// Generate payment QR code
generateQRCode(paypalUrl, {
  size: 200,
  errorCorrectionLevel: 'M'
})
```

### 3. WiFi Password Sharing

Generate WiFi connection QR codes:

```
WiFi QR Code Format:
WIFI:T:WPA;S:NetworkName;P:Password;H:false;;

Example:
WIFI:T:WPA;S:ToolMi_5G;P:12345678;H:false;;

Parameters:
- T: Encryption type (WPA/WEP/nopass)
- S: Network name (SSID)
- P: Password
- H: Hidden network (true/false)
```

### 4. Event Check-in

Generate event check-in QR codes:

```json
{
  "type": "event_checkin",
  "event_id": "tech_meetup_2024",
  "event_name": "Tech Meetup",
  "location": "Convention Center",
  "date": "2024-06-15",
  "checkin_url": "https://event.toolmi.com/checkin/tech_meetup_2024"
}
```

### 5. Product Traceability

Generate product traceability QR codes:

```javascript
// Product information
const productInfo = {
  id: 'TM2024001',
  name: 'Smart Toolbox',
  batch: 'B20240615',
  production_date: '2024-06-15',
  manufacturer: 'ToolMi Tech',
  quality_check: 'PASS',
  trace_url: 'https://trace.toolmi.com/product/TM2024001'
}

// Generate traceability QR code
const traceData = JSON.stringify(productInfo)
generateQRCode(traceData)
```

## 🔧 Technical Details

### QR Code Structure

Basic structure of QR codes includes:

**Functional Patterns:**
- Finder patterns: Large squares in three corners
- Separators: White borders around finder patterns
- Alignment patterns: Small dots for orientation

**Data Areas:**
- Format information: Error correction level and mask info
- Version information: QR code version number
- Data and error correction codewords: Actual stored data

**Capacity Specifications:**
- Version 1: 21×21 modules, up to 25 characters
- Version 40: 177×177 modules, up to 4296 characters
- Supports numeric, alphanumeric, Chinese characters, binary data

### Error Correction Levels

QR codes support four error correction levels:

| Level | Recovery Rate | Use Cases |
|-------|---------------|-----------|
| L | ~7% | Clean environment, high-quality printing |
| M | ~15% | General environment, standard printing |
| Q | ~25% | Harsh environment, possible damage |
| H | ~30% | Very harsh environment, severe damage |

**Selection Recommendations:**
- URL sharing: Use L or M level
- Outdoor advertising: Use Q or H level
- Product labels: Use M or Q level

### Encoding Modes

QR codes support multiple encoding modes:

**Numeric Mode:**
- Only stores digits 0-9
- Highest storage efficiency
- Suitable for pure numeric content

**Alphanumeric Mode:**
- Stores digits, uppercase letters, some symbols
- High efficiency
- Suitable for URLs, codes, etc.

**Byte Mode:**
- Can store any characters
- Supports Chinese characters, special symbols
- Most versatile

**Kanji Mode:**
- Optimized for Chinese characters
- Higher efficiency in Chinese environments

## 💡 Usage Tips

- **Content Optimization**: Use concise content to improve scan success rate
- **Size Selection**: Choose appropriate size based on scanning distance
- **Error Correction**: Select suitable error correction level based on environment
- **Testing**: Test scanning with multiple devices after generation

## ⚠️ Important Notes

- **Content Length**: Overly long content makes QR codes too complex, affecting scanning
- **Print Quality**: Ensure clear printing, avoid blur or distortion
- **Color Contrast**: Maintain sufficient color contrast, recommend black and white
- **Quiet Zone**: Leave sufficient white space around QR codes

## 🚀 Getting Started

1. **Input Content**: Enter the content for QR code generation in the input box
2. **Adjust Settings**: Select appropriate size and error correction level
3. **Generate Preview**: Click generate button to view QR code preview
4. **Download**: Click download button to save QR code image
5. **Test Scan**: Test the generated QR code with mobile devices

> **Tip**: This tool generates QR codes locally on the client side and does not upload your data to the server, ensuring privacy and security.
