# YAML Formatter Tool

YAML (YAML Ain't Markup Language) is a human-readable data serialization standard commonly used for configuration files, data exchange, and document writing. This tool provides YAML format validation, beautification, and compression features to help developers process and manage YAML files.

## ✨ Key Features

- 🎯 **Format Validation**: Checks YAML syntax errors and provides detailed hints
- 🎨 **Format Beautification**: Automatically indents and formats YAML content
- 🗜️ **Content Compression**: Removes extra spaces and comments to reduce file size
- 📋 **One-click Copy**: Formatted results can be directly copied for use
- 🔧 **Error Location**: Precisely locates syntax error positions

## 📖 Usage Examples

### Formatting Example

**Original YAML:**
```yaml
name:ToolMi
version:1.0.0
features:
- format
- validate
- compress
config:
  debug:true
  port:3000
```

**After Formatting:**
```yaml
name: ToolMi
version: 1.0.0
features:
  - format
  - validate
  - compress
config:
  debug: true
  port: 3000
```

### Complex Structure Example

**Input YAML:**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
  pools:
    - name: read_pool
      size: 10
    - name: write_pool
      size: 5
```

**After Validation and Beautification:**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
  pools:
    - name: read_pool
      size: 10
    - name: write_pool
      size: 5
```

## 🎯 Application Scenarios

### 1. Configuration File Management

Organizing and validating application configuration files:

```yaml
# Application configuration file (config.yml)
app:
  name: ToolMi
  version: 1.0.0
  environment: production
  
server:
  host: 0.0.0.0
  port: 3000
  ssl:
    enabled: true
    cert_path: /etc/ssl/certs/app.crt
    key_path: /etc/ssl/private/app.key
    
database:
  type: postgresql
  host: localhost
  port: 5432
  name: toolmi_db
  username: ${DB_USER}
  password: ${DB_PASSWORD}
  pool:
    min_connections: 2
    max_connections: 10
    
redis:
  host: localhost
  port: 6379
  password: ${REDIS_PASSWORD}
  db: 0
  
logging:
  level: info
  format: json
  outputs:
    - type: file
      path: /var/log/app.log
    - type: console
```

### 2. Docker Compose Files

Managing Docker container orchestration configuration:

```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=database
    depends_on:
      - database
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - app-network
      
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: toolmi_db
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
      
  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 3. CI/CD Configuration

GitHub Actions workflow configuration:

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Run linting
        run: npm run lint
        
  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Build Docker image
        run: |
          docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest .
          
      - name: Push to registry
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
```

### 4. Kubernetes Configuration

Kubernetes deployment configuration files:

```yaml
# deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  labels:
    app: toolmi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: toolmi
  template:
    metadata:
      labels:
        app: toolmi
    spec:
      containers:
        - name: app
          image: toolmi/app:latest
          ports:
            - containerPort: 3000
          env:
            - name: NODE_ENV
              value: "production"
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: db-host
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
spec:
  selector:
    app: toolmi
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer
```

## 🔧 Technical Details

### YAML Syntax Rules

Basic syntax rules of YAML:

**Indentation Rules:**
- Use spaces for indentation, tabs are not allowed
- Elements at the same level must be left-aligned
- Child elements must be indented more than parent elements

**Data Types:**
- Strings: Can be unquoted, special characters need quotes
- Numbers: Integers and floating-point numbers
- Booleans: true/false, yes/no, on/off
- Null values: null, ~, or empty

**Collection Types:**
- Arrays: Use `-` to indicate list items
- Objects: Use `key: value` to represent key-value pairs
- Nesting: Supports arbitrary levels of nested structures

### Common Errors

Common errors that the YAML formatter can detect:

**Indentation Errors:**
```yaml
# Wrong example
config:
  debug: true
 port: 3000  # Inconsistent indentation

# Correct example
config:
  debug: true
  port: 3000
```

**Quote Issues:**
```yaml
# Wrong example
message: It's a test  # Unescaped single quote

# Correct example
message: "It's a test"
# or
message: 'It''s a test'
```

**List Format:**
```yaml
# Wrong example
features:
- format
 - validate  # Indentation error

# Correct example
features:
  - format
  - validate
```

## 💡 Usage Tips

- **Consistent Indentation**: Always use the same number of spaces for indentation (2 spaces recommended)
- **Quote Usage**: Surround strings containing special characters with quotes
- **Comment Standards**: Use `#` to add comments, add space before comments
- **Multi-line Strings**: Use `|` or `>` to handle multi-line text

## ⚠️ Important Notes

- **Tab Prohibition**: YAML does not allow tabs, only spaces can be used
- **Indentation Sensitive**: Indentation errors will cause parsing failures
- **Special Characters**: Colons, quotes and other special characters need proper handling
- **Encoding Format**: Ensure files use UTF-8 encoding

## 🚀 Getting Started

1. **Input YAML**: Paste the YAML content to be processed in the input box
2. **Choose Operation**: Click "Format", "Validate", or "Compress" button
3. **View Results**: Check the processed YAML in the output box
4. **Copy for Use**: Click "Copy" button to copy results to clipboard
5. **Fix Errors**: Fix syntax issues based on error hints

> **Tip**: This tool processes locally on the client side and does not upload your YAML content to the server, ensuring data security.
