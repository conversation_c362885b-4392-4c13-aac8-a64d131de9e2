# 数字转中文工具

数字转中文工具可以将阿拉伯数字快速转换为中文数字格式，支持简体中文、繁体中文和大写金额三种格式。广泛应用于财务报表、合同文件、发票开具等需要中文数字表示的场景。

## ✨ 主要特性

- 🔢 **多格式支持**：简体中文、繁体中文、大写金额三种格式
- ⚡ **实时转换**：输入数字即时显示转换结果
- 💰 **金额专用**：支持财务金额的标准大写格式
- 📊 **大数支持**：支持万、亿等大数单位转换
- 📋 **一键复制**：转换结果可直接复制使用
- 🔧 **智能识别**：自动处理小数点和负数

## 📖 使用示例

### 基础数字转换

**输入数字：**
```
12345
```

**转换结果：**
- **简体中文**：一万二千三百四十五
- **繁体中文**：壹萬貳仟參佰肆拾伍
- **大写金额**：壹万贰仟叁佰肆拾伍元整

### 小数转换

**输入数字：**
```
1234.56
```

**转换结果：**
- **简体中文**：一千二百三十四点五六
- **繁体中文**：壹仟貳佰參拾肆點伍陸
- **大写金额**：壹仟贰佰叁拾肆元伍角陆分

### 大数转换

**输入数字：**
```
123456789
```

**转换结果：**
- **简体中文**：一亿二千三百四十五万六千七百八十九
- **繁体中文**：壹億貳仟參佰肆拾伍萬陸仟柒佰捌拾玖
- **大写金额**：壹亿贰仟叁佰肆拾伍万陆仟柒佰捌拾玖元整

## 🎯 应用场景

### 1. 财务报表

在财务报表中使用大写金额：

```
原始金额：￥1,234,567.89
大写金额：壹佰贰拾叁万肆仟伍佰陆拾柒元捌角玖分

应用场景：
- 资产负债表
- 利润表
- 现金流量表
- 审计报告
```

### 2. 合同文件

合同中的金额条款：

```
合同金额条款示例：

甲方应向乙方支付服务费用人民币 ￥50,000.00 元
（大写：伍万元整）

分期付款：
第一期：￥20,000.00（贰万元整）
第二期：￥30,000.00（叁万元整）
```

### 3. 发票开具

发票金额的标准格式：

```
发票信息：
商品金额：￥8,888.88
大写金额：捌仟捌佰捌拾捌元捌角捌分

税额：￥1,111.11
大写税额：壹仟壹佰壹拾壹元壹角壹分

价税合计：￥9,999.99
大写合计：玖仟玖佰玖拾玖元玖角玖分
```

### 4. 银行业务

银行转账和支票填写：

```
转账凭证：
转账金额：￥100,000.00
大写金额：壹拾万元整

支票填写：
￥ 25,000.00
（大写）贰万伍仟元整

汇款单：
汇款金额：￥3,456.78
大写金额：叁仟肆佰伍拾陆元柒角捌分
```

## 🔧 技术详解

### 转换规则

数字转中文遵循以下规则：

**基础数字对应：**
- 0-9：零一二三四五六七八九
- 10：十/拾
- 100：百/佰
- 1000：千/仟
- 10000：万/萬

**单位处理：**
- 万：10^4
- 亿：10^8
- 兆：10^12（部分地区使用）

**零的处理：**
- 中间的零：一千零五（1005）
- 末尾的零：一千（1000）
- 连续的零：一万零五（10005）

**小数处理：**
- 小数点：点/點
- 角分：用于金额（0.1元=1角，0.01元=1分）

### 格式差异

三种格式的区别：

**简体中文：**
- 使用简化汉字：万、千、百、十
- 数字：一二三四五六七八九零
- 适用：日常使用、简体中文环境

**繁体中文：**
- 使用繁体汉字：萬、仟、佰、拾
- 数字：壹貳參肆伍陸柒捌玖零
- 适用：港澳台地区、正式文件

**大写金额：**
- 防篡改设计：壹贰叁肆伍陆柒捌玖零
- 单位：万仟佰拾元角分整
- 适用：财务、法律、银行文件

### 算法实现

转换算法的核心步骤：

1. **数字分解**：将数字按位分解
2. **单位匹配**：为每位数字匹配对应单位
3. **零值处理**：处理零的显示规则
4. **格式组装**：按照中文数字规则组装
5. **特殊处理**：处理小数、负数等特殊情况

## 💡 使用技巧

- **金额规范**：财务文件建议使用大写金额格式
- **小数精度**：金额计算建议保留两位小数
- **负数处理**：负数会在前面加"负"字
- **验证检查**：转换后建议人工核对重要金额

## ⚠️ 注意事项

- **精度限制**：超大数字可能存在精度问题
- **地区差异**：不同地区的中文数字习惯可能略有差异
- **法律效力**：重要文件中的大写金额具有法律效力
- **格式统一**：同一文档中建议使用统一的数字格式

## 🚀 开始使用

1. **输入数字**：在输入框中输入要转换的阿拉伯数字
2. **选择格式**：选择简体中文、繁体中文或大写金额格式
3. **查看结果**：转换结果会实时显示在输出区域
4. **复制使用**：点击"复制"按钮将结果复制到剪贴板
5. **示例演示**：点击"载入示例"查看演示数据

> **提示**：本工具在客户端本地进行转换，不会上传您的数据到服务器，确保隐私安全。
