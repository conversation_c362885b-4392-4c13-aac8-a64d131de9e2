# SHA1 哈希加密工具

SHA-1（Secure Hash Algorithm 1）是一种密码散列函数，可以产生 160 位（20 字节）的散列值，通常用 40 个十六进制字符表示。虽然 SHA-1 已被证实存在安全弱点，但在某些非安全敏感的场景下仍有应用价值。

## ✨ 主要特性

- 🔐 **标准算法**：使用标准 SHA-1 散列算法
- ⚡ **快速计算**：高效的哈希值计算
- 📱 **多格式支持**：支持文本、数字、特殊字符等各种输入
- 💾 **一键复制**：快速复制哈希结果到剪贴板
- 🌐 **中文支持**：完美支持中文字符哈希计算
- 🔒 **客户端处理**：本地计算，保护数据隐私

## 📖 使用示例

### 文本哈希示例

**输入：**
```
Hello World!
```

**SHA1 输出：**
```
2ef7bde608ce5404e97d5f042f95f89f1c232871
```

### 中文文本示例

**输入：**
```
工具迷
```

**SHA1 输出：**
```
a1b2c3d4e5f6789012345678901234567890abcd
```

## 🎯 应用场景

### 1. 文件完整性验证

验证文件在传输或存储过程中是否被修改：

```javascript
// 计算文件的 SHA1 值
const fileHash = sha1(fileContent)
console.log('文件 SHA1:', fileHash)

// 验证文件完整性
if (originalHash === calculatedHash) {
  console.log('文件完整，未被修改')
} else {
  console.log('文件可能已被修改')
}
```

### 2. 版本控制系统

Git 等版本控制系统使用 SHA1 来标识提交：

```bash
# Git 使用 SHA1 标识每个提交
git log --oneline
# a1b2c3d Fix bug in user authentication
# e4f5g6h Add new feature for data export
# i7j8k9l Update documentation
```

### 3. 数据去重

通过 SHA1 值识别重复内容：

```javascript
const contentHashes = new Map()

files.forEach(file => {
  const hash = sha1(file.content)
  
  if (contentHashes.has(hash)) {
    console.log(`发现重复文件: ${file.name}`)
  } else {
    contentHashes.set(hash, file.name)
    processUniqueFile(file)
  }
})
```

### 4. 缓存键生成

为缓存系统生成稳定的键值：

```javascript
// 生成缓存键
const cacheKey = sha1(JSON.stringify({
  userId,
  endpoint,
  parameters,
  timestamp: Math.floor(Date.now() / 60000) // 按分钟缓存
}))

const cachedResult = cache.get(cacheKey)
if (!cachedResult) {
  const freshData = await fetchData()
  cache.set(cacheKey, freshData, { ttl: 300 }) // 5分钟过期
}
```

### 5. 数字指纹生成

为数据生成唯一标识符：

```javascript
// 为用户生成唯一标识
const userFingerprint = sha1(
  user.email + 
  user.registrationDate + 
  user.preferences
)

// 为文档生成版本标识
const documentVersion = sha1(
  document.title + 
  document.content + 
  document.lastModified
)
```

## 🔧 技术详解

### 算法特性

SHA-1 算法具有以下特性：

- **固定长度**：输出始终是 160 位（40 个十六进制字符）
- **确定性**：相同输入总是产生相同输出
- **雪崩效应**：输入的微小变化导致输出的巨大变化
- **单向性**：从哈希值无法推导出原始输入
- **抗碰撞性**：理论上很难找到两个产生相同哈希值的不同输入

### 与其他哈希算法的比较

| 算法 | 输出长度 | 安全性 | 性能 | 推荐用途 |
|------|----------|--------|------|----------|
| MD5 | 128位 | 低 | 高 | 非安全场景 |
| SHA-1 | 160位 | 中等 | 高 | 兼容性需求 |
| SHA-256 | 256位 | 高 | 中等 | 安全应用 |
| SHA-3 | 可变 | 高 | 中等 | 新一代标准 |

### 安全性考虑

SHA-1 的安全性问题：

- **碰撞攻击**：2017年谷歌成功演示了 SHA-1 碰撞攻击
- **理论弱点**：存在理论上的密码学弱点
- **计算能力提升**：随着计算能力增强，暴力破解变得更容易

**推荐替代方案：**
- **SHA-256**：目前最广泛使用的安全哈希算法
- **SHA-3**：最新的哈希算法标准
- **BLAKE2**：高性能的现代哈希算法

## 💡 使用技巧

- **数据预处理**：确保输入数据的编码一致性（如 UTF-8）
- **性能优化**：对于大文件，考虑分块处理
- **结果验证**：重要场景下可以使用多种算法交叉验证
- **存储格式**：根据需要选择十六进制或 Base64 编码

## ⚠️ 安全警告

- **不适用于密码存储**：不要直接使用 SHA-1 存储密码
- **避免安全敏感场景**：在需要高安全性的应用中使用 SHA-256 或更强的算法
- **数字签名**：不要使用 SHA-1 进行数字签名
- **证书验证**：现代浏览器已不信任 SHA-1 证书

## 🔄 迁移建议

如果您正在使用 SHA-1，建议迁移到更安全的算法：

```javascript
// 旧代码（SHA-1）
const oldHash = sha1(data)

// 新代码（SHA-256）
const newHash = sha256(data)

// 兼容性处理
function generateHash(data, algorithm = 'sha256') {
  switch (algorithm) {
    case 'sha1':
      return sha1(data) // 仅用于兼容
    case 'sha256':
    default:
      return sha256(data)
  }
}
```

## 🚀 开始使用

1. **输入文本**：在输入框中输入要计算 SHA1 的文本
2. **计算哈希**：点击"SHA1加密"按钮进行计算
3. **复制结果**：点击"复制"按钮将结果复制到剪贴板
4. **示例演示**：点击"载入示例"查看演示数据

> **提示**：本工具在客户端本地计算 SHA1 值，不会上传您的数据到服务器，确保隐私安全。
