# JSON 格式化工具

这个工具提供了完整的JSON处理功能，包括格式化、压缩、验证、修复和美化。支持语法高亮、错误检测、路径查询和数据转换，是开发者处理JSON数据的必备工具。

## ✨ 主要特性

- 🎨 **智能格式化** : 自动缩进和语法高亮
- 🔍 **错误检测** : 精确定位JSON语法错误
- 🛠️ **自动修复** : 智能修复常见JSON问题
- 📊 **数据分析** : 显示JSON结构统计信息
- 🔄 **格式转换** : JSON与其他格式互转

## 📖 使用示例

### 基础格式化

**输入 (压缩的JSON):**
```json
{"name":"张三","age":30,"skills":["JavaScript","Python","Java"],"address":{"city":"北京","district":"朝阳区"}}
```

**输出 (格式化后):**
```json
{
  "name": "张三",
  "age": 30,
  "skills": [
    "JavaScript",
    "Python", 
    "Java"
  ],
  "address": {
    "city": "北京",
    "district": "朝阳区"
  }
}
```

### 错误检测和修复

**有错误的JSON:**
```json
{
  "name": "张三",
  "age": 30,
  "skills": ["JavaScript", "Python", "Java",],  // 多余的逗号
  "address": {
    "city": "北京"
    "district": "朝阳区"  // 缺少逗号
  }
}
```

**修复后的JSON:**
```json
{
  "name": "张三",
  "age": 30,
  "skills": ["JavaScript", "Python", "Java"],
  "address": {
    "city": "北京",
    "district": "朝阳区"
  }
}
```

## 🎯 应用场景

### 1. API 开发和调试

```javascript
// JSON API 响应处理工具
class JSONAPIHandler {
  constructor() {
    this.baseURL = 'https://api.example.com';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  // 格式化API响应
  async fetchAndFormat(endpoint) {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        headers: this.defaultHeaders
      });
      
      const rawData = await response.text();
      console.log('原始响应:', rawData);
      
      // 解析和格式化JSON
      const jsonData = JSON.parse(rawData);
      const formattedJSON = JSON.stringify(jsonData, null, 2);
      
      console.log('格式化后的JSON:');
      console.log(formattedJSON);
      
      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data: jsonData,
        formatted: formattedJSON
      };
    } catch (error) {
      console.error('JSON解析错误:', error);
      return this.handleJSONError(error);
    }
  }

  // 处理JSON错误
  handleJSONError(error) {
    const errorInfo = {
      type: 'JSON_PARSE_ERROR',
      message: error.message,
      suggestions: []
    };

    // 常见错误模式检测
    if (error.message.includes('Unexpected token')) {
      errorInfo.suggestions.push('检查是否有多余的逗号或缺少引号');
    }
    
    if (error.message.includes('Unexpected end')) {
      errorInfo.suggestions.push('检查是否有未闭合的括号或大括号');
    }

    return errorInfo;
  }

  // 验证JSON Schema
  validateSchema(data, schema) {
    const errors = [];
    
    // 简单的schema验证示例
    if (schema.required) {
      schema.required.forEach(field => {
        if (!(field in data)) {
          errors.push(`缺少必需字段: ${field}`);
        }
      });
    }

    if (schema.properties) {
      Object.entries(schema.properties).forEach(([key, propSchema]) => {
        if (key in data) {
          const value = data[key];
          
          if (propSchema.type && typeof value !== propSchema.type) {
            errors.push(`字段 ${key} 类型错误，期望 ${propSchema.type}，实际 ${typeof value}`);
          }
          
          if (propSchema.minLength && value.length < propSchema.minLength) {
            errors.push(`字段 ${key} 长度不足，最小长度 ${propSchema.minLength}`);
          }
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // 生成模拟数据
  generateMockData(schema) {
    const mockData = {};
    
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([key, propSchema]) => {
        switch (propSchema.type) {
          case 'string':
            mockData[key] = propSchema.example || `示例${key}`;
            break;
          case 'number':
            mockData[key] = propSchema.example || Math.floor(Math.random() * 100);
            break;
          case 'boolean':
            mockData[key] = propSchema.example !== undefined ? propSchema.example : Math.random() > 0.5;
            break;
          case 'array':
            mockData[key] = propSchema.example || ['示例项目1', '示例项目2'];
            break;
          case 'object':
            mockData[key] = propSchema.example || { 嵌套字段: '嵌套值' };
            break;
          default:
            mockData[key] = null;
        }
      });
    }

    return mockData;
  }
}

// 使用示例
const apiHandler = new JSONAPIHandler();

// 定义API Schema
const userSchema = {
  type: 'object',
  required: ['id', 'name', 'email'],
  properties: {
    id: { type: 'number' },
    name: { type: 'string', minLength: 2 },
    email: { type: 'string' },
    age: { type: 'number' },
    skills: { type: 'array' },
    address: { type: 'object' }
  }
};

// 生成模拟数据
const mockUser = apiHandler.generateMockData(userSchema);
console.log('模拟用户数据:', JSON.stringify(mockUser, null, 2));

// 验证数据
const validationResult = apiHandler.validateSchema(mockUser, userSchema);
console.log('验证结果:', validationResult);
```

### 2. 配置文件管理

```javascript
// 配置文件管理系统
class ConfigManager {
  constructor() {
    this.configs = new Map();
    this.watchers = new Map();
  }

  // 加载配置文件
  async loadConfig(name, filePath) {
    try {
      const response = await fetch(filePath);
      const configText = await response.text();
      
      // 尝试解析JSON
      let config;
      try {
        config = JSON.parse(configText);
      } catch (parseError) {
        // 尝试修复常见JSON错误
        const fixedJSON = this.fixCommonJSONErrors(configText);
        config = JSON.parse(fixedJSON);
        console.warn(`配置文件 ${name} 包含错误，已自动修复`);
      }

      // 验证配置结构
      const validation = this.validateConfig(config);
      if (!validation.valid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }

      this.configs.set(name, config);
      this.notifyWatchers(name, config);
      
      return config;
    } catch (error) {
      console.error(`加载配置文件 ${name} 失败:`, error);
      throw error;
    }
  }

  // 修复常见JSON错误
  fixCommonJSONErrors(jsonString) {
    let fixed = jsonString;
    
    // 移除注释
    fixed = fixed.replace(/\/\*[\s\S]*?\*\//g, '');
    fixed = fixed.replace(/\/\/.*$/gm, '');
    
    // 修复多余的逗号
    fixed = fixed.replace(/,(\s*[}\]])/g, '$1');
    
    // 修复缺少的逗号
    fixed = fixed.replace(/}(\s*")/g, '},$1');
    fixed = fixed.replace(/](\s*")/g, '],$1');
    fixed = fixed.replace(/"(\s*")/g, '",$1');
    
    // 修复单引号
    fixed = fixed.replace(/'/g, '"');
    
    // 修复未引用的键
    fixed = fixed.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');
    
    return fixed;
  }

  // 验证配置
  validateConfig(config) {
    const errors = [];
    
    // 基本结构检查
    if (typeof config !== 'object' || config === null) {
      errors.push('配置必须是一个对象');
      return { valid: false, errors };
    }

    // 检查必需字段
    const requiredFields = ['app', 'database', 'server'];
    requiredFields.forEach(field => {
      if (!(field in config)) {
        errors.push(`缺少必需配置节: ${field}`);
      }
    });

    // 检查应用配置
    if (config.app) {
      if (!config.app.name) {
        errors.push('app.name 是必需的');
      }
      if (!config.app.version) {
        errors.push('app.version 是必需的');
      }
    }

    // 检查数据库配置
    if (config.database) {
      const dbRequired = ['host', 'port', 'name'];
      dbRequired.forEach(field => {
        if (!config.database[field]) {
          errors.push(`database.${field} 是必需的`);
        }
      });
    }

    // 检查服务器配置
    if (config.server) {
      if (typeof config.server.port !== 'number') {
        errors.push('server.port 必须是数字');
      }
      if (config.server.port < 1 || config.server.port > 65535) {
        errors.push('server.port 必须在 1-65535 范围内');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // 保存配置
  saveConfig(name, config) {
    // 验证配置
    const validation = this.validateConfig(config);
    if (!validation.valid) {
      throw new Error(`配置无效: ${validation.errors.join(', ')}`);
    }

    // 格式化JSON
    const formattedConfig = JSON.stringify(config, null, 2);
    
    // 保存到本地存储或发送到服务器
    localStorage.setItem(`config_${name}`, formattedConfig);
    
    this.configs.set(name, config);
    this.notifyWatchers(name, config);
    
    return formattedConfig;
  }

  // 监听配置变化
  watchConfig(name, callback) {
    if (!this.watchers.has(name)) {
      this.watchers.set(name, []);
    }
    this.watchers.get(name).push(callback);
  }

  // 通知监听者
  notifyWatchers(name, config) {
    const callbacks = this.watchers.get(name);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(config);
        } catch (error) {
          console.error('配置监听器错误:', error);
        }
      });
    }
  }

  // 获取配置
  getConfig(name) {
    return this.configs.get(name);
  }

  // 更新配置
  updateConfig(name, updates) {
    const currentConfig = this.configs.get(name);
    if (!currentConfig) {
      throw new Error(`配置 ${name} 不存在`);
    }

    const updatedConfig = this.deepMerge(currentConfig, updates);
    return this.saveConfig(name, updatedConfig);
  }

  // 深度合并对象
  deepMerge(target, source) {
    const result = { ...target };
    
    Object.keys(source).forEach(key => {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    });
    
    return result;
  }

  // 生成配置模板
  generateTemplate() {
    return {
      app: {
        name: "我的应用",
        version: "1.0.0",
        description: "应用描述",
        environment: "development"
      },
      server: {
        host: "localhost",
        port: 3000,
        ssl: false
      },
      database: {
        type: "mysql",
        host: "localhost",
        port: 3306,
        name: "myapp_db",
        username: "root",
        password: "",
        charset: "utf8mb4"
      },
      cache: {
        type: "redis",
        host: "localhost",
        port: 6379,
        ttl: 3600
      },
      logging: {
        level: "info",
        file: "logs/app.log",
        maxSize: "10MB",
        maxFiles: 5
      }
    };
  }
}

// 使用示例
const configManager = new ConfigManager();

// 生成配置模板
const template = configManager.generateTemplate();
console.log('配置模板:', JSON.stringify(template, null, 2));

// 监听配置变化
configManager.watchConfig('app', (config) => {
  console.log('应用配置已更新:', config.app.name);
});

// 保存配置
try {
  const savedConfig = configManager.saveConfig('app', template);
  console.log('配置已保存');
} catch (error) {
  console.error('保存配置失败:', error.message);
}

// 更新配置
configManager.updateConfig('app', {
  app: {
    environment: 'production'
  },
  server: {
    port: 8080
  }
});
```

### 3. 数据转换和处理

```javascript
// JSON数据转换工具
class JSONTransformer {
  constructor() {
    this.transformers = new Map();
    this.registerDefaultTransformers();
  }

  // 注册默认转换器
  registerDefaultTransformers() {
    // 扁平化对象
    this.registerTransformer('flatten', (data) => {
      const flatten = (obj, prefix = '') => {
        const flattened = {};
        
        Object.keys(obj).forEach(key => {
          const newKey = prefix ? `${prefix}.${key}` : key;
          
          if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
            Object.assign(flattened, flatten(obj[key], newKey));
          } else {
            flattened[newKey] = obj[key];
          }
        });
        
        return flattened;
      };
      
      return flatten(data);
    });

    // 反扁平化对象
    this.registerTransformer('unflatten', (data) => {
      const unflatten = (obj) => {
        const result = {};
        
        Object.keys(obj).forEach(key => {
          const keys = key.split('.');
          let current = result;
          
          keys.forEach((k, index) => {
            if (index === keys.length - 1) {
              current[k] = obj[key];
            } else {
              current[k] = current[k] || {};
              current = current[k];
            }
          });
        });
        
        return result;
      };
      
      return unflatten(data);
    });

    // 数组转对象
    this.registerTransformer('arrayToObject', (data, keyField = 'id') => {
      if (!Array.isArray(data)) {
        throw new Error('数据必须是数组');
      }
      
      return data.reduce((obj, item) => {
        if (item[keyField] !== undefined) {
          obj[item[keyField]] = item;
        }
        return obj;
      }, {});
    });

    // 对象转数组
    this.registerTransformer('objectToArray', (data) => {
      if (typeof data !== 'object' || data === null) {
        throw new Error('数据必须是对象');
      }
      
      return Object.entries(data).map(([key, value]) => ({
        key,
        value
      }));
    });

    // 过滤字段
    this.registerTransformer('pick', (data, fields) => {
      if (Array.isArray(fields)) {
        const result = {};
        fields.forEach(field => {
          if (field in data) {
            result[field] = data[field];
          }
        });
        return result;
      }
      throw new Error('字段列表必须是数组');
    });

    // 排除字段
    this.registerTransformer('omit', (data, fields) => {
      if (Array.isArray(fields)) {
        const result = { ...data };
        fields.forEach(field => {
          delete result[field];
        });
        return result;
      }
      throw new Error('字段列表必须是数组');
    });
  }

  // 注册转换器
  registerTransformer(name, transformer) {
    this.transformers.set(name, transformer);
  }

  // 执行转换
  transform(data, transformerName, ...args) {
    const transformer = this.transformers.get(transformerName);
    if (!transformer) {
      throw new Error(`转换器 '${transformerName}' 不存在`);
    }
    
    try {
      return transformer(data, ...args);
    } catch (error) {
      throw new Error(`转换失败: ${error.message}`);
    }
  }

  // 链式转换
  chain(data) {
    let result = data;
    
    return {
      transform: (transformerName, ...args) => {
        result = this.transform(result, transformerName, ...args);
        return this;
      },
      value: () => result,
      json: () => JSON.stringify(result, null, 2)
    };
  }

  // 批量转换
  batchTransform(dataArray, transformerName, ...args) {
    return dataArray.map(data => this.transform(data, transformerName, ...args));
  }

  // 条件转换
  conditionalTransform(data, condition, transformerName, ...args) {
    if (typeof condition === 'function' ? condition(data) : condition) {
      return this.transform(data, transformerName, ...args);
    }
    return data;
  }
}

// 使用示例
const transformer = new JSONTransformer();

// 示例数据
const userData = {
  id: 1,
  name: '张三',
  profile: {
    age: 30,
    address: {
      city: '北京',
      district: '朝阳区'
    }
  },
  skills: ['JavaScript', 'Python', 'Java']
};

// 扁平化数据
const flattened = transformer.transform(userData, 'flatten');
console.log('扁平化后:', JSON.stringify(flattened, null, 2));

// 链式转换
const result = transformer.chain(userData)
  .transform('flatten')
  .transform('pick', ['id', 'name', 'profile.age', 'profile.address.city'])
  .value();

console.log('链式转换结果:', JSON.stringify(result, null, 2));

// 数组数据转换
const usersArray = [
  { id: 1, name: '张三', age: 30 },
  { id: 2, name: '李四', age: 25 },
  { id: 3, name: '王五', age: 35 }
];

const usersObject = transformer.transform(usersArray, 'arrayToObject', 'id');
console.log('数组转对象:', JSON.stringify(usersObject, null, 2));
```

## 🔧 技术细节

### JSON 语法规则

**基本数据类型:**
- **字符串**: 必须用双引号包围
- **数字**: 整数或浮点数
- **布尔值**: `true` 或 `false`
- **null**: 空值
- **对象**: 键值对集合 `{}`
- **数组**: 有序值列表 `[]`

**语法要求:**
- 键必须是字符串且用双引号包围
- 值可以是任何有效的JSON数据类型
- 最后一个元素后不能有逗号
- 不支持注释
- 不支持undefined

### 常见错误类型

**语法错误:**
- 缺少引号或括号
- 多余的逗号
- 使用单引号
- 未转义的特殊字符

**结构错误:**
- 不匹配的括号
- 缺少逗号分隔符
- 重复的键名

### 性能优化

**大文件处理:**
- 流式解析
- 分块处理
- 内存管理
- 进度显示

## 💡 使用技巧

- **验证优先** : 始终先验证JSON格式
- **错误定位** : 使用行号快速定位错误
- **备份数据** : 修改前备份原始数据
- **格式一致** : 保持团队内JSON格式一致

## ⚠️ 注意事项

- **数据安全** : 不要处理包含敏感信息的JSON
- **文件大小** : 超大JSON文件可能影响性能
- **浏览器限制** : 某些浏览器对JSON大小有限制
- **编码问题** : 确保使用UTF-8编码

## 🚀 如何使用

1. **粘贴JSON** : 将JSON数据粘贴到输入区域
2. **自动检测** : 工具自动检测并显示错误
3. **一键修复** : 点击修复按钮自动修复常见错误
4. **格式化** : 选择缩进样式进行格式化
5. **复制结果** : 点击复制按钮获取处理后的JSON

> **提示** : 此工具在客户端本地处理，不会向服务器发送数据，确保数据安全和处理速度。
