# SQL 格式化工具

SQL 格式化工具可以将压缩或混乱的 SQL 语句转换为格式规范、易于阅读的形式。支持自动缩进、换行、关键字大写等功能，同时提供 SQL 压缩功能，适用于各种数据库系统。

## ✨ 主要特性

- 🎯 **智能格式化**：自动添加适当的缩进和换行
- 🔤 **关键字大写**：SQL 关键字自动转换为大写
- 📐 **自动缩进**：根据语句结构自动调整缩进
- 🗜️ **SQL 压缩**：移除多余空格和换行，压缩 SQL 语句
- 📋 **一键复制**：格式化结果可直接复制使用
- 🔧 **多数据库支持**：支持 MySQL、PostgreSQL、SQLite 等

## 📖 使用示例

### SQL 格式化示例

**原始 SQL：**
```sql
SELECT u.id, u.name, u.email, p.title, p.content FROM users u LEFT JOIN posts p ON u.id = p.user_id WHERE u.status = 'active' AND p.published_at IS NOT NULL ORDER BY p.created_at DESC LIMIT 10;
```

**格式化后：**
```sql
SELECT 
    u.id,
    u.name,
    u.email,
    p.title,
    p.content
FROM users u
LEFT JOIN posts p ON u.id = p.user_id
WHERE u.status = 'active'
    AND p.published_at IS NOT NULL
ORDER BY p.created_at DESC
LIMIT 10;
```

### 复杂查询格式化

**原始 SQL：**
```sql
SELECT COUNT(*) as total, AVG(salary) as avg_salary, department FROM employees WHERE hire_date >= '2020-01-01' GROUP BY department HAVING COUNT(*) > 5 ORDER BY avg_salary DESC;
```

**格式化后：**
```sql
SELECT 
    COUNT(*) AS total,
    AVG(salary) AS avg_salary,
    department
FROM employees
WHERE hire_date >= '2020-01-01'
GROUP BY department
HAVING COUNT(*) > 5
ORDER BY avg_salary DESC;
```

## 🎯 应用场景

### 1. 代码审查和维护

提升 SQL 代码的可读性，便于团队协作：

```sql
-- 格式化前：难以阅读
SELECT o.order_id,o.order_date,c.customer_name,SUM(oi.quantity*oi.price) as total FROM orders o JOIN customers c ON o.customer_id=c.customer_id JOIN order_items oi ON o.order_id=oi.order_id WHERE o.order_date BETWEEN '2024-01-01' AND '2024-12-31' GROUP BY o.order_id,o.order_date,c.customer_name HAVING total>1000 ORDER BY total DESC;

-- 格式化后：清晰易读
SELECT 
    o.order_id,
    o.order_date,
    c.customer_name,
    SUM(oi.quantity * oi.price) AS total
FROM orders o
JOIN customers c ON o.customer_id = c.customer_id
JOIN order_items oi ON o.order_id = oi.order_id
WHERE o.order_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY 
    o.order_id,
    o.order_date,
    c.customer_name
HAVING total > 1000
ORDER BY total DESC;
```

### 2. 数据库迁移脚本

整理迁移脚本，确保执行的准确性：

```sql
-- 创建表结构
CREATE TABLE user_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 插入初始数据
INSERT INTO user_profiles (
    user_id,
    first_name,
    last_name,
    email,
    phone,
    address
) VALUES 
    (1, '张', '三', '<EMAIL>', '13800138000', '北京市朝阳区'),
    (2, '李', '四', '<EMAIL>', '13900139000', '上海市浦东新区'),
    (3, '王', '五', '<EMAIL>', '13700137000', '广州市天河区');
```

### 3. 性能优化分析

格式化复杂查询，便于性能分析：

```sql
-- 性能优化前的查询
SELECT 
    p.product_name,
    c.category_name,
    s.supplier_name,
    i.quantity_in_stock,
    i.reorder_level,
    CASE 
        WHEN i.quantity_in_stock <= i.reorder_level THEN '需要补货'
        WHEN i.quantity_in_stock <= i.reorder_level * 2 THEN '库存偏低'
        ELSE '库存充足'
    END AS stock_status
FROM products p
INNER JOIN categories c ON p.category_id = c.category_id
INNER JOIN suppliers s ON p.supplier_id = s.supplier_id
INNER JOIN inventory i ON p.product_id = i.product_id
WHERE p.discontinued = 0
    AND i.quantity_in_stock > 0
ORDER BY 
    c.category_name,
    stock_status,
    p.product_name;
```

### 4. 文档生成

为数据库文档生成格式化的 SQL 示例：

```sql
-- 用户权限管理示例
WITH user_permissions AS (
    SELECT 
        u.user_id,
        u.username,
        r.role_name,
        p.permission_name
    FROM users u
    JOIN user_roles ur ON u.user_id = ur.user_id
    JOIN roles r ON ur.role_id = r.role_id
    JOIN role_permissions rp ON r.role_id = rp.role_id
    JOIN permissions p ON rp.permission_id = p.permission_id
    WHERE u.is_active = 1
)
SELECT 
    username,
    role_name,
    STRING_AGG(permission_name, ', ') AS permissions
FROM user_permissions
GROUP BY username, role_name
ORDER BY username;
```

## 🔧 技术详解

### 格式化规则

SQL 格式化遵循以下规则：

**关键字处理：**
- SELECT、FROM、WHERE、JOIN 等关键字大写
- 每个主要子句独占一行
- 子查询适当缩进

**字段和表名：**
- 多个字段时每个字段独占一行
- 使用逗号前置或后置（可配置）
- 表别名保持简洁

**条件语句：**
- WHERE 条件适当换行
- AND/OR 逻辑运算符对齐
- 复杂条件使用括号分组

**JOIN 语句：**
- 每个 JOIN 独占一行
- ON 条件与 JOIN 对齐
- 多个 JOIN 条件垂直对齐

### 压缩规则

SQL 压缩功能：

**空白字符处理：**
- 移除多余的空格和制表符
- 保留必要的空格分隔符
- 移除空行和注释

**语句合并：**
- 将多行语句合并为单行
- 保持 SQL 语法的正确性
- 减少文件大小

## 💡 使用技巧

- **批量处理**：可以同时格式化多个 SQL 语句
- **保留注释**：格式化时可以选择保留或移除注释
- **自定义缩进**：支持 2 空格、4 空格或制表符缩进
- **关键字大小写**：可以选择大写、小写或保持原样

## ⚠️ 注意事项

- **语法检查**：格式化前请确保 SQL 语法正确
- **数据库差异**：不同数据库的语法可能略有差异
- **复杂语句**：极其复杂的 SQL 可能需要手动调整
- **备份原始**：格式化前建议备份原始 SQL 语句

## 🚀 开始使用

1. **输入 SQL**：在输入框中粘贴要格式化的 SQL 语句
2. **选择操作**：点击"格式化"或"压缩"按钮
3. **查看结果**：在输出框中查看处理后的 SQL
4. **复制使用**：点击"复制"按钮将结果复制到剪贴板
5. **示例演示**：点击"载入示例"查看演示数据

> **提示**：本工具在客户端本地进行处理，不会上传您的 SQL 语句到服务器，确保数据安全。
