# 时间戳转换器

这个工具提供了全面的时间戳转换功能，支持Unix时间戳、ISO 8601格式、本地时间等多种时间格式之间的相互转换。包含时区处理、批量转换、时间计算和格式化功能，是处理时间数据的专业工具。

## ✨ 主要特性

- 🕐 **多格式支持** : Unix时间戳、ISO 8601、RFC 2822等
- 🌍 **时区转换** : 支持全球所有时区的转换
- 📅 **智能解析** : 自动识别多种时间格式
- ⚡ **实时转换** : 输入即时显示转换结果
- 📊 **批量处理** : 支持批量时间戳转换

## 📖 使用示例

### 基础时间戳转换

**Unix时间戳转可读时间:**
```
输入: 1704067200
输出: 2024-01-01 00:00:00 (UTC)
输出: 2024-01-01 08:00:00 (北京时间)
```

**可读时间转Unix时间戳:**
```
输入: 2024-01-01 08:00:00
输出: 1704067200 (秒)
输出: 1704067200000 (毫秒)
```

### 多种时间格式

**ISO 8601格式:**
```
输入: 2024-01-01T08:00:00.000Z
输出: 1704067200
```

**RFC 2822格式:**
```
输入: Mon, 01 Jan 2024 08:00:00 GMT
输出: 1704067200
```

**中文格式:**
```
输入: 2024年1月1日 8时0分0秒
输出: 1704067200
```

## 🎯 应用场景

### 1. 日志分析系统

```javascript
// 日志时间戳分析工具
class LogTimestampAnalyzer {
  constructor() {
    this.timeZones = {
      'UTC': 0,
      'Beijing': 8,
      'Tokyo': 9,
      'NewYork': -5,
      'London': 0,
      'Sydney': 11
    };
  }

  // 解析日志时间戳
  parseLogTimestamp(logEntry) {
    const patterns = [
      // Apache日志格式: [01/Jan/2024:08:00:00 +0800]
      /\[(\d{2}\/\w{3}\/\d{4}:\d{2}:\d{2}:\d{2})\s*([+-]\d{4})\]/,

      // Nginx日志格式: 01/Jan/2024:08:00:00 +0800
      /(\d{2}\/\w{3}\/\d{4}:\d{2}:\d{2}:\d{2})\s*([+-]\d{4})/,

      // 系统日志格式: Jan 1 08:00:00
      /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{1,2})\s+(\d{2}:\d{2}:\d{2})/,

      // ISO格式: 2024-01-01T08:00:00.000Z
      /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?)/,

      // Unix时间戳: 1704067200
      /(\d{10})/
    ];

    for (const pattern of patterns) {
      const match = logEntry.match(pattern);
      if (match) {
        return this.convertToStandardFormat(match);
      }
    }

    return null;
  }

  // 转换为标准格式
  convertToStandardFormat(match) {
    const fullMatch = match[0];

    // Unix时间戳
    if (/^\d{10}$/.test(match[1])) {
      const timestamp = parseInt(match[1]);
      return {
        timestamp,
        iso: new Date(timestamp * 1000).toISOString(),
        local: this.formatLocalTime(timestamp),
        format: 'unix'
      };
    }

    // ISO格式
    if (match[1] && match[1].includes('T')) {
      const date = new Date(match[1]);
      const timestamp = Math.floor(date.getTime() / 1000);
      return {
        timestamp,
        iso: date.toISOString(),
        local: this.formatLocalTime(timestamp),
        format: 'iso'
      };
    }

    // Apache/Nginx格式
    if (match[1] && match[2]) {
      const dateStr = match[1];
      const timezone = match[2];

      // 解析日期字符串
      const date = this.parseApacheDate(dateStr, timezone);
      const timestamp = Math.floor(date.getTime() / 1000);

      return {
        timestamp,
        iso: date.toISOString(),
        local: this.formatLocalTime(timestamp),
        format: 'apache'
      };
    }

    return null;
  }

  // 解析Apache日期格式
  parseApacheDate(dateStr, timezone) {
    // 01/Jan/2024:08:00:00
    const parts = dateStr.split(/[\/:\s]/);
    const day = parseInt(parts[0]);
    const month = this.getMonthNumber(parts[1]);
    const year = parseInt(parts[2]);
    const hour = parseInt(parts[3]);
    const minute = parseInt(parts[4]);
    const second = parseInt(parts[5]);

    const date = new Date(year, month, day, hour, minute, second);

    // 处理时区偏移
    if (timezone) {
      const sign = timezone[0] === '+' ? 1 : -1;
      const hours = parseInt(timezone.slice(1, 3));
      const minutes = parseInt(timezone.slice(3, 5));
      const offsetMs = sign * (hours * 60 + minutes) * 60 * 1000;
      date.setTime(date.getTime() - offsetMs);
    }

    return date;
  }

  // 获取月份数字
  getMonthNumber(monthName) {
    const months = {
      'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3,
      'May': 4, 'Jun': 5, 'Jul': 6, 'Aug': 7,
      'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
    };
    return months[monthName] || 0;
  }

  // 格式化本地时间
  formatLocalTime(timestamp) {
    const date = new Date(timestamp * 1000);
    return {
      beijing: this.formatTimeInZone(date, 8),
      utc: this.formatTimeInZone(date, 0),
      local: date.toLocaleString()
    };
  }

  // 在指定时区格式化时间
  formatTimeInZone(date, offsetHours) {
    const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
    const targetTime = new Date(utc + (offsetHours * 3600000));

    return targetTime.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '');
  }

  // 分析日志时间分布
  analyzeLogTimeDistribution(logEntries) {
    const timestamps = [];
    const parseErrors = [];

    logEntries.forEach((entry, index) => {
      const parsed = this.parseLogTimestamp(entry);
      if (parsed) {
        timestamps.push(parsed.timestamp);
      } else {
        parseErrors.push({ index, entry });
      }
    });

    if (timestamps.length === 0) {
      return { error: '没有找到有效的时间戳' };
    }

    timestamps.sort((a, b) => a - b);

    const startTime = timestamps[0];
    const endTime = timestamps[timestamps.length - 1];
    const duration = endTime - startTime;

    // 按小时分组
    const hourlyDistribution = {};
    timestamps.forEach(ts => {
      const hour = new Date(ts * 1000).getUTCHours();
      hourlyDistribution[hour] = (hourlyDistribution[hour] || 0) + 1;
    });

    // 按日期分组
    const dailyDistribution = {};
    timestamps.forEach(ts => {
      const date = new Date(ts * 1000).toISOString().split('T')[0];
      dailyDistribution[date] = (dailyDistribution[date] || 0) + 1;
    });

    return {
      totalEntries: logEntries.length,
      validTimestamps: timestamps.length,
      parseErrors: parseErrors.length,
      timeRange: {
        start: this.formatLocalTime(startTime),
        end: this.formatLocalTime(endTime),
        duration: this.formatDuration(duration)
      },
      distribution: {
        hourly: hourlyDistribution,
        daily: dailyDistribution
      },
      statistics: {
        averageInterval: duration / (timestamps.length - 1),
        peakHour: this.findPeakHour(hourlyDistribution),
        peakDate: this.findPeakDate(dailyDistribution)
      }
    };
  }

  // 格式化持续时间
  formatDuration(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    const parts = [];
    if (days > 0) parts.push(`${days}天`);
    if (hours > 0) parts.push(`${hours}小时`);
    if (minutes > 0) parts.push(`${minutes}分钟`);
    if (secs > 0) parts.push(`${secs}秒`);

    return parts.join(' ') || '0秒';
  }

  // 找到峰值小时
  findPeakHour(hourlyDist) {
    let maxCount = 0;
    let peakHour = 0;

    Object.entries(hourlyDist).forEach(([hour, count]) => {
      if (count > maxCount) {
        maxCount = count;
        peakHour = parseInt(hour);
      }
    });

    return { hour: peakHour, count: maxCount };
  }

  // 找到峰值日期
  findPeakDate(dailyDist) {
    let maxCount = 0;
    let peakDate = '';

    Object.entries(dailyDist).forEach(([date, count]) => {
      if (count > maxCount) {
        maxCount = count;
        peakDate = date;
      }
    });

    return { date: peakDate, count: maxCount };
  }
}

// 使用示例
const analyzer = new LogTimestampAnalyzer();

// 示例日志条目
const logEntries = [
  '192.168.1.1 - - [01/Jan/2024:08:00:00 +0800] "GET /index.html HTTP/1.1" 200 1234',
  '192.168.1.2 - - [01/Jan/2024:08:05:30 +0800] "POST /api/login HTTP/1.1" 200 567',
  '2024-01-01T08:10:15.123Z INFO: User login successful',
  'Jan 1 08:15:45 server01 nginx: access log entry',
  '1704067800 DEBUG: Cache miss for key user:123'
];

// 分析日志时间分布
const analysis = analyzer.analyzeLogTimeDistribution(logEntries);
console.log('日志时间分析结果:', JSON.stringify(analysis, null, 2));

// 解析单个时间戳
logEntries.forEach((entry, index) => {
  const parsed = analyzer.parseLogTimestamp(entry);
  if (parsed) {
    console.log(`条目 ${index + 1}:`, parsed);
  }
});
```

### 2. 数据库时间处理

```javascript
// 数据库时间戳处理工具
class DatabaseTimestampHandler {
  constructor() {
    this.dbFormats = {
      mysql: 'YYYY-MM-DD HH:mm:ss',
      postgresql: 'YYYY-MM-DD HH:mm:ss.SSS',
      mongodb: 'ISODate',
      sqlite: 'YYYY-MM-DD HH:mm:ss'
    };
  }

  // 转换为数据库格式
  toDatabase(timestamp, dbType = 'mysql') {
    const date = new Date(timestamp * 1000);

    switch (dbType.toLowerCase()) {
      case 'mysql':
      case 'sqlite':
        return date.toISOString().slice(0, 19).replace('T', ' ');

      case 'postgresql':
        return date.toISOString().slice(0, 23).replace('T', ' ');

      case 'mongodb':
        return `ISODate("${date.toISOString()}")`;

      default:
        return date.toISOString();
    }
  }

  // 从数据库格式转换
  fromDatabase(dbTimestamp, dbType = 'mysql') {
    let date;

    switch (dbType.toLowerCase()) {
      case 'mysql':
      case 'sqlite':
      case 'postgresql':
        // YYYY-MM-DD HH:mm:ss 或 YYYY-MM-DD HH:mm:ss.SSS
        date = new Date(dbTimestamp.replace(' ', 'T') + 'Z');
        break;

      case 'mongodb':
        // ISODate("2024-01-01T08:00:00.000Z")
        const isoMatch = dbTimestamp.match(/ISODate\("(.+)"\)/);
        if (isoMatch) {
          date = new Date(isoMatch[1]);
        } else {
          date = new Date(dbTimestamp);
        }
        break;

      default:
        date = new Date(dbTimestamp);
    }

    return Math.floor(date.getTime() / 1000);
  }

  // 生成数据库查询条件
  generateQueryConditions(startTime, endTime, dbType = 'mysql', fieldName = 'created_at') {
    const startDb = this.toDatabase(startTime, dbType);
    const endDb = this.toDatabase(endTime, dbType);

    switch (dbType.toLowerCase()) {
      case 'mysql':
        return {
          sql: `${fieldName} BETWEEN ? AND ?`,
          params: [startDb, endDb],
          example: `SELECT * FROM users WHERE ${fieldName} BETWEEN '${startDb}' AND '${endDb}'`
        };

      case 'postgresql':
        return {
          sql: `${fieldName} BETWEEN $1 AND $2`,
          params: [startDb, endDb],
          example: `SELECT * FROM users WHERE ${fieldName} BETWEEN '${startDb}' AND '${endDb}'`
        };

      case 'mongodb':
        return {
          query: {
            [fieldName]: {
              $gte: new Date(startTime * 1000),
              $lte: new Date(endTime * 1000)
            }
          },
          example: `db.users.find({${fieldName}: {$gte: ISODate("${new Date(startTime * 1000).toISOString()}"), $lte: ISODate("${new Date(endTime * 1000).toISOString()}")})`
        };

      case 'sqlite':
        return {
          sql: `${fieldName} BETWEEN ? AND ?`,
          params: [startDb, endDb],
          example: `SELECT * FROM users WHERE ${fieldName} BETWEEN '${startDb}' AND '${endDb}'`
        };

      default:
        return {
          sql: `${fieldName} BETWEEN ? AND ?`,
          params: [startDb, endDb]
        };
    }
  }

  // 批量转换时间戳
  batchConvert(timestamps, fromFormat, toFormat) {
    return timestamps.map(ts => {
      try {
        let unixTimestamp;

        // 转换为Unix时间戳
        if (fromFormat === 'unix') {
          unixTimestamp = ts;
        } else {
          unixTimestamp = this.fromDatabase(ts, fromFormat);
        }

        // 转换为目标格式
        if (toFormat === 'unix') {
          return unixTimestamp;
        } else {
          return this.toDatabase(unixTimestamp, toFormat);
        }
      } catch (error) {
        return { error: error.message, original: ts };
      }
    });
  }

  // 时间范围分析
  analyzeTimeRange(timestamps, dbType = 'mysql') {
    const unixTimestamps = timestamps.map(ts => {
      if (typeof ts === 'number') {
        return ts;
      } else {
        return this.fromDatabase(ts, dbType);
      }
    }).filter(ts => !isNaN(ts));

    if (unixTimestamps.length === 0) {
      return { error: '没有有效的时间戳' };
    }

    unixTimestamps.sort((a, b) => a - b);

    const min = unixTimestamps[0];
    const max = unixTimestamps[unixTimestamps.length - 1];
    const range = max - min;

    // 计算时间间隔分布
    const intervals = [];
    for (let i = 1; i < unixTimestamps.length; i++) {
      intervals.push(unixTimestamps[i] - unixTimestamps[i - 1]);
    }

    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;

    return {
      count: unixTimestamps.length,
      range: {
        min: {
          unix: min,
          formatted: this.toDatabase(min, dbType),
          iso: new Date(min * 1000).toISOString()
        },
        max: {
          unix: max,
          formatted: this.toDatabase(max, dbType),
          iso: new Date(max * 1000).toISOString()
        },
        duration: this.formatDuration(range)
      },
      intervals: {
        average: avgInterval,
        min: Math.min(...intervals),
        max: Math.max(...intervals),
        formatted: {
          average: this.formatDuration(avgInterval),
          min: this.formatDuration(Math.min(...intervals)),
          max: this.formatDuration(Math.max(...intervals))
        }
      }
    };
  }

  // 格式化持续时间
  formatDuration(seconds) {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
    } else if (seconds < 86400) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes}分`;
    } else {
      const days = Math.floor(seconds / 86400);
      const hours = Math.floor((seconds % 86400) / 3600);
      return `${days}天${hours}小时`;
    }
  }

  // 生成时间序列
  generateTimeSeries(startTime, endTime, interval = 3600) {
    const series = [];
    let current = startTime;

    while (current <= endTime) {
      series.push({
        unix: current,
        iso: new Date(current * 1000).toISOString(),
        mysql: this.toDatabase(current, 'mysql'),
        postgresql: this.toDatabase(current, 'postgresql'),
        mongodb: this.toDatabase(current, 'mongodb')
      });
      current += interval;
    }

    return series;
  }
}

// 使用示例
const dbHandler = new DatabaseTimestampHandler();

// 当前时间戳
const now = Math.floor(Date.now() / 1000);

// 转换为不同数据库格式
console.log('MySQL格式:', dbHandler.toDatabase(now, 'mysql'));
console.log('PostgreSQL格式:', dbHandler.toDatabase(now, 'postgresql'));
console.log('MongoDB格式:', dbHandler.toDatabase(now, 'mongodb'));

// 生成查询条件
const queryConditions = dbHandler.generateQueryConditions(
  now - 86400, // 24小时前
  now,          // 现在
  'mysql',
  'created_at'
);
console.log('MySQL查询条件:', queryConditions);

// 批量转换
const mysqlTimestamps = [
  '2024-01-01 08:00:00',
  '2024-01-01 09:00:00',
  '2024-01-01 10:00:00'
];

const converted = dbHandler.batchConvert(mysqlTimestamps, 'mysql', 'postgresql');
console.log('批量转换结果:', converted);

// 时间范围分析
const analysis = dbHandler.analyzeTimeRange(mysqlTimestamps, 'mysql');
console.log('时间范围分析:', JSON.stringify(analysis, null, 2));
```

### 3. 时间计算和调度

```javascript
// 时间计算和调度工具
class TimeCalculator {
  constructor() {
    this.timeUnits = {
      second: 1,
      minute: 60,
      hour: 3600,
      day: 86400,
      week: 604800,
      month: 2592000, // 30天
      year: 31536000  // 365天
    };
  }

  // 时间加法
  addTime(timestamp, amount, unit) {
    const seconds = this.timeUnits[unit];
    if (!seconds) {
      throw new Error(`不支持的时间单位: ${unit}`);
    }

    return timestamp + (amount * seconds);
  }

  // 时间减法
  subtractTime(timestamp, amount, unit) {
    return this.addTime(timestamp, -amount, unit);
  }

  // 计算时间差
  timeDifference(timestamp1, timestamp2) {
    const diff = Math.abs(timestamp2 - timestamp1);

    return {
      seconds: diff,
      minutes: diff / 60,
      hours: diff / 3600,
      days: diff / 86400,
      weeks: diff / 604800,
      months: diff / 2592000,
      years: diff / 31536000,
      formatted: this.formatDuration(diff)
    };
  }

  // 格式化持续时间
  formatDuration(seconds) {
    const units = [
      { name: '年', value: 31536000 },
      { name: '月', value: 2592000 },
      { name: '周', value: 604800 },
      { name: '天', value: 86400 },
      { name: '小时', value: 3600 },
      { name: '分钟', value: 60 },
      { name: '秒', value: 1 }
    ];

    const parts = [];
    let remaining = seconds;

    for (const unit of units) {
      if (remaining >= unit.value) {
        const count = Math.floor(remaining / unit.value);
        parts.push(`${count}${unit.name}`);
        remaining %= unit.value;
      }
    }

    return parts.length > 0 ? parts.join(' ') : '0秒';
  }

  // 获取时间边界
  getTimeBoundaries(timestamp, boundary = 'day') {
    const date = new Date(timestamp * 1000);

    switch (boundary) {
      case 'hour':
        const hourStart = new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours());
        const hourEnd = new Date(hourStart.getTime() + 3600000 - 1);
        return {
          start: Math.floor(hourStart.getTime() / 1000),
          end: Math.floor(hourEnd.getTime() / 1000)
        };

      case 'day':
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dayEnd = new Date(dayStart.getTime() + 86400000 - 1);
        return {
          start: Math.floor(dayStart.getTime() / 1000),
          end: Math.floor(dayEnd.getTime() / 1000)
        };

      case 'week':
        const dayOfWeek = date.getDay();
        const weekStart = new Date(date.getTime() - dayOfWeek * 86400000);
        weekStart.setHours(0, 0, 0, 0);
        const weekEnd = new Date(weekStart.getTime() + 7 * 86400000 - 1);
        return {
          start: Math.floor(weekStart.getTime() / 1000),
          end: Math.floor(weekEnd.getTime() / 1000)
        };

      case 'month':
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);
        return {
          start: Math.floor(monthStart.getTime() / 1000),
          end: Math.floor(monthEnd.getTime() / 1000)
        };

      case 'year':
        const yearStart = new Date(date.getFullYear(), 0, 1);
        const yearEnd = new Date(date.getFullYear(), 11, 31, 23, 59, 59, 999);
        return {
          start: Math.floor(yearStart.getTime() / 1000),
          end: Math.floor(yearEnd.getTime() / 1000)
        };

      default:
        throw new Error(`不支持的边界类型: ${boundary}`);
    }
  }

  // 生成定时任务时间
  generateScheduleTimes(startTime, pattern, count = 10) {
    const times = [];
    let current = startTime;

    for (let i = 0; i < count; i++) {
      times.push({
        timestamp: current,
        iso: new Date(current * 1000).toISOString(),
        local: new Date(current * 1000).toLocaleString('zh-CN')
      });

      // 根据模式计算下一次时间
      current = this.getNextScheduleTime(current, pattern);
    }

    return times;
  }

  // 获取下一次调度时间
  getNextScheduleTime(currentTime, pattern) {
    const { type, interval, at } = pattern;

    switch (type) {
      case 'interval':
        return this.addTime(currentTime, interval.amount, interval.unit);

      case 'daily':
        // 每天在指定时间执行
        const nextDay = this.addTime(currentTime, 1, 'day');
        const dayBoundary = this.getTimeBoundaries(nextDay, 'day');
        return dayBoundary.start + (at.hour * 3600) + (at.minute * 60);

      case 'weekly':
        // 每周在指定日期和时间执行
        const nextWeek = this.addTime(currentTime, 7, 'day');
        const weekBoundary = this.getTimeBoundaries(nextWeek, 'week');
        return weekBoundary.start + (at.dayOfWeek * 86400) + (at.hour * 3600) + (at.minute * 60);

      case 'monthly':
        // 每月在指定日期和时间执行
        const date = new Date(currentTime * 1000);
        const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, at.day, at.hour, at.minute);
        return Math.floor(nextMonth.getTime() / 1000);

      default:
        throw new Error(`不支持的调度类型: ${type}`);
    }
  }

  // 计算工作日
  calculateWorkdays(startTime, endTime, holidays = []) {
    let current = startTime;
    let workdays = 0;
    const holidaySet = new Set(holidays);

    while (current <= endTime) {
      const date = new Date(current * 1000);
      const dayOfWeek = date.getDay();
      const dateString = date.toISOString().split('T')[0];

      // 检查是否是工作日（周一到周五）且不是节假日
      if (dayOfWeek >= 1 && dayOfWeek <= 5 && !holidaySet.has(dateString)) {
        workdays++;
      }

      current = this.addTime(current, 1, 'day');
    }

    return workdays;
  }

  // 时区转换
  convertTimezone(timestamp, fromZone, toZone) {
    const timezones = {
      'UTC': 0,
      'GMT': 0,
      'EST': -5,    // 美国东部标准时间
      'PST': -8,    // 美国太平洋标准时间
      'CST': 8,     // 中国标准时间
      'JST': 9,     // 日本标准时间
      'IST': 5.5,   // 印度标准时间
      'CET': 1,     // 中欧时间
      'AEST': 10    // 澳大利亚东部标准时间
    };

    const fromOffset = timezones[fromZone];
    const toOffset = timezones[toZone];

    if (fromOffset === undefined || toOffset === undefined) {
      throw new Error('不支持的时区');
    }

    const offsetDiff = (toOffset - fromOffset) * 3600;
    return timestamp + offsetDiff;
  }

  // 获取时间统计信息
  getTimeStatistics(timestamps) {
    if (timestamps.length === 0) {
      return { error: '没有时间戳数据' };
    }

    const sorted = [...timestamps].sort((a, b) => a - b);
    const min = sorted[0];
    const max = sorted[sorted.length - 1];
    const range = max - min;

    // 计算平均值
    const sum = timestamps.reduce((acc, ts) => acc + ts, 0);
    const average = sum / timestamps.length;

    // 计算中位数
    const median = sorted.length % 2 === 0
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)];

    // 按小时分布
    const hourlyDistribution = {};
    timestamps.forEach(ts => {
      const hour = new Date(ts * 1000).getHours();
      hourlyDistribution[hour] = (hourlyDistribution[hour] || 0) + 1;
    });

    // 按星期分布
    const weeklyDistribution = {};
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    timestamps.forEach(ts => {
      const day = new Date(ts * 1000).getDay();
      const dayName = weekdays[day];
      weeklyDistribution[dayName] = (weeklyDistribution[dayName] || 0) + 1;
    });

    return {
      count: timestamps.length,
      range: {
        min: {
          timestamp: min,
          formatted: new Date(min * 1000).toLocaleString('zh-CN')
        },
        max: {
          timestamp: max,
          formatted: new Date(max * 1000).toLocaleString('zh-CN')
        },
        duration: this.formatDuration(range)
      },
      statistics: {
        average: {
          timestamp: Math.floor(average),
          formatted: new Date(average * 1000).toLocaleString('zh-CN')
        },
        median: {
          timestamp: Math.floor(median),
          formatted: new Date(median * 1000).toLocaleString('zh-CN')
        }
      },
      distribution: {
        hourly: hourlyDistribution,
        weekly: weeklyDistribution
      }
    };
  }
}

// 使用示例
const calculator = new TimeCalculator();

// 当前时间
const now = Math.floor(Date.now() / 1000);

// 时间计算
const futureTime = calculator.addTime(now, 7, 'day');
console.log('7天后:', new Date(futureTime * 1000).toLocaleString('zh-CN'));

const pastTime = calculator.subtractTime(now, 30, 'day');
console.log('30天前:', new Date(pastTime * 1000).toLocaleString('zh-CN'));

// 时间差计算
const diff = calculator.timeDifference(pastTime, futureTime);
console.log('时间差:', diff.formatted);

// 获取今天的边界
const dayBoundaries = calculator.getTimeBoundaries(now, 'day');
console.log('今天开始:', new Date(dayBoundaries.start * 1000).toLocaleString('zh-CN'));
console.log('今天结束:', new Date(dayBoundaries.end * 1000).toLocaleString('zh-CN'));

// 生成调度时间（每天上午9点）
const schedulePattern = {
  type: 'daily',
  at: { hour: 9, minute: 0 }
};
const scheduleTimes = calculator.generateScheduleTimes(now, schedulePattern, 5);
console.log('未来5次调度时间:', scheduleTimes);

// 计算工作日
const workdays = calculator.calculateWorkdays(
  now,
  calculator.addTime(now, 30, 'day'),
  ['2024-01-01', '2024-01-02'] // 节假日
);
console.log('30天内工作日数量:', workdays);

// 时区转换
const utcTime = calculator.convertTimezone(now, 'CST', 'UTC');
console.log('UTC时间:', new Date(utcTime * 1000).toISOString());
```

## 🔧 技术细节

### 时间戳格式

**Unix时间戳:**
- 秒级: 1704067200
- 毫秒级: 1704067200000
- 微秒级: 1704067200000000

**ISO 8601格式:**
- 基本格式: 2024-01-01T08:00:00Z
- 带毫秒: 2024-01-01T08:00:00.000Z
- 带时区: 2024-01-01T08:00:00+08:00

**RFC 2822格式:**
- Mon, 01 Jan 2024 08:00:00 GMT
- Mon, 01 Jan 2024 08:00:00 +0800

### 时区处理

**常用时区:**
- UTC/GMT: 协调世界时
- CST: 中国标准时间 (UTC+8)
- EST: 美国东部标准时间 (UTC-5)
- PST: 美国太平洋标准时间 (UTC-8)
- JST: 日本标准时间 (UTC+9)

**夏令时:**
- 自动检测和处理
- 历史夏令时规则
- 地区特定规则

### 精度和范围

**JavaScript Date对象:**
- 范围: 1970-01-01 到 2038-01-19 (32位系统)
- 精度: 毫秒级
- 时区: 本地时区或UTC

**数据库时间类型:**
- MySQL: DATETIME, TIMESTAMP
- PostgreSQL: TIMESTAMP, TIMESTAMPTZ
- MongoDB: Date, ISODate

## 💡 使用技巧

- **时区一致性** : 统一使用UTC时间存储
- **精度选择** : 根据需求选择秒级或毫秒级
- **边界处理** : 注意月末、年末的边界情况
- **性能优化** : 批量处理大量时间戳

## ⚠️ 注意事项

- **2038年问题** : 32位系统的时间戳限制
- **闰秒** : 某些系统不处理闰秒
- **时区变更** : 政治因素导致的时区规则变化
- **精度损失** : 浮点数运算可能导致精度问题

## 🚀 如何使用

1. **输入时间** : 输入时间戳或选择时间格式
2. **选择格式** : 选择源格式和目标格式
3. **时区设置** : 设置源时区和目标时区
4. **批量处理** : 上传文件进行批量转换
5. **复制结果** : 点击复制按钮获取转换结果

> **提示** : 此工具在客户端本地处理，不会向服务器发送数据，确保数据安全和处理速度。
```