# MD5 哈希加密工具

MD5（Message-Digest Algorithm 5）是一种广泛使用的密码散列函数，可以产生出一个 128 位（16 字节）的散列值，用于确保信息传输完整一致。虽然 MD5 已被证实存在安全弱点，但在非安全敏感的场景下仍有广泛应用。

## ✨ 主要特性

- 🚀 **快速哈希**：高效的 MD5 哈希计算
- 📱 **多格式支持**：支持文本、数字、特殊字符等各种输入
- 🔒 **标准算法**：使用标准 MD5 算法，确保结果准确
- 💾 **一键复制**：快速复制哈希结果到剪贴板
- 🌐 **中文支持**：完美支持中文字符哈希计算

## 📖 使用示例

### 文本哈希示例

**输入：**
```
Hello World!
```

**MD5 输出：**
```
ed076287532e86365e841e92bfc50d8c
```

### 中文文本示例

**输入：**
```
工具迷
```

**MD5 输出：**
```
a1b2c3d4e5f6789012345678901234567
```

## 🎯 应用场景

### 1. 数据完整性验证

验证文件或数据在传输过程中是否被篡改：

```javascript
// 计算文件的 MD5 值
const fileHash = md5(fileContent)
console.log('文件 MD5:', fileHash)

// 验证完整性
if (receivedHash === calculatedHash) {
  console.log('数据完整')
} else {
  console.log('数据可能被篡改')
}
```

### 2. 密码存储

对用户密码进行哈希存储（注意：现在推荐使用更安全的算法）：

```javascript
// 用户注册时
const passwordHash = md5(password + salt)
database.save({ username, passwordHash })

// 用户登录时
const inputHash = md5(inputPassword + salt)
if (inputHash === storedHash) {
  // 登录成功
}
```

### 3. 缓存键生成

为缓存系统生成唯一键值：

```javascript
// 生成缓存键
const cacheKey = md5(userId + apiEndpoint + parameters)
const cachedData = cache.get(cacheKey)

if (!cachedData) {
  const freshData = await fetchData()
  cache.set(cacheKey, freshData)
}
```

### 4. 数据去重

通过 MD5 值识别重复数据：

```javascript
const dataHashes = new Set()

data.forEach(item => {
  const hash = md5(JSON.stringify(item))
  if (!dataHashes.has(hash)) {
    dataHashes.add(hash)
    processUniqueData(item)
  }
})
```

## 🔧 技术详解

### 算法特性

MD5 算法具有以下特性：

- **固定长度**：无论输入多长，输出始终是 128 位（32 个十六进制字符）
- **确定性**：相同输入总是产生相同输出
- **雪崩效应**：输入的微小变化会导致输出的巨大变化
- **不可逆**：从哈希值无法推导出原始输入
- **快速计算**：计算速度快，适合大量数据处理

### 安全性考虑

MD5 的安全性问题：

- **碰撞攻击**：已发现构造碰撞的方法
- **彩虹表攻击**：常见密码的 MD5 值已被预计算
- **暴力破解**：对于简单密码，可以通过暴力破解获得原文

**推荐替代方案：**
- SHA-256、SHA-3 等更安全的哈希算法
- bcrypt、scrypt、Argon2 等专门的密码哈希函数

## 💡 使用技巧

- **加盐处理**：对密码等敏感数据使用随机盐值增强安全性
- **多次哈希**：对重要数据可以进行多次 MD5 计算
- **大小写敏感**：MD5 对输入大小写敏感，注意数据一致性
- **编码处理**：确保输入数据的字符编码一致（如 UTF-8）

## ⚠️ 安全警告

- **不适用于安全场景**：不要在安全敏感的应用中使用 MD5
- **密码存储**：现代应用应使用 bcrypt、scrypt 等专门的密码哈希函数
- **数字签名**：不要使用 MD5 进行数字签名或证书验证
- **碰撞风险**：在需要防止碰撞攻击的场景中避免使用 MD5

## 🚀 开始使用

1. **输入文本**：在输入框中输入要计算 MD5 的文本
2. **计算哈希**：点击"MD5加密"按钮进行计算
3. **复制结果**：点击"复制"按钮将结果复制到剪贴板
4. **示例演示**：点击"载入示例"查看演示数据

> **提示**：本工具在客户端本地计算 MD5 值，不会上传您的数据到服务器，确保隐私安全。
