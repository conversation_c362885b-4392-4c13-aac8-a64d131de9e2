# JSON 转 PHP 数组代码工具

这个工具可以将 **JSON 数据** 快速转换为 **PHP 数组代码**，方便在 PHP 项目中直接使用。支持嵌套结构、中文字符、布尔值、数字等各种数据类型。

## ✨ 主要特性

- 🚀 **一键转换**：粘贴 JSON 内容，立即生成 PHP 数组代码
- 🎯 **完美格式**：生成的代码格式规范，可直接复制使用
- 🌐 **多类型支持**：支持字符串、数字、布尔值、数组、对象等所有 JSON 类型
- 🔧 **特殊字符处理**：自动转义单引号、换行符等特殊字符
- 📱 **响应式设计**：支持手机、平板等各种设备

## 📖 使用示例

### 基础示例

假设你有如下 JSON 数据：

\`\`\`json
{
  "name": "ChatGPT",
  "features": ["AI", "NLP", "Code"],
  "active": true,
  "version": 4.0
}
\`\`\`

转换后的 PHP 数组代码：

\`\`\`php
<?php

$data = [
  'name' => 'ChatGPT',
  'features' => [
    'AI',
    'NLP',
    'Code'
  ],
  'active' => true,
  'version' => 4.0
];
\`\`\`

### 复杂嵌套示例

对于复杂的嵌套结构：

\`\`\`json
{
  "user": {
    "id": 123,
    "profile": {
      "name": "张三",
      "email": "<EMAIL>",
      "preferences": {
        "theme": "dark",
        "notifications": true
      }
    },
    "roles": ["admin", "editor"]
  }
}
\`\`\`

转换结果：

\`\`\`php
<?php

$data = [
  'user' => [
    'id' => 123,
    'profile' => [
      'name' => '张三',
      'email' => '<EMAIL>',
      'preferences' => [
        'theme' => 'dark',
        'notifications' => true
      ]
    ],
    'roles' => [
      'admin',
      'editor'
    ]
  ]
];
\`\`\`

## 🎯 应用场景

### 1. 配置文件生成

将 JSON 配置转换为 PHP 配置数组：

\`\`\`php
<?php
// config/app.php
return [
  'name' => 'My Application',
  'debug' => true,
  'timezone' => 'Asia/Shanghai'
];
\`\`\`

### 2. API Mock 数据

快速生成测试数据：

\`\`\`php
<?php
// 模拟 API 返回数据
$mockData = [
  'status' => 'success',
  'data' => [
    'users' => [
      ['id' => 1, 'name' => '用户1'],
      ['id' => 2, 'name' => '用户2']
    ]
  ]
];
\`\`\`

### 3. 数据库种子文件

生成数据库初始化数据：

\`\`\`php
<?php
// database/seeders/UserSeeder.php
$users = [
  [
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'role' => 'admin'
  ],
  [
    'name' => 'Regular User',
    'email' => '<EMAIL>',
    'role' => 'user'
  ]
];
\`\`\`

## 🔧 技术细节

### 数据类型转换

| JSON 类型 | PHP 类型 | 示例 |
|-----------|----------|------|
| string | string | \`"hello"\` → \`'hello'\` |
| number | int/float | \`42\` → \`42\`, \`3.14\` → \`3.14\` |
| boolean | boolean | \`true\` → \`true\`, \`false\` → \`false\` |
| null | null | \`null\` → \`null\` |
| array | array | \`[1,2,3]\` → \`[1, 2, 3]\` |
| object | array | \`{"key": "value"}\` → \`['key' => 'value']\` |

### 特殊字符处理

工具会自动处理以下特殊字符：

- **单引号**：\`'\` → \`\\'\`
- **反斜杠**：\`\\\` → \`\\\\\`
- **换行符**：\`\\n\` → \`\\\\n\`
- **制表符**：\`\\t\` → \`\\\\t\`
- **回车符**：\`\\r\` → \`\\\\r\`

## 💡 使用技巧

1. **复制粘贴**：可以直接从 API 响应、配置文件等地方复制 JSON 内容
2. **格式验证**：工具会自动验证 JSON 格式，提示错误信息
3. **一键复制**：生成的 PHP 代码可以一键复制到剪贴板
4. **示例学习**：点击"加载示例"查看更多使用案例

## 🚀 开始使用

1. 在上方输入框中粘贴或输入 JSON 数据
2. 点击"转换"按钮
3. 复制生成的 PHP 数组代码
4. 粘贴到你的 PHP 项目中使用

> **提示**：确保输入的是有效的 JSON 格式，工具会自动检测并提示格式错误。