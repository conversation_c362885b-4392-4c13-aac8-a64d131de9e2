# Netscape Cookies 格式化工具

Netscape Cookies 格式是一种经典的 Cookie 存储格式，由 Netscape 浏览器首创，现在仍广泛用于浏览器数据导入导出。本工具支持 Netscape 格式与 JSON 格式的双向转换，方便开发者处理和管理 Cookie 数据。

## ✨ 主要特性

- 🔄 **双向转换**：支持 Netscape 格式与 JSON 格式互转
- 📊 **格式验证**：自动验证 Cookie 格式的正确性
- 🎨 **格式美化**：自动格式化输出结果，提升可读性
- 📋 **一键复制**：转换结果可直接复制使用
- 🔧 **错误处理**：友好的错误提示和修复建议

## 📖 使用示例

### Netscape 格式示例

**标准 Netscape Cookies 格式：**
```
# Netscape HTTP Cookie File
# This is a generated file!  Do not edit.

.example.com	TRUE	/	FALSE	1640995200	session_id	abc123def456
.example.com	TRUE	/	FALSE	1640995200	user_pref	dark_theme
example.com	FALSE	/login	TRUE	1640995200	csrf_token	xyz789uvw012
```

### JSON 格式示例

**对应的 JSON 格式：**
```json
[
  {
    "domain": ".example.com",
    "flag": true,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "session_id",
    "value": "abc123def456"
  },
  {
    "domain": ".example.com",
    "flag": true,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "user_pref",
    "value": "dark_theme"
  },
  {
    "domain": "example.com",
    "flag": false,
    "path": "/login",
    "secure": true,
    "expiration": 1640995200,
    "name": "csrf_token",
    "value": "xyz789uvw012"
  }
]
```

## 🎯 应用场景

### 1. 浏览器数据迁移

在不同浏览器间迁移 Cookie 数据：

```
场景：从 Chrome 导出 Cookie 到 Firefox

步骤：
1. 从 Chrome 导出 Netscape 格式 Cookie 文件
2. 使用工具转换为 JSON 格式进行编辑
3. 转换回 Netscape 格式
4. 导入到 Firefox 浏览器

应用：
- 浏览器切换
- 开发环境同步
- 测试数据准备
```

### 2. Web 开发调试

开发过程中管理和调试 Cookie：

```javascript
// 开发场景：批量设置测试 Cookie
const testCookies = [
  {
    "domain": "localhost",
    "flag": false,
    "path": "/",
    "secure": false,
    "expiration": 1640995200,
    "name": "debug_mode",
    "value": "true"
  },
  {
    "domain": "localhost",
    "flag": false,
    "path": "/admin",
    "secure": true,
    "expiration": 1640995200,
    "name": "admin_token",
    "value": "dev_token_123"
  }
]

// 转换为 Netscape 格式后导入浏览器
```

### 3. 自动化测试

在自动化测试中预设 Cookie 状态：

```python
# Selenium 测试示例
from selenium import webdriver

# 从 Netscape 文件加载 Cookie
def load_cookies_from_netscape(driver, cookie_file):
    with open(cookie_file, 'r') as f:
        for line in f:
            if line.startswith('#') or not line.strip():
                continue
            
            parts = line.strip().split('\t')
            if len(parts) == 7:
                cookie = {
                    'name': parts[5],
                    'value': parts[6],
                    'domain': parts[0],
                    'path': parts[2],
                    'secure': parts[3] == 'TRUE'
                }
                driver.add_cookie(cookie)

# 使用示例
driver = webdriver.Chrome()
driver.get("https://example.com")
load_cookies_from_netscape(driver, "test_cookies.txt")
```

### 4. 数据分析

分析网站的 Cookie 使用情况：

```javascript
// Cookie 分析脚本
function analyzeCookies(netscapeData) {
  const cookies = parseNetscapeCookies(netscapeData)
  
  const analysis = {
    totalCookies: cookies.length,
    domains: [...new Set(cookies.map(c => c.domain))],
    secureCount: cookies.filter(c => c.secure).length,
    sessionCount: cookies.filter(c => c.expiration === 0).length,
    expiredCount: cookies.filter(c => 
      c.expiration > 0 && c.expiration < Date.now() / 1000
    ).length
  }
  
  return analysis
}
```

## 🔧 技术详解

### Netscape 格式规范

Netscape Cookies 文件格式说明：

**文件头部：**
```
# Netscape HTTP Cookie File
# This is a generated file!  Do not edit.
```

**字段格式（制表符分隔）：**
1. **domain**：Cookie 的域名
2. **flag**：是否包含子域名（TRUE/FALSE）
3. **path**：Cookie 的路径
4. **secure**：是否仅 HTTPS 传输（TRUE/FALSE）
5. **expiration**：过期时间（Unix 时间戳）
6. **name**：Cookie 名称
7. **value**：Cookie 值

**示例行：**
```
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123
```

### 转换规则

格式转换遵循以下规则：

**Netscape 到 JSON：**
- 解析制表符分隔的字段
- 转换布尔值（TRUE/FALSE → true/false）
- 处理时间戳格式
- 忽略注释行和空行

**JSON 到 Netscape：**
- 添加标准文件头部
- 转换布尔值（true/false → TRUE/FALSE）
- 格式化时间戳
- 使用制表符分隔字段

**特殊处理：**
- 空值和特殊字符的转义
- 域名格式的标准化
- 路径的规范化处理

## 💡 使用技巧

- **格式检查**：转换前验证输入格式的正确性
- **备份数据**：重要 Cookie 数据转换前请备份
- **时间戳转换**：注意时间戳格式（Unix 时间戳）
- **域名规范**：确保域名格式符合标准

## ⚠️ 注意事项

- **格式严格**：Netscape 格式对制表符和字段顺序要求严格
- **字符编码**：确保文件使用 UTF-8 编码
- **安全性**：Cookie 数据可能包含敏感信息，注意保护
- **兼容性**：不同浏览器对 Cookie 导入的支持可能有差异

## 🚀 开始使用

1. **选择转换方向**：点击"Netscape转JSON"或"JSON转Netscape"按钮
2. **输入数据**：在输入框中粘贴要转换的数据
3. **执行转换**：点击转换按钮进行格式转换
4. **复制结果**：点击"复制"按钮将结果复制到剪贴板
5. **示例演示**：点击"载入示例"查看演示数据

> **提示**：本工具在客户端本地进行转换，不会上传您的 Cookie 数据到服务器，确保隐私安全。
