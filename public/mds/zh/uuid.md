# UUID 生成器

UUID（Universally Unique Identifier，通用唯一识别码）是一种标准化的识别信息，用于在分布式系统中唯一标识信息。UUID 由 128 位数字组成，通常以 32 个十六进制数字表示，并用连字符分隔成五组。

## ✨ 主要特性

- 🔢 **多版本支持**：支持 UUID v1、v4 等不同版本
- ⚡ **批量生成**：支持一次生成多个 UUID
- 📋 **多种格式**：支持标准格式、无连字符格式等
- 💾 **一键复制**：生成的 UUID 可直接复制使用
- 🔧 **实时生成**：点击即可生成新的 UUID

## 📖 使用示例

### 标准 UUID v4

**生成示例：**
```
f47ac10b-58cc-4372-a567-0e02b2c3d479
```

**特点：**
- 随机生成，无时间信息
- 碰撞概率极低
- 最常用的 UUID 版本

### UUID v1（基于时间）

**生成示例：**
```
6ba7b810-9dad-11d1-80b4-00c04fd430c8
```

**特点：**
- 包含时间戳信息
- 包含 MAC 地址信息
- 可以推断生成时间

### 无连字符格式

**生成示例：**
```
f47ac10b58cc4372a5670e02b2c3d479
```

**特点：**
- 32 个连续的十六进制字符
- 适用于某些数据库或系统

## 🎯 应用场景

### 1. 数据库主键

在数据库中使用 UUID 作为主键：

```sql
-- 创建使用 UUID 主键的表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据（自动生成 UUID）
INSERT INTO users (username, email) 
VALUES ('zhangsan', '<EMAIL>');

-- 查询数据
SELECT * FROM users WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
```

### 2. 分布式系统

在分布式系统中生成唯一标识：

```javascript
// 微服务中的请求追踪
class RequestTracker {
  constructor() {
    this.requestId = generateUUID()
    this.timestamp = new Date().toISOString()
  }

  log(message) {
    console.log(`[${this.requestId}] ${this.timestamp}: ${message}`)
  }
}

// 使用示例
const tracker = new RequestTracker()
tracker.log('开始处理用户请求')
tracker.log('调用用户服务')
tracker.log('调用订单服务')
tracker.log('请求处理完成')

// 输出示例：
// [f47ac10b-58cc-4372-a567-0e02b2c3d479] 2024-06-15T10:30:00.000Z: 开始处理用户请求
```

### 3. 文件命名

为上传的文件生成唯一名称：

```javascript
// 文件上传处理
function handleFileUpload(file) {
  const fileExtension = file.name.split('.').pop()
  const uniqueFileName = `${generateUUID()}.${fileExtension}`
  
  // 保存文件
  const filePath = `/uploads/${uniqueFileName}`
  saveFile(file, filePath)
  
  return {
    originalName: file.name,
    fileName: uniqueFileName,
    filePath: filePath,
    uploadTime: new Date().toISOString()
  }
}

// 使用示例
const uploadResult = handleFileUpload(userFile)
console.log(uploadResult)
// {
//   originalName: "document.pdf",
//   fileName: "f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   filePath: "/uploads/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   uploadTime: "2024-06-15T10:30:00.000Z"
// }
```

### 4. 会话管理

为用户会话生成唯一标识：

```javascript
// 会话管理系统
class SessionManager {
  constructor() {
    this.sessions = new Map()
  }

  createSession(userId) {
    const sessionId = generateUUID()
    const session = {
      id: sessionId,
      userId: userId,
      createdAt: new Date(),
      lastAccess: new Date(),
      data: {}
    }
    
    this.sessions.set(sessionId, session)
    return sessionId
  }

  getSession(sessionId) {
    const session = this.sessions.get(sessionId)
    if (session) {
      session.lastAccess = new Date()
    }
    return session
  }

  destroySession(sessionId) {
    return this.sessions.delete(sessionId)
  }
}

// 使用示例
const sessionManager = new SessionManager()
const sessionId = sessionManager.createSession('user123')
console.log('会话ID:', sessionId)
// 会话ID: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

### 5. API 密钥生成

为 API 访问生成唯一密钥：

```javascript
// API 密钥管理
class ApiKeyManager {
  constructor() {
    this.apiKeys = new Map()
  }

  generateApiKey(userId, permissions = []) {
    const apiKey = generateUUID()
    const keyInfo = {
      key: apiKey,
      userId: userId,
      permissions: permissions,
      createdAt: new Date(),
      lastUsed: null,
      isActive: true
    }
    
    this.apiKeys.set(apiKey, keyInfo)
    return apiKey
  }

  validateApiKey(apiKey) {
    const keyInfo = this.apiKeys.get(apiKey)
    if (keyInfo && keyInfo.isActive) {
      keyInfo.lastUsed = new Date()
      return keyInfo
    }
    return null
  }

  revokeApiKey(apiKey) {
    const keyInfo = this.apiKeys.get(apiKey)
    if (keyInfo) {
      keyInfo.isActive = false
      return true
    }
    return false
  }
}

// 使用示例
const apiManager = new ApiKeyManager()
const apiKey = apiManager.generateApiKey('user123', ['read', 'write'])
console.log('API 密钥:', apiKey)
// API 密钥: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

## 🔧 技术详解

### UUID 版本

不同版本的 UUID 有不同的生成方式：

**UUID v1（基于时间）：**
- 包含时间戳（60位）
- 包含时钟序列（14位）
- 包含节点标识符（48位，通常是 MAC 地址）
- 可以推断生成时间和位置

**UUID v4（随机）：**
- 122位随机数
- 6位版本和变体标识
- 碰撞概率约为 1/2^122
- 最常用的版本

**UUID v5（基于名称）：**
- 使用 SHA-1 哈希算法
- 基于命名空间和名称生成
- 相同输入总是产生相同 UUID

### 格式结构

标准 UUID 格式：`xxxxxxxx-xxxx-Mxxx-Nxxx-xxxxxxxxxxxx`

- **M**：版本号（1、4、5等）
- **N**：变体标识（通常是 8、9、A、B）
- **x**：十六进制数字（0-9、A-F）

### 碰撞概率

UUID v4 的碰撞概率极低：

- 总共有 2^122 种可能的 UUID
- 生成 10^18 个 UUID 的碰撞概率约为 50%
- 在实际应用中可以认为是唯一的

## 💡 使用技巧

- **版本选择**：一般情况下使用 UUID v4，需要时间信息时使用 v1
- **存储优化**：数据库中可以使用 BINARY(16) 存储以节省空间
- **索引性能**：UUID 作为主键时注意索引性能影响
- **格式统一**：在同一系统中保持 UUID 格式的一致性

## ⚠️ 注意事项

- **性能影响**：UUID 作为主键可能影响数据库性能
- **排序问题**：UUID 无法按生成时间排序（除非使用 v1）
- **存储空间**：UUID 占用较多存储空间（36字符或16字节）
- **可读性**：UUID 不如自增 ID 直观易读

## 🚀 开始使用

1. **选择版本**：根据需求选择 UUID 版本
2. **设置数量**：选择要生成的 UUID 数量
3. **选择格式**：选择标准格式或无连字符格式
4. **生成 UUID**：点击生成按钮创建 UUID
5. **复制使用**：点击复制按钮将 UUID 复制到剪贴板

> **提示**：本工具在客户端本地生成 UUID，不会上传任何数据到服务器，确保隐私安全。
