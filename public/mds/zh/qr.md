# 二维码生成器

二维码（QR Code）是一种矩阵式二维条码，由日本 Denso Wave 公司于 1994 年发明。QR 来自英文 Quick Response 的缩写，即快速响应的意思。本工具可以将文本、网址、联系方式等信息快速生成为二维码图片。

## ✨ 主要特性

- 📱 **多内容支持**：支持文本、网址、联系方式、WiFi 密码等
- 🎨 **高清输出**：生成高质量的二维码图片
- 📏 **尺寸可调**：支持多种尺寸规格
- 🎯 **容错级别**：支持不同的容错级别设置
- 💾 **一键下载**：生成的二维码可直接下载保存
- 🔧 **实时预览**：输入内容即时生成二维码预览

## 📖 使用示例

### 网址二维码

**输入内容：**
```
https://www.gongjumi.com
```

**生成效果：**
- 扫描后直接跳转到工具迷网站
- 适用于网站推广、链接分享

### 文本二维码

**输入内容：**
```
欢迎使用工具迷在线工具！
这里有丰富的开发工具和实用功能。
```

**生成效果：**
- 扫描后显示完整文本内容
- 适用于信息传递、说明展示

### 联系方式二维码

**输入内容：**
```
BEGIN:VCARD
VERSION:3.0
FN:张三
ORG:工具迷科技
TEL:+86-138-0013-8000
EMAIL:<EMAIL>
URL:https://www.gongjumi.com
END:VCARD
```

**生成效果：**
- 扫描后可直接添加到通讯录
- 包含姓名、公司、电话、邮箱等信息

## 🎯 应用场景

### 1. 网站推广

为网站或应用生成推广二维码：

```html
<!-- 网站推广示例 -->
<div class="promotion">
  <h3>扫码访问工具迷</h3>
  <img src="qr-code.png" alt="工具迷二维码">
  <p>发现更多实用工具</p>
</div>

<!-- 应用场景 -->
- 宣传海报
- 名片设计
- 产品包装
- 广告投放
```

### 2. 移动支付

生成支付二维码：

```javascript
// 支付宝支付链接示例
const alipayUrl = 'alipays://platformapi/startapp?saId=10000007&qrcode=https://qr.alipay.com/fkx12345'

// 微信支付链接示例
const wechatPayUrl = 'weixin://wxpay/bizpayurl?pr=abc123def'

// 生成支付二维码
generateQRCode(alipayUrl, {
  size: 200,
  errorCorrectionLevel: 'M'
})
```

### 3. WiFi 密码分享

生成 WiFi 连接二维码：

```
WiFi 二维码格式：
WIFI:T:WPA;S:网络名称;P:密码;H:false;;

示例：
WIFI:T:WPA;S:GongJuMi_5G;P:12345678;H:false;;

参数说明：
- T: 加密类型 (WPA/WEP/nopass)
- S: 网络名称 (SSID)
- P: 密码
- H: 是否隐藏网络 (true/false)
```

### 4. 活动签到

生成活动签到二维码：

```json
{
  "type": "event_checkin",
  "event_id": "tech_meetup_2024",
  "event_name": "技术交流会",
  "location": "北京国际会议中心",
  "date": "2024-06-15",
  "checkin_url": "https://event.gongjumi.com/checkin/tech_meetup_2024"
}
```

### 5. 产品溯源

为产品生成溯源二维码：

```javascript
// 产品信息
const productInfo = {
  id: 'GJM2024001',
  name: '智能工具箱',
  batch: 'B20240615',
  production_date: '2024-06-15',
  manufacturer: '工具迷科技',
  quality_check: 'PASS',
  trace_url: 'https://trace.gongjumi.com/product/GJM2024001'
}

// 生成溯源二维码
const traceData = JSON.stringify(productInfo)
generateQRCode(traceData)
```

## 🔧 技术详解

### 二维码结构

QR 码的基本结构包含：

**功能图形：**
- 位置探测图形：三个角的大方块
- 位置探测图形分隔符：白色边框
- 定位图形：小黑点，用于确定方向

**数据区域：**
- 格式信息：错误修正级别和掩码信息
- 版本信息：QR 码的版本号
- 数据和纠错码字：实际存储的数据

**容量规格：**
- Version 1: 21×21 模块，最多 25 个字符
- Version 40: 177×177 模块，最多 4296 个字符
- 支持数字、字母、汉字、二进制数据

### 容错级别

QR 码支持四种容错级别：

| 级别 | 容错率 | 适用场景 |
|------|--------|----------|
| L | ~7% | 清洁环境，高质量打印 |
| M | ~15% | 一般环境，标准打印 |
| Q | ~25% | 恶劣环境，可能有污损 |
| H | ~30% | 极恶劣环境，严重污损 |

**选择建议：**
- 网址分享：使用 L 或 M 级别
- 户外广告：使用 Q 或 H 级别
- 产品标签：使用 M 或 Q 级别

### 编码模式

QR 码支持多种编码模式：

**数字模式：**
- 只能存储数字 0-9
- 存储效率最高
- 适用于纯数字内容

**字母数字模式：**
- 存储数字、大写字母、部分符号
- 效率较高
- 适用于网址、代码等

**字节模式：**
- 可存储任意字符
- 支持中文、特殊符号
- 通用性最强

**汉字模式：**
- 专门优化中文字符
- 在中文环境下效率更高

## 💡 使用技巧

- **内容优化**：尽量使用简短的内容，提高扫描成功率
- **尺寸选择**：根据使用距离选择合适的尺寸
- **容错级别**：根据使用环境选择合适的容错级别
- **测试验证**：生成后用多种设备测试扫描效果

## ⚠️ 注意事项

- **内容长度**：内容过长会导致二维码过于复杂，影响扫描
- **打印质量**：确保打印清晰，避免模糊或变形
- **颜色对比**：保持足够的颜色对比度，建议黑白配色
- **周围留白**：二维码周围需要留有足够的空白区域

## 🚀 开始使用

1. **输入内容**：在输入框中输入要生成二维码的内容
2. **调整设置**：选择合适的尺寸和容错级别
3. **生成预览**：点击生成按钮查看二维码预览
4. **下载保存**：点击下载按钮保存二维码图片
5. **测试扫描**：用手机扫描测试生成效果

> **提示**：本工具在客户端本地生成二维码，不会上传您的数据到服务器，确保隐私安全。
