# JSON 转 YAML 工具

JSON 和 YAML 都是常用的数据交换格式。JSON 更适合程序处理和 API 传输，YAML 更适合人类阅读和配置文件编写。本工具提供两种格式的双向转换功能，帮助开发者在不同场景下选择合适的数据格式。

## ✨ 主要特性

- 🔄 **双向转换**：支持 JSON 转 YAML 和 YAML 转 JSON
- ✅ **语法验证**：自动检测格式错误并提供详细提示
- 🎨 **格式美化**：自动格式化输出结果，提升可读性
- 📋 **一键复制**：转换结果可直接复制使用
- 🔧 **错误处理**：友好的错误提示和修复建议

## 📖 使用示例

### JSON 转 YAML 示例

**输入 JSON：**
```json
{
  "app": {
    "name": "工具迷",
    "version": "1.0.0"
  },
  "features": ["格式化", "转换", "验证"]
}
```

**输出 YAML：**
```yaml
app:
  name: 工具迷
  version: 1.0.0
features:
  - 格式化
  - 转换
  - 验证
```

### YAML 转 JSON 示例

**输入 YAML：**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
```

**输出 JSON：**
```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "credentials": {
      "username": "admin",
      "password": "secret"
    }
  }
}
```

## 🎯 应用场景

### 1. 配置文件转换

在不同项目间迁移配置文件格式：

```yaml
# 原始 YAML 配置 (config.yml)
server:
  host: 0.0.0.0
  port: 3000
  ssl:
    enabled: true
    cert: /path/to/cert.pem

database:
  type: postgresql
  host: localhost
  port: 5432
  name: myapp
  pool:
    min: 2
    max: 10
```

转换为 JSON 配置后可用于不同的部署环境。

### 2. API 文档编写

将 JSON Schema 转换为更易读的 YAML 格式：

```yaml
# OpenAPI 规范 YAML 格式
openapi: 3.0.0
info:
  title: 工具迷 API
  version: 1.0.0
  description: 在线工具平台 API

paths:
  /tools:
    get:
      summary: 获取工具列表
      parameters:
        - name: category
          in: query
          schema:
            type: string
      responses:
        '200':
          description: 成功返回工具列表
```

### 3. CI/CD 配置迁移

在不同 CI/CD 平台间迁移配置：

```yaml
# GitHub Actions (YAML)
name: Build and Test
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test
```

### 4. 数据迁移

在数据库迁移或数据导入时转换格式：

```yaml
# 用户数据 YAML 格式
users:
  - id: 1
    username: john_doe
    email: <EMAIL>
    profile:
      firstName: John
      lastName: Doe
      age: 30
    preferences:
      theme: dark
      language: en
```

## 🔧 技术详解

### 格式特点对比

JSON 和 YAML 的主要区别：

**JSON 特点：**
- 语法严格，使用大括号和方括号
- 所有字符串必须使用双引号
- 不支持注释
- 文件体积相对较小
- 解析速度快
- 广泛支持，几乎所有编程语言都支持

**YAML 特点：**
- 使用缩进表示层级关系
- 字符串通常不需要引号
- 支持注释（使用 # 符号）
- 可读性更好，更适合人类编辑
- 支持多行字符串
- 支持更复杂的数据类型

**选择建议：**
- API 传输：使用 JSON
- 配置文件：使用 YAML
- 数据存储：根据需求选择

### 转换规则

格式转换遵循以下规则：

**数据类型映射：**
- 字符串：JSON 字符串 ↔ YAML 字符串
- 数字：JSON 数字 ↔ YAML 数字
- 布尔值：JSON true/false ↔ YAML true/false
- 空值：JSON null ↔ YAML null
- 数组：JSON 数组 ↔ YAML 列表
- 对象：JSON 对象 ↔ YAML 映射

**特殊处理：**
- JSON 中的转义字符会被正确处理
- YAML 中的多行字符串转换为 JSON 字符串
- YAML 注释在转换为 JSON 时会丢失
- 数字和布尔值的类型会被保持

**格式化选项：**
- JSON 输出使用 2 个空格缩进
- YAML 输出使用标准缩进格式
- 保持数据的原始类型不变

## 💡 使用技巧

- **语法检查**：转换前会自动验证输入格式的正确性
- **注释处理**：YAML 转 JSON 时注释会丢失，转换前请备份
- **缩进规范**：YAML 使用空格缩进，避免使用 Tab 字符
- **引号使用**：包含特殊字符的 YAML 字符串建议使用引号

## ⚠️ 注意事项

- **注释丢失**：YAML 转 JSON 时会丢失所有注释内容
- **格式验证**：确保输入的格式正确，否则转换会失败
- **数据类型**：注意布尔值和字符串的区别（true vs "true"）
- **文件编码**：确保文件使用 UTF-8 编码，避免中文乱码

## 🚀 开始使用

1. **选择转换方向**：点击"JSON转YAML"或"YAML转JSON"按钮
2. **输入数据**：在输入框中粘贴要转换的数据
3. **执行转换**：点击转换按钮进行格式转换
4. **复制结果**：点击"复制"按钮将结果复制到剪贴板
5. **示例演示**：点击"载入示例"查看演示数据

> **提示**：本工具在客户端本地进行转换，不会上传您的数据到服务器，确保数据安全。
