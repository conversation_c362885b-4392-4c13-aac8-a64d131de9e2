# 字符串反转工具

字符串反转是一种基础的文本处理操作，将字符串中的字符顺序完全颠倒。本工具支持多种反转模式，包括整体反转、按行反转、按单词反转等，适用于文本处理、数据变换和编程练习等场景。

## ✨ 主要特性

- 🔄 **多种反转模式**：支持整体反转、按行反转、按单词反转
- 🌐 **Unicode 支持**：正确处理中文、表情符号等 Unicode 字符
- ⚡ **实时处理**：输入文本即时显示反转结果
- 📋 **一键复制**：反转结果可直接复制使用
- 🔧 **批量处理**：支持多行文本的批量反转

## 📖 使用示例

### 整体反转

**输入文本：**
```
Hello World!
```

**反转结果：**
```
!dlroW olleH
```

### 中文文本反转

**输入文本：**
```
欢迎使用工具迷
```

**反转结果：**
```
迷具工用使迎欢
```

### 按行反转

**输入文本：**
```
第一行文本
第二行文本
第三行文本
```

**反转结果：**
```
本文行一第
本文行二第
本文行三第
```

### 按单词反转

**输入文本：**
```
Hello World Welcome
```

**反转结果：**
```
Welcome World Hello
```

## 🎯 应用场景

### 1. 数据处理

在数据处理和转换中使用字符串反转：

```javascript
// 数据脱敏处理
function maskSensitiveData(data) {
  // 将敏感数据反转作为简单的混淆
  const reversed = data.split('').reverse().join('')
  return btoa(reversed) // 再进行 Base64 编码
}

// 使用示例
const sensitiveInfo = "用户敏感信息"
const masked = maskSensitiveData(sensitiveInfo)
console.log('脱敏后:', masked)

// 解密函数
function unmaskSensitiveData(maskedData) {
  const decoded = atob(maskedData)
  return decoded.split('').reverse().join('')
}

const original = unmaskSensitiveData(masked)
console.log('原始数据:', original)
```

### 2. 文本游戏

创建文字游戏和谜题：

```javascript
// 回文检测器
function isPalindrome(str) {
  const cleaned = str.toLowerCase().replace(/[^a-z0-9\u4e00-\u9fff]/g, '')
  const reversed = cleaned.split('').reverse().join('')
  return cleaned === reversed
}

// 测试示例
console.log(isPalindrome("上海海上")) // true
console.log(isPalindrome("A man a plan a canal Panama")) // true
console.log(isPalindrome("race a car")) // false

// 文字谜题生成器
function generateWordPuzzle(sentence) {
  const words = sentence.split(' ')
  const puzzles = words.map(word => {
    return {
      original: word,
      reversed: word.split('').reverse().join(''),
      hint: `${word.length} 个字符`
    }
  })
  return puzzles
}

// 使用示例
const puzzles = generateWordPuzzle("工具迷 在线 工具")
console.log(puzzles)
// [
//   { original: "工具迷", reversed: "迷具工", hint: "3 个字符" },
//   { original: "在线", reversed: "线在", hint: "2 个字符" },
//   { original: "工具", reversed: "具工", hint: "2 个字符" }
// ]
```

### 3. 编程练习

用于算法学习和编程练习：

```javascript
// 字符串反转的多种实现方法

// 方法1：使用内置方法
function reverseString1(str) {
  return str.split('').reverse().join('')
}

// 方法2：使用循环
function reverseString2(str) {
  let result = ''
  for (let i = str.length - 1; i >= 0; i--) {
    result += str[i]
  }
  return result
}

// 方法3：使用递归
function reverseString3(str) {
  if (str === '') return ''
  return reverseString3(str.substr(1)) + str.charAt(0)
}

// 方法4：使用双指针
function reverseString4(str) {
  const arr = str.split('')
  let left = 0
  let right = arr.length - 1
  
  while (left < right) {
    [arr[left], arr[right]] = [arr[right], arr[left]]
    left++
    right--
  }
  
  return arr.join('')
}

// 性能测试
function performanceTest(str) {
  const methods = [reverseString1, reverseString2, reverseString3, reverseString4]
  const methodNames = ['内置方法', '循环', '递归', '双指针']
  
  methods.forEach((method, index) => {
    const start = performance.now()
    const result = method(str)
    const end = performance.now()
    console.log(`${methodNames[index]}: ${end - start}ms`)
  })
}

// 测试
const testString = "这是一个用于测试性能的长字符串".repeat(1000)
performanceTest(testString)
```

### 4. 文本艺术

创建文本艺术和特效：

```javascript
// 文本动画效果
class TextAnimation {
  constructor(element, text) {
    this.element = element
    this.originalText = text
    this.currentText = text
  }

  // 逐字符反转动画
  async reverseAnimation(speed = 100) {
    const chars = this.originalText.split('')
    
    for (let i = 0; i < chars.length; i++) {
      // 反转前 i 个字符
      const reversed = chars.slice(0, i + 1).reverse()
      const remaining = chars.slice(i + 1)
      this.currentText = [...reversed, ...remaining].join('')
      
      this.element.textContent = this.currentText
      await this.delay(speed)
    }
  }

  // 波浪反转效果
  async waveReverse(speed = 50) {
    const chars = this.originalText.split('')
    const length = chars.length
    
    for (let wave = 0; wave < length; wave++) {
      const newChars = [...chars]
      
      // 创建波浪效果
      for (let i = 0; i < length; i++) {
        const distance = Math.abs(i - wave)
        if (distance <= 2) {
          // 在波浪范围内的字符进行反转
          const start = Math.max(0, wave - 2)
          const end = Math.min(length, wave + 3)
          const section = chars.slice(start, end).reverse()
          newChars.splice(start, section.length, ...section)
        }
      }
      
      this.element.textContent = newChars.join('')
      await this.delay(speed)
    }
    
    // 最终完全反转
    this.element.textContent = chars.reverse().join('')
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 使用示例
const textElement = document.getElementById('animated-text')
const animation = new TextAnimation(textElement, '工具迷在线工具平台')

// 启动动画
animation.reverseAnimation(200)
```

### 5. 数据验证

用于数据验证和完整性检查：

```javascript
// 简单的数据完整性检查
class DataIntegrityChecker {
  // 生成校验码
  static generateChecksum(data) {
    const reversed = data.split('').reverse().join('')
    const combined = data + reversed
    
    // 简单的哈希算法
    let hash = 0
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }

  // 验证数据完整性
  static verifyIntegrity(data, checksum) {
    const calculatedChecksum = this.generateChecksum(data)
    return calculatedChecksum === checksum
  }
}

// 使用示例
const originalData = "重要的业务数据"
const checksum = DataIntegrityChecker.generateChecksum(originalData)
console.log('校验码:', checksum)

// 验证数据
const isValid = DataIntegrityChecker.verifyIntegrity(originalData, checksum)
console.log('数据完整性:', isValid ? '正常' : '异常')

// 模拟数据被篡改
const tamperedData = "重要的业务数据被修改"
const isTamperedValid = DataIntegrityChecker.verifyIntegrity(tamperedData, checksum)
console.log('篡改数据验证:', isTamperedValid ? '正常' : '异常')
```

## 🔧 技术详解

### Unicode 字符处理

正确处理 Unicode 字符的反转：

**基本字符反转：**
- ASCII 字符：直接按字节反转
- 中文字符：按 Unicode 码点反转
- 表情符号：需要特殊处理复合字符

**复杂字符处理：**
```javascript
// 正确处理 Unicode 字符的反转
function reverseUnicode(str) {
  // 使用 Array.from 正确分割 Unicode 字符
  return Array.from(str).reverse().join('')
}

// 测试不同类型的字符
console.log(reverseUnicode("Hello 世界 🌍"))
// 输出: 🌍 界世 olleH
```

### 性能优化

不同反转方法的性能对比：

**方法性能排序：**
1. 内置方法：`split('').reverse().join('')`
2. 双指针算法：适合大字符串
3. 循环构建：内存效率高
4. 递归方法：简洁但性能较差

## 💡 使用技巧

- **Unicode 处理**：使用 `Array.from()` 正确处理复合字符
- **性能考虑**：大文本建议使用双指针算法
- **内存优化**：避免创建过多临时字符串
- **特殊字符**：注意处理换行符和特殊符号

## ⚠️ 注意事项

- **字符编码**：确保正确处理 UTF-8 编码的字符
- **复合字符**：表情符号等复合字符需要特殊处理
- **性能影响**：超长文本反转可能影响性能
- **内存使用**：大文本处理时注意内存占用

## 🚀 开始使用

1. **输入文本**：在输入框中输入要反转的文本
2. **选择模式**：选择整体反转、按行反转或按单词反转
3. **查看结果**：反转结果会实时显示在输出区域
4. **复制使用**：点击"复制"按钮将结果复制到剪贴板
5. **批量处理**：支持多行文本的批量反转操作

> **提示**：本工具在客户端本地进行处理，不会上传您的文本内容到服务器，确保隐私安全。
