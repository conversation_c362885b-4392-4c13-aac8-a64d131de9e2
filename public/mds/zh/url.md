# URL 编码解码工具

URL 编码（百分号编码）是一种在 URL 中安全传输数据的编码机制。当 URL 包含特殊字符、中文字符或空格时，需要进行编码以确保正确传输。本工具提供快速的 URL 编码和解码功能。

## ✨ 主要特性

- 🔄 **双向转换**：支持 URL 编码和解码操作
- 🌐 **中文支持**：完美处理中文字符编码
- ⚡ **快速处理**：即时编码解码，无需等待
- 📋 **一键复制**：编码结果可直接复制使用
- 🔧 **错误处理**：智能检测格式错误并提供修复建议

## 📖 使用示例

### URL 编码示例

**原始 URL：**
```
https://example.com/search?q=你好世界&type=1
```

**编码后：**
```
https%3A//example.com/search%3Fq%3D%E4%BD%A0%E5%A5%BD%E4%B8%96%E7%95%8C%26type%3D1
```

### URL 解码示例

**编码的 URL：**
```
https%3A//www.gongjumi.com/zh/tools%3Fcategory%3D%E7%BC%96%E7%A0%81
```

**解码后：**
```
https://www.gongjumi.com/zh/tools?category=编码
```

## 🎯 应用场景

### 1. Web 开发

在 Web 开发中处理 URL 参数：

```javascript
// 构建带参数的 URL
const baseUrl = 'https://api.example.com/search'
const query = '工具迷'
const category = '在线工具'

// 编码参数
const encodedQuery = encodeURIComponent(query)
const encodedCategory = encodeURIComponent(category)

const fullUrl = `${baseUrl}?q=${encodedQuery}&category=${encodedCategory}`
console.log(fullUrl)
// 输出: https://api.example.com/search?q=%E5%B7%A5%E5%85%B7%E8%BF%B7&category=%E5%9C%A8%E7%BA%BF%E5%B7%A5%E5%85%B7
```

### 2. 表单数据提交

处理表单中的特殊字符：

```html
<!-- HTML 表单 -->
<form action="/submit" method="GET">
  <input name="username" value="张三@公司">
  <input name="message" value="Hello World!">
  <button type="submit">提交</button>
</form>

<!-- 提交后的 URL -->
<!-- /submit?username=%E5%BC%A0%E4%B8%89%40%E5%85%AC%E5%8F%B8&message=Hello%20World%21 -->
```

### 3. API 接口调用

在 API 调用中编码参数：

```javascript
// 搜索 API 调用
async function searchTools(keyword) {
  const encodedKeyword = encodeURIComponent(keyword)
  const response = await fetch(`/api/search?q=${encodedKeyword}`)
  return response.json()
}

// 使用示例
searchTools('JSON 转 YAML')
// 实际请求: /api/search?q=JSON%20%E8%BD%AC%20YAML
```

### 4. 社交媒体分享

生成社交媒体分享链接：

```javascript
// 生成微博分享链接
function generateWeiboShareUrl(text, url) {
  const encodedText = encodeURIComponent(text)
  const encodedUrl = encodeURIComponent(url)
  
  return `https://service.weibo.com/share/share.php?title=${encodedText}&url=${encodedUrl}`
}

// 使用示例
const shareUrl = generateWeiboShareUrl(
  '发现一个好用的在线工具网站！',
  'https://www.gongjumi.com'
)
```

## 🔧 技术详解

### 编码规则

URL 编码遵循 RFC 3986 标准：

**需要编码的字符：**
- 保留字符：`:/?#[]@!$&'()*+,;=`
- 非 ASCII 字符：中文、日文、韩文等
- 不安全字符：空格、引号、尖括号等
- 控制字符：换行符、制表符等

**编码格式：**
- 使用百分号 `%` 后跟两位十六进制数
- 例如：空格编码为 `%20`，中文"你"编码为 `%E4%BD%A0`

**不需要编码的字符：**
- 字母：`A-Z`、`a-z`
- 数字：`0-9`
- 安全字符：`-`、`_`、`.`、`~`

### 编码类型

不同的编码函数有不同的用途：

**encodeURI()：**
- 编码整个 URI
- 不编码 URI 的保留字符（如 `:/?#`）
- 适用于编码完整的 URL

**encodeURIComponent()：**
- 编码 URI 的组件部分
- 编码所有特殊字符，包括保留字符
- 适用于编码 URL 参数值

**示例对比：**
```javascript
const url = 'https://example.com/search?q=hello world'

console.log(encodeURI(url))
// https://example.com/search?q=hello%20world

console.log(encodeURIComponent(url))
// https%3A%2F%2Fexample.com%2Fsearch%3Fq%3Dhello%20world
```

### 中文字符处理

中文字符的 URL 编码过程：

1. **UTF-8 编码**：将中文字符转换为 UTF-8 字节序列
2. **百分号编码**：将每个字节转换为 `%XX` 格式

**示例：**
```
字符: 你
UTF-8: E4 BD A0
URL编码: %E4%BD%A0
```

## 💡 使用技巧

- **选择合适的编码函数**：整个 URL 用 `encodeURI()`，参数值用 `encodeURIComponent()`
- **避免重复编码**：检查字符串是否已经编码，避免多次编码
- **处理特殊情况**：注意加号 `+` 在某些情况下表示空格
- **测试验证**：编码后测试 URL 是否能正确访问

## ⚠️ 注意事项

- **编码范围**：只对需要编码的部分进行编码，避免对整个 URL 使用 `encodeURIComponent()`
- **解码安全**：解码时注意防止 XSS 攻击，对解码结果进行验证
- **字符集一致**：确保编码和解码使用相同的字符集（通常是 UTF-8）
- **浏览器兼容**：不同浏览器对某些字符的编码可能略有差异

## 🚀 开始使用

1. **选择操作**：点击"URL编码"或"URL解码"按钮
2. **输入内容**：在输入框中粘贴要处理的 URL 或文本
3. **执行转换**：点击相应按钮进行编码或解码
4. **复制结果**：点击"复制"按钮将结果复制到剪贴板
5. **示例演示**：点击"载入示例"查看演示数据

> **提示**：本工具在客户端本地进行编码解码，不会上传您的数据到服务器，确保隐私安全。
