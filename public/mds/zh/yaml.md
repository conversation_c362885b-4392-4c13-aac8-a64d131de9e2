# YAML 格式化工具

YAML（YAML Ain't Markup Language）是一种人类可读的数据序列化标准，常用于配置文件、数据交换和文档编写。本工具提供 YAML 格式验证、美化和压缩功能，帮助开发者处理和管理 YAML 文件。

## ✨ 主要特性

- 🎯 **格式验证**：检查 YAML 语法错误并提供详细提示
- 🎨 **格式美化**：自动缩进和格式化 YAML 内容
- 🗜️ **内容压缩**：移除多余空格和注释，压缩文件大小
- 📋 **一键复制**：格式化结果可直接复制使用
- 🔧 **错误定位**：精确定位语法错误位置

## 📖 使用示例

### 格式化示例

**原始 YAML：**
```yaml
name:工具迷
version:1.0.0
features:
- 格式化
- 验证
- 压缩
config:
  debug:true
  port:3000
```

**格式化后：**
```yaml
name: 工具迷
version: 1.0.0
features:
  - 格式化
  - 验证
  - 压缩
config:
  debug: true
  port: 3000
```

### 复杂结构示例

**输入 YAML：**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
  pools:
    - name: read_pool
      size: 10
    - name: write_pool
      size: 5
```

**验证和美化后：**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
  pools:
    - name: read_pool
      size: 10
    - name: write_pool
      size: 5
```

## 🎯 应用场景

### 1. 配置文件管理

整理和验证应用配置文件：

```yaml
# 应用配置文件 (config.yml)
app:
  name: 工具迷
  version: 1.0.0
  environment: production
  
server:
  host: 0.0.0.0
  port: 3000
  ssl:
    enabled: true
    cert_path: /etc/ssl/certs/app.crt
    key_path: /etc/ssl/private/app.key
    
database:
  type: postgresql
  host: localhost
  port: 5432
  name: toolmi_db
  username: ${DB_USER}
  password: ${DB_PASSWORD}
  pool:
    min_connections: 2
    max_connections: 10
    
redis:
  host: localhost
  port: 6379
  password: ${REDIS_PASSWORD}
  db: 0
  
logging:
  level: info
  format: json
  outputs:
    - type: file
      path: /var/log/app.log
    - type: console
```

### 2. Docker Compose 文件

管理 Docker 容器编排配置：

```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=database
    depends_on:
      - database
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - app-network
      
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: toolmi_db
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
      
  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 3. CI/CD 配置

GitHub Actions 工作流配置：

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Run linting
        run: npm run lint
        
  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Build Docker image
        run: |
          docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest .
          
      - name: Push to registry
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
```

### 4. Kubernetes 配置

Kubernetes 部署配置文件：

```yaml
# deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  labels:
    app: toolmi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: toolmi
  template:
    metadata:
      labels:
        app: toolmi
    spec:
      containers:
        - name: app
          image: toolmi/app:latest
          ports:
            - containerPort: 3000
          env:
            - name: NODE_ENV
              value: "production"
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: db-host
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
spec:
  selector:
    app: toolmi
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer
```

## 🔧 技术详解

### YAML 语法规则

YAML 的基本语法规则：

**缩进规则：**
- 使用空格进行缩进，不能使用制表符
- 相同层级的元素必须左对齐
- 子元素必须比父元素多缩进

**数据类型：**
- 字符串：可以不用引号，特殊字符需要引号
- 数字：整数和浮点数
- 布尔值：true/false、yes/no、on/off
- 空值：null、~、或空

**集合类型：**
- 数组：使用 `-` 表示列表项
- 对象：使用 `key: value` 表示键值对
- 嵌套：支持任意层级的嵌套结构

### 常见错误

YAML 格式化工具可以检测的常见错误：

**缩进错误：**
```yaml
# 错误示例
config:
  debug: true
 port: 3000  # 缩进不一致

# 正确示例
config:
  debug: true
  port: 3000
```

**引号问题：**
```yaml
# 错误示例
message: It's a test  # 单引号未转义

# 正确示例
message: "It's a test"
# 或
message: 'It''s a test'
```

**列表格式：**
```yaml
# 错误示例
features:
- format
 - validate  # 缩进错误

# 正确示例
features:
  - format
  - validate
```

## 💡 使用技巧

- **缩进一致**：始终使用相同数量的空格进行缩进（推荐2个空格）
- **引号使用**：包含特殊字符的字符串使用引号包围
- **注释规范**：使用 `#` 添加注释，注释前加空格
- **多行字符串**：使用 `|` 或 `>` 处理多行文本

## ⚠️ 注意事项

- **制表符禁用**：YAML 不允许使用制表符，只能使用空格
- **缩进敏感**：缩进错误会导致解析失败
- **特殊字符**：冒号、引号等特殊字符需要正确处理
- **编码格式**：确保文件使用 UTF-8 编码

## 🚀 开始使用

1. **输入 YAML**：在输入框中粘贴要处理的 YAML 内容
2. **选择操作**：点击"格式化"、"验证"或"压缩"按钮
3. **查看结果**：在输出框中查看处理后的 YAML
4. **复制使用**：点击"复制"按钮将结果复制到剪贴板
5. **错误修复**：根据错误提示修复语法问题

> **提示**：本工具在客户端本地进行处理，不会上传您的 YAML 内容到服务器，确保数据安全。
