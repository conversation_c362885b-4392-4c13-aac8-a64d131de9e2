# 颜色选择器工具

这个工具提供了一个全面的颜色选择和管理系统。支持多种颜色格式转换、调色板生成、颜色对比度分析和主题系统，是设计师和开发者的必备工具。

## ✨ 主要特性

- 🎨 **多格式支持** : HEX、RGB、HSL、HSV等格式互转
- 🌈 **调色板生成** : 自动生成和谐配色方案
- 📊 **对比度分析** : WCAG无障碍标准检测
- 🎯 **精确取色** : 支持精确的颜色值输入和调整
- 💾 **颜色历史** : 自动保存最近使用的颜色

## 📖 使用示例

### 基础颜色转换

**HEX转RGB:**
```
输入: #3498db
输出: rgb(52, 152, 219)
```

**RGB转HSL:**
```
输入: rgb(52, 152, 219)
输出: hsl(204, 70%, 53%)
```

**HSL转HSV:**
```
输入: hsl(204, 70%, 53%)
输出: hsv(204, 76%, 86%)
```

### 调色板生成

**互补色方案:**
```
基础色: #3498db (蓝色)
互补色: #db7834 (橙色)
```

**三角色方案:**
```
基础色: #3498db
三角色1: #34db98
三角色2: #db3456
```

**类似色方案:**
```
基础色: #3498db
类似色1: #3456db
类似色2: #34dbdb
```

## 🎯 应用场景

### 1. 网站设计系统

```css
/* 基于颜色工具生成的设计系统 */
:root {
  /* 主色调 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* 语义化颜色 */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

/* 组件样式 */
.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: var(--primary-700);
}

.btn-primary:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 状态颜色 */
.alert-success {
  background-color: #d1fae5;
  border-color: var(--success);
  color: #065f46;
}

.alert-warning {
  background-color: #fef3c7;
  border-color: var(--warning);
  color: #92400e;
}

.alert-error {
  background-color: #fee2e2;
  border-color: var(--error);
  color: #991b1b;
}
```

### 2. 品牌色彩系统

```javascript
// 品牌色彩管理系统
class BrandColorSystem {
  constructor() {
    this.brandColors = {
      primary: '#2563eb',
      secondary: '#7c3aed',
      accent: '#06b6d4',
      neutral: '#6b7280'
    };
    
    this.colorPalettes = new Map();
    this.generatePalettes();
  }

  // 生成完整调色板
  generatePalettes() {
    Object.entries(this.brandColors).forEach(([name, color]) => {
      this.colorPalettes.set(name, this.generateShades(color));
    });
  }

  // 生成色阶
  generateShades(baseColor) {
    const shades = {};
    const lightness = [95, 90, 80, 70, 60, 50, 40, 30, 20, 10];
    
    lightness.forEach((l, index) => {
      const shade = (index + 1) * 100;
      shades[shade] = this.adjustLightness(baseColor, l);
    });
    
    return shades;
  }

  // 调整亮度
  adjustLightness(hexColor, lightness) {
    // 转换为HSL
    const hsl = this.hexToHsl(hexColor);
    
    // 调整亮度
    hsl.l = lightness;
    
    // 转换回HEX
    return this.hslToHex(hsl);
  }

  // HEX转HSL
  hexToHsl(hex) {
    const r = parseInt(hex.slice(1, 3), 16) / 255;
    const g = parseInt(hex.slice(3, 5), 16) / 255;
    const b = parseInt(hex.slice(5, 7), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0;
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  }

  // HSL转HEX
  hslToHex({ h, s, l }) {
    h /= 360;
    s /= 100;
    l /= 100;

    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l;
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    const toHex = (c) => {
      const hex = Math.round(c * 255).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }

  // 获取颜色调色板
  getPalette(colorName) {
    return this.colorPalettes.get(colorName);
  }

  // 生成CSS变量
  generateCSSVariables() {
    let css = ':root {\n';
    
    this.colorPalettes.forEach((palette, name) => {
      Object.entries(palette).forEach(([shade, color]) => {
        css += `  --${name}-${shade}: ${color};\n`;
      });
    });
    
    css += '}';
    return css;
  }

  // 检查颜色对比度
  checkContrast(color1, color2) {
    const luminance1 = this.getLuminance(color1);
    const luminance2 = this.getLuminance(color2);
    
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);
    
    const contrast = (lighter + 0.05) / (darker + 0.05);
    
    return {
      ratio: contrast,
      aa: contrast >= 4.5,
      aaa: contrast >= 7,
      aaLarge: contrast >= 3
    };
  }

  // 获取相对亮度
  getLuminance(hex) {
    const rgb = this.hexToRgb(hex);
    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  // HEX转RGB
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }
}

// 使用示例
const brandSystem = new BrandColorSystem();

// 生成CSS变量
console.log(brandSystem.generateCSSVariables());

// 检查对比度
const contrast = brandSystem.checkContrast('#2563eb', '#ffffff');
console.log('对比度检查:', contrast);

// 获取主色调色板
const primaryPalette = brandSystem.getPalette('primary');
console.log('主色调色板:', primaryPalette);
```

### 3. 动态主题系统

```javascript
// 动态主题切换系统
class ThemeManager {
  constructor() {
    this.themes = new Map();
    this.currentTheme = null;
    this.initializeDefaultThemes();
  }

  // 初始化默认主题
  initializeDefaultThemes() {
    // 浅色主题
    this.addTheme('light', {
      name: '浅色主题',
      colors: {
        background: '#ffffff',
        surface: '#f8fafc',
        primary: '#3b82f6',
        secondary: '#64748b',
        accent: '#06b6d4',
        text: '#1e293b',
        textSecondary: '#64748b',
        border: '#e2e8f0',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
      }
    });

    // 深色主题
    this.addTheme('dark', {
      name: '深色主题',
      colors: {
        background: '#0f172a',
        surface: '#1e293b',
        primary: '#60a5fa',
        secondary: '#94a3b8',
        accent: '#22d3ee',
        text: '#f1f5f9',
        textSecondary: '#94a3b8',
        border: '#334155',
        success: '#34d399',
        warning: '#fbbf24',
        error: '#f87171'
      }
    });

    // 高对比度主题
    this.addTheme('high-contrast', {
      name: '高对比度主题',
      colors: {
        background: '#000000',
        surface: '#1a1a1a',
        primary: '#ffffff',
        secondary: '#cccccc',
        accent: '#ffff00',
        text: '#ffffff',
        textSecondary: '#cccccc',
        border: '#ffffff',
        success: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000'
      }
    });
  }

  // 添加主题
  addTheme(id, theme) {
    this.themes.set(id, theme);
  }

  // 应用主题
  applyTheme(themeId) {
    const theme = this.themes.get(themeId);
    if (!theme) {
      throw new Error(`主题 '${themeId}' 不存在`);
    }

    const root = document.documentElement;
    
    // 应用颜色变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // 保存当前主题
    this.currentTheme = themeId;
    localStorage.setItem('preferred-theme', themeId);

    // 触发主题变更事件
    window.dispatchEvent(new CustomEvent('theme-changed', {
      detail: { themeId, theme }
    }));

    return theme;
  }

  // 获取当前主题
  getCurrentTheme() {
    return this.currentTheme ? this.themes.get(this.currentTheme) : null;
  }

  // 自动检测系统主题
  detectSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }

  // 初始化主题
  initialize() {
    // 尝试从本地存储获取主题
    const savedTheme = localStorage.getItem('preferred-theme');
    
    if (savedTheme && this.themes.has(savedTheme)) {
      this.applyTheme(savedTheme);
    } else {
      // 使用系统主题
      const systemTheme = this.detectSystemTheme();
      this.applyTheme(systemTheme);
    }

    // 监听系统主题变化
    if (window.matchMedia) {
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('preferred-theme')) {
          this.applyTheme(e.matches ? 'dark' : 'light');
        }
      });
    }
  }

  // 生成主题预览
  generateThemePreview(themeId) {
    const theme = this.themes.get(themeId);
    if (!theme) return null;

    return {
      id: themeId,
      name: theme.name,
      preview: {
        background: theme.colors.background,
        surface: theme.colors.surface,
        primary: theme.colors.primary,
        text: theme.colors.text
      }
    };
  }

  // 获取所有主题预览
  getAllThemePreviews() {
    return Array.from(this.themes.keys()).map(id => this.generateThemePreview(id));
  }

  // 创建自定义主题
  createCustomTheme(baseThemeId, customColors) {
    const baseTheme = this.themes.get(baseThemeId);
    if (!baseTheme) {
      throw new Error(`基础主题 '${baseThemeId}' 不存在`);
    }

    const customTheme = {
      name: '自定义主题',
      colors: {
        ...baseTheme.colors,
        ...customColors
      }
    };

    const customId = `custom-${Date.now()}`;
    this.addTheme(customId, customTheme);
    
    return customId;
  }
}

// 使用示例
const themeManager = new ThemeManager();

// 初始化主题系统
themeManager.initialize();

// 监听主题变化
window.addEventListener('theme-changed', (event) => {
  console.log('主题已切换:', event.detail.theme.name);
});

// 获取主题预览
const themePreviews = themeManager.getAllThemePreviews();
console.log('可用主题:', themePreviews);

// 创建自定义主题
const customThemeId = themeManager.createCustomTheme('light', {
  primary: '#8b5cf6',
  accent: '#a855f7'
});

// 应用自定义主题
themeManager.applyTheme(customThemeId);
```

## 🔧 技术细节

### 支持的颜色格式

**HEX (十六进制):**
- 格式: `#RRGGBB` 或 `#RGB`
- 示例: `#3498db`, `#fff`

**RGB (红绿蓝):**
- 格式: `rgb(r, g, b)`
- 值范围: 每个通道0-255
- 示例: `rgb(52, 152, 219)`

**HSL (色相、饱和度、亮度):**
- 格式: `hsl(h, s%, l%)`
- H: 0-360°, S: 0-100%, L: 0-100%
- 示例: `hsl(204, 70%, 53%)`

**HSV/HSB (色相、饱和度、明度):**
- 类似HSL，但使用明度而非亮度
- 示例: `hsv(204, 76%, 86%)`

### 色彩理论

**色彩和谐:**
- **互补色**: 色轮上相对的颜色
- **类似色**: 色轮上相邻的颜色
- **三角色**: 色轮上等距的三个颜色
- **单色**: 同一颜色的不同变化

**对比度和无障碍:**
- **WCAG AA**: 最小对比度比例4.5:1
- **WCAG AAA**: 最小对比度比例7:1
- **大文本**: 最小对比度比例3:1

## 💡 使用技巧

- **无障碍性** : 始终检查文本和背景的对比度
- **一致性** : 在整个项目中使用一致的颜色系统
- **文化背景** : 考虑颜色的文化含义
- **设备测试** : 在不同设备和光照条件下测试颜色

## ⚠️ 重要提醒

- **色盲友好** : 考虑色觉障碍用户
- **打印效果** : 颜色在打印时可能不同
- **显示器差异** : 显示器校准影响颜色感知
- **文化差异** : 颜色含义在不同文化中有差异

## 🚀 如何使用

1. **选择颜色** : 使用可视化选择器或输入颜色代码
2. **格式转换** : 自动查看不同格式的颜色
3. **生成调色板** : 选择和谐类型生成配色方案
4. **对比度分析** : 检查颜色组合的无障碍性
5. **复制使用** : 点击任意代码即可复制

> **提示** : 此工具在客户端本地处理，不会向服务器发送数据，确保隐私安全和快速响应。
