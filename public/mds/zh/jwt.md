# JWT 解析工具

JWT（JSON Web Token）是一种开放标准（RFC 7519），定义了一种紧凑、自包含的方式，用于在各方之间安全地传输信息。本工具可以解析 JWT Token，查看其 Header、Payload 和 Signature 内容。

## ✨ 主要特性

- 🔍 **完整解析**：解析 JWT 的 Header、Payload 和 Signature
- 📊 **结构化显示**：以 JSON 格式清晰展示解析结果
- 🔧 **错误检测**：自动检测无效的 JWT 格式
- 📋 **一键复制**：解析结果可直接复制使用
- 🛡️ **安全提示**：提供 JWT 安全使用建议

## 📖 使用示例

### 标准 JWT 解析

**输入 JWT：**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

**解析结果：**

**Header:**
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

**Payload:**
```json
{
  "sub": "1234567890",
  "name": "John Doe",
  "iat": 1516239022
}
```

**Signature:**
```
SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

## 🎯 应用场景

### 1. API 身份验证

在 RESTful API 中使用 JWT 进行用户身份验证：

```javascript
// 服务端生成 JWT
const jwt = require('jsonwebtoken')

// 用户登录成功后生成 Token
function generateToken(user) {
  const payload = {
    sub: user.id,
    username: user.username,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时过期
  }

  return jwt.sign(payload, process.env.JWT_SECRET, {
    algorithm: 'HS256'
  })
}

// 验证 Token
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET)
    return { valid: true, payload: decoded }
  } catch (error) {
    return { valid: false, error: error.message }
  }
}

// 中间件验证
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.sendStatus(401)
  }

  const result = verifyToken(token)
  if (!result.valid) {
    return res.sendStatus(403)
  }

  req.user = result.payload
  next()
}
```

### 2. 微服务架构

在微服务之间传递用户信息和权限：

```javascript
// 微服务 A 生成包含用户信息的 JWT
const serviceAToken = jwt.sign({
  userId: 123,
  username: 'john_doe',
  permissions: ['read:posts', 'write:posts'],
  serviceId: 'service-a',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 300 // 5分钟
}, process.env.SERVICE_SECRET)

// 微服务 B 验证并使用 JWT 信息
function handleRequest(req, res) {
  const token = req.headers['x-service-token']

  try {
    const decoded = jwt.verify(token, process.env.SERVICE_SECRET)

    // 检查权限
    if (!decoded.permissions.includes('read:posts')) {
      return res.status(403).json({ error: 'Insufficient permissions' })
    }

    // 使用用户信息
    const posts = getPostsByUser(decoded.userId)
    res.json(posts)
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' })
  }
}
```

### 3. 前端应用状态管理

在前端应用中管理用户登录状态：

```javascript
// Vue.js 中的 JWT 处理
import { ref, computed } from 'vue'

export const useAuth = () => {
  const token = ref(localStorage.getItem('jwt_token'))

  // 解析 JWT 获取用户信息
  const user = computed(() => {
    if (!token.value) return null

    try {
      const parts = token.value.split('.')
      const payload = JSON.parse(atob(parts[1]))

      // 检查是否过期
      if (payload.exp * 1000 < Date.now()) {
        logout()
        return null
      }

      return {
        id: payload.sub,
        username: payload.username,
        role: payload.role,
        exp: payload.exp
      }
    } catch (error) {
      console.error('Invalid JWT:', error)
      logout()
      return null
    }
  })

  const isAuthenticated = computed(() => !!user.value)

  const login = (newToken) => {
    token.value = newToken
    localStorage.setItem('jwt_token', newToken)
  }

  const logout = () => {
    token.value = null
    localStorage.removeItem('jwt_token')
  }

  // 自动刷新 Token
  const refreshToken = async () => {
    try {
      const response = await fetch('/api/refresh', {
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })

      if (response.ok) {
        const { token: newToken } = await response.json()
        login(newToken)
      } else {
        logout()
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
      logout()
    }
  }

  return {
    user,
    isAuthenticated,
    login,
    logout,
    refreshToken
  }
}
```

### 4. 移动应用认证

在移动应用中使用 JWT 进行用户认证：

```javascript
// React Native 中的 JWT 处理
import AsyncStorage from '@react-native-async-storage/async-storage'

class AuthService {
  static TOKEN_KEY = 'jwt_token'

  // 保存 Token
  static async saveToken(token) {
    try {
      await AsyncStorage.setItem(this.TOKEN_KEY, token)
    } catch (error) {
      console.error('Failed to save token:', error)
    }
  }

  // 获取 Token
  static async getToken() {
    try {
      return await AsyncStorage.getItem(this.TOKEN_KEY)
    } catch (error) {
      console.error('Failed to get token:', error)
      return null
    }
  }

  // 解析 Token
  static parseToken(token) {
    if (!token) return null

    try {
      const parts = token.split('.')
      const payload = JSON.parse(atob(parts[1]))

      // 检查过期时间
      if (payload.exp * 1000 < Date.now()) {
        this.removeToken()
        return null
      }

      return payload
    } catch (error) {
      console.error('Invalid token format:', error)
      return null
    }
  }

  // 移除 Token
  static async removeToken() {
    try {
      await AsyncStorage.removeItem(this.TOKEN_KEY)
    } catch (error) {
      console.error('Failed to remove token:', error)
    }
  }

  // 检查是否已登录
  static async isLoggedIn() {
    const token = await this.getToken()
    const payload = this.parseToken(token)
    return !!payload
  }
}
```

## 🔧 技术详解

### JWT 结构

JWT 由三部分组成，用点号（.）分隔：

**Header（头部）：**
- 包含 Token 类型（typ）和签名算法（alg）
- Base64URL 编码的 JSON 对象
- 示例：{"alg": "HS256", "typ": "JWT"}

**Payload（载荷）：**
- 包含声明（Claims）信息
- Base64URL 编码的 JSON 对象
- 包含标准声明和自定义声明

**Signature（签名）：**
- 用于验证 Token 的完整性
- 使用 Header 中指定的算法生成
- 防止 Token 被篡改

**完整格式：**
`header.payload.signature`

### 标准声明

JWT 定义了一些标准声明（Claims）：

**注册声明（Registered Claims）：**
- `iss`：发行者（Issuer）
- `sub`：主题（Subject），通常是用户 ID
- `aud`：受众（Audience）
- `exp`：过期时间（Expiration Time）
- `nbf`：生效时间（Not Before）
- `iat`：签发时间（Issued At）
- `jti`：JWT ID，唯一标识符

**公共声明（Public Claims）：**
- 可以在 IANA JWT Registry 中定义
- 或使用 URI 命名空间避免冲突

**私有声明（Private Claims）：**
- 自定义声明，用于特定应用
- 如：username、role、permissions 等

### 签名算法

JWT 支持多种签名算法：

**对称算法（HMAC）：**
- HS256：HMAC SHA-256
- HS384：HMAC SHA-384
- HS512：HMAC SHA-512

**非对称算法（RSA）：**
- RS256：RSA SHA-256
- RS384：RSA SHA-384
- RS512：RSA SHA-512

**椭圆曲线算法（ECDSA）：**
- ES256：ECDSA SHA-256
- ES384：ECDSA SHA-384
- ES512：ECDSA SHA-512

**选择建议：**
- 单体应用：使用 HS256
- 微服务：使用 RS256
- 高性能需求：使用 ES256

## 💡 使用技巧

- **过期时间**：合理设置 Token 过期时间，平衡安全性和用户体验
- **刷新机制**：实现 Token 刷新机制，避免频繁登录
- **存储安全**：在客户端安全存储 Token，避免 XSS 攻击
- **传输安全**：始终通过 HTTPS 传输 JWT Token

## ⚠️ 安全注意事项

- **敏感信息**：不要在 Payload 中存储敏感信息，JWT 内容是可见的
- **密钥安全**：妥善保管签名密钥，定期轮换
- **Token 撤销**：JWT 无法撤销，考虑使用黑名单或短期 Token
- **算法验证**：验证时必须检查算法，防止算法替换攻击
- **大小限制**：JWT 有大小限制，避免存储过多信息

## 🚀 开始使用

1. **输入 JWT**：在输入框中粘贴要解析的 JWT Token
2. **解析**：点击"解析JWT"按钮进行解析
3. **查看结果**：查看解析出的 Header、Payload 和 Signature
4. **复制**：点击"复制"按钮复制解析结果
5. **示例**：点击"载入示例"查看演示数据

> **提示**：本工具仅在客户端解析 JWT，不会上传您的 Token 到服务器，确保数据安全。
