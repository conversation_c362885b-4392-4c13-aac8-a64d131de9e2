# Herramienta de Cifrado Hash SHA1

SHA1 (Secure Hash Algorithm 1) es una función hash criptográfica que genera un valor hash de 160 bits (40 caracteres hexadecimales) a partir de datos de entrada de cualquier longitud. Se ha utilizado en verificación de integridad de datos, firmas digitales y sistemas de control de versiones, pero actualmente se considera obsoleto por razones de seguridad.

## ✨ Características Principales

- 🔐 **Generación Hash SHA1**: Convierte texto a valores hash SHA1 de 160 bits
- 🌐 **Soporte Multiidioma**: Compatible con caracteres Unicode como español, chino, inglés
- 📊 **Unicidad**: Genera siempre el mismo valor hash para la misma entrada
- 📋 **Copia con Un Clic**: Los valores hash generados se pueden copiar directamente
- ⚡ **Procesamiento Rápido**: Muestra valores hash al mismo tiempo que la entrada

## 📖 Ejemplos de Uso

### Hash de Texto

**Texto de Entrada:**
```
¡Hola, mundo!
```

**Valor Hash SHA1:**
```
a94a8fe5ccb19ba61c4c0873d391e987982fbbd3
```

### Verificación de Integridad de Archivos

**Contenido de Archivo de Entrada:**
```
Documento importante
Versión: 1.0
Fecha de creación: 2024-01-15
```

**Valor Hash SHA1:**
```
2fd4e1c67a2d28fced849ee1bb76e7391b93eb12
```

## 🎯 Escenarios de Aplicación

### 1. Sistemas de Control de Versiones

Identificación de commits en Git:

```bash
# Hash de commits en Git (basado en SHA1)
git log --oneline
# a94a8fe Agregar nueva funcionalidad
# 2fd4e1c Corrección de errores
# 7b3c9d1 Commit inicial

# Verificar hash SHA1 de archivo
git hash-object README.md
# 2fd4e1c67a2d28fced849ee1bb76e7391b93eb12
```

### 2. Verificación de Integridad de Datos

Sistema de verificación de integridad de archivos:

```javascript
// Clase de verificación de integridad de archivos
class VerificadorIntegridadArchivos {
  constructor() {
    this.checksums = new Map();
  }

  // Calcular checksum SHA1 de archivo
  async calcularSHA1(archivo) {
    return new Promise((resolver, rechazar) => {
      const lector = new FileReader();
      
      lector.onload = async (evento) => {
        try {
          const arrayBuffer = evento.target.result;
          const hashBuffer = await crypto.subtle.digest('SHA-1', arrayBuffer);
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
          resolver(hashHex);
        } catch (error) {
          rechazar(error);
        }
      };
      
      lector.onerror = rechazar;
      lector.readAsArrayBuffer(archivo);
    });
  }

  // Registrar checksum
  async registrarChecksum(idArchivo, archivo) {
    const checksum = await this.calcularSHA1(archivo);
    this.checksums.set(idArchivo, {
      checksum: checksum,
      nombreArchivo: archivo.name,
      tamañoArchivo: archivo.size,
      registradoEn: new Date().toISOString()
    });
    return checksum;
  }

  // Verificar integridad
  async verificarIntegridad(idArchivo, archivoActual) {
    const registro = this.checksums.get(idArchivo);
    if (!registro) {
      return { valido: false, razon: 'Checksum no registrado' };
    }

    const checksumActual = await this.calcularSHA1(archivoActual);
    const esValido = registro.checksum === checksumActual;

    return {
      valido: esValido,
      checksumOriginal: registro.checksum,
      checksumActual: checksumActual,
      nombreArchivo: registro.nombreArchivo,
      razon: esValido ? 'Integridad OK' : 'El archivo ha sido modificado'
    };
  }
}

// Ejemplo de uso
const verificadorIntegridad = new VerificadorIntegridadArchivos();

// Procesamiento al seleccionar archivo
document.getElementById('entradaArchivo').addEventListener('change', async (evento) => {
  const archivo = evento.target.files[0];
  if (archivo) {
    try {
      const checksum = await verificadorIntegridad.registrarChecksum('archivo001', archivo);
      console.log(`Checksum del archivo ${archivo.name}:`, checksum);
    } catch (error) {
      console.error('Error al calcular checksum:', error);
    }
  }
});
```

## 🔧 Detalles Técnicos

### Algoritmo SHA1

Características básicas de SHA1:

**Longitud de Hash:** 160 bits (40 caracteres hexadecimales)
**Tamaño de Bloque:** 512 bits
**Rondas de Procesamiento:** 80 rondas
**Seguridad:** Actualmente no recomendado

### Problemas de Seguridad

Limitaciones de SHA1 y alternativas:

```javascript
// Problemas de SHA1
const problemassha1 = {
  colision: 'Google demostró ataques de colisión en 2017',
  obsolescencia: 'Obsoleto en muchos navegadores y sistemas',
  debilidad: 'Fuerza criptográfica insuficiente',
  recomendacion: 'Se recomienda usar SHA-256 o superior'
};

// Alternativas recomendadas
const alternativas = {
  'SHA-256': 'Estándar actual, hash de 256 bits',
  'SHA-3': 'Hash más reciente basado en Keccak',
  'BLAKE2': 'Función hash rápida y segura',
  'BLAKE3': 'Función hash de alto rendimiento más reciente'
};
```

## 💡 Consejos de Uso

- **Limitación de Uso**: Usar solo cuando se requiere compatibilidad con sistemas legacy
- **Considerar Alternativas**: Usar SHA-256 o superior para nuevos desarrollos
- **Verificación de Integridad**: Limitar a verificación de integridad de datos no críticos
- **Plan de Migración**: Planificar migración de SHA1 a funciones hash más seguras

## ⚠️ Notas Importantes

- **Riesgo de Seguridad**: SHA1 no es criptográficamente seguro
- **Ataques de Colisión**: Existe la posibilidad de que diferentes entradas generen el mismo valor hash
- **Obsoleto**: No se recomienda su uso en nuevos desarrollos
- **Migración Necesaria**: Los sistemas existentes deben considerar migrar a SHA-256 o superior urgentemente

## 🚀 Cómo Usar

1. **Entrada de Texto**: Ingrese el texto a hacer hash
2. **Generación Automática**: El valor hash SHA1 se genera al ingresar
3. **Verificación de Resultados**: Verifique el valor hash hexadecimal de 40 caracteres
4. **Uso de Copia**: Copie el valor hash al portapapeles

> **Importante**: SHA1 actualmente está obsoleto por razones de seguridad. Se recomienda encarecidamente usar SHA-256 o superior para nuevos desarrollos.
