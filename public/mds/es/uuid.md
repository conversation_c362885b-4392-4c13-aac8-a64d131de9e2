# Generador UUID

UUID (Universally Unique Identifier, Identificador Único Universal) es información de identificación estandarizada utilizada para identificar de manera única información en sistemas distribuidos. Un UUID está compuesto por 128 bits, generalmente representado por 32 dígitos hexadecimales, divididos en 5 grupos separados por guiones.

## ✨ Características Principales

- 🔢 **Soporte Múltiples Versiones**: Compatible con diferentes versiones como UUID v1, v4, etc.
- ⚡ **Generación por Lotes**: Compatible con generación de múltiples UUID a la vez
- 📋 **Múltiples Formatos**: Compatible con formato estándar, formato sin guiones, etc.
- 💾 **Copia con Un Clic**: Los UUID generados se pueden copiar directamente para usar
- 🔧 **Generación en Tiempo Real**: Haga clic para generar nuevos UUID instantáneamente

## 📖 Ejemplos de Uso

### UUID v4 Estándar

**Ejemplo Generado:**
```
f47ac10b-58cc-4372-a567-0e02b2c3d479
```

**Características:**
- Generación aleatoria, sin información de tiempo
- Probabilidad de colisión extremadamente baja
- Versión UUID más comúnmente utilizada

### UUID v1 (Basado en Tiempo)

**Ejemplo Generado:**
```
6ba7b810-9dad-11d1-80b4-00c04fd430c8
```

**Características:**
- Contiene información de marca de tiempo
- Contiene información de dirección MAC
- Tiempo de generación puede ser inferido

### Formato Sin Guiones

**Ejemplo Generado:**
```
f47ac10b58cc4372a5670e02b2c3d479
```

**Características:**
- 32 caracteres hexadecimales continuos
- Aplicable a ciertas bases de datos y sistemas

## 🎯 Escenarios de Aplicación

### 1. Clave Primaria de Base de Datos

Usar UUID como clave primaria en base de datos:

```sql
-- Crear tabla usando UUID como clave primaria
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insertar datos (UUID generado automáticamente)
INSERT INTO users (username, email) 
VALUES ('juan', '<EMAIL>');

-- Consultar datos
SELECT * FROM users WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
```

### 2. Sistemas Distribuidos

Generar identificadores únicos en sistemas distribuidos:

```javascript
// Rastreador de solicitudes en microservicios
class RequestTracker {
  constructor() {
    this.requestId = generateUUID()
    this.timestamp = new Date().toISOString()
  }

  log(message) {
    console.log(`[${this.requestId}] ${this.timestamp}: ${message}`)
  }
}

// Ejemplo de uso
const tracker = new RequestTracker()
tracker.log('Inicio de procesamiento de solicitud de usuario')
tracker.log('Llamada a servicio de usuario')
tracker.log('Llamada a servicio de pedidos')
tracker.log('Procesamiento de solicitud completado')

// Salida de ejemplo:
// [f47ac10b-58cc-4372-a567-0e02b2c3d479] 2024-06-15T10:30:00.000Z: Inicio de procesamiento de solicitud de usuario
```

### 3. Nomenclatura de Archivos

Generar nombres únicos para archivos subidos:

```javascript
// Procesamiento de subida de archivos
function handleFileUpload(file) {
  const fileExtension = file.name.split('.').pop()
  const uniqueFileName = `${generateUUID()}.${fileExtension}`
  
  // Guardar archivo
  const filePath = `/uploads/${uniqueFileName}`
  saveFile(file, filePath)
  
  return {
    originalName: file.name,
    fileName: uniqueFileName,
    filePath: filePath,
    uploadTime: new Date().toISOString()
  }
}

// Ejemplo de uso
const uploadResult = handleFileUpload(userFile)
console.log(uploadResult)
// {
//   originalName: "documento.pdf",
//   fileName: "f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   filePath: "/uploads/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
//   uploadTime: "2024-06-15T10:30:00.000Z"
// }
```

### 4. Gestión de Sesiones

Generar identificadores únicos para sesiones de usuario:

```javascript
// Sistema de gestión de sesiones
class SessionManager {
  constructor() {
    this.sessions = new Map()
  }

  createSession(userId) {
    const sessionId = generateUUID()
    const session = {
      id: sessionId,
      userId: userId,
      createdAt: new Date(),
      lastAccess: new Date(),
      data: {}
    }
    
    this.sessions.set(sessionId, session)
    return sessionId
  }

  getSession(sessionId) {
    const session = this.sessions.get(sessionId)
    if (session) {
      session.lastAccess = new Date()
    }
    return session
  }

  destroySession(sessionId) {
    return this.sessions.delete(sessionId)
  }
}

// Ejemplo de uso
const sessionManager = new SessionManager()
const sessionId = sessionManager.createSession('user123')
console.log('ID de Sesión:', sessionId)
// ID de Sesión: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

### 5. Generación de Claves API

Generar claves únicas para acceso API:

```javascript
// Gestión de claves API
class ApiKeyManager {
  constructor() {
    this.apiKeys = new Map()
  }

  generateApiKey(userId, permissions = []) {
    const apiKey = generateUUID()
    const keyInfo = {
      key: apiKey,
      userId: userId,
      permissions: permissions,
      createdAt: new Date(),
      lastUsed: null,
      isActive: true
    }
    
    this.apiKeys.set(apiKey, keyInfo)
    return apiKey
  }

  validateApiKey(apiKey) {
    const keyInfo = this.apiKeys.get(apiKey)
    if (keyInfo && keyInfo.isActive) {
      keyInfo.lastUsed = new Date()
      return keyInfo
    }
    return null
  }

  revokeApiKey(apiKey) {
    const keyInfo = this.apiKeys.get(apiKey)
    if (keyInfo) {
      keyInfo.isActive = false
      return true
    }
    return false
  }
}

// Ejemplo de uso
const apiManager = new ApiKeyManager()
const apiKey = apiManager.generateApiKey('user123', ['read', 'write'])
console.log('Clave API:', apiKey)
// Clave API: f47ac10b-58cc-4372-a567-0e02b2c3d479
```

## 🔧 Detalles Técnicos

### Versiones UUID

Diferentes versiones UUID tienen diferentes métodos de generación:

**UUID v1 (Basado en Tiempo):**
- Contiene marca de tiempo (60 bits)
- Contiene secuencia de reloj (14 bits)
- Contiene identificador de nodo (48 bits, generalmente dirección MAC)
- Tiempo y lugar de generación pueden ser inferidos

**UUID v4 (Aleatorio):**
- 122 bits de números aleatorios
- 6 bits de identificadores de versión y variante
- Probabilidad de colisión aproximadamente 1/2^122
- Versión más comúnmente utilizada

**UUID v5 (Basado en Nombre):**
- Usa algoritmo hash SHA-1
- Generado basado en espacio de nombres y nombre
- La misma entrada siempre genera el mismo UUID

### Estructura de Formato

Formato UUID estándar: `xxxxxxxx-xxxx-Mxxx-Nxxx-xxxxxxxxxxxx`

- **M**: Número de versión (1, 4, 5, etc.)
- **N**: Identificador de variante (generalmente 8, 9, A, B)
- **x**: Dígito hexadecimal (0-9, A-F)

### Probabilidad de Colisión

La probabilidad de colisión de UUID v4 es extremadamente baja:

- Total de 2^122 tipos de UUID posibles
- Probabilidad de colisión de aproximadamente 50% al generar 10^18 UUID
- Puede considerarse único en aplicaciones prácticas

## 💡 Consejos de Uso

- **Selección de Versión**: Generalmente usar UUID v4, usar v1 cuando se necesita información de tiempo
- **Optimización de Almacenamiento**: Usar BINARY(16) en base de datos para ahorrar espacio
- **Rendimiento de Índices**: Prestar atención al impacto en el rendimiento de índices de base de datos al usar UUID como clave primaria
- **Unificación de Formato**: Mantener consistencia de formato UUID dentro del mismo sistema

## ⚠️ Notas Importantes

- **Impacto en Rendimiento**: Usar UUID como clave primaria puede afectar el rendimiento de la base de datos
- **Problema de Ordenamiento**: UUID no puede ordenarse por tiempo de generación (a menos que use v1)
- **Espacio de Almacenamiento**: UUID ocupa más espacio de almacenamiento que ID enteros (36 caracteres o 16 bytes)
- **Legibilidad**: UUID no es tan intuitivo y legible como ID auto-incrementales

## 🚀 Cómo Usar

1. **Selección de Versión**: Elija la versión UUID según los requisitos
2. **Configuración de Cantidad**: Seleccione el número de UUID a generar
3. **Selección de Formato**: Elija formato estándar o formato sin guiones
4. **Generar UUID**: Haga clic en el botón generar para crear UUID
5. **Uso de Copia**: Haga clic en el botón copiar para copiar UUID al portapapeles

> **Consejo**: Esta herramienta genera UUID localmente en el lado del cliente y no sube datos al servidor, garantizando privacidad y seguridad.
