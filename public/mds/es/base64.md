# Herramienta de Codificación y Decodificación Base64

Base64 es un método de codificación utilizado para representar datos binarios en formato de texto. Se utiliza principalmente en el envío de correos electrónicos, desarrollo web, almacenamiento de datos y otros escenarios para transferir y almacenar de forma segura datos binarios como texto.

## ✨ Características Principales

- 🔄 **Conversión Bidireccional**: Compatible con codificación y decodificación Base64 de texto
- 🌐 **Soporte Multiidioma**: Compatible con caracteres Unicode como español, chino, inglés
- 📁 **Procesamiento de Archivos**: Conversión Base64 de archivos como imágenes y documentos
- 📋 **Copia con Un Clic**: Los resultados de conversión se pueden copiar directamente para su uso
- ⚡ **Conversión en Tiempo Real**: Muestra resultados al mismo tiempo que la entrada

## 📖 Ejemplos de Uso

### Codificación de Texto

**Texto de Entrada:**
```
¡Hola, ToolMi!
```

**Resultado de Codificación Base64:**
```
wqFIb2xhLCBUb29sTWkh
```

### Codificación de URL

**URL de Entrada:**
```
https://www.toolmi.com/buscar?q=herramienta Base64
```

**Resultado de Codificación Base64:**
```
aHR0cHM6Ly93d3cudG9vbG1pLmNvbS9idXNjYXI/cT1oZXJyYW1pZW50YSBCYXNlNjQ=
```

### Codificación de Datos JSON

**JSON de Entrada:**
```json
{
  "nombre": "Juan Pérez",
  "email": "<EMAIL>",
  "edad": 30
}
```

**Resultado de Codificación Base64:**
```
ewogICJub21icmUiOiAiSnVhbiBQw6lyZXoiLAogICJlbWFpbCI6ICJqdWFuQGV4YW1wbGUuY29tIiwKICAiZWRhZCI6IDMwCn0=
```

## 🎯 Escenarios de Aplicación

### 1. Desarrollo Web

Ejemplos de uso de Base64 en desarrollo web:

```html
<!-- Incrustación de imagen Base64 -->
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA6VP8EQAAAABJRU5ErkJggg==" alt="Imagen transparente 1x1">

<!-- Incrustación de fuente Base64 en CSS -->
@font-face {
  font-family: 'FuentePersonalizada';
  src: url('data:font/woff2;base64,d09GMgABAAAAAA...') format('woff2');
}

<!-- Procesamiento de datos Base64 en JavaScript -->
<script>
// Codificación Base64
const codificado = btoa('¡Hola!');
console.log(codificado); // wqFIb2xhIQ==

// Decodificación Base64
const decodificado = atob(codificado);
console.log(decodificado); // ¡Hola!
</script>
```

### 2. Desarrollo de API

Ejemplo de uso de Base64 en comunicación API:

```javascript
// API de carga de archivos
const subirArchivo = async (archivo) => {
  const lector = new FileReader();
  
  return new Promise((resolver, rechazar) => {
    lector.onload = async () => {
      const datosBase64 = lector.result.split(',')[1];
      
      try {
        const respuesta = await fetch('/api/subir', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            nombreArchivo: archivo.name,
            datos: datosBase64,
            tipoMime: archivo.type
          })
        });
        
        const resultado = await respuesta.json();
        resolver(resultado);
      } catch (error) {
        rechazar(error);
      }
    };
    
    lector.onerror = rechazar;
    lector.readAsDataURL(archivo);
  });
};

// Generación de encabezado de autenticación
const crearEncabezadoAuth = (usuario, contraseña) => {
  const credenciales = `${usuario}:${contraseña}`;
  const codificado = btoa(credenciales);
  return `Basic ${codificado}`;
};

// Ejemplo de uso
const encabezadoAuth = crearEncabezadoAuth('<EMAIL>', 'contraseña123');
console.log(encabezadoAuth); // Basic dXN1YXJpb0BleGFtcGxlLmNvbTpjb250cmFzZcOxYTEyMw==
```

### 3. Almacenamiento de Datos

Uso de Base64 en almacenamiento local:

```javascript
// Guardar datos de configuración
const guardarConfiguracionUsuario = (configuracion) => {
  const configuracionJson = JSON.stringify(configuracion);
  const configuracionCodificada = btoa(configuracionJson);
  localStorage.setItem('configuracionUsuario', configuracionCodificada);
};

// Cargar datos de configuración
const cargarConfiguracionUsuario = () => {
  const configuracionCodificada = localStorage.getItem('configuracionUsuario');
  if (configuracionCodificada) {
    try {
      const configuracionJson = atob(configuracionCodificada);
      return JSON.parse(configuracionJson);
    } catch (error) {
      console.error('Error al cargar configuración:', error);
      return null;
    }
  }
  return null;
};

// Ejemplo de uso
const configuracionUsuario = {
  tema: 'oscuro',
  idioma: 'es',
  notificaciones: true
};

guardarConfiguracionUsuario(configuracionUsuario);
const configuracionCargada = cargarConfiguracionUsuario();
console.log(configuracionCargada);
```

### 4. Envío de Correo

Codificación Base64 de archivos adjuntos de correo:

```javascript
// Ejemplo de envío de correo en Node.js
const nodemailer = require('nodemailer');
const fs = require('fs');

const enviarCorreoConAdjunto = async (para, asunto, texto, rutaArchivo) => {
  // Codificar archivo a Base64
  const contenidoArchivo = fs.readFileSync(rutaArchivo);
  const contenidoBase64 = contenidoArchivo.toString('base64');
  
  const transportador = nodemailer.createTransporter({
    service: 'gmail',
    auth: {
      user: '<EMAIL>',
      pass: 'tu-contraseña'
    }
  });

  const opcionesCorreo = {
    from: '<EMAIL>',
    to: para,
    subject: asunto,
    text: texto,
    attachments: [
      {
        filename: 'documento.pdf',
        content: contenidoBase64,
        encoding: 'base64'
      }
    ]
  };

  try {
    const resultado = await transportador.sendMail(opcionesCorreo);
    console.log('Correo enviado exitosamente:', resultado.messageId);
    return resultado;
  } catch (error) {
    console.error('Error al enviar correo:', error);
    throw error;
  }
};
```

## 🔧 Detalles Técnicos

### Principio de Codificación Base64

Funcionamiento de la codificación Base64:

**Conjunto de Caracteres:**
- A-Z (26 caracteres)
- a-z (26 caracteres)  
- 0-9 (10 caracteres)
- +, / (2 caracteres)
- = (carácter de relleno)

**Pasos de Codificación:**
1. Leer datos de entrada en grupos de 8 bits
2. Dividir 3 bytes (24 bits) en 4 grupos de 6 bits
3. Convertir cada grupo de 6 bits al carácter Base64 correspondiente
4. Agregar caracteres de relleno "=" según sea necesario

### Codificación de Caracteres

Procesamiento de caracteres en español:

```javascript
// Conversión Base64 con codificación UTF-8
function codificarUTF8ABase64(str) {
  // Convertir cadena a array de bytes UTF-8
  const bytesUtf8 = new TextEncoder().encode(str);
  
  // Convertir array de bytes a cadena
  let binario = '';
  bytesUtf8.forEach(byte => {
    binario += String.fromCharCode(byte);
  });
  
  // Codificación Base64
  return btoa(binario);
}

function decodificarBase64AUtf8(base64) {
  // Decodificación Base64
  const binario = atob(base64);
  
  // Convertir cadena a array de bytes
  const bytes = new Uint8Array(binario.length);
  for (let i = 0; i < binario.length; i++) {
    bytes[i] = binario.charCodeAt(i);
  }
  
  // Decodificación UTF-8
  return new TextDecoder().decode(bytes);
}

// Ejemplo de uso
const original = "¡Hola, mundo!";
const codificado = codificarUTF8ABase64(original);
const decodificado = decodificarBase64AUtf8(codificado);

console.log('Cadena original:', original);
console.log('Resultado codificado:', codificado);
console.log('Resultado decodificado:', decodificado);
```

## 💡 Consejos de Uso

- **Codificación de Caracteres**: Prestar atención a la codificación UTF-8 al manejar caracteres en español
- **Tamaño de Datos**: El tamaño después de la codificación Base64 es aproximadamente 1.33 veces el de los datos originales
- **Rendimiento**: Prestar atención al uso de memoria al procesar archivos grandes
- **Seguridad**: Base64 no es cifrado, solo tiene un efecto de ofuscación de datos

## ⚠️ Notas Importantes

- **Seguridad**: Base64 no es cifrado, por lo que no es adecuado para proteger datos confidenciales
- **Aumento de Tamaño**: El tamaño de los datos después de la codificación aumenta aproximadamente un 33%
- **Limitaciones de Caracteres**: Algunos sistemas pueden tener limitaciones en cadenas Base64 largas
- **Procesamiento de Saltos de Línea**: Diferentes sistemas pueden manejar los saltos de línea en cadenas Base64 de manera diferente

## 🚀 Cómo Usar

1. **Entrada de Texto**: Ingrese el texto a codificar/decodificar en el cuadro de entrada
2. **Selección de Operación**: Haga clic en el botón "Codificar" o "Decodificar"
3. **Verificación de Resultados**: Los resultados de conversión se muestran en el área de salida
4. **Uso de Copia**: Haga clic en el botón "Copiar" para copiar los resultados al portapapeles
5. **Procesamiento de Archivos**: Arrastre y suelte archivos para conversión Base64

> **Consejo**: Esta herramienta procesa localmente en el lado del cliente y no sube datos al servidor, garantizando privacidad y seguridad.
