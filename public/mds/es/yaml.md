# Herramienta de Formato YAML

YAML (YAML Ain't Markup Language) es un estándar de serialización de datos legible por humanos, comúnmente utilizado en archivos de configuración, intercambio de datos y creación de documentos. Esta herramienta proporciona funciones de validación, embellecimiento y compresión de formato YAML para ayudar a desarrolladores a procesar y gestionar archivos YAML.

## ✨ Características Principales

- 🎯 **Validación de Formato**: Verifica errores de sintaxis YAML y proporciona sugerencias detalladas
- 🎨 **Embellecimiento de Formato**: Formatea automáticamente contenido YAML con indentación y formato
- 🗜️ **Compresión de Contenido**: Elimina espacios y comentarios innecesarios para comprimir el tamaño del archivo
- 📋 **Copia con Un Clic**: Los resultados de formato se pueden copiar directamente para usar
- 🔧 **Localización de Errores**: Localiza con precisión la posición de errores de sintaxis

## 📖 Ejemplos de Uso

### Ejemplo de Formato

**YAML Original:**
```yaml
name:ToolMi
version:1.0.0
features:
- Formato
- Validación
- Compresión
config:
  debug:true
  port:3000
```

**Después del Formato:**
```yaml
name: ToolMi
version: 1.0.0
features:
  - Formato
  - Validación
  - Compresión
config:
  debug: true
  port: 3000
```

### Ejemplo de Estructura Compleja

**YAML de Entrada:**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
  pools:
    - name: read_pool
      size: 10
    - name: write_pool
      size: 5
```

**Después de Validación y Embellecimiento:**
```yaml
database:
  host: localhost
  port: 5432
  credentials:
    username: admin
    password: secret
  pools:
    - name: read_pool
      size: 10
    - name: write_pool
      size: 5
```

## 🎯 Escenarios de Aplicación

### 1. Gestión de Archivos de Configuración

Organizar y validar archivos de configuración de aplicaciones:

```yaml
# Archivo de configuración de aplicación (config.yml)
app:
  name: ToolMi
  version: 1.0.0
  environment: production
  
server:
  host: 0.0.0.0
  port: 3000
  ssl:
    enabled: true
    cert_path: /etc/ssl/certs/app.crt
    key_path: /etc/ssl/private/app.key
    
database:
  type: postgresql
  host: localhost
  port: 5432
  name: toolmi_db
  username: ${DB_USER}
  password: ${DB_PASSWORD}
  pool:
    min_connections: 2
    max_connections: 10
    
redis:
  host: localhost
  port: 6379
  password: ${REDIS_PASSWORD}
  db: 0
  
logging:
  level: info
  format: json
  outputs:
    - type: file
      path: /var/log/app.log
    - type: console
```

### 2. Archivos Docker Compose

Gestión de configuración de orquestación de contenedores Docker:

```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=database
    depends_on:
      - database
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - app-network
      
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: toolmi_db
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
      
  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 3. Configuración CI/CD

Configuración de flujo de trabajo de GitHub Actions:

```yaml
# .github/workflows/deploy.yml
name: Despliegue a Producción

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout código
        uses: actions/checkout@v3
        
      - name: Configurar Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Instalar dependencias
        run: npm ci
        
      - name: Ejecutar pruebas
        run: npm test
        
      - name: Ejecutar linting
        run: npm run lint
        
  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout código
        uses: actions/checkout@v3
        
      - name: Construir imagen Docker
        run: |
          docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest .
          
      - name: Push a registro
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
```

### 4. Configuración Kubernetes

Archivos de configuración de despliegue Kubernetes:

```yaml
# deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: toolmi-app
  labels:
    app: toolmi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: toolmi
  template:
    metadata:
      labels:
        app: toolmi
    spec:
      containers:
        - name: app
          image: toolmi/app:latest
          ports:
            - containerPort: 3000
          env:
            - name: NODE_ENV
              value: "production"
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: db-host
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: toolmi-service
spec:
  selector:
    app: toolmi
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer
```

## 🔧 Detalles Técnicos

### Reglas de Sintaxis YAML

Reglas básicas de sintaxis YAML:

**Reglas de Indentación:**
- Usar espacios para indentación, no se permiten tabs
- Elementos del mismo nivel necesitan alineación izquierda
- Elementos hijos necesitan más indentación que elementos padre

**Tipos de Datos:**
- Cadenas: Pueden no tener comillas, caracteres especiales necesitan comillas
- Números: Enteros y números de punto flotante
- Booleanos: true/false, yes/no, on/off
- Valores null: null, ~, o vacío

**Tipos de Colección:**
- Arrays: Usar - para mostrar elementos de lista
- Objetos: Usar key: value para mostrar pares clave-valor
- Anidamiento: Compatible con estructura anidada de cualquier nivel

### Errores Comunes

Errores comunes que la herramienta de formato YAML puede detectar:

**Errores de Indentación:**
```yaml
# Ejemplo incorrecto
config:
  debug: true
 port: 3000  # Indentación no coincide

# Ejemplo correcto
config:
  debug: true
  port: 3000
```

**Problemas de Comillas:**
```yaml
# Ejemplo incorrecto
message: It's a test  # Comilla simple no escapada

# Ejemplo correcto
message: "It's a test"
# o
message: 'It''s a test'
```

**Formato de Lista:**
```yaml
# Ejemplo incorrecto
features:
- format
 - validate  # Error de indentación

# Ejemplo correcto
features:
  - format
  - validate
```

## 💡 Consejos de Uso

- **Indentación Consistente**: Siempre usar el mismo número de espacios para indentación (se recomiendan 2 espacios)
- **Uso de Comillas**: Cadenas que contienen caracteres especiales deben estar entre comillas
- **Normas de Comentarios**: Usar # para agregar comentarios, agregar espacio antes del comentario
- **Cadenas Multilínea**: Usar | o > para manejar texto multilínea

## ⚠️ Notas Importantes

- **Prohibición de Tabs**: YAML no permite tabs, solo se pueden usar espacios
- **Sensible a Indentación**: Errores de indentación causarán fallo de análisis
- **Caracteres Especiales**: Caracteres especiales como dos puntos, comillas necesitan manejo correcto
- **Formato de Codificación**: Asegurar que el archivo use codificación UTF-8

## 🚀 Cómo Usar

1. **Entrada YAML**: Pegue el contenido YAML a procesar en el cuadro de entrada
2. **Selección de Operación**: Haga clic en los botones "Formatear", "Validar" o "Comprimir"
3. **Verificación de Resultados**: Verifique el YAML procesado en el cuadro de salida
4. **Uso de Copia**: Haga clic en el botón "Copiar" para copiar los resultados al portapapeles
5. **Corrección de Errores**: Corrija problemas de sintaxis basándose en sugerencias de error

> **Consejo**: Esta herramienta procesa localmente en el lado del cliente y no sube contenido YAML al servidor, garantizando seguridad de datos.
