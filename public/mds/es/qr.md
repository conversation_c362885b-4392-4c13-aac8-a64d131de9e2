# Generador de Códigos QR

El código QR (Quick Response Code) es un código de barras bidimensional inventado por Denso Wave de Japón en 1994. Esta herramienta puede convertir rápidamente texto, URLs, información de contacto y otros contenidos diversos en imágenes de códigos QR.

## ✨ Características Principales

- 📱 **Soporte Multicontenido**: Compatible con texto, URL, contactos, contraseñas WiFi, etc.
- 🎨 **Salida de Alta Calidad**: Genera imágenes de códigos QR de alta calidad
- 📏 **Tamaños Ajustables**: Compatible con múltiples especificaciones de tamaño
- 🎯 **Niveles de Corrección de Errores**: Compatible con diferentes configuraciones de niveles de corrección de errores
- 💾 **Descarga con Un Clic**: Descarga y guarda directamente códigos QR generados
- 🔧 **Vista Previa en Tiempo Real**: Genera vista previa de código QR instantáneamente según el contenido de entrada

## 📖 Ejemplos de Uso

### Código QR de URL

**Contenido de Entrada:**
```
https://www.toolmi.com
```

**Efecto Generado:**
- Después del escaneo, salta directamente al sitio web de ToolMi
- Aplicable a promoción de sitios web, compartir enlaces

### Código QR de Texto

**Contenido de Entrada:**
```
¡Bienvenido a las herramientas en línea de ToolMi!
Aquí hay herramientas de desarrollo ricas y funciones prácticas.
```

**Efecto Generado:**
- Después del escaneo, muestra el contenido completo del texto
- Aplicable a transmisión de información, visualización de instrucciones

### Código QR de Contacto

**Contenido de Entrada:**
```
BEGIN:VCARD
VERSION:3.0
FN:Juan Pérez
ORG:Tecnología ToolMi
TEL:+34-600-123-456
EMAIL:<EMAIL>
URL:https://www.toolmi.com
END:VCARD
```

**Efecto Generado:**
- Después del escaneo, se puede agregar directamente a contactos
- Incluye información como nombre, empresa, teléfono, correo electrónico

## 🎯 Escenarios de Aplicación

### 1. Promoción de Sitios Web

Generar códigos QR de promoción para sitios web y aplicaciones:

```html
<!-- Código QR en póster promocional -->
<div class="promotion">
  <h3>Escanea el código QR para acceder a ToolMi</h3>
  <img src="qr-code.png" alt="Código QR de ToolMi">
  <p>Descubre más herramientas prácticas</p>
</div>

<!-- Escenarios de aplicación -->
- Pósters promocionales
- Diseño de tarjetas de presentación
- Embalaje de productos
- Publicaciones publicitarias
```

### 2. Pagos Móviles

Generar códigos QR de pago:

```javascript
// Ejemplo de enlace de pago PayPal
const paypalUrl = 'https://paypal.me/usuario/50EUR'

// Ejemplo de enlace de pago Bizum
const bizumUrl = 'bizum://payment?amount=50&concept=Servicio'

// Generar código QR de pago
generateQRCode(paypalUrl, {
  size: 200,
  errorCorrectionLevel: 'M'
})
```

### 3. Compartir Contraseña WiFi

Generar código QR de conexión WiFi:

```
Formato de código QR WiFi:
WIFI:T:WPA;S:nombre_red;P:contraseña;H:false;;

Ejemplo:
WIFI:T:WPA;S:ToolMi_5G;P:12345678;H:false;;

Explicación de parámetros:
- T: Tipo de cifrado (WPA/WEP/nopass)
- S: Nombre de red (SSID)
- P: Contraseña
- H: Si ocultar red (true/false)
```

### 4. Check-in de Eventos

Generar código QR de check-in de eventos:

```json
{
  "type": "event_checkin",
  "event_id": "tech_meetup_2024",
  "event_name": "Encuentro Tecnológico",
  "location": "Centro de Convenciones de Madrid",
  "date": "2024-06-15",
  "checkin_url": "https://event.toolmi.com/checkin/tech_meetup_2024"
}
```

### 5. Trazabilidad de Productos

Generar código QR de trazabilidad de productos:

```javascript
// Información del producto
const productInfo = {
  id: 'TM2024001',
  name: 'Caja de Herramientas Inteligente',
  batch: 'B20240615',
  production_date: '2024-06-15',
  manufacturer: 'Tecnología ToolMi',
  quality_check: 'PASS',
  trace_url: 'https://trace.toolmi.com/product/TM2024001'
}

// Generar código QR de trazabilidad
const traceData = JSON.stringify(productInfo)
generateQRCode(traceData)
```

## 🔧 Detalles Técnicos

### Estructura del Código QR

Estructura básica del código QR:

**Patrones Funcionales:**
- Patrones de detección de posición: Cuadrados grandes en tres esquinas
- Separadores de patrones de detección de posición: Marcos blancos
- Patrones de alineación: Puntos negros pequeños para determinar dirección

**Área de Datos:**
- Información de formato: Nivel de corrección de errores e información de máscara
- Información de versión: Número de versión del código QR
- Palabras de código de datos y corrección de errores: Datos realmente almacenados

**Especificaciones de Capacidad:**
- Versión 1: 21×21 módulos, máximo 25 caracteres
- Versión 40: 177×177 módulos, máximo 4296 caracteres
- Compatible con datos numéricos, alfabéticos, kanji y binarios

### Niveles de Corrección de Errores

El código QR soporta 4 niveles de corrección de errores:

| Nivel | Tasa de Corrección | Escenario de Aplicación |
|-------|-------------------|-------------------------|
| L | ~7% | Entorno limpio, impresión de alta calidad |
| M | ~15% | Entorno general, impresión estándar |
| Q | ~25% | Entorno malo, posible contaminación |
| H | ~30% | Entorno muy malo, contaminación severa |

**Recomendaciones de Selección:**
- Compartir URL: Usar nivel L o M
- Publicidad exterior: Usar nivel Q o H
- Etiquetas de productos: Usar nivel M o Q

### Modos de Codificación

El código QR soporta múltiples modos de codificación:

**Modo Numérico:**
- Solo puede almacenar dígitos 0-9
- Eficiencia de almacenamiento más alta
- Aplicable a contenido puramente numérico

**Modo Alfanumérico:**
- Puede almacenar dígitos, letras mayúsculas y algunos símbolos
- Alta eficiencia
- Aplicable a URL, códigos, etc.

**Modo Byte:**
- Puede almacenar cualquier carácter
- Compatible con español, símbolos especiales
- Versatilidad más fuerte

**Modo Kanji:**
- Optimización específica para caracteres japoneses
- Mayor eficiencia en entorno japonés

## 💡 Consejos de Uso

- **Optimización de Contenido**: Usar contenido lo más corto posible para mejorar la tasa de éxito del escaneo
- **Selección de Tamaño**: Elegir tamaño apropiado según la distancia de uso
- **Nivel de Corrección de Errores**: Elegir nivel de corrección de errores apropiado según el entorno de uso
- **Verificación de Pruebas**: Después de generar, probar el efecto de escaneo con múltiples dispositivos

## ⚠️ Notas Importantes

- **Longitud de Contenido**: Contenido demasiado largo hará que el código QR sea complejo, afectando el escaneo
- **Calidad de Impresión**: Asegurar que la impresión sea clara, evitar borrosidad y deformación
- **Contraste de Color**: Mantener suficiente contraste de color, se recomienda combinación blanco y negro
- **Margen Circundante**: Dejar suficiente área en blanco alrededor del código QR

## 🚀 Cómo Usar

1. **Entrada de Contenido**: Ingrese el contenido para generar código QR en el cuadro de entrada
2. **Ajuste de Configuración**: Seleccione tamaño apropiado y nivel de corrección de errores
3. **Generar Vista Previa**: Haga clic en el botón generar para ver la vista previa del código QR
4. **Descargar y Guardar**: Haga clic en el botón descargar para guardar la imagen del código QR
5. **Prueba de Escaneo**: Use un teléfono inteligente para escanear y probar el efecto generado

> **Consejo**: Esta herramienta genera códigos QR localmente en el lado del cliente y no sube datos al servidor, garantizando privacidad y seguridad.
