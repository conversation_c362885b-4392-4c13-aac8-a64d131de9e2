# Herramienta de Conversión de Formato Netscape Cookies

Herramienta de conversión mutua entre formato Netscape Cookies y formato JSON, aplicable a migración de datos de navegador, desarrollo de web crawlers, pruebas web y otros escenarios, proporcionando compatibilidad de datos de cookies entre diferentes sistemas.

## ✨ Características Principales

- 🔄 **Conversión Bidireccional**: Compatible con conversión mutua entre formato Netscape y formato JSON
- 📊 **Validación de Datos**: Verificación automática de la exactitud del formato de cookies
- 🌐 **Compatibilidad de Navegadores**: Compatible con formatos de cookies de navegadores principales
- 📋 **Copia con Un Clic**: Los resultados de conversión se pueden copiar directamente para usar
- 🔧 **Detección de Errores**: Detección automática de formatos de cookies inválidos

## 📖 Ejemplos de Uso

### Conversión Netscape → JSON

**Formato Netscape de Entrada:**
```
# Netscape HTTP Cookie File
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123def456
.example.com	TRUE	/	FALSE	1640995200	user_pref	theme=dark
example.com	FALSE	/login	TRUE	1640995200	csrf_token	xyz789uvw012
```

**JSON Convertido:**
```json
[
  {
    "domain": ".example.com",
    "hostOnly": false,
    "path": "/",
    "secure": false,
    "expirationDate": 1640995200,
    "name": "session_id",
    "value": "abc123def456"
  },
  {
    "domain": ".example.com",
    "hostOnly": false,
    "path": "/",
    "secure": false,
    "expirationDate": 1640995200,
    "name": "user_pref",
    "value": "theme=dark"
  },
  {
    "domain": "example.com",
    "hostOnly": true,
    "path": "/login",
    "secure": true,
    "expirationDate": 1640995200,
    "name": "csrf_token",
    "value": "xyz789uvw012"
  }
]
```

### Conversión JSON → Netscape

**JSON de Entrada:**
```json
[
  {
    "domain": ".toolmi.com",
    "hostOnly": false,
    "path": "/",
    "secure": true,
    "expirationDate": 1735689600,
    "name": "auth_token",
    "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
  },
  {
    "domain": "toolmi.com",
    "hostOnly": true,
    "path": "/dashboard",
    "secure": false,
    "expirationDate": 1735689600,
    "name": "dashboard_settings",
    "value": "layout=grid&theme=light"
  }
]
```

**Formato Netscape Convertido:**
```
# Netscape HTTP Cookie File
.toolmi.com	TRUE	/	TRUE	1735689600	auth_token	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9
toolmi.com	FALSE	/dashboard	FALSE	1735689600	dashboard_settings	layout=grid&theme=light
```

## 🎯 Escenarios de Aplicación

### 1. Migración de Datos de Navegador

Migración de datos de cookies entre diferentes navegadores:

```javascript
// Herramienta de migración de cookies de navegador
class BrowserCookieMigrator {
  constructor() {
    this.supportedFormats = ['netscape', 'json', 'chrome', 'firefox']
  }

  // Convertir formato Netscape a JSON
  netscapeToJson(netscapeContent) {
    const lines = netscapeContent.split('\n')
    const cookies = []

    for (const line of lines) {
      // Omitir líneas de comentarios y líneas vacías
      if (line.startsWith('#') || line.trim() === '') continue

      const parts = line.split('\t')
      if (parts.length >= 7) {
        const cookie = {
          domain: parts[0],
          hostOnly: parts[1] === 'FALSE',
          path: parts[2],
          secure: parts[3] === 'TRUE',
          expirationDate: parseInt(parts[4]),
          name: parts[5],
          value: parts[6]
        }
        cookies.push(cookie)
      }
    }

    return cookies
  }

  // Convertir JSON a formato Netscape
  jsonToNetscape(jsonCookies) {
    let result = '# Netscape HTTP Cookie File\n'
    
    for (const cookie of jsonCookies) {
      const line = [
        cookie.domain,
        cookie.hostOnly ? 'FALSE' : 'TRUE',
        cookie.path,
        cookie.secure ? 'TRUE' : 'FALSE',
        cookie.expirationDate || Math.floor(Date.now() / 1000) + 86400,
        cookie.name,
        cookie.value
      ].join('\t')
      
      result += line + '\n'
    }

    return result
  }

  // Procesar formato específico de navegador
  processBrowserExport(data, browserType) {
    switch (browserType) {
      case 'chrome':
        return this.processChromeExport(data)
      case 'firefox':
        return this.processFirefoxExport(data)
      case 'safari':
        return this.processSafariExport(data)
      default:
        throw new Error(`Tipo de navegador no soportado: ${browserType}`)
    }
  }

  processChromeExport(chromeData) {
    // Procesamiento específico de cookies de Chrome
    return chromeData.map(cookie => ({
      domain: cookie.domain,
      hostOnly: !cookie.domain.startsWith('.'),
      path: cookie.path,
      secure: cookie.secure,
      expirationDate: cookie.expirationDate,
      name: cookie.name,
      value: cookie.value,
      httpOnly: cookie.httpOnly || false,
      sameSite: cookie.sameSite || 'unspecified'
    }))
  }

  // Gestión de expiración de cookies
  manageCookieExpiration(cookies, action = 'extend') {
    const now = Math.floor(Date.now() / 1000)
    const oneYear = 365 * 24 * 60 * 60

    return cookies.map(cookie => {
      switch (action) {
        case 'extend':
          cookie.expirationDate = now + oneYear
          break
        case 'session':
          delete cookie.expirationDate
          break
        case 'expire':
          cookie.expirationDate = now - 1
          break
      }
      return cookie
    })
  }

  // Filtrado de cookies
  filterCookies(cookies, criteria) {
    return cookies.filter(cookie => {
      if (criteria.domain && !cookie.domain.includes(criteria.domain)) {
        return false
      }
      if (criteria.secure !== undefined && cookie.secure !== criteria.secure) {
        return false
      }
      if (criteria.name && !cookie.name.includes(criteria.name)) {
        return false
      }
      return true
    })
  }
}

// Ejemplo de uso
const migrator = new BrowserCookieMigrator()

// Cargar archivo de cookies formato Netscape
const netscapeCookies = `# Netscape HTTP Cookie File
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123
.example.com	TRUE	/	TRUE	1640995200	auth_token	xyz789`

// Convertir a JSON
const jsonCookies = migrator.netscapeToJson(netscapeCookies)
console.log('Formato JSON:', JSON.stringify(jsonCookies, null, 2))

// Extender fecha de expiración
const extendedCookies = migrator.manageCookieExpiration(jsonCookies, 'extend')

// Convertir de nuevo a formato Netscape
const newNetscapeCookies = migrator.jsonToNetscape(extendedCookies)
console.log('Nuevo formato Netscape:', newNetscapeCookies)
```

### 2. Desarrollo de Web Crawler

Gestión de cookies para web scraping:

```javascript
// Gestor de cookies para crawler
class CrawlerCookieManager {
  constructor() {
    this.cookieJar = new Map()
    this.sessionCookies = new Set()
  }

  // Cargar cookies desde archivo
  loadCookiesFromFile(filePath, format = 'netscape') {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      
      if (format === 'netscape') {
        return this.parseNetscapeCookies(content)
      } else if (format === 'json') {
        return JSON.parse(content)
      }
    } catch (error) {
      console.error('Error al cargar archivo de cookies:', error)
      return []
    }
  }

  // Analizar formato Netscape
  parseNetscapeCookies(content) {
    const lines = content.split('\n')
    const cookies = []

    for (const line of lines) {
      if (line.startsWith('#') || line.trim() === '') continue

      const parts = line.split('\t')
      if (parts.length >= 7) {
        const cookie = {
          domain: parts[0],
          includeSubdomains: parts[1] === 'TRUE',
          path: parts[2],
          secure: parts[3] === 'TRUE',
          expires: parseInt(parts[4]),
          name: parts[5],
          value: parts[6]
        }
        cookies.push(cookie)
      }
    }

    return cookies
  }

  // Generar encabezado Cookie para solicitudes
  generateCookieHeader(url, cookies) {
    const urlObj = new URL(url)
    const applicableCookies = cookies.filter(cookie => {
      // Coincidencia de dominio
      if (cookie.domain.startsWith('.')) {
        if (!urlObj.hostname.endsWith(cookie.domain.substring(1))) {
          return false
        }
      } else {
        if (urlObj.hostname !== cookie.domain) {
          return false
        }
      }

      // Coincidencia de ruta
      if (!urlObj.pathname.startsWith(cookie.path)) {
        return false
      }

      // Verificación de atributo secure
      if (cookie.secure && urlObj.protocol !== 'https:') {
        return false
      }

      // Verificación de fecha de expiración
      if (cookie.expires && cookie.expires < Math.floor(Date.now() / 1000)) {
        return false
      }

      return true
    })

    return applicableCookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ')
  }

  // Extraer cookies de respuesta
  extractCookiesFromResponse(response, url) {
    const setCookieHeaders = response.headers['set-cookie'] || []
    const urlObj = new URL(url)
    const newCookies = []

    for (const header of setCookieHeaders) {
      const cookie = this.parseCookieHeader(header, urlObj.hostname)
      if (cookie) {
        newCookies.push(cookie)
      }
    }

    return newCookies
  }

  parseCookieHeader(header, defaultDomain) {
    const parts = header.split(';').map(part => part.trim())
    const [nameValue] = parts
    const [name, value] = nameValue.split('=', 2)

    const cookie = {
      name: name.trim(),
      value: value ? value.trim() : '',
      domain: defaultDomain,
      path: '/',
      secure: false,
      httpOnly: false,
      expires: null
    }

    // Analizar atributos
    for (let i = 1; i < parts.length; i++) {
      const [attrName, attrValue] = parts[i].split('=', 2)
      
      switch (attrName.toLowerCase()) {
        case 'domain':
          cookie.domain = attrValue
          break
        case 'path':
          cookie.path = attrValue
          break
        case 'expires':
          cookie.expires = Math.floor(new Date(attrValue).getTime() / 1000)
          break
        case 'max-age':
          cookie.expires = Math.floor(Date.now() / 1000) + parseInt(attrValue)
          break
        case 'secure':
          cookie.secure = true
          break
        case 'httponly':
          cookie.httpOnly = true
          break
      }
    }

    return cookie
  }

  // Guardar cookies en archivo
  saveCookiesToFile(cookies, filePath, format = 'netscape') {
    try {
      let content = ''
      
      if (format === 'netscape') {
        content = this.cookiesToNetscape(cookies)
      } else if (format === 'json') {
        content = JSON.stringify(cookies, null, 2)
      }

      fs.writeFileSync(filePath, content, 'utf8')
      console.log(`Cookies guardadas en ${filePath}`)
    } catch (error) {
      console.error('Error al guardar cookies:', error)
    }
  }

  cookiesToNetscape(cookies) {
    let content = '# Netscape HTTP Cookie File\n'
    
    for (const cookie of cookies) {
      const line = [
        cookie.domain,
        cookie.includeSubdomains ? 'TRUE' : 'FALSE',
        cookie.path,
        cookie.secure ? 'TRUE' : 'FALSE',
        cookie.expires || 0,
        cookie.name,
        cookie.value
      ].join('\t')
      
      content += line + '\n'
    }

    return content
  }
}

// Ejemplo de uso
const cookieManager = new CrawlerCookieManager()

// Cargar archivo de cookies
const cookies = cookieManager.loadCookiesFromFile('./cookies.txt', 'netscape')

// Generar encabezado Cookie para solicitud
const cookieHeader = cookieManager.generateCookieHeader('https://example.com/api/data', cookies)
console.log('Encabezado Cookie:', cookieHeader)

// Usar en solicitud HTTP
const requestOptions = {
  method: 'GET',
  headers: {
    'Cookie': cookieHeader,
    'User-Agent': 'Mozilla/5.0 (compatible; WebCrawler/1.0)'
  }
}
```

## 🔧 Detalles Técnicos

### Formato Netscape Cookie

Estructura del formato Netscape Cookie:

**Composición de Campos:**
1. **domain**: Dominio donde la cookie es válida
2. **flag**: Si incluye subdominios (TRUE/FALSE)
3. **path**: Ruta donde la cookie es válida
4. **secure**: Si solo se envía en conexiones HTTPS (TRUE/FALSE)
5. **expiration**: Fecha de expiración (timestamp Unix)
6. **name**: Nombre de la cookie
7. **value**: Valor de la cookie

**Ejemplo de formato:**
```
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123def456
```

### Formato JSON Cookie

Representación de cookies en formato JSON:

```json
{
  "domain": ".example.com",
  "hostOnly": false,
  "path": "/",
  "secure": false,
  "expirationDate": 1640995200,
  "name": "session_id",
  "value": "abc123def456",
  "httpOnly": false,
  "sameSite": "unspecified"
}
```

## 💡 Consejos de Uso

- **Verificación de Formato**: Verificar que el formato de cookies sea correcto antes de conversión
- **Atención al Dominio**: Entender el significado del punto inicial (.) en el dominio
- **Fecha de Expiración**: Verificar la exactitud del timestamp Unix
- **Seguridad**: Prestar atención al manejo de cookies confidenciales

## ⚠️ Notas Importantes

- **Privacidad**: La información de cookies puede contener datos personales
- **Seguridad**: Prestar atención a la filtración de cookies de autenticación
- **Fecha de Expiración**: Manejar apropiadamente cookies expiradas
- **Restricciones de Dominio**: Entender y usar las restricciones de dominio de cookies

## 🚀 Cómo Usar

1. **Selección de Formato**: Seleccione formato de entrada (Netscape o JSON)
2. **Entrada de Datos**: Pegue los datos de cookies a convertir en el cuadro de entrada
3. **Ejecutar Conversión**: Haga clic en el botón "Convertir" para ejecutar conversión
4. **Verificar Resultados**: Los resultados de conversión se muestran en el área de salida
5. **Uso de Copia**: Use el botón "Copiar" para copiar los resultados al portapapeles

> **Consejo**: Esta herramienta procesa localmente en el lado del cliente y no envía datos de cookies al servidor, por lo que información confidencial de sesión también se puede convertir de forma segura.
