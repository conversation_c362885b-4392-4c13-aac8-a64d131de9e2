# Herramienta de Formato SQL

SQL (Structured Query Language) es el lenguaje estándar para gestión y manipulación de bases de datos relacionales. Esta herramienta proporciona formato, embellecimiento y compresión de declaraciones SQL para mejorar la legibilidad y mantenibilidad del código.

## ✨ Características Principales

- 🎨 **Formato Automático**: Formatea automáticamente declaraciones SQL en un estilo legible
- 🗜️ **Compresión de Código**: Elimina espacios y saltos de línea innecesarios para reducir el tamaño del archivo
- ✅ **Validación de Sintaxis**: Detecta errores básicos de sintaxis SQL
- 📋 **Copia con Un Clic**: Las declaraciones SQL formateadas se pueden copiar directamente
- 🔧 **Personalizable**: Ajustar estilo de indentación y configuración de saltos de línea

## 📖 Ejemplos de Uso

### Formato de Declaración SELECT Básica

**Antes del formato:**
```sql
select u.id,u.name,u.email,p.title from users u join posts p on u.id=p.user_id where u.active=1 and p.published=1 order by p.created_at desc limit 10;
```

**Después del formato:**
```sql
SELECT 
    u.id,
    u.name,
    u.email,
    p.title
FROM users u
JOIN posts p ON u.id = p.user_id
WHERE u.active = 1
    AND p.published = 1
ORDER BY p.created_at DESC
LIMIT 10;
```

### Formato de Consulta Compleja

**Antes del formato:**
```sql
with monthly_sales as(select date_trunc('month',order_date)as month,sum(total_amount)as total_sales from orders where order_date>=current_date-interval'12 months'group by date_trunc('month',order_date)),avg_sales as(select avg(total_sales)as avg_monthly_sales from monthly_sales)select m.month,m.total_sales,a.avg_monthly_sales,case when m.total_sales>a.avg_monthly_sales then'Above Average'else'Below Average'end as performance from monthly_sales m cross join avg_sales a order by m.month;
```

**Después del formato:**
```sql
WITH monthly_sales AS (
    SELECT 
        DATE_TRUNC('month', order_date) AS month,
        SUM(total_amount) AS total_sales
    FROM orders
    WHERE order_date >= CURRENT_DATE - INTERVAL '12 months'
    GROUP BY DATE_TRUNC('month', order_date)
),
avg_sales AS (
    SELECT AVG(total_sales) AS avg_monthly_sales
    FROM monthly_sales
)
SELECT 
    m.month,
    m.total_sales,
    a.avg_monthly_sales,
    CASE 
        WHEN m.total_sales > a.avg_monthly_sales THEN 'Above Average'
        ELSE 'Below Average'
    END AS performance
FROM monthly_sales m
CROSS JOIN avg_sales a
ORDER BY m.month;
```

## 🎯 Escenarios de Aplicación

### 1. Desarrollo de Bases de Datos

Estandarización SQL para equipos de desarrollo:

```sql
-- Ejemplo de formato estándar del equipo

-- Obtener información de usuarios y número de publicaciones
SELECT 
    u.id AS user_id,
    u.username,
    u.email,
    u.created_at AS registration_date,
    COUNT(p.id) AS post_count,
    MAX(p.created_at) AS last_post_date
FROM users u
LEFT JOIN posts p ON u.id = p.user_id
WHERE u.status = 'active'
    AND u.created_at >= '2024-01-01'
GROUP BY 
    u.id,
    u.username,
    u.email,
    u.created_at
HAVING COUNT(p.id) > 0
ORDER BY 
    post_count DESC,
    u.username ASC
LIMIT 50;

-- Consulta de análisis de ventas
WITH daily_sales AS (
    SELECT 
        DATE(order_date) AS sale_date,
        SUM(total_amount) AS daily_total,
        COUNT(*) AS order_count,
        AVG(total_amount) AS avg_order_value
    FROM orders
    WHERE order_date >= CURRENT_DATE - INTERVAL '30 days'
        AND status = 'completed'
    GROUP BY DATE(order_date)
),
sales_with_trend AS (
    SELECT 
        *,
        LAG(daily_total) OVER (ORDER BY sale_date) AS prev_day_total,
        AVG(daily_total) OVER (
            ORDER BY sale_date 
            ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
        ) AS seven_day_avg
    FROM daily_sales
)
SELECT 
    sale_date,
    daily_total,
    order_count,
    avg_order_value,
    ROUND(
        ((daily_total - prev_day_total) / prev_day_total * 100), 2
    ) AS day_over_day_growth,
    ROUND(seven_day_avg, 2) AS seven_day_moving_avg
FROM sales_with_trend
ORDER BY sale_date DESC;
```

### 2. Optimización de Rendimiento

Mejora de legibilidad de consultas y análisis de rendimiento:

```sql
-- Consulta antes de optimización
SELECT * FROM orders o, customers c, products p, order_items oi 
WHERE o.customer_id = c.id AND oi.order_id = o.id AND oi.product_id = p.id 
AND o.order_date > '2024-01-01' AND c.country = 'Spain' AND p.category = 'Electronics';

-- Consulta después de optimización (formateada)
SELECT 
    o.id AS order_id,
    o.order_date,
    o.total_amount,
    c.name AS customer_name,
    c.email AS customer_email,
    p.name AS product_name,
    p.price AS product_price,
    oi.quantity,
    oi.unit_price
FROM orders o
INNER JOIN customers c ON o.customer_id = c.id
INNER JOIN order_items oi ON oi.order_id = o.id
INNER JOIN products p ON oi.product_id = p.id
WHERE o.order_date > '2024-01-01'
    AND c.country = 'Spain'
    AND p.category = 'Electronics'
ORDER BY o.order_date DESC;

-- Consulta para análisis de optimización de índices
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    c.name,
    COUNT(o.id) AS order_count,
    SUM(o.total_amount) AS total_spent
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id
    AND o.order_date >= CURRENT_DATE - INTERVAL '1 year'
WHERE c.status = 'active'
GROUP BY c.id, c.name
HAVING SUM(o.total_amount) > 1000
ORDER BY total_spent DESC;
```

### 3. Migración de Datos

Organización SQL durante migración de bases de datos:

```sql
-- Ejemplo de script de migración de datos

-- 1. Crear nueva estructura de tabla
CREATE TABLE users_new (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    date_of_birth DATE,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT users_status_check 
        CHECK (status IN ('active', 'inactive', 'suspended')),
    CONSTRAINT users_email_format_check 
        CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- 2. Migración de datos
INSERT INTO users_new (
    username,
    email,
    password_hash,
    first_name,
    last_name,
    phone,
    status,
    created_at
)
SELECT 
    LOWER(TRIM(old_username)) AS username,
    LOWER(TRIM(old_email)) AS email,
    old_password AS password_hash,
    INITCAP(TRIM(old_fname)) AS first_name,
    INITCAP(TRIM(old_lname)) AS last_name,
    REGEXP_REPLACE(old_phone, '[^0-9+\-()]', '', 'g') AS phone,
    CASE 
        WHEN old_active = 1 THEN 'active'
        WHEN old_active = 0 THEN 'inactive'
        ELSE 'suspended'
    END AS status,
    old_created_date AS created_at
FROM users_old
WHERE old_email IS NOT NULL
    AND old_email != ''
    AND old_username IS NOT NULL
    AND old_username != '';

-- 3. Verificación de integridad de datos
SELECT 
    'users_old' AS table_name,
    COUNT(*) AS record_count
FROM users_old
WHERE old_email IS NOT NULL AND old_email != ''

UNION ALL

SELECT 
    'users_new' AS table_name,
    COUNT(*) AS record_count
FROM users_new;

-- 4. Verificación de datos duplicados
SELECT 
    email,
    COUNT(*) AS duplicate_count
FROM users_new
GROUP BY email
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;
```

### 4. Generación de Reportes

SQL organizado para reportes de negocio:

```sql
-- Reporte de ventas mensuales
WITH monthly_metrics AS (
    SELECT 
        DATE_TRUNC('month', order_date) AS month,
        COUNT(DISTINCT customer_id) AS unique_customers,
        COUNT(*) AS total_orders,
        SUM(total_amount) AS total_revenue,
        AVG(total_amount) AS avg_order_value,
        MIN(total_amount) AS min_order_value,
        MAX(total_amount) AS max_order_value
    FROM orders
    WHERE order_date >= CURRENT_DATE - INTERVAL '12 months'
        AND status = 'completed'
    GROUP BY DATE_TRUNC('month', order_date)
),
monthly_growth AS (
    SELECT 
        *,
        LAG(total_revenue) OVER (ORDER BY month) AS prev_month_revenue,
        LAG(unique_customers) OVER (ORDER BY month) AS prev_month_customers
    FROM monthly_metrics
)
SELECT 
    TO_CHAR(month, 'YYYY-MM') AS month_year,
    unique_customers,
    total_orders,
    ROUND(total_revenue, 2) AS total_revenue,
    ROUND(avg_order_value, 2) AS avg_order_value,
    ROUND(
        CASE 
            WHEN prev_month_revenue > 0 THEN
                ((total_revenue - prev_month_revenue) / prev_month_revenue * 100)
            ELSE 0
        END, 2
    ) AS revenue_growth_percent,
    ROUND(
        CASE 
            WHEN prev_month_customers > 0 THEN
                ((unique_customers - prev_month_customers) / prev_month_customers * 100)
            ELSE 0
        END, 2
    ) AS customer_growth_percent
FROM monthly_growth
ORDER BY month DESC;

-- Análisis por categoría de productos
SELECT 
    p.category,
    COUNT(DISTINCT p.id) AS product_count,
    COUNT(oi.id) AS total_sales,
    SUM(oi.quantity) AS total_quantity_sold,
    SUM(oi.quantity * oi.unit_price) AS total_revenue,
    ROUND(AVG(oi.unit_price), 2) AS avg_unit_price,
    ROUND(
        SUM(oi.quantity * oi.unit_price) / 
        SUM(SUM(oi.quantity * oi.unit_price)) OVER () * 100, 2
    ) AS revenue_percentage
FROM products p
INNER JOIN order_items oi ON p.id = oi.product_id
INNER JOIN orders o ON oi.order_id = o.id
WHERE o.order_date >= CURRENT_DATE - INTERVAL '3 months'
    AND o.status = 'completed'
GROUP BY p.category
ORDER BY total_revenue DESC;
```

## 🔧 Detalles Técnicos

### Reglas de Formato

Reglas básicas de formato SQL:

**Palabras clave:**
- SELECT, FROM, WHERE, etc. en mayúsculas
- Cada cláusula principal comienza en nueva línea
- Subconsultas con indentación apropiada

**Indentación:**
- Usar 2 o 4 espacios de manera consistente
- Consultas anidadas con indentación adicional
- Lista de columnas alineada verticalmente

**Saltos de línea:**
- Consultas largas divididas en unidades lógicas
- Condiciones JOIN colocadas de manera legible
- Declaraciones CASE estructuradas para mostrar

### Soporte de Dialectos de Base de Datos

```sql
-- Características específicas de PostgreSQL
SELECT 
    id,
    name,
    created_at::DATE AS creation_date,
    EXTRACT(YEAR FROM created_at) AS creation_year
FROM users
WHERE created_at >= CURRENT_DATE - INTERVAL '1 month';

-- Características específicas de MySQL
SELECT 
    id,
    name,
    DATE(created_at) AS creation_date,
    YEAR(created_at) AS creation_year
FROM users
WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH);

-- Características específicas de SQL Server
SELECT 
    id,
    name,
    CAST(created_at AS DATE) AS creation_date,
    YEAR(created_at) AS creation_year
FROM users
WHERE created_at >= DATEADD(MONTH, -1, GETDATE());
```

## 💡 Consejos de Uso

- **Consistencia**: Unificar reglas de formato dentro del equipo
- **Legibilidad**: Agregar comentarios apropiados para consultas complejas
- **Rendimiento**: Verificar rendimiento de consultas después del formato
- **Control de Versiones**: Gestionar SQL formateado en sistemas de control de versiones

## ⚠️ Notas Importantes

- **Errores de Sintaxis**: Verificar que la sintaxis SQL sea correcta antes del formato
- **Dialectos de Base de Datos**: Prestar atención a los dialectos de la base de datos en uso
- **Rendimiento**: Verificar que el formato no afecte el rendimiento de las consultas
- **Respaldo**: Crear respaldo de consultas importantes antes del formato

## 🚀 Cómo Usar

1. **Entrada SQL**: Pegue la declaración SQL a formatear en el cuadro de entrada
2. **Selección de Opción**: Seleccione "Formatear" o "Comprimir"
3. **Ejecutar**: Haga clic en el botón para ejecutar el procesamiento
4. **Verificar Resultados**: El SQL formateado se muestra en el área de salida
5. **Uso de Copia**: Use el botón "Copiar" para copiar los resultados al portapapeles

> **Consejo**: Esta herramienta procesa localmente en el lado del cliente y no envía consultas SQL al servidor, por lo que las consultas confidenciales también se pueden formatear de forma segura.
