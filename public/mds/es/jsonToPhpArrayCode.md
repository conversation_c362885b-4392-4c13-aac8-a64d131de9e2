# Herramienta JSON → Código de Array PHP

Herramienta para convertir datos JSON a código de array PHP, compatible con estructuras anidadas, caracteres en español, escape de caracteres especiales. Convierte con un clic datos JSON a código de array PHP con formato estándar, aplicable a archivos de configuración, datos Mock API, archivos seed de base de datos y otros escenarios.

## ✨ Características Principales

- 🔄 **Conversión Completa**: Convierte datos JSON a código de array PHP ejecutable
- 🌐 **Soporte Multiidioma**: Maneja correctamente español, chino, caracteres especiales
- 🎨 **Formato Hermoso**: Genera código con indentación legible y estructura
- 📋 **Copia con Un Clic**: El código PHP generado se puede copiar directamente para usar
- 🔧 **Procesamiento de Escape**: Escape automático de caracteres especiales y procesamiento de seguridad

## 📖 Ejemplos de Uso

### Conversión JSON Básica

**JSON de Entrada:**
```json
{
  "nombre": "ToolMi",
  "version": "1.0.0",
  "caracteristicas": [
    "Conversión JSON",
    "Generación de array PHP",
    "Generación automática de código"
  ],
  "configuracion": {
    "debug": true,
    "puerto": 3000
  }
}
```

**Código PHP Generado:**
```php
<?php
$data = [
    'nombre' => 'ToolMi',
    'version' => '1.0.0',
    'caracteristicas' => [
        'Conversión JSON',
        'Generación de array PHP',
        'Generación automática de código'
    ],
    'configuracion' => [
        'debug' => true,
        'puerto' => 3000
    ]
];
?>
```

### Estructura Anidada Compleja

**JSON de Entrada:**
```json
{
  "usuarios": [
    {
      "id": 1,
      "nombre": "Juan Pérez",
      "email": "<EMAIL>",
      "perfil": {
        "edad": 30,
        "ciudad": "Madrid",
        "aficiones": ["lectura", "cine", "programación"]
      }
    },
    {
      "id": 2,
      "nombre": "María García",
      "email": "<EMAIL>",
      "perfil": {
        "edad": 25,
        "ciudad": "Barcelona",
        "aficiones": ["viajes", "cocina"]
      }
    }
  ],
  "meta": {
    "total": 2,
    "pagina": 1,
    "por_pagina": 10
  }
}
```

**Código PHP Generado:**
```php
<?php
$data = [
    'usuarios' => [
        [
            'id' => 1,
            'nombre' => 'Juan Pérez',
            'email' => '<EMAIL>',
            'perfil' => [
                'edad' => 30,
                'ciudad' => 'Madrid',
                'aficiones' => [
                    'lectura',
                    'cine',
                    'programación'
                ]
            ]
        ],
        [
            'id' => 2,
            'nombre' => 'María García',
            'email' => '<EMAIL>',
            'perfil' => [
                'edad' => 25,
                'ciudad' => 'Barcelona',
                'aficiones' => [
                    'viajes',
                    'cocina'
                ]
            ]
        ]
    ],
    'meta' => [
        'total' => 2,
        'pagina' => 1,
        'por_pagina' => 10
    ]
];
?>
```

## 🎯 Escenarios de Aplicación

### 1. Generación de Archivos de Configuración

Crear archivos de configuración de aplicaciones:

```php
<?php
// config/app.php
return [
    'nombre' => 'Aplicación ToolMi',
    'entorno' => 'produccion',
    'debug' => false,
    'url' => 'https://www.toolmi.com',
    
    'base_datos' => [
        'por_defecto' => 'mysql',
        'conexiones' => [
            'mysql' => [
                'driver' => 'mysql',
                'host' => 'localhost',
                'puerto' => 3306,
                'base_datos' => 'toolmi_db',
                'usuario' => 'root',
                'contraseña' => 'secreto',
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci'
            ]
        ]
    ],
    
    'cache' => [
        'por_defecto' => 'redis',
        'almacenes' => [
            'redis' => [
                'driver' => 'redis',
                'host' => 'localhost',
                'puerto' => 6379,
                'base_datos' => 0
            ]
        ]
    ],
    
    'correo' => [
        'driver' => 'smtp',
        'host' => 'smtp.gmail.com',
        'puerto' => 587,
        'cifrado' => 'tls',
        'usuario' => '<EMAIL>',
        'contraseña' => 'contraseña_correo'
    ],
    
    'servicios' => [
        'stripe' => [
            'clave' => 'pk_test_...',
            'secreto' => 'sk_test_...'
        ],
        'aws' => [
            'clave' => 'AKIAIOSFODNN7EXAMPLE',
            'secreto' => 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
            'region' => 'eu-west-1'
        ]
    ]
];
```

### 2. Datos Mock API

Generar datos mock para pruebas:

```php
<?php
// tests/fixtures/api_responses.php
return [
    'lista_usuarios' => [
        'datos' => [
            [
                'id' => 1,
                'nombre_usuario' => 'juan_perez',
                'email' => '<EMAIL>',
                'nombre' => 'Juan',
                'apellido' => 'Pérez',
                'avatar' => 'https://example.com/avatars/1.jpg',
                'creado_en' => '2024-01-15T10:30:00Z',
                'actualizado_en' => '2024-06-15T14:20:00Z',
                'estado' => 'activo',
                'roles' => ['usuario', 'editor']
            ],
            [
                'id' => 2,
                'nombre_usuario' => 'maria_garcia',
                'email' => '<EMAIL>',
                'nombre' => 'María',
                'apellido' => 'García',
                'avatar' => 'https://example.com/avatars/2.jpg',
                'creado_en' => '2024-02-20T09:15:00Z',
                'actualizado_en' => '2024-06-14T16:45:00Z',
                'estado' => 'activo',
                'roles' => ['usuario', 'admin']
            ]
        ],
        'meta' => [
            'pagina_actual' => 1,
            'por_pagina' => 20,
            'total' => 2,
            'ultima_pagina' => 1
        ]
    ],
    
    'catalogo_productos' => [
        'categorias' => [
            [
                'id' => 1,
                'nombre' => 'Electrónicos',
                'slug' => 'electronicos',
                'descripcion' => 'Dispositivos electrónicos y gadgets más recientes',
                'productos' => [
                    [
                        'id' => 101,
                        'nombre' => 'Smartphone Pro',
                        'precio' => 899.99,
                        'moneda' => 'EUR',
                        'en_stock' => true,
                        'especificaciones' => [
                            'tamaño_pantalla' => '6.1 pulgadas',
                            'almacenamiento' => '128GB',
                            'camara' => '12MP',
                            'bateria' => '3000mAh'
                        ]
                    ],
                    [
                        'id' => 102,
                        'nombre' => 'Auriculares Inalámbricos',
                        'precio' => 159.99,
                        'moneda' => 'EUR',
                        'en_stock' => true,
                        'especificaciones' => [
                            'duracion_bateria' => '24 horas',
                            'cancelacion_ruido' => true,
                            'resistencia_agua' => 'IPX4'
                        ]
                    ]
                ]
            ]
        ]
    ]
];
```

### 3. Archivos Seed de Base de Datos

Generar datos iniciales de base de datos:

```php
<?php
// database/seeders/UsersTableSeeder.php
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UsersTableSeeder extends Seeder
{
    public function run()
    {
        $usuarios = [
            [
                'nombre' => 'Administrador',
                'email' => '<EMAIL>',
                'email_verificado_en' => now(),
                'contraseña' => Hash::make('contraseña'),
                'rol' => 'admin',
                'perfil' => [
                    'nombre' => 'Admin',
                    'apellido' => 'istrador',
                    'telefono' => '+34-600-123-456',
                    'departamento' => 'Administración del Sistema',
                    'biografia' => 'Responsable de la gestión general del sistema.'
                ],
                'configuracion' => [
                    'tema' => 'oscuro',
                    'idioma' => 'es',
                    'zona_horaria' => 'Europe/Madrid',
                    'notificaciones' => [
                        'email' => true,
                        'push' => true,
                        'sms' => false
                    ]
                ],
                'creado_en' => now(),
                'actualizado_en' => now()
            ],
            [
                'nombre' => 'Juan Pérez',
                'email' => '<EMAIL>',
                'email_verificado_en' => now(),
                'contraseña' => Hash::make('contraseña'),
                'rol' => 'usuario',
                'perfil' => [
                    'nombre' => 'Juan',
                    'apellido' => 'Pérez',
                    'telefono' => '+34-600-234-567',
                    'departamento' => 'Desarrollo',
                    'biografia' => 'Responsable del desarrollo frontend.'
                ],
                'configuracion' => [
                    'tema' => 'claro',
                    'idioma' => 'es',
                    'zona_horaria' => 'Europe/Madrid',
                    'notificaciones' => [
                        'email' => true,
                        'push' => false,
                        'sms' => false
                    ]
                ],
                'creado_en' => now(),
                'actualizado_en' => now()
            ]
        ];

        foreach ($usuarios as $datosUsuario) {
            DB::table('usuarios')->insert([
                'nombre' => $datosUsuario['nombre'],
                'email' => $datosUsuario['email'],
                'email_verificado_en' => $datosUsuario['email_verificado_en'],
                'contraseña' => $datosUsuario['contraseña'],
                'rol' => $datosUsuario['rol'],
                'perfil' => json_encode($datosUsuario['perfil']),
                'configuracion' => json_encode($datosUsuario['configuracion']),
                'creado_en' => $datosUsuario['creado_en'],
                'actualizado_en' => $datosUsuario['actualizado_en']
            ]);
        }
    }
}
```

### 4. Plantillas de Respuesta API

Plantillas de respuesta para desarrollo API:

```php
<?php
// app/Http/Resources/ApiResponseTemplates.php
class PlantillasRespuestaApi
{
    public static function exito($datos = null, $mensaje = 'Éxito')
    {
        return [
            'estado' => 'exito',
            'mensaje' => $mensaje,
            'datos' => $datos,
            'marca_tiempo' => now()->toISOString(),
            'id_solicitud' => request()->header('X-Request-ID', uniqid())
        ];
    }

    public static function error($mensaje = 'Ha ocurrido un error', $codigo = 500, $errores = null)
    {
        return [
            'estado' => 'error',
            'mensaje' => $mensaje,
            'codigo_error' => $codigo,
            'errores' => $errores,
            'marca_tiempo' => now()->toISOString(),
            'id_solicitud' => request()->header('X-Request-ID', uniqid())
        ];
    }

    public static function paginacion($datos, $total, $pagina, $porPagina)
    {
        return [
            'estado' => 'exito',
            'datos' => $datos,
            'paginacion' => [
                'pagina_actual' => $pagina,
                'por_pagina' => $porPagina,
                'total' => $total,
                'ultima_pagina' => ceil($total / $porPagina),
                'desde' => ($pagina - 1) * $porPagina + 1,
                'hasta' => min($pagina * $porPagina, $total)
            ],
            'marca_tiempo' => now()->toISOString()
        ];
    }

    public static function perfilUsuario($usuario)
    {
        return [
            'id' => $usuario->id,
            'nombre_usuario' => $usuario->nombre_usuario,
            'email' => $usuario->email,
            'perfil' => [
                'nombre' => $usuario->nombre,
                'apellido' => $usuario->apellido,
                'nombre_completo' => $usuario->nombre . ' ' . $usuario->apellido,
                'avatar' => $usuario->url_avatar,
                'biografia' => $usuario->biografia,
                'ubicacion' => $usuario->ubicacion,
                'sitio_web' => $usuario->sitio_web
            ],
            'estadisticas' => [
                'conteo_publicaciones' => $usuario->publicaciones()->count(),
                'conteo_seguidores' => $usuario->seguidores()->count(),
                'conteo_siguiendo' => $usuario->siguiendo()->count()
            ],
            'configuracion' => [
                'tema' => $usuario->configuracion['tema'] ?? 'claro',
                'idioma' => $usuario->configuracion['idioma'] ?? 'es',
                'privacidad' => [
                    'perfil_publico' => $usuario->configuracion['privacidad']['perfil_publico'] ?? true,
                    'email_publico' => $usuario->configuracion['privacidad']['email_publico'] ?? false
                ]
            ],
            'marcas_tiempo' => [
                'creado_en' => $usuario->creado_en->toISOString(),
                'actualizado_en' => $usuario->actualizado_en->toISOString(),
                'ultimo_acceso_en' => $usuario->ultimo_acceso_en?->toISOString()
            ]
        ];
    }
}
```

## 🔧 Detalles Técnicos

### Mapeo de Tipos de Datos

Mapeo de tipos de datos de JSON a PHP:

| Tipo JSON | Tipo PHP | Ejemplo de Conversión |
|-----------|----------|-----------------------|
| string | string | `"texto"` → `'texto'` |
| number | int/float | `123` → `123`, `12.3` → `12.3` |
| boolean | boolean | `true` → `true`, `false` → `false` |
| null | null | `null` → `null` |
| array | array | `[1,2,3]` → `[1, 2, 3]` |
| object | array | `{"clave":"valor"}` → `['clave' => 'valor']` |

### Escape de Caracteres Especiales

Procesamiento de caracteres especiales en código PHP:

```php
// Ejemplos de escape de cadenas
$ejemplos = [
    'comilla_simple' => 'Es una prueba',           // Escape de comilla simple
    'comilla_doble' => "Él dijo \"Hola\"",        // Escape de comilla doble
    'barra_invertida' => 'Ruta: C:\\Users\\<USER>\nLínea 2",          // Carácter de nueva línea
    'tabulacion' => "Columna1\tColumna2",         // Carácter de tabulación
    'unicode' => 'Hola mundo',                    // Caracteres Unicode (tal como están)
    'emoji' => '😀🎉🚀',                          // Emojis (tal como están)
];
```

### Opciones de Generación de Código

Opciones de personalización para código PHP generado:

```php
// Sintaxis de array corta (PHP 5.4+)
$datos = [
    'clave' => 'valor'
];

// Sintaxis de array tradicional
$datos = array(
    'clave' => 'valor'
);

// Estilo de indentación
$datos = [
    'nivel1' => [
        'nivel2' => [
            'nivel3' => 'valor'
        ]
    ]
];
```

## 💡 Consejos de Uso

- **Verificación de Sintaxis**: Verificar que la sintaxis del código PHP generado sea correcta
- **Validación de Escape**: Verificar que los caracteres especiales estén escapados apropiadamente
- **Rendimiento**: Prestar atención al uso de memoria para conjuntos de datos grandes
- **Seguridad**: Implementar saneamiento apropiado cuando se incluyen datos de entrada del usuario

## ⚠️ Notas Importantes

- **Formato JSON**: Verificar que el JSON de entrada tenga formato válido
- **Codificación de Caracteres**: Verificar que los caracteres en español se procesen correctamente
- **Límites de Memoria**: Prestar atención a los límites de memoria al procesar archivos JSON muy grandes
- **Seguridad**: Verificar que el código generado no contenga información confidencial

## 🚀 Cómo Usar

1. **Entrada JSON**: Pegue los datos JSON a convertir en el cuadro de entrada
2. **Ejecutar Conversión**: Haga clic en el botón "Convertir" para generar código de array PHP
3. **Verificar Resultados**: El código PHP generado se muestra en el área de salida
4. **Uso de Copia**: Use el botón "Copiar" para copiar el código PHP al portapapeles
5. **Uso de Código**: Pegue en archivos PHP para usar como archivos de configuración o datos

> **Consejo**: Esta herramienta procesa localmente en el lado del cliente y no envía datos JSON al servidor, por lo que información confidencial de configuración también se puede convertir de forma segura.
