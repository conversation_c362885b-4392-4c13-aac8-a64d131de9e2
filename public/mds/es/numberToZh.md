# Herramienta de Conversión de Números a Chino

Herramienta para convertir números arábigos a números chinos, compatible con chino simplificado, chino tradicional y notación de cantidades en mayúsculas, aplicable a escenarios como informes financieros, contratos, emisión de facturas. Compatible con conversión de enteros y decimales.

## ✨ Características Principales

- 🔢 **Múltiples Tipos de Conversión**: Compatible con chino simplificado, chino tradicional, cantidades en mayúsculas
- 💰 **Conversión de Cantidades**: Compatible con notación de cantidades en mayúsculas para uso financiero
- 📊 **Soporte de Decimales**: Conversión precisa tanto de enteros como decimales
- 📋 **Copia con Un Clic**: Los resultados de conversión se pueden copiar directamente para usar
- ⚡ **Conversión en Tiempo Real**: Muestra resultados de conversión simultáneamente con la entrada

## 📖 Ejemplos de Uso

### Conversión de Números Básicos

**Número de Entrada:**
```
12345
```

**Chino Simplificado:**
```
一万二千三百四十五
```

**Chino Tradicional:**
```
一萬二千三百四十五
```

**Cantidades en Mayúsculas:**
```
壹万贰仟叁佰肆拾伍元整
```

### Conversión de Decimales

**Número de Entrada:**
```
1234.56
```

**Chino Simplificado:**
```
一千二百三十四点五六
```

**Cantidades en Mayúsculas:**
```
壹仟贰佰叁拾肆元伍角陆分
```

### Números Grandes

**Número de Entrada:**
```
9876543210
```

**Chino Simplificado:**
```
九十八亿七千六百五十四万三千二百一十
```

**Chino Tradicional:**
```
九十八億七千六百五十四萬三千二百一十
```

## 🎯 Escenarios de Aplicación

### 1. Informes Financieros

Notación de cantidades en documentos financieros:

```javascript
// Sistema de conversión de cantidades financieras
class FinancialAmountConverter {
  constructor() {
    this.digitMap = {
      0: '零', 1: '壹', 2: '贰', 3: '叁', 4: '肆',
      5: '伍', 6: '陆', 7: '柒', 8: '捌', 9: '玖'
    }
    this.unitMap = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿']
  }

  // Convertir cantidad a chino en mayúsculas
  convertToChineseAmount(amount) {
    if (amount === 0) return '零元整'
    
    const [integerPart, decimalPart] = amount.toString().split('.')
    let result = this.convertInteger(parseInt(integerPart)) + '元'
    
    if (decimalPart) {
      result += this.convertDecimal(decimalPart)
    } else {
      result += '整'
    }
    
    return result
  }

  convertInteger(num) {
    if (num === 0) return '零'
    
    const str = num.toString()
    const len = str.length
    let result = ''
    let zeroFlag = false
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(str[i])
      const unit = this.unitMap[len - i - 1]
      
      if (digit === 0) {
        zeroFlag = true
      } else {
        if (zeroFlag && result !== '') {
          result += '零'
        }
        result += this.digitMap[digit] + unit
        zeroFlag = false
      }
    }
    
    return result
  }

  convertDecimal(decimal) {
    const jiao = parseInt(decimal[0] || 0)
    const fen = parseInt(decimal[1] || 0)
    let result = ''
    
    if (jiao > 0) {
      result += this.digitMap[jiao] + '角'
    }
    if (fen > 0) {
      result += this.digitMap[fen] + '分'
    }
    
    return result || '整'
  }

  // Formato para facturas
  formatForInvoice(amount, currency = '人民币') {
    const chineseAmount = this.convertToChineseAmount(amount)
    return {
      original: `€${amount.toFixed(2)}`,
      chinese: chineseAmount,
      currency: currency,
      formatted: `${currency}${chineseAmount}`
    }
  }
}

// Ejemplo de uso
const converter = new FinancialAmountConverter()

// Conversión de cantidad de factura
const invoiceAmount = 12345.67
const formatted = converter.formatForInvoice(invoiceAmount)

console.log('Información de factura:')
console.log('Cantidad:', formatted.original)
console.log('Mayúsculas:', formatted.chinese)
console.log('Formato completo:', formatted.formatted)

// Salida:
// Información de factura:
// Cantidad: €12345.67
// Mayúsculas: 壹万贰仟叁佰肆拾伍元陆角柒分
// Formato completo: 人民币壹万贰仟叁佰肆拾伍元陆角柒分
```

### 2. Creación de Contratos

Notación de números en documentos legales:

```javascript
// Sistema de conversión de números para contratos
class ContractNumberConverter {
  constructor() {
    this.simpleDigits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    this.traditionalDigits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    this.units = ['', '十', '百', '千', '万', '十', '百', '千', '亿']
  }

  // Conversión de período de contrato
  convertContractPeriod(years, months = 0, days = 0) {
    let result = ''
    
    if (years > 0) {
      result += this.convertToSimpleChinese(years) + '年'
    }
    if (months > 0) {
      result += this.convertToSimpleChinese(months) + '个月'
    }
    if (days > 0) {
      result += this.convertToSimpleChinese(days) + '日'
    }
    
    return result || '零日'
  }

  // Conversión a chino simplificado
  convertToSimpleChinese(num) {
    if (num === 0) return '零'
    if (num < 10) return this.simpleDigits[num]
    
    const str = num.toString()
    const len = str.length
    let result = ''
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(str[i])
      const unitIndex = len - i - 1
      
      if (digit !== 0) {
        result += this.simpleDigits[digit]
        if (unitIndex > 0) {
          result += this.units[unitIndex]
        }
      } else if (result !== '' && i < len - 1) {
        const nextDigit = parseInt(str[i + 1])
        if (nextDigit !== 0) {
          result += '零'
        }
      }
    }
    
    return result
  }

  // Generar cláusula de contrato
  generateContractClause(amount, period, paymentTerms) {
    const chineseAmount = this.convertToTraditionalChinese(amount)
    const chinesePeriod = this.convertContractPeriod(period.years, period.months, period.days)
    
    return {
      amount: `La cantidad del contrato es de ${chineseAmount} euros (€${amount.toFixed(2)})`,
      period: `El período del contrato es de ${chinesePeriod}`,
      payment: `Método de pago: ${paymentTerms}`,
      fullClause: `La cantidad de este contrato es de ${chineseAmount} euros (€${amount.toFixed(2)}), el período del contrato es de ${chinesePeriod}, método de pago: ${paymentTerms}.`
    }
  }

  convertToTraditionalChinese(num) {
    // Implementación de conversión a tradicional (simplificada)
    return this.convertToSimpleChinese(num).replace(/一/g, '壹').replace(/二/g, '贰')
  }
}

// Ejemplo de uso
const contractConverter = new ContractNumberConverter()

// Información del contrato
const contractInfo = {
  amount: 500000,
  period: { years: 2, months: 6, days: 0 },
  paymentTerms: 'Pago a plazos, pago mensual'
}

const clause = contractConverter.generateContractClause(
  contractInfo.amount,
  contractInfo.period,
  contractInfo.paymentTerms
)

console.log('Cláusula del contrato:')
console.log(clause.fullClause)
// Salida: La cantidad de este contrato es de cincuenta万 euros (€500000.00), el período del contrato es de dos años seis meses, método de pago: Pago a plazos, pago mensual.
```

### 3. Sistema Contable

Procesamiento de números en software contable:

```javascript
// Sistema de conversión de números contables
class AccountingNumberConverter {
  constructor() {
    this.config = {
      currency: '人民币',
      precision: 2,
      useTraditional: true
    }
  }

  // Crear entrada de diario contable
  createJournalEntry(debit, credit, description) {
    return {
      date: new Date().toISOString().split('T')[0],
      description: description,
      debit: {
        amount: debit,
        chinese: this.convertToAccountingFormat(debit)
      },
      credit: {
        amount: credit,
        chinese: this.convertToAccountingFormat(credit)
      },
      balance: debit - credit
    }
  }

  // Conversión a formato contable
  convertToAccountingFormat(amount) {
    const absAmount = Math.abs(amount)
    const chineseAmount = this.convertToChinese(absAmount)
    const sign = amount >= 0 ? '' : '负'
    
    return `${sign}${this.config.currency}${chineseAmount}元`
  }

  // Generar informe mensual
  generateMonthlyReport(transactions) {
    const totalDebit = transactions.reduce((sum, t) => sum + t.debit, 0)
    const totalCredit = transactions.reduce((sum, t) => sum + t.credit, 0)
    const netIncome = totalCredit - totalDebit
    
    return {
      period: 'Este mes',
      totalDebit: {
        amount: totalDebit,
        chinese: this.convertToAccountingFormat(totalDebit)
      },
      totalCredit: {
        amount: totalCredit,
        chinese: this.convertToAccountingFormat(totalCredit)
      },
      netIncome: {
        amount: netIncome,
        chinese: this.convertToAccountingFormat(netIncome)
      },
      summary: `Ingresos totales de este mes ${this.convertToAccountingFormat(totalCredit)}, gastos totales ${this.convertToAccountingFormat(totalDebit)}, ingresos netos ${this.convertToAccountingFormat(netIncome)}.`
    }
  }

  convertToChinese(num) {
    // Implementación de conversión a chino (simplificada)
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    const units = ['', '拾', '佰', '仟', '万']
    
    if (num === 0) return '零'
    
    let result = ''
    const str = num.toString()
    const len = str.length
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(str[i])
      const unitIndex = len - i - 1
      
      if (digit !== 0) {
        result += digits[digit]
        if (unitIndex > 0 && unitIndex < units.length) {
          result += units[unitIndex]
        }
      }
    }
    
    return result
  }
}

// Ejemplo de uso
const accounting = new AccountingNumberConverter()

// Registros de transacciones
const transactions = [
  { debit: 0, credit: 50000, description: 'Ingresos por ventas' },
  { debit: 15000, credit: 0, description: 'Compra de suministros de oficina' },
  { debit: 8000, credit: 0, description: 'Gastos de transporte' }
]

// Generar informe mensual
const monthlyReport = accounting.generateMonthlyReport(transactions)
console.log('Informe mensual:')
console.log(monthlyReport.summary)
```

## 🔧 Detalles Técnicos

### Sistema de Números Chinos

Estructura básica de números chinos:

**Números Básicos:**
- Simplificado: 零、一、二、三、四、五、六、七、八、九
- Tradicional: 零、壹、贰、叁、肆、伍、陆、柒、捌、玖

**Unidades:**
- Unidades básicas: 十、百、千、万、亿
- Unidades de cantidad: 元、角、分

**Reglas Especiales:**
- Manejo de ceros: Ceros consecutivos se representan con un solo "零"
- Omisión de unidades: Omitir unidades bajo ciertas condiciones
- Uso de mayúsculas: Usar mayúsculas en documentos financieros para prevenir alteraciones

### Algoritmo de Conversión

```javascript
// Algoritmo completo de conversión
class ChineseNumberConverter {
  constructor() {
    this.digits = {
      simple: ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'],
      traditional: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    }
    this.units = ['', '十', '百', '千', '万', '十', '百', '千', '亿']
  }

  convert(number, type = 'simple') {
    if (number === 0) return this.digits[type][0]
    
    const isNegative = number < 0
    const absNumber = Math.abs(number)
    const [integerPart, decimalPart] = absNumber.toString().split('.')
    
    let result = this.convertInteger(parseInt(integerPart), type)
    
    if (decimalPart) {
      result += '点' + this.convertDecimal(decimalPart, type)
    }
    
    return isNegative ? '负' + result : result
  }

  convertInteger(num, type) {
    const str = num.toString()
    const len = str.length
    let result = ''
    let hasZero = false
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(str[i])
      const unitIndex = len - i - 1
      
      if (digit === 0) {
        hasZero = true
      } else {
        if (hasZero && result !== '') {
          result += this.digits[type][0] // 零
        }
        result += this.digits[type][digit]
        if (unitIndex > 0) {
          result += this.units[unitIndex]
        }
        hasZero = false
      }
    }
    
    return result
  }

  convertDecimal(decimal, type) {
    return decimal.split('').map(d => this.digits[type][parseInt(d)]).join('')
  }
}
```

## 💡 Consejos de Uso

- **Selección de Uso**: Usar mayúsculas para documentos financieros, simplificado para documentos generales
- **Verificación de Precisión**: Prestar atención al número de decimales
- **Consideración de Contexto**: Elegir formato apropiado según el tipo de documento usado
- **Verificación Importante**: Siempre verificar resultados de conversión para documentos importantes

## ⚠️ Notas Importantes

- **Límites de Precisión**: Prestar atención a la precisión para números muy grandes o decimales complejos
- **Diferencias Regionales**: China continental y Taiwán/Hong Kong pueden tener diferentes notaciones
- **Requisitos Legales**: Usar notación que cumpla con requisitos legales en documentos financieros
- **Codificación de Caracteres**: Asegurar visualización correcta de caracteres chinos

## 🚀 Cómo Usar

1. **Entrada de Números**: Ingrese el número a convertir en el cuadro de entrada
2. **Selección de Tipo**: Elija simplificado, tradicional o cantidades en mayúsculas
3. **Verificación de Resultados**: Los resultados de conversión se muestran en tiempo real
4. **Uso de Copia**: Use el botón "Copiar" para copiar los resultados al portapapeles
5. **Uso Aplicado**: Use en contratos, facturas, informes financieros, etc.

> **Consejo**: Esta herramienta procesa localmente en el lado del cliente y no envía datos de entrada al servidor, por lo que información financiera confidencial también se puede convertir de forma segura.
