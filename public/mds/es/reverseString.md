# Herramienta de Inversión de Cadenas

La inversión de cadenas es una operación básica de procesamiento de texto que invierte completamente el orden de los caracteres en una cadena. Esta herramienta soporta múltiples modos de inversión, incluyendo inversión completa, inversión por líneas, inversión por palabras, etc., aplicable a escenarios como procesamiento de texto, conversión de datos, práctica de programación.

## ✨ Características Principales

- 🔄 **Múltiples Modos de Inversión**: Compatible con inversión completa, inversión por líneas, inversión por palabras
- 🌐 **Soporte Unicode**: Maneja correctamente caracteres Unicode como español, emojis
- ⚡ **Procesamiento en Tiempo Real**: Muestra resultados de inversión simultáneamente con la entrada de texto
- 📋 **Copia con Un Clic**: Los resultados de inversión se pueden copiar directamente para usar
- 🔧 **Procesamiento por Lotes**: Compatible con inversión por lotes de texto multilínea

## 📖 Ejemplos de Uso

### Inversión Completa

**Texto de Entrada:**
```
¡Hola Mundo!
```

**Resultado de Inversión:**
```
!odnuM aloH¡
```

### Inversión de Texto en Español

**Texto de Entrada:**
```
Bienvenido a ToolMi
```

**Resultado de Inversión:**
```
iMlooT a odineinevB
```

### Inversión por Líneas

**Texto de Entrada:**
```
Primera línea de texto
Segunda línea de texto
Tercera línea de texto
```

**Resultado de Inversión:**
```
otxet ed aenìl aremirP
otxet ed aenìl adnugeS
otxet ed aenìl arecreT
```

### Inversión por Palabras

**Texto de Entrada:**
```
Hola Mundo Bienvenido
```

**Resultado de Inversión:**
```
Bienvenido Mundo Hola
```

## 🎯 Escenarios de Aplicación

### 1. Procesamiento de Datos

Uso de inversión de cadenas en procesamiento y conversión de datos:

```javascript
// Procesamiento de enmascaramiento de datos
function maskSensitiveData(data) {
  // Usar inversión como ofuscación simple
  const reversed = data.split('').reverse().join('')
  return btoa(reversed) // Codificación Base64 adicional
}

// Ejemplo de uso
const sensitiveInfo = "Información confidencial del usuario"
const masked = maskSensitiveData(sensitiveInfo)
console.log('Después del enmascaramiento:', masked)

// Función de descifrado
function unmaskSensitiveData(maskedData) {
  const decoded = atob(maskedData)
  return decoded.split('').reverse().join('')
}

const original = unmaskSensitiveData(masked)
console.log('Datos originales:', original)
```

### 2. Juegos de Texto

Crear juegos de caracteres y rompecabezas:

```javascript
// Detector de palíndromos
function isPalindrome(str) {
  const cleaned = str.toLowerCase().replace(/[^a-z0-9áéíóúñü]/g, '')
  const reversed = cleaned.split('').reverse().join('')
  return cleaned === reversed
}

// Ejemplos de prueba
console.log(isPalindrome("A man a plan a canal Panama")) // true
console.log(isPalindrome("race a car")) // false
console.log(isPalindrome("Anita lava la tina")) // true

// Generador de rompecabezas de palabras
function generateWordPuzzle(sentence) {
  const words = sentence.split(' ')
  const puzzles = words.map(word => {
    return {
      original: word,
      reversed: word.split('').reverse().join(''),
      hint: `${word.length} caracteres`
    }
  })
  return puzzles
}

// Ejemplo de uso
const puzzles = generateWordPuzzle("ToolMi Herramientas En Línea")
console.log(puzzles)
// [
//   { original: "ToolMi", reversed: "iMlooT", hint: "6 caracteres" },
//   { original: "Herramientas", reversed: "satneimarre", hint: "12 caracteres" },
//   { original: "En", reversed: "nE", hint: "2 caracteres" },
//   { original: "Línea", reversed: "aenìL", hint: "5 caracteres" }
// ]
```

### 3. Práctica de Programación

Para aprendizaje de algoritmos y práctica de programación:

```javascript
// Múltiples métodos de implementación de inversión de cadenas

// Método 1: Usar métodos integrados
function reverseString1(str) {
  return str.split('').reverse().join('')
}

// Método 2: Usar bucle
function reverseString2(str) {
  let result = ''
  for (let i = str.length - 1; i >= 0; i--) {
    result += str[i]
  }
  return result
}

// Método 3: Usar recursión
function reverseString3(str) {
  if (str === '') return ''
  return reverseString3(str.substr(1)) + str.charAt(0)
}

// Método 4: Usar algoritmo de dos punteros
function reverseString4(str) {
  const arr = str.split('')
  let left = 0
  let right = arr.length - 1
  
  while (left < right) {
    [arr[left], arr[right]] = [arr[right], arr[left]]
    left++
    right--
  }
  
  return arr.join('')
}

// Prueba de rendimiento
function performanceTest(str) {
  const methods = [reverseString1, reverseString2, reverseString3, reverseString4]
  const methodNames = ['Métodos integrados', 'Bucle', 'Recursión', 'Dos punteros']
  
  methods.forEach((method, index) => {
    const start = performance.now()
    const result = method(str)
    const end = performance.now()
    console.log(`${methodNames[index]}: ${end - start}ms`)
  })
}

// Prueba
const testString = "Esta es una cadena larga para prueba de rendimiento".repeat(1000)
performanceTest(testString)
```

### 4. Arte de Texto

Crear arte de texto y efectos:

```javascript
// Efectos de animación de texto
class TextAnimation {
  constructor(element, text) {
    this.element = element
    this.originalText = text
    this.currentText = text
  }

  // Animación de inversión carácter por carácter
  async reverseAnimation(speed = 100) {
    const chars = this.originalText.split('')
    
    for (let i = 0; i < chars.length; i++) {
      // Invertir los primeros i caracteres
      const reversed = chars.slice(0, i + 1).reverse()
      const remaining = chars.slice(i + 1)
      this.currentText = [...reversed, ...remaining].join('')
      
      this.element.textContent = this.currentText
      await this.delay(speed)
    }
  }

  // Efecto de inversión en onda
  async waveReverse(speed = 50) {
    const chars = this.originalText.split('')
    const length = chars.length
    
    for (let wave = 0; wave < length; wave++) {
      const newChars = [...chars]
      
      // Crear efecto de onda
      for (let i = 0; i < length; i++) {
        const distance = Math.abs(i - wave)
        if (distance <= 2) {
          // Invertir caracteres dentro del rango de onda
          const start = Math.max(0, wave - 2)
          const end = Math.min(length, wave + 3)
          const section = chars.slice(start, end).reverse()
          newChars.splice(start, section.length, ...section)
        }
      }
      
      this.element.textContent = newChars.join('')
      await this.delay(speed)
    }
    
    // Finalmente invertir completamente
    this.element.textContent = chars.reverse().join('')
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Ejemplo de uso
const textElement = document.getElementById('animated-text')
const animation = new TextAnimation(textElement, 'Plataforma en línea ToolMi')

// Iniciar animación
animation.reverseAnimation(200)
```

### 5. Validación de Datos

Para validación de datos y verificación de integridad:

```javascript
// Verificador simple de integridad de datos
class DataIntegrityChecker {
  // Generar checksum
  static generateChecksum(data) {
    const reversed = data.split('').reverse().join('')
    const combined = data + reversed
    
    // Algoritmo hash simple
    let hash = 0
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convertir a entero de 32 bits
    }
    
    return Math.abs(hash).toString(36)
  }

  // Verificar integridad de datos
  static verifyIntegrity(data, checksum) {
    const calculatedChecksum = this.generateChecksum(data)
    return calculatedChecksum === checksum
  }
}

// Ejemplo de uso
const originalData = "Datos importantes de negocio"
const checksum = DataIntegrityChecker.generateChecksum(originalData)
console.log('Checksum:', checksum)

// Verificación de datos
const isValid = DataIntegrityChecker.verifyIntegrity(originalData, checksum)
console.log('Integridad de datos:', isValid ? 'Normal' : 'Anormal')

// Simular alteración de datos
const tamperedData = "Datos importantes de negocio modificados"
const isTamperedValid = DataIntegrityChecker.verifyIntegrity(tamperedData, checksum)
console.log('Verificación de datos alterados:', isTamperedValid ? 'Normal' : 'Anormal')
```

## 🔧 Detalles Técnicos

### Procesamiento de Caracteres Unicode

Manejar correctamente la inversión de caracteres Unicode:

**Inversión de caracteres básicos:**
- Caracteres ASCII: Inversión directa de bytes
- Caracteres españoles: Inversión por puntos de código Unicode
- Emojis: Necesita manejo especial de caracteres compuestos

**Procesamiento de caracteres complejos:**
```javascript
// Manejar correctamente la inversión de caracteres Unicode
function reverseUnicode(str) {
  // Usar Array.from para dividir correctamente caracteres Unicode
  return Array.from(str).reverse().join('')
}

// Probar diferentes tipos de caracteres
console.log(reverseUnicode("Hola 世界 🌍"))
// Salida: 🌍 界世 aloH
```

### Optimización de Rendimiento

Comparación de rendimiento de diferentes métodos de inversión:

**Ranking de rendimiento de métodos:**
1. Métodos integrados: `split('').reverse().join('')`
2. Algoritmo de dos punteros: Aplicable a cadenas grandes
3. Construcción de bucle: Alta eficiencia de memoria
4. Método recursivo: Conciso pero rendimiento inferior

## 💡 Consejos de Uso

- **Procesamiento Unicode**: Usar `Array.from()` para manejar correctamente caracteres compuestos
- **Consideración de Rendimiento**: Se recomienda algoritmo de dos punteros para texto grande
- **Optimización de Memoria**: Evitar crear demasiadas cadenas temporales
- **Caracteres Especiales**: Prestar atención al manejo de caracteres de nueva línea y símbolos especiales

## ⚠️ Notas Importantes

- **Codificación de Caracteres**: Asegurar manejo correcto de caracteres codificados en UTF-8
- **Caracteres Compuestos**: Emojis y otros caracteres compuestos necesitan manejo especial
- **Impacto en Rendimiento**: Inversión de texto súper largo puede afectar el rendimiento
- **Uso de Memoria**: Prestar atención al uso de memoria al procesar texto grande

## 🚀 Cómo Usar

1. **Entrada de Texto**: Ingrese el texto a invertir en el cuadro de entrada
2. **Selección de Modo**: Elija inversión completa, inversión por líneas o inversión por palabras
3. **Verificación de Resultados**: Los resultados de inversión se muestran en tiempo real en el área de salida
4. **Uso de Copia**: Haga clic en el botón "Copiar" para copiar los resultados al portapapeles
5. **Procesamiento por Lotes**: Compatible con operación de inversión por lotes de texto multilínea

> **Consejo**: Esta herramienta procesa localmente en el lado del cliente y no sube contenido de texto al servidor, garantizando privacidad y seguridad.
