# Herramienta de Conversión JSON ⇄ YAML

JSON (JavaScript Object Notation) y YAML (YAML Ain't Markup Language) son ambos formatos ampliamente utilizados para intercambio de datos y archivos de configuración. Esta herramienta permite realizar conversiones mutuas entre JSON y YAML de manera sencilla.

## ✨ Características Principales

- 🔄 **Conversión Bidireccional**: Compatible con conversión en ambas direcciones JSON a YAML y YAML a JSON
- ✅ **Validación de Formato**: Verificación de sintaxis de datos de entrada y visualización de errores
- 🎨 **Salida Hermosa**: Formato en estilo legible
- 📋 **Copia con Un Clic**: Los resultados de conversión se pueden copiar directamente
- 🌐 **Soporte Multiidioma**: Totalmente compatible con caracteres Unicode como español

## 📖 Ejemplos de Uso

### Conversión JSON → YAML

**JSON de Entrada:**
```json
{
  "nombre": "ToolMi",
  "version": "1.0.0",
  "descripcion": "Colección de herramientas en línea útiles",
  "caracteristicas": [
    "Conversión JSON",
    "Conversión YAML",
    "Validación de datos"
  ],
  "configuracion": {
    "debug": true,
    "puerto": 3000,
    "baseDatos": {
      "host": "localhost",
      "puerto": 5432
    }
  }
}
```

**YAML Convertido:**
```yaml
nombre: ToolMi
version: 1.0.0
descripcion: Colección de herramientas en línea útiles
caracteristicas:
  - Conversión JSON
  - Conversión YAML
  - Validación de datos
configuracion:
  debug: true
  puerto: 3000
  baseDatos:
    host: localhost
    puerto: 5432
```

### Conversión YAML → JSON

**YAML de Entrada:**
```yaml
# Configuración de aplicación
app:
  nombre: Aplicación de Ejemplo
  version: 2.1.0
  
servidor:
  host: 0.0.0.0
  puerto: 8080
  ssl:
    habilitado: true
    rutaCertificado: /etc/ssl/cert.pem
    
baseDatos:
  tipo: postgresql
  host: localhost
  puerto: 5432
  credenciales:
    usuario: admin
    contraseña: secreto
```

**JSON Convertido:**
```json
{
  "app": {
    "nombre": "Aplicación de Ejemplo",
    "version": "2.1.0"
  },
  "servidor": {
    "host": "0.0.0.0",
    "puerto": 8080,
    "ssl": {
      "habilitado": true,
      "rutaCertificado": "/etc/ssl/cert.pem"
    }
  },
  "baseDatos": {
    "tipo": "postgresql",
    "host": "localhost",
    "puerto": 5432,
    "credenciales": {
      "usuario": "admin",
      "contraseña": "secreto"
    }
  }
}
```

## 🎯 Escenarios de Aplicación

### 1. Gestión de Archivos de Configuración

Conversión de formato de configuración de aplicaciones:

```javascript
// Utilidad de conversión de archivos de configuración
class ConvertidorConfiguracion {
  constructor() {
    this.analizadorYaml = require('js-yaml');
  }

  // Convertir configuración JSON a YAML
  jsonAYaml(configuracionJson) {
    try {
      const objetoConfiguracion = typeof configuracionJson === 'string' 
        ? JSON.parse(configuracionJson) 
        : configuracionJson;
      
      return this.analizadorYaml.dump(objetoConfiguracion, {
        indent: 2,
        lineWidth: 120,
        noRefs: true
      });
    } catch (error) {
      throw new Error(`Error de conversión JSON→YAML: ${error.message}`);
    }
  }

  // Convertir configuración YAML a JSON
  yamlAJson(configuracionYaml, bonito = true) {
    try {
      const objetoConfiguracion = this.analizadorYaml.load(configuracionYaml);
      
      return bonito 
        ? JSON.stringify(objetoConfiguracion, null, 2)
        : JSON.stringify(objetoConfiguracion);
    } catch (error) {
      throw new Error(`Error de conversión YAML→JSON: ${error.message}`);
    }
  }

  // Validar configuración
  validarConfiguracion(configuracion, formato) {
    try {
      if (formato === 'json') {
        JSON.parse(configuracion);
        return { valido: true, mensaje: 'Formato JSON correcto' };
      } else if (formato === 'yaml') {
        this.analizadorYaml.load(configuracion);
        return { valido: true, mensaje: 'Formato YAML correcto' };
      }
    } catch (error) {
      return { 
        valido: false, 
        mensaje: `Error de formato ${formato.toUpperCase()}: ${error.message}` 
      };
    }
  }

  // Migrar configuración
  migrarConfiguracion(configuracionAntigua, formatoAntiguo, formatoNuevo) {
    try {
      // Validar formato original
      const validacion = this.validarConfiguracion(configuracionAntigua, formatoAntiguo);
      if (!validacion.valido) {
        throw new Error(validacion.mensaje);
      }

      // Ejecutar conversión
      if (formatoAntiguo === 'json' && formatoNuevo === 'yaml') {
        return this.jsonAYaml(configuracionAntigua);
      } else if (formatoAntiguo === 'yaml' && formatoNuevo === 'json') {
        return this.yamlAJson(configuracionAntigua);
      } else {
        throw new Error('Formato de conversión no soportado');
      }
    } catch (error) {
      throw new Error(`Error de migración de configuración: ${error.message}`);
    }
  }
}

// Ejemplo de uso
const convertidor = new ConvertidorConfiguracion();

// Migrar configuración JSON existente a YAML
const configuracionJson = `{
  "baseDatos": {
    "host": "localhost",
    "puerto": 5432,
    "nombre": "miapp"
  },
  "redis": {
    "host": "localhost",
    "puerto": 6379
  }
}`;

try {
  const configuracionYaml = convertidor.migrarConfiguracion(configuracionJson, 'json', 'yaml');
  console.log('Archivo de configuración YAML:');
  console.log(configuracionYaml);
} catch (error) {
  console.error('Error de conversión:', error.message);
}
```

### 2. Conversión de Documentación API

Conversión de formato de especificaciones OpenAPI:

```javascript
// Convertidor de especificaciones OpenAPI
class ConvertidorOpenAPI {
  constructor() {
    this.yaml = require('js-yaml');
  }

  // Convertir especificación OpenAPI JSON a YAML
  convertirAYaml(openApiJson) {
    try {
      const especificacion = typeof openApiJson === 'string' 
        ? JSON.parse(openApiJson) 
        : openApiJson;

      // Validación básica de especificación OpenAPI
      if (!especificacion.openapi && !especificacion.swagger) {
        throw new Error('No es una especificación OpenAPI válida');
      }

      return this.yaml.dump(especificacion, {
        indent: 2,
        lineWidth: 120,
        noRefs: true,
        sortKeys: false
      });
    } catch (error) {
      throw new Error(`Error de conversión OpenAPI: ${error.message}`);
    }
  }

  // Convertir especificación OpenAPI YAML a JSON
  convertirAJson(openApiYaml, minificar = false) {
    try {
      const especificacion = this.yaml.load(openApiYaml);

      // Validación básica de especificación OpenAPI
      if (!especificacion.openapi && !especificacion.swagger) {
        throw new Error('No es una especificación OpenAPI válida');
      }

      return minificar 
        ? JSON.stringify(especificacion)
        : JSON.stringify(especificacion, null, 2);
    } catch (error) {
      throw new Error(`Error de conversión OpenAPI: ${error.message}`);
    }
  }

  // Extraer información de especificación
  extraerInfoApi(especificacion) {
    try {
      const especificacionApi = typeof especificacion === 'string' 
        ? this.yaml.load(especificacion) 
        : especificacion;

      return {
        version: especificacionApi.openapi || especificacionApi.swagger,
        titulo: especificacionApi.info?.title || 'Título no establecido',
        descripcion: especificacionApi.info?.description || 'Sin descripción',
        version: especificacionApi.info?.version || '1.0.0',
        servidores: especificacionApi.servers || [],
        conteoRutas: Object.keys(especificacionApi.paths || {}).length,
        conteoComponentes: Object.keys(especificacionApi.components?.schemas || {}).length
      };
    } catch (error) {
      throw new Error(`Error de análisis de especificación: ${error.message}`);
    }
  }
}

// Ejemplo de uso
const convertidorApi = new ConvertidorOpenAPI();

// Especificación OpenAPI de ejemplo (JSON)
const openApiJson = {
  "openapi": "3.0.0",
  "info": {
    "title": "API ToolMi",
    "description": "Especificación API de herramientas útiles",
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "https://api.toolmi.com/v1",
      "description": "Entorno de producción"
    }
  ],
  "paths": {
    "/herramientas": {
      "get": {
        "summary": "Obtener lista de herramientas",
        "responses": {
          "200": {
            "description": "Éxito",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/Herramienta"
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "Herramienta": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string"
          },
          "nombre": {
            "type": "string"
          },
          "descripcion": {
            "type": "string"
          }
        }
      }
    }
  }
};

// Conversión JSON → YAML
try {
  const especificacionYaml = convertidorApi.convertirAYaml(openApiJson);
  console.log('Especificación OpenAPI en formato YAML:');
  console.log(especificacionYaml);

  // Extraer información de especificación
  const infoApi = convertidorApi.extraerInfoApi(openApiJson);
  console.log('Información API:', infoApi);
} catch (error) {
  console.error('Error de conversión:', error.message);
}
```

### 3. Configuración de Pipeline CI/CD

Conversión de configuración de GitHub Actions y Docker:

```javascript
// Convertidor de configuración CI/CD
class ConvertidorConfiguracionCICD {
  constructor() {
    this.yaml = require('js-yaml');
  }

  // Convertir configuración de GitHub Actions
  convertirGitHubActions(configuracion, formatoDestino) {
    try {
      if (formatoDestino === 'yaml') {
        const flujoTrabajo = typeof configuracion === 'string' ? JSON.parse(configuracion) : configuracion;
        return this.yaml.dump(flujoTrabajo, { indent: 2 });
      } else {
        const flujoTrabajo = this.yaml.load(configuracion);
        return JSON.stringify(flujoTrabajo, null, 2);
      }
    } catch (error) {
      throw new Error(`Error de conversión de configuración GitHub Actions: ${error.message}`);
    }
  }

  // Convertir configuración de Docker Compose
  convertirDockerCompose(configuracion, formatoDestino) {
    try {
      if (formatoDestino === 'yaml') {
        const compose = typeof configuracion === 'string' ? JSON.parse(configuracion) : configuracion;
        return this.yaml.dump(compose, { indent: 2 });
      } else {
        const compose = this.yaml.load(configuracion);
        return JSON.stringify(compose, null, 2);
      }
    } catch (error) {
      throw new Error(`Error de conversión de configuración Docker Compose: ${error.message}`);
    }
  }

  // Validar archivo de configuración
  validarConfiguracionCI(configuracion, tipo) {
    try {
      const objetoConfiguracion = typeof configuracion === 'string' ? this.yaml.load(configuracion) : configuracion;
      
      switch (tipo) {
        case 'github-actions':
          if (!objetoConfiguracion.on || !objetoConfiguracion.jobs) {
            throw new Error('Faltan campos requeridos en configuración GitHub Actions');
          }
          break;
        case 'docker-compose':
          if (!objetoConfiguracion.version || !objetoConfiguracion.services) {
            throw new Error('Faltan campos requeridos en configuración Docker Compose');
          }
          break;
        default:
          throw new Error('Tipo de configuración no soportado');
      }

      return { valido: true, mensaje: 'Configuración correcta' };
    } catch (error) {
      return { valido: false, mensaje: error.message };
    }
  }
}

// Ejemplo de uso
const convertidorCicd = new ConvertidorConfiguracionCICD();

// Configuración GitHub Actions de ejemplo (YAML)
const githubActionsYaml = `
name: Pipeline CI/CD
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Configurar Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Instalar dependencias
        run: npm ci
      - name: Ejecutar pruebas
        run: npm test
`;

try {
  // Conversión YAML → JSON
  const configuracionJson = convertidorCicd.convertirGitHubActions(githubActionsYaml, 'json');
  console.log('Configuración GitHub Actions en formato JSON:');
  console.log(configuracionJson);

  // Validar configuración
  const validacion = convertidorCicd.validarConfiguracionCI(githubActionsYaml, 'github-actions');
  console.log('Resultado de validación:', validacion);
} catch (error) {
  console.error('Error de conversión:', error.message);
}
```

## 🔧 Detalles Técnicos

### Comparación de Características JSON y YAML

| Característica | JSON | YAML |
|----------------|------|------|
| Legibilidad | Media | Alta |
| Tamaño de archivo | Pequeño | Grande |
| Comentarios | No | Sí |
| Cadenas multilínea | No | Sí |
| Tipos de datos | Limitados | Ricos |
| Velocidad de análisis | Rápida | Media |

### Consideraciones de Conversión

```javascript
// Consideraciones y mejores prácticas de conversión
const mejoresPracticasConversion = {
  // Manejo de comentarios
  comentarios: {
    problema: 'JSON no soporta comentarios, se pierden al convertir YAML→JSON',
    solucion: 'Registrar información importante en campos description en lugar de comentarios'
  },

  // Diferencias de tipos de datos
  tiposDatos: {
    problema: 'YAML expresa fechas, null, booleanos de manera más flexible',
    solucion: 'Verificar tipos de datos después de conversión y ajustar según sea necesario'
  },

  // Cadenas multilínea
  cadenaMultilinea: {
    problema: 'Cadenas multilínea YAML (|, >) se convierten en cadenas simples en JSON',
    solucion: 'Usar caracteres de nueva línea (\\n) para representar'
  },

  // Referencias y anclas
  referencias: {
    problema: 'Anclas (&) y referencias (*) de YAML se expanden en JSON',
    solucion: 'Reconocer que pueden ocurrir datos duplicados'
  }
};
```

## 💡 Consejos de Uso

- **Selección de Formato**: YAML es adecuado para archivos de configuración, JSON para comunicación API
- **Validación Importante**: Siempre verificar integridad de datos antes y después de conversión
- **Uso de Comentarios**: Aprovechar comentarios en YAML para mejorar legibilidad
- **Respaldo**: Crear respaldo de archivos de configuración importantes antes de conversión

## ⚠️ Notas Importantes

- **Pérdida de Comentarios**: Los comentarios se pierden al convertir YAML→JSON
- **Cambio de Tipos de Datos**: Los tipos de datos pueden cambiar durante conversión
- **Errores de Sintaxis**: Entrada con formato inválido resultará en error
- **Codificación de Caracteres**: Se recomienda usar codificación UTF-8

## 🚀 Cómo Usar

1. **Entrada de Datos**: Pegue los datos JSON o YAML a convertir en el cuadro de entrada
2. **Selección de Dirección de Conversión**: Seleccione "JSON→YAML" o "YAML→JSON"
3. **Ejecutar Conversión**: Haga clic en el botón "Convertir" para ejecutar conversión
4. **Verificar Resultados**: Los resultados de conversión se muestran en el área de salida
5. **Uso de Copia**: Use el botón "Copiar" para copiar los resultados al portapapeles

> **Consejo**: Esta herramienta procesa localmente en el lado del cliente y no envía datos al servidor, por lo que la información confidencial también se puede convertir de forma segura.
